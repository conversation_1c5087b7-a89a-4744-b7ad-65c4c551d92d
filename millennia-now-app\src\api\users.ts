// 用户管理API接口
import { UserInfo, UserRole } from './auth'
import { buildUrl } from '@/utils/url'
// 导入统一的API配置
import { getBaseURL } from '@/config/api'

const BASE_URL = getBaseURL()

// 用户列表响应接口
export interface UserListResponse {
  users: UserInfo[]
  total: number
  page: number
  page_size: number
}

// 用户列表查询参数
export interface UserListQuery {
  page?: number
  page_size?: number
  role?: UserRole
  province_id?: number
  city_id?: number
  district_id?: number
  search?: string
}

// 用户角色更新请求
export interface UserRoleUpdateRequest {
  role: UserRole
  province_id?: number
  city_id?: number
  district_id?: number
  module_permissions?: ModulePermissions
}

// 模块权限接口
export interface ModulePermissions {
  ancient_books: boolean
  paintings: boolean
  archives: boolean
  videos: boolean
}

// 模块权限更新请求
export interface ModulePermissionUpdateRequest {
  module_permissions: ModulePermissions
}

// 可用模块信息
export interface ModuleInfo {
  key: string
  name: string
  description: string
}

// 可用模块列表响应
export interface AvailableModulesResponse {
  modules: ModuleInfo[]
}

// 搜索游客用户的查询参数
export interface SearchGuestQuery {
  phone: string  // 必须提供完整的手机号
  page?: number
  page_size?: number
}

// 获取用户列表
export const getUserList = async (query: UserListQuery = {}): Promise<UserListResponse> => {
  try {
    const token = uni.getStorageSync('access_token')
    const url = buildUrl(`${BASE_URL}/users/`, query)
    
    const response = await uni.request({
      url,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    throw error
  }
}

// 获取用户详情
export const getUserDetail = async (userId: number): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    throw error
  }
}

// 更新用户角色
export const updateUserRole = async (userId: number, request: UserRoleUpdateRequest): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}/role`,
      method: 'PUT',
      data: request,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '更新用户角色失败')
    }
  } catch (error) {
    console.error('更新用户角色失败:', error)
    throw error
  }
}

// 更新用户状态
export const updateUserStatus = async (userId: number, isActive: boolean): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}/status`,
      method: 'PUT',
      data: { is_active: isActive },
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '更新用户状态失败')
    }
  } catch (error) {
    console.error('更新用户状态失败:', error)
    throw error
  }
}

// 删除用户
export const deleteUser = async (userId: number): Promise<void> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}`,
      method: 'DELETE',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode !== 200) {
      throw new Error(response.data?.detail || '删除用户失败')
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    throw error
  }
}

// 获取角色显示名称
export const getRoleDisplayName = (role: string): string => {
  const roleMap = {
    [UserRole.GUEST]: '游客',
    [UserRole.DISTRICT_ADMIN]: '区县管理员',
    [UserRole.CITY_ADMIN]: '市级管理员',
    [UserRole.PROVINCE_ADMIN]: '省级管理员',
    [UserRole.SUPER_ADMIN]: '超级管理员'
  }
  return roleMap[role as UserRole] || role
}

// 获取性别显示名称
export const getGenderDisplayName = (gender: number): string => {
  const genderMap = {
    0: '未知',
    1: '男',
    2: '女'
  }
  return genderMap[gender as keyof typeof genderMap] || '未知'
}

// 搜索游客用户
export const searchGuestUsers = async (query: SearchGuestQuery): Promise<UserListResponse> => {
  try {
    const token = uni.getStorageSync('access_token')
    const url = buildUrl(`${BASE_URL}/users/search/guests`, query)
    
    const response = await uni.request({
      url,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '搜索游客用户失败')
    }
  } catch (error) {
    console.error('搜索游客用户失败:', error)
    throw error
  }
}

// 获取可用模块列表
export const getAvailableModules = async (): Promise<AvailableModulesResponse> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/module-permissions/available`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '获取可用模块列表失败')
    }
  } catch (error) {
    console.error('获取可用模块列表失败:', error)
    throw error
  }
}

// 获取用户模块权限
export const getUserModulePermissions = async (userId: number): Promise<{ user_id: number; module_permissions: ModulePermissions }> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}/module-permissions`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '获取用户模块权限失败')
    }
  } catch (error) {
    console.error('获取用户模块权限失败:', error)
    throw error
  }
}

// 更新用户模块权限
export const updateUserModulePermissions = async (userId: number, request: ModulePermissionUpdateRequest): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/users/${userId}/module-permissions`,
      method: 'PUT',
      data: request,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '更新用户模块权限失败')
    }
  } catch (error) {
    console.error('更新用户模块权限失败:', error)
    throw error
  }
} 