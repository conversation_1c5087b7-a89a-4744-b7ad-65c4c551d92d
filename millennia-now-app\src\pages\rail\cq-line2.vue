<template>
  <view class="line2-tour-app">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">轨道游重庆-2号线</text>
        <view class="line-badge"
              @click="showLineSelector">
          <text class="line-number">2</text>
          <text class="line-name">号线</text>
          <text class="line-arrow">▼</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 线路概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <view class="line-info">
            <view class="line-color-bar"></view>
            <view class="line-details">
              <text class="line-title">重庆轨道交通<text style="color:#00a651;">2</text>号线</text>
              <text class="line-subtitle">穿越山城的绿色长龙</text>
            </view>
          </view>
          <view class="line-stats">
            <view class="stat-item">
              <text class="stat-number">{{ featuredStations.length }}</text>
              <text class="stat-label">精选站点</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">25</text>
              <text class="stat-label">总站数</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 地铁线路图 -->
      <view class="metro-map-section">
        <view class="map-header">
          <text class="map-title">线路图</text>
        </view>

        <view class="metro-map"
              :class="{ 'expanded': isMapExpanded }">
          <!-- Canvas地铁线路图 -->
          <view class="canvas-container">
            <canvas canvas-id="metroCanvas"
                    id="metroCanvas"
                    class="metro-canvas"
                    @tap="onCanvasClick"></canvas>
          </view>

          <!-- 精选站点列表 -->
          <view class="featured-stations"
                v-if="isRecommendExpanded">
            <view class="stations-header">
              <view class="header-left">
                <text class="stations-title">精选站点推荐</text>
                <text class="stations-count">{{ featuredStations.length }}个站点</text>
              </view>
              <button class="collapse-btn"
                      @click="toggleRecommendList">
                收起推荐
              </button>
            </view>
            <scroll-view class="stations-scroll"
                         scroll-y="true"
                         :style="{ maxHeight: featuredStations.length > 5 ? '400rpx' : 'auto' }">
              <view v-for="station in featuredStations"
                    :key="station.id"
                    class="station-item"
                    @click="selectStation(station)">
                <view class="station-icon">
                  <view class="station-star">⭐</view>
                  <view class="station-badge"
                        v-if="station.isTransfer">换乘</view>
                </view>
                <view class="station-content">
                  <view class="station-header">
                    <text class="station-name">{{ station.name }}</text>
                    <text class="station-highlight"
                          v-if="station.highlight">{{ station.highlight }}</text>
                  </view>
                  <text class="station-desc">{{ station.description }}</text>
                  <view class="station-features"
                        v-if="station.features && station.features.length">
                    <text v-for="feature in station.features.slice(0, 3)"
                          :key="feature"
                          class="feature-tag">{{ feature }}</text>
                  </view>
                </view>
                <view class="station-arrow">
                  <text>></text>
                </view>
              </view>
            </scroll-view>
          </view>

          <!-- 收起状态下的简化列表 -->
          <view class="featured-stations-mini"
                v-else>
            <view class="mini-header">
              <text class="mini-title">精选站点</text>
              <button class="expand-btn"
                      @click="toggleRecommendList">
                展开推荐
              </button>
            </view>
            <view class="mini-stations">
              <view v-for="station in featuredStations.slice(0, 5)"
                    :key="station.id"
                    class="mini-station-item"
                    @click="selectStation(station)">
                <view class="mini-station-star">⭐</view>
                <text class="mini-station-name">{{ station.name }}</text>
              </view>
              <view class="more-indicator"
                    v-if="featuredStations.length > 5">
                <text>+{{ featuredStations.length - 5 }}个</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 线路信息 -->
      <view class="line-info-section">
        <view class="info-card">
          <text class="info-title">线路信息</text>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">运营时间</text>
              <text class="info-value">6:00 - 23:00</text>
            </view>
            <view class="info-item">
              <text class="info-label">全程时间</text>
              <text class="info-value">约60分钟</text>
            </view>
            <view class="info-item">
              <text class="info-label">票价</text>
              <text class="info-value">2-7元</text>
            </view>
            <view class="info-item">
              <text class="info-label">车辆类型</text>
              <text class="info-value">跨座式单轨</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 站点详情弹窗 -->
    <view v-if="selectedStation"
          class="station-modal"
          @click="closeStationDetail">
      <view class="modal-content"
            @click.stop>
        <!-- 弹窗头部 -->
        <view class="modal-header">
          <view class="header-left">
            <view class="station-title-area">
              <text class="modal-title">{{ selectedStation.name }}</text>
              <view class="station-badges">
                <view class="station-type-badge"
                      :class="{ 'transfer': selectedStation.isTransfer, 'featured': selectedStation.isFeatured }">
                  <text v-if="selectedStation.isTransfer">换乘站</text>
                  <text v-else-if="selectedStation.isFeatured">精选站点</text>
                  <text v-else>普通站</text>
                </view>
                <view class="station-highlight"
                      v-if="selectedStation.highlight">
                  <text>{{ selectedStation.highlight }}</text>
                </view>
              </view>
            </view>
          </view>
          <button class="close-btn"
                  @click="closeStationDetail">
            <text class="modal-close-icon">×</text>
          </button>
        </view>

        <view class="modal-body">
          <!-- 站点图片 -->
          <view class="station-image"
                v-if="selectedStation.image">
            <image :src="getImageSrc(selectedStation.image)"
                   :alt="selectedStation.name"
                   mode="aspectFill"
                   class="station-main-image"
                   @error="onImageError" />
            <view class="image-overlay">
              <text class="image-title">{{ selectedStation.name }}站</text>
            </view>
          </view>

          <view class="station-details">
            <!-- 站点介绍 -->
            <view class="station-intro-section">
              <text class="section-label">站点介绍</text>
              <text class="station-intro">{{ selectedStation.detailedDescription }}</text>
            </view>

            <!-- 特色标签 -->
            <view class="modal-features"
                  v-if="selectedStation.features && selectedStation.features.length">
              <text class="features-title">站点特色</text>
              <view class="feature-tags">
                <text v-for="feature in selectedStation.features"
                      :key="feature"
                      class="feature-tag">
                  {{ feature }}
                </text>
              </view>
            </view>

            <!-- 周边景点 -->
            <view class="modal-attractions"
                  v-if="selectedStation.attractions && selectedStation.attractions.length">
              <text class="attractions-title">周边景点/设施</text>
              <view class="attraction-list">
                <view v-for="attraction in selectedStation.attractions"
                      :key="attraction.name"
                      class="attraction-item"
                      @click="openAttractionModal(attraction)">
                  <view class="attraction-image">
                    <image v-if="attraction.image"
                           :src="getImageSrc(attraction.image)"
                           :alt="attraction.name"
                           class="attraction-thumbnail"
                           mode="aspectFill"
                           @error="onImageError" />
                    <view v-else
                          class="no-image-placeholder">
                      <text class="no-image-icon">🖼️</text>
                      <text class="no-image-text">暂无图片</text>
                    </view>
                  </view>
                  <view class="attraction-info">
                    <view class="attraction-header">
                      <text class="attraction-name">{{ attraction.name }}</text>
                      <text class="attraction-distance">{{ attraction.distance }}</text>
                    </view>
                    <text class="attraction-description"
                          v-if="attraction.description">{{ attraction.description }}</text>
                  </view>
                  <view class="attraction-arrow">
                    <text>></text>
                  </view>
                </view>
              </view>
            </view>

            <view class="detail-sections">
              <view class="detail-section"
                    v-if="selectedStation.history">
                <text class="section-title">历史背景</text>
                <text class="section-content">{{ selectedStation.history }}</text>
              </view>

              <view class="detail-section"
                    v-if="selectedStation.culture">
                <text class="section-title">文化特色</text>
                <text class="section-content">{{ selectedStation.culture }}</text>
              </view>

              <view class="detail-section"
                    v-if="selectedStation.tips">
                <text class="section-title">游览贴士</text>
                <text class="section-content">{{ selectedStation.tips }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 景点详细弹窗 -->
    <view v-if="isAttractionModalOpen"
          class="modal-overlay attraction-modal-overlay"
          @click="closeAttractionModal">
      <view class="modal-content attraction-modal-content"
            @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ selectedAttraction?.name }}</text>
          <button class="close-btn"
                  @click="closeAttractionModal">
            <text class="modal-close-icon">×</text>
          </button>
        </view>

        <view class="modal-body attraction-modal-body">
          <!-- 景点图片 -->
          <view class="attraction-gallery"
                v-if="hasAttractionImages()">
            <view class="gallery-main">
              <image :src="getImageSrc(currentImage)"
                     :alt="selectedAttraction?.name || '景点图片'"
                     class="gallery-main-image"
                     mode="aspectFill"
                     @click="openImagePreview"
                     @error="onImageError" />

              <!-- 版权保护水印 -->
              <view class="copyright-watermark">
                <text class="watermark-icon">🚫</text>
                <text class="watermark-text">版权保护，禁止转载</text>
              </view>

              <!-- 图片导航 -->
              <view class="gallery-nav"
                    v-if="getValidImages().length > 1">
                <button class="nav-btn prev-btn"
                        @click.stop="prevImage">
                  <text class="nav-icon">‹</text>
                </button>
                <button class="nav-btn next-btn"
                        @click.stop="nextImage">
                  <text class="nav-icon">›</text>
                </button>
              </view>
            </view>
            <!-- 图片指示器 -->
            <view class="gallery-indicators"
                  v-if="getValidImages().length > 1">
              <view v-for="(img, index) in getValidImages()"
                    :key="index"
                    :class="['indicator', { active: index === currentImageIndex }]"
                    @click="setCurrentImage(index)"></view>
            </view>
          </view>

          <!-- 景点信息 -->
          <view class="attraction-details">
            <view class="attraction-basic-info">
              <view class="attraction-distance-badge">
                距离站点 {{ selectedAttraction?.distance }}
              </view>
            </view>

            <text class="attraction-intro"
                  v-if="selectedAttraction?.description">
              {{ selectedAttraction.description }}
            </text>

            <view class="attraction-detail-content"
                  v-if="selectedAttraction?.details">
              <text class="detail-title">详细介绍</text>
              <text class="detail-text">{{ selectedAttraction.details }}</text>
            </view>

            <!-- 实用信息 -->
            <view class="attraction-practical-info">
              <text class="practical-title">实用信息</text>
              <view class="info-grid">
                <view class="info-item">
                  <text class="info-label">步行时间</text>
                  <text class="info-value">约{{ Math.ceil(parseInt(selectedAttraction?.distance || '0') / 80) }}分钟</text>
                </view>
                <view class="info-item">
                  <text class="info-label">建议游览</text>
                  <text class="info-value">30-60分钟</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义图片预览 (App环境) -->
    <view v-if="isCustomPreviewOpen"
          class="custom-preview-modal"
          @click="closeCustomPreview">
      <view class="custom-preview-container"
            @click.stop>
        <!-- 图片显示区域 -->
        <view class="custom-preview-image-wrapper">

          <image :src="customPreviewImages[customPreviewIndex]"
                 class="custom-preview-image"
                 mode="aspectFit"
                 :style="{ width: '100%', height: '100%', minHeight: '200px' }"
                 @error="onPreviewImageError"
                 @load="onPreviewImageLoad" />

          <!-- 版权保护水印 -->
          <view class="custom-preview-watermark">
            <text class="watermark-text">版权保护，禁止转载</text>
          </view>
        </view>

        <!-- 导航按钮 -->
        <view v-if="customPreviewImages.length > 1"
              class="custom-preview-nav">
          <button class="custom-nav-btn prev"
                  @click="prevCustomImage">
            <text class="nav-icon">‹</text>
          </button>
          <button class="custom-nav-btn next"
                  @click="nextCustomImage">
            <text class="nav-icon">›</text>
          </button>
        </view>

        <!-- 关闭按钮 -->
        <view class="custom-preview-close"
              @click="closeCustomPreview">
          <text class="close-icon">×</text>
        </view>

        <!-- 图片指示器 -->
        <view v-if="customPreviewImages.length > 1"
              class="custom-preview-indicators">
          <view v-for="(img, index) in customPreviewImages"
                :key="index"
                :class="['custom-indicator', { active: index === customPreviewIndex }]"
                @click="customPreviewIndex = index"></view>
        </view>
      </view>
    </view>

    <!-- 线路选择器弹窗 -->
    <view v-if="isLineSelectorOpen"
          class="line-selector-modal"
          @click="closeLineSelector">
      <view class="line-selector-container"
            @click.stop>
        <view class="line-selector-header">
          <text class="selector-title">选择线路</text>
          <view class="selector-close"
                @click="closeLineSelector">
            <text class="selector-close-icon">×</text>
          </view>
        </view>

        <view class="line-list">
          <!-- 当前线路 -->
          <view class="line-item current">
            <view class="line-info">
              <view class="line-badge-small line-2">
                <text class="line-number-small">2</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">2号线</text>
                <text class="line-desc">穿越山城的绿色长龙</text>
              </view>
            </view>
            <view class="line-status current-status">
              <text class="status-text">当前</text>
            </view>
          </view>

          <!-- 其他线路 -->
          <view v-for="line in otherLines"
                :key="line.number"
                class="line-item"
                @click="selectLine(line)">
            <view class="line-info">
              <view class="line-badge-small"
                    :class="`line-${line.number}`">
                <text class="line-number-small">{{ line.number }}</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">{{ line.number }}号线</text>
                <text class="line-desc">{{ line.description }}</text>
              </view>
            </view>
            <view class="line-status">
              <text class="status-text">建设中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  featuredStations as importedFeaturedStations,
  allStations as importedAllStations,
  stationPositions as importedStationPositions,
  type Station,
  type Attraction,
} from '../../data/cq2_data'
import { getStaticImageUrl } from '../../utils/image'

// 响应式数据
const isMapExpanded = ref(true) // 默认展开地图
const isRecommendExpanded = ref(false) // 推荐列表默认收起
const selectedStation = ref<Station | null>(null)
const selectedAttraction = ref<Attraction | null>(null)
const isAttractionModalOpen = ref(false)
const currentImageIndex = ref(0)
const currentImage = ref('')

// 自定义预览状态
const isCustomPreviewOpen = ref(false)
const customPreviewImages = ref<string[]>([])
const customPreviewIndex = ref(0)

// 线路选择器状态
const isLineSelectorOpen = ref(false)

// 其他线路数据
const otherLines = ref([
  { number: 1, description: '朝天门至大学城', color: '#E60012' },
  { number: 3, description: '鱼洞至江北机场', color: '#FFD320' },
  { number: 4, description: '民安大道至唐家沱', color: '#8B5A96' },
  { number: 5, description: '园博园至跳磴', color: '#00A0E9' },
  { number: 6, description: '茶园至北碚', color: '#E4007F' },
  { number: 9, description: '回兴至春华大道', color: '#00B04F' },
  { number: 10, description: '王家庄至鲤鱼池', color: '#A05EB5' },
  { number: 18, description: '富华路至跳磴南', color: '#F39800' },
])

// 从导入的数据创建响应式引用
const featuredStations = ref(importedFeaturedStations)
const allStations = ref(importedAllStations)
const stationPositions = ref(importedStationPositions)

// 计算属性和方法

const toggleRecommendList = () => {
  isRecommendExpanded.value = !isRecommendExpanded.value
}

const selectStation = (station: Station) => {
  selectedStation.value = station
}

const selectStationByName = (stationName: string) => {
  // 首先在所有站点中查找站点数据
  const station = allStations.value.find((s) => s.name === stationName)
  if (station) {
    selectStation(station)
  } else {
    // 如果找不到站点数据，创建一个基本的站点对象
    const basicStation: Station = {
      id: stationName.toLowerCase(),
      name: stationName,
      order: 0,
      isTransfer: false,
      isFeatured: false,
      description: `${stationName}站`,
      detailedDescription: `${stationName}是重庆轨道交通2号线的一个站点。`,
      features: ['轨道交通'],
      attractions: [],
      history: '暂无详细历史信息。',
      culture: '暂无详细文化信息。',
      tips: '欢迎乘坐重庆轨道交通。',
    }
    selectStation(basicStation)
  }
}

const closeStationDetail = () => {
  selectedStation.value = null
}

// 线路选择器相关方法
const showLineSelector = () => {
  isLineSelectorOpen.value = true
}

const closeLineSelector = () => {
  isLineSelectorOpen.value = false
}

const selectLine = (line: any) => {
  console.log('选择线路:', line)
  uni.showToast({
    title: `${line.number}号线建设中...`,
    icon: 'none',
    duration: 2000,
  })
  closeLineSelector()
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  console.log(imageSrc)
  return getStaticImageUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event: any) => {
  console.error('图片加载失败:', event)
  // 将图片源替换为错误占位图
  const target = event.target || event.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}

// 检查景点是否有可显示的图片
const hasAttractionImages = () => {
  if (!selectedAttraction.value) return false

  // 检查是否有单张图片
  if (
    selectedAttraction.value.image &&
    selectedAttraction.value.image.trim() !== ''
  ) {
    return true
  }

  // 检查是否有图片数组
  if (
    selectedAttraction.value.images &&
    selectedAttraction.value.images.length > 0
  ) {
    // 过滤掉空字符串
    const validImages = selectedAttraction.value.images.filter(
      (img) => img && img.trim() !== ''
    )
    if (validImages.length > 0) {
      return true
    }
  }

  return false
}

const openAttractionModal = (attraction: any) => {
  selectedAttraction.value = attraction
  isAttractionModalOpen.value = true
  currentImageIndex.value = 0
  updateCurrentImage()
}

const closeAttractionModal = () => {
  selectedAttraction.value = null
  isAttractionModalOpen.value = false
  currentImageIndex.value = 0
  currentImage.value = ''
}

// 图片轮播相关函数
const updateCurrentImage = () => {
  if (selectedAttraction.value) {
    const validImages = getValidImages()
    if (validImages.length > 0) {
      const imageIndex = Math.min(
        currentImageIndex.value,
        validImages.length - 1
      )
      currentImage.value = validImages[imageIndex]
    } else {
      currentImage.value = ''
    }
  }
}

const nextImage = () => {
  if (!selectedAttraction.value) return

  // 获取有效图片数组
  const validImages = getValidImages()
  if (validImages.length > 1) {
    currentImageIndex.value = (currentImageIndex.value + 1) % validImages.length
    updateCurrentImage()
  }
}

const prevImage = () => {
  if (!selectedAttraction.value) return

  // 获取有效图片数组
  const validImages = getValidImages()
  if (validImages.length > 1) {
    currentImageIndex.value =
      currentImageIndex.value === 0
        ? validImages.length - 1
        : currentImageIndex.value - 1
    updateCurrentImage()
  }
}

// 获取有效图片数组的辅助函数
const getValidImages = () => {
  if (!selectedAttraction.value) return []

  const images = []

  // 如果有图片数组，过滤有效图片
  if (
    selectedAttraction.value.images &&
    selectedAttraction.value.images.length > 0
  ) {
    const validArrayImages = selectedAttraction.value.images.filter(
      (img) => img && img.trim() !== ''
    )
    images.push(...validArrayImages)
  }

  // 如果没有图片数组但有单张图片，添加单张图片
  if (
    images.length === 0 &&
    selectedAttraction.value.image &&
    selectedAttraction.value.image.trim() !== ''
  ) {
    images.push(selectedAttraction.value.image)
  }

  return images
}

const setCurrentImage = (index: number) => {
  currentImageIndex.value = index
  updateCurrentImage()
}

// 检测当前运行环境
const isAppEnvironment = () => {
  // #ifdef APP-PLUS
  return true
  // #endif
  // #ifndef APP-PLUS
  return false
  // #endif
}

// 创建带水印的图片
const createWatermarkedImageUrl = (imageUrl: string): Promise<string> => {
  return new Promise((resolve) => {
    if (isAppEnvironment()) {
      // App环境使用自定义预览，添加标记
      resolve(imageUrl + '?watermark=true')
    } else {
      // H5环境直接返回原图
      resolve(imageUrl)
    }
  })
}

// 图片预览功能
const openImagePreview = async () => {
  console.log('开始图片预览，当前图片:', currentImage.value)

  if (currentImage.value) {
    try {
      // 获取当前图片和所有有效图片
      const validImages = getValidImages()
      const currentImageUrl = getImageSrc(currentImage.value)

      console.log('有效图片列表:', validImages)
      console.log('当前图片URL:', currentImageUrl)
      console.log('当前环境是否为App:', isAppEnvironment())

      // 创建带水印的预览图片数组
      const watermarkedUrls = await Promise.all(
        validImages.map(async (img) => {
          const originalUrl = getImageSrc(img)
          try {
            const watermarkedUrl = await createWatermarkedImageUrl(originalUrl)
            console.log('水印处理:', originalUrl, '->', watermarkedUrl)
            return watermarkedUrl
          } catch (error) {
            console.warn('创建水印图片失败，使用原图:', error)
            return originalUrl
          }
        })
      )

      // 找到当前图片在数组中的索引
      const currentIndex = validImages.findIndex(
        (img) => getImageSrc(img) === currentImageUrl
      )
      // 检查是否为App环境的自定义预览
      const hasWatermarkFlag = watermarkedUrls.some((url) =>
        url.includes('?watermark=true')
      )
      showCustomImagePreview(validImages, currentIndex >= 0 ? currentIndex : 0)
    } catch (error) {
      console.error('预览准备失败:', error)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none',
      })
    }
  }
}

// 自定义预览功能 (App环境)
const showCustomImagePreview = (images: string[], startIndex: number) => {
  console.log('显示自定义预览:', images, startIndex)

  // 处理图片路径，确保在App环境下能正确显示
  customPreviewImages.value = images.map((img) => {
    const processedUrl = getImageSrc(img)
    console.log('处理图片路径:', img, '->', processedUrl)
    return processedUrl
  })

  customPreviewIndex.value = startIndex
  console.log('预览图片数组:', customPreviewImages.value)
  console.log('当前索引:', customPreviewIndex.value)
  console.log('轮播图当前显示的URL:', getImageSrc(currentImage.value))
  isCustomPreviewOpen.value = true
}

const closeCustomPreview = () => {
  isCustomPreviewOpen.value = false
  customPreviewImages.value = []
  customPreviewIndex.value = 0
}

const prevCustomImage = () => {
  if (customPreviewIndex.value > 0) {
    customPreviewIndex.value--
  } else {
    customPreviewIndex.value = customPreviewImages.value.length - 1
  }
}

const nextCustomImage = () => {
  if (customPreviewIndex.value < customPreviewImages.value.length - 1) {
    customPreviewIndex.value++
  } else {
    customPreviewIndex.value = 0
  }
}

// 预览图片加载处理
const onPreviewImageLoad = (e: any) => {
  console.log('预览图片加载成功')
  console.log('图片尺寸:', e.detail.width, 'x', e.detail.height)
  console.log(
    '当前显示的图片URL:',
    customPreviewImages.value[customPreviewIndex.value]
  )
}

const onPreviewImageError = (e: any) => {
  console.error('预览图片加载失败:', e)
  console.log(
    '当前图片URL:',
    customPreviewImages.value[customPreviewIndex.value]
  )
}

// Canvas相关变量 (用于地铁线路图)
let canvasContext: any = null
const canvasWidth = ref(0)
const canvasHeight = ref(0)

// 绘制地铁线路图
const drawMetroMap = () => {
  const query = uni.createSelectorQuery()
  query
    .select('#metroCanvas')
    .boundingClientRect((rect: any) => {
      if (rect) {
        canvasWidth.value = rect.width
        canvasHeight.value = rect.height

        // 获取Canvas上下文
        canvasContext = uni.createCanvasContext('metroCanvas')

        // 设置Canvas绘制尺寸
        canvasContext.scale(canvasWidth.value / 500, canvasHeight.value / 600)

        // 清空画布
        canvasContext.clearRect(0, 0, 500, 600)

        // 设置背景
        canvasContext.fillStyle = '#ffffff'
        canvasContext.fillRect(0, 0, 500, 600)

        // 绘制线路
        drawMetroLine()

        // 绘制站点
        drawStations()

        // 绘制站点名称
        drawStationNames()

        // 提交绘制
        canvasContext.draw()
      }
    })
    .exec()
}

// 绘制地铁线路
const drawMetroLine = () => {
  canvasContext.strokeStyle = '#00A651'
  canvasContext.lineWidth = 6
  canvasContext.lineCap = 'round'
  canvasContext.lineJoin = 'round'

  canvasContext.beginPath()

  // 鱼洞到大江的弧线
  canvasContext.moveTo(115, 555)
  canvasContext.quadraticCurveTo(82, 565, 50, 550)
  canvasContext.quadraticCurveTo(25, 520, 25, 450)

  // 白居寺到大渡口直线
  canvasContext.lineTo(25, 330)

  // 大渡口到平安到马王场的弧线
  canvasContext.quadraticCurveTo(30, 320, 35, 310)
  canvasContext.quadraticCurveTo(45, 300, 55, 290)

  // 马王场到动物园直线
  canvasContext.lineTo(115, 290)

  // 动物园到杨家坪弧线
  canvasContext.quadraticCurveTo(130, 280, 145, 265)

  // 杨家坪到大坪直线
  canvasContext.lineTo(145, 165)

  // 大坪到牛角沱直线
  canvasContext.lineTo(235, 75)

  // 牛角沱到曾家岩弧线
  canvasContext.quadraticCurveTo(260, 50, 275, 50)
  canvasContext.lineTo(335, 50)

  // 曾家岩到黄花园直线
  canvasContext.lineTo(395, 50)

  // 黄花园到临江门弧线
  canvasContext.quadraticCurveTo(430, 60, 425, 80)

  // 临江门到较场口直线
  canvasContext.lineTo(425, 110)

  canvasContext.stroke()
}

// 绘制站点
const drawStations = () => {
  stationPositions.value.forEach((station) => {
    if (station.isFeatured) {
      // 绘制精选站点（五角星）
      drawStar(station.x, station.y, 8, '#FFD700', '#ffffff')
    } else {
      // 绘制普通站点（圆圈）
      canvasContext.beginPath()
      canvasContext.arc(station.x, station.y, 6, 0, 2 * Math.PI)
      canvasContext.fillStyle = '#00A651'
      canvasContext.fill()
      canvasContext.strokeStyle = '#ffffff'
      canvasContext.lineWidth = 2
      canvasContext.stroke()
    }
  })
}

// 绘制五角星
const drawStar = (
  x: number,
  y: number,
  radius: number,
  fillColor: string,
  strokeColor: string
) => {
  const spikes = 5
  const outerRadius = radius
  const innerRadius = radius * 0.4

  canvasContext.beginPath()

  for (let i = 0; i < spikes * 2; i++) {
    const angle = (i * Math.PI) / spikes
    const r = i % 2 === 0 ? outerRadius : innerRadius
    const pointX = x + Math.cos(angle - Math.PI / 2) * r
    const pointY = y + Math.sin(angle - Math.PI / 2) * r

    if (i === 0) {
      canvasContext.moveTo(pointX, pointY)
    } else {
      canvasContext.lineTo(pointX, pointY)
    }
  }

  canvasContext.closePath()
  canvasContext.fillStyle = fillColor
  canvasContext.fill()
  canvasContext.strokeStyle = strokeColor
  canvasContext.lineWidth = 1
  canvasContext.stroke()
}

// 绘制站点名称
const drawStationNames = () => {
  canvasContext.fillStyle = '#333333'
  canvasContext.font = '12px Arial'
  canvasContext.textAlign = 'left'

  stationPositions.value.forEach((station) => {
    let textX = station.x + 10
    let textY = station.y + 5

    // 调整部分站点名称位置避免重叠
    if (station.name === '曾家岩') {
      textX = station.x - 30
      textY = station.y - 10
    }
    if (station.name === '大溪沟') {
      textX = station.x - 20
      textY = station.y - 10
    }
    if (station.name === '平安') {
      textX = station.x - 30
    }
    if (station.name === '马王场') {
      textX = station.x - 30
      textY = station.y - 10
    }
    if (station.name === '大堰村') {
      textX = station.x - 20
      textY = station.y - 10
    }
    canvasContext.fillText(station.name, textX, textY)
  })
}

// Canvas点击事件处理
const onCanvasClick = (e: any) => {
  // 获取Canvas的位置信息
  const query = uni.createSelectorQuery()
  query
    .select('#metroCanvas')
    .boundingClientRect((rect: any) => {
      if (rect && e.detail) {
        // 计算相对于Canvas的坐标
        const x = e.detail.x - rect.left
        const y = e.detail.y - rect.top

        // 根据Canvas实际尺寸调整坐标比例
        const scaleX = 500 / rect.width // 500是我们绘制时使用的坐标系宽度
        const scaleY = 600 / rect.height // 600是我们绘制时使用的坐标系高度

        const adjustedX = x * scaleX
        const adjustedY = y * scaleY

        // 检查点击的是否为站点或站点名称
        stationPositions.value.forEach((station) => {
          // 计算站点图标的点击距离
          const iconDistance = Math.sqrt(
            Math.pow(adjustedX - station.x, 2) +
              Math.pow(adjustedY - station.y, 2)
          )

          // 计算站点名称的点击区域
          let textX = station.x + 10
          let textY = station.y + 5

          // 调整部分站点名称位置避免重叠
          if (station.name === '曾家岩') {
            textX = station.x - 30
            textY = station.y - 10
          }
          if (station.name === '大溪沟') {
            textX = station.x - 20
            textY = station.y - 10
          }
          if (station.name === '平安') {
            textX = station.x - 30
          }
          if (station.name === '马王场') {
            textX = station.x - 30
            textY = station.y - 10
          }
          if (station.name === '大堰村') {
            textX = station.x - 20
            textY = station.y - 10
          }
          // 文字区域点击检测（矩形区域）
          const textWidth = station.name.length * 12 // 估算文字宽度
          const textHeight = 16 // 文字高度
          const textDistance =
            adjustedX >= textX &&
            adjustedX <= textX + textWidth &&
            adjustedY >= textY - textHeight &&
            adjustedY <= textY

          // 如果点击了站点图标或站点名称
          if (iconDistance <= 20 || textDistance) {
            selectStationByName(station.name)
          }
        })
      }
    })
    .exec()
}

onMounted(() => {
  // 设置导航栏
  uni.setNavigationBarTitle({
    title: '重庆轨道2号线',
  })

  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#00a651',
  })

  // 延迟绘制Canvas，确保DOM已渲染
  setTimeout(() => {
    drawMetroMap()
  }, 500)
})
</script>

<style scoped>
/* 重庆轨道交通2号线专题页面样式 */
.line2-tour-app {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #00a651 0%, #008b3d 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
}

/* 顶部标题栏 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 30rpx 40rpx;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200rpx;
  margin: 0 auto;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.line-badge {
  display: flex;
  align-items: center;
  background: #00a651;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.line-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 16rpx rgba(0, 166, 81, 0.4);
}

.line-arrow {
  font-size: 20rpx;
  margin-left: 4rpx;
  transition: transform 0.3s ease;
}

.line-number {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-right: 5rpx;
}

.line-name {
  font-size: 24rpx;
  color: white;
}

/* 主要内容区域 */
.main-content {
  padding: 40rpx 30rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}

/* 线路概览卡片 */
.overview-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.line-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.line-color-bar {
  width: 8rpx;
  height: 80rpx;
  background: #00a651;
  border-radius: 4rpx;
  margin-right: 20rpx;
}

.line-details {
  flex: 1;
}

.line-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.line-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.line-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #00a651;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 5rpx;
}

/* 地铁线路图 */
.metro-map-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.map-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.map-toggle {
  background: #00a651;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin-left: auto;
}

.metro-map {
  min-height: 200rpx;
  transition: all 0.3s ease;
}

.metro-map.expanded {
  min-height: 800rpx;
}

/* Canvas线路图样式 */
.canvas-container {
  width: 100%;
  height: 600rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.metro-canvas {
  width: 100%;
  height: 100%;
  background: white;
}

.metro-line {
  stroke-linecap: round;
  stroke-linejoin: round;
}

.station-circle {
  cursor: pointer;
  transition: all 0.3s ease;
}

.station-circle:hover {
  transform: scale(1.3);
}

.station-star {
  cursor: pointer;
  transition: all 0.3s ease;
}

.station-star:hover {
  transform: scale(1.2);
}

.station-star.featured {
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.5));
}

.station-name {
  font-size: 12px;
  fill: #333;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.station-name:hover {
  fill: #00a651;
  font-weight: bold;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.map-placeholder text:first-child {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.map-note {
  font-size: 24rpx;
  color: #666;
}

/* 精选站点列表 - 展开状态 */
.featured-stations {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stations-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stations-count {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.collapse-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.stations-scroll {
  max-height: 400rpx;
}

.station-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.station-item:active {
  background: #e3f2fd;
  border-color: #00a651;
  transform: scale(0.98);
}

.station-item:last-child {
  margin-bottom: 0;
}

.station-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.station-star {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.station-badge {
  font-size: 20rpx;
  color: #ff6b6b;
  background: #ffe0e0;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.station-content {
  flex: 1;
}

.station-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.station-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 12rpx;
}

.station-highlight {
  font-size: 22rpx;
  color: #00a651;
  background: #e8f5e8;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.station-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.station-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.feature-tag {
  font-size: 22rpx;
  color: #1976d2;
  background: #e3f2fd;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.station-arrow {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #ccc;
}

/* 精选站点列表 - 收起状态 */
.featured-stations-mini {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.mini-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #f0f0f0;
}

.mini-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.expand-btn {
  background: #00a651;
  color: white;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.mini-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.mini-station-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
}

.mini-station-item:active {
  background: #e3f2fd;
  transform: scale(0.95);
}

.mini-station-star {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.mini-station-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.more-indicator {
  background: #e0e0e0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 线路信息 */
.line-info-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.info-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

/* 站点详情弹窗 */
.station-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  max-width: 90%;
  max-height: 85%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 30rpx 100rpx 20rpx 40rpx;
  background: linear-gradient(135deg, #00a651 0%, #008b3d 100%);
  color: white;
}

.header-left {
  flex: 1;
}

.station-title-area {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
}

.station-badges {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.station-type-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10px);
}

.station-type-badge.transfer {
  background: rgba(255, 107, 107, 0.9);
}

.station-type-badge.featured {
  background: rgba(255, 215, 0, 0.9);
  color: #333;
}

.station-highlight {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10px);
}

.close-btn {
  background: white;
  border: none;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1001;
  transition: all 0.3s ease;
}

.close-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.modal-close-icon {
  font-size: 36rpx;
  color: #666;
  font-weight: bold;
  line-height: 1;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.station-image {
  position: relative;
  width: 100%;
  height: 300rpx;
  overflow: hidden;
}

.station-image image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 20rpx;
}

.image-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.station-details {
  padding: 30rpx 40rpx 40rpx;
}

.station-intro-section {
  margin-bottom: 30rpx;
}

.section-label {
  font-size: 26rpx;
  color: #00a651;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.station-intro {
  font-size: 28rpx;
  line-height: 1.7;
  color: #333;
  display: block;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #00a651;
}

.modal-features {
  margin-bottom: 30rpx;
}

.features-title {
  font-size: 26rpx;
  color: #00a651;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  color: #1976d2;
  padding: 10rpx 18rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1px solid rgba(25, 118, 210, 0.2);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.modal-attractions {
  margin-bottom: 30rpx;
}

.attractions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.attraction-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.attraction-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.attraction-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.attraction-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.attraction-thumbnail {
  width: 100%;
  height: 100%;
}

.no-image-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-image-icon {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.no-image-text {
  font-size: 20rpx;
  color: #999;
}

.attraction-info {
  flex: 1;
}

.attraction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.attraction-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.attraction-distance {
  font-size: 24rpx;
  color: #00a651;
}

.attraction-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

.attraction-arrow {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #ccc;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.section-content {
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
  display: block;
}

/* 景点详细弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.attraction-modal-content {
  background: white;
  border-radius: 24rpx;
  max-width: 95%;
  max-height: 90%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.attraction-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 景点图片画廊 */
.attraction-gallery {
  position: relative;
  margin-bottom: 30rpx;
}

.gallery-main {
  position: relative;
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.gallery-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 1;
}

.gallery-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
  pointer-events: none;
  z-index: 10;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  pointer-events: auto;
  transition: all 0.3s ease;
  z-index: 20;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.prev-btn {
  margin-left: 15rpx;
}

.next-btn {
  margin-right: 15rpx;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
}

.nav-btn:active {
  transform: scale(0.95);
}

.nav-icon {
  font-size: 28rpx;
  line-height: 1;
  user-select: none;
}

/* 版权保护水印样式 */
.copyright-watermark {
  position: absolute;
  bottom: 15rpx;
  right: 15rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10rpx 16rpx;
  border-radius: 25rpx;
  font-size: 20rpx;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  z-index: 15;
  opacity: 0.95;
  backdrop-filter: blur(6rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.watermark-icon {
  font-size: 24rpx;
  line-height: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.5));
}

.watermark-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  letter-spacing: 0.5rpx;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.gallery-indicators {
  display: flex;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
}

.indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ccc;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #00a651;
  transform: scale(1.2);
}

/* 景点详情 */
.attraction-details {
  padding: 40rpx;
}

.attraction-basic-info {
  margin-bottom: 20rpx;
}

.attraction-distance-badge {
  background: #e8f5e8;
  color: #00a651;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  display: inline-block;
}

.attraction-intro {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.attraction-detail-content {
  margin-bottom: 30rpx;
}

.detail-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.detail-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
  display: block;
}

.attraction-practical-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
}

.practical-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 已移除版权保护样式 */
.protected-image-container {
  overflow: hidden;
}

.protected-image {
  -webkit-tap-highlight-color: transparent !important;
  pointer-events: auto;
}

.copyright-watermark {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  z-index: 10;
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  align-items: center;
  gap: 4rpx;
}

.watermark-text {
  font-size: 20rpx;
  color: white;
}

.gallery-watermark {
  bottom: 15rpx;
  right: 15rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.screenshot-protection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 2;
  pointer-events: none;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 防止通过CSS隐藏水印 */
.copyright-watermark {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
}

/* 图片容器额外保护 */
.protected-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 0;
  pointer-events: none;
}

/* 禁用图片的右键菜单和拖拽 */
.protected-image {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
}

/* 防止图片被选中 */
.protected-image::selection {
  background: transparent;
}

.protected-image::-moz-selection {
  background: transparent;
}

/* #ifdef MP-WEIXIN */
.protected-image {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}
/* #endif */

/* 自定义预览样式 (App环境) */
.custom-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-preview-image-wrapper {
  position: relative;
  width: 90%;
  height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  overflow: hidden;
}

.custom-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: transparent;
  display: block;
}

.custom-preview-watermark {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 10;
}

.custom-preview-watermark .watermark-text {
  font-size: 80rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.15);
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 8rpx;
  transform: rotate(-45deg);
  white-space: nowrap;
  user-select: none;
  -webkit-user-select: none;
}

.custom-preview-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
  pointer-events: none;
}

.custom-nav-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  pointer-events: auto;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.custom-nav-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.custom-nav-btn:active {
  transform: scale(0.95);
}

.custom-preview-close {
  position: absolute;
  top: 60rpx;
  right: 60rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.custom-preview-close:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.custom-preview-close .close-icon {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

.debug-info {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.7);
  padding: 10rpx;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.custom-preview-indicators {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
}

.custom-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.custom-indicator.active {
  background: white;
  transform: scale(1.2);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .line-stats {
    gap: 20rpx;
  }

  .stat-number {
    font-size: 40rpx;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
  }

  .line-stats {
    align-self: stretch;
    justify-content: space-around;
  }
}

/* 线路选择器样式 */
.line-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 4000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.line-selector-container {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.line-selector-header {
  background: linear-gradient(135deg, #00a651, #00d463);
  color: white;
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-title {
  font-size: 36rpx;
  font-weight: bold;
}

.selector-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selector-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.selector-close-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
}

.line-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20rpx 0;
}

.line-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.line-item:hover {
  background: #f8f9fa;
}

.line-item.current {
  background: #f0f9f4;
  border-left: 6rpx solid #00a651;
}

.line-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.line-badge-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.line-number-small {
  font-size: 28rpx;
  font-weight: bold;
}

.line-details-small {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.line-name-small {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.line-desc {
  font-size: 24rpx;
  color: #666;
}

.line-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
}

.current-status {
  background: #00a651;
  color: white;
}

.status-text {
  font-size: 24rpx;
  font-weight: bold;
}

/* 各线路颜色 */
.line-1 {
  background: #e60012;
}
.line-2 {
  background: #00a651;
}
.line-3 {
  background: #ffd320;
  color: #333 !important;
}
.line-4 {
  background: #8b5a96;
}
.line-5 {
  background: #00a0e9;
}
.line-6 {
  background: #e4007f;
}
.line-9 {
  background: #00b04f;
}
.line-10 {
  background: #a05eb5;
}
.line-18 {
  background: #f39800;
}
</style>
