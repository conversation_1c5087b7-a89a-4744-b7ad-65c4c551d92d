<template>
  <view class="users-management">
    <!-- 搜索和筛选 -->
    <view class="search-section">
      <view class="search-bar">
        <input v-model="searchQuery.search"
               class="search-input"
               placeholder="搜索昵称或手机号"
               @input="debounceSearch" />
        <button @click="handleSearch"
                class="search-btn">搜索</button>
        <button @click="showGuestSearch"
                class="guest-search-btn">搜索游客</button>
      </view>

      <view class="filter-bar">
        <picker :value="roleFilterIndex"
                :range="roleFilterOptions"
                range-key="label"
                @change="onRoleFilterChange">
          <view class="filter-item">
            角色：{{ roleFilterOptions[roleFilterIndex]?.label || '全部' }}
          </view>
        </picker>

        <view class="filter-item"
              @click="showRegionFilter">
          区域：{{ getRegionFilterText() }}
        </view>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="user-list">
      <view v-for="user in userList"
            :key="user.id"
            class="user-item"
            @click="showUserDetail(user)">
        <view class="user-avatar">
          <image v-if="user.avatar_url"
                 :src="user.avatar_url"
                 class="avatar-img" />
          <view v-else
                class="avatar-placeholder">
            <text class="avatar-text">{{ getUserAvatarText(user) }}</text>
          </view>
        </view>

        <view class="user-info">
          <view class="user-name">{{ user.nickname || '未设置昵称' }}</view>
          <view class="user-role">{{ getRoleDisplayName(user.role) }}</view>
          <view v-if="getUserRegionText(user)"
                class="user-region">{{ getUserRegionText(user) }}</view>
          <view v-if="user.phone"
                class="user-phone">{{ user.phone }}</view>
          <!-- 模块权限标识 -->
          <view v-if="user.module_permissions && user.role !== 'guest'"
                class="user-permissions">
            <text class="permission-label">模块权限：</text>
            <view class="permission-tags">
              <text v-if="user.module_permissions.ancient_books"
                    class="permission-tag active">古籍</text>
              <text v-if="user.module_permissions.paintings"
                    class="permission-tag active">书画</text>
              <text v-if="user.module_permissions.archives"
                    class="permission-tag active">档案</text>
              <text v-if="user.module_permissions.videos"
                    class="permission-tag active">影像</text>
              <text v-if="!hasAnyModulePermission(user)"
                    class="permission-tag inactive">无权限</text>
            </view>
          </view>
          <view class="user-meta">
            ID: {{ user.id }} |
            {{ user.is_active ? '正常' : '已禁用' }} |
            {{ formatDate(user.created_at) }}
          </view>
        </view>

        <view class="user-actions">
          <button @click.stop="editUserRole(user)"
                  class="auth-btn">
            授权管理
          </button>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore"
            class="load-more"
            @click="loadMore">
        <text>{{ loading ? '加载中...' : '加载更多' }}</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && userList.length === 0"
            class="empty-state">
        <text>暂无用户数据</text>
      </view>
    </view>

    <!-- 用户详情弹窗 -->
    <view v-if="showUserDetailPopup && selectedUser"
          class="popup-overlay"
          @click="closeUserDetail">
      <view class="user-detail-popup"
            @click.stop>
        <view class="popup-header">
          <text class="popup-title">用户详情</text>
          <text @click="closeUserDetail"
                class="close-btn">×</text>
        </view>

        <view class="detail-content">
          <view class="detail-item">
            <text class="detail-label">用户ID:</text>
            <text class="detail-value">{{ selectedUser.id }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">昵称:</text>
            <text class="detail-value">{{ selectedUser.nickname || '未设置' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">手机号:</text>
            <text class="detail-value">{{ selectedUser.phone || '未绑定' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">性别:</text>
            <text class="detail-value">{{ getGenderDisplayName(selectedUser.gender) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">角色:</text>
            <text class="detail-value">{{ getRoleDisplayName(selectedUser.role) }}</text>
          </view>
          <view v-if="getUserRegionText(selectedUser)"
                class="detail-item">
            <text class="detail-label">管辖地区:</text>
            <text class="detail-value">{{ getUserRegionText(selectedUser) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">状态:</text>
            <text class="detail-value">{{ selectedUser.is_active ? '正常' : '已禁用' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">注册时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedUser.created_at) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">最后登录:</text>
            <text class="detail-value">{{ selectedUser.last_login_at ? formatDateTime(selectedUser.last_login_at) : '未登录' }}</text>
          </view>

          <!-- 操作按钮 -->
          <!-- <view class="detail-actions">
            <button @click="toggleUserStatusFromDetail"
                    :class="['detail-action-btn', selectedUser.is_active ? 'disable-btn' : 'enable-btn']">
              {{ selectedUser.is_active ? '禁用用户' : '启用用户' }}
            </button>
            <button @click="editUserRoleFromDetail"
                    class="detail-action-btn auth-btn">
              授权管理
            </button>
                    </view> -->
        </view>
      </view>
    </view>

    <!-- 角色编辑弹窗 -->
    <view v-if="showRoleEditPopup"
          class="popup-overlay role-edit-overlay"
          @click="closeRoleEdit">
      <view class="role-edit-popup"
            @click.stop>
        <view class="popup-title">编辑用户角色</view>

        <view class="form-item">
          <text class="form-label">角色</text>
          <picker :value="roleEditForm.roleIndex"
                  :range="availableRoles"
                  range-key="label"
                  @change="onRoleEditChange">
            <view class="form-input picker-input">
              {{ (roleEditForm.roleIndex >= 0 && roleEditForm.roleIndex < availableRoles.length) ? availableRoles[roleEditForm.roleIndex]?.label : '请选择' }}
            </view>
          </picker>
        </view>

        <view v-if="needsRegionSelection"
              class="region-selection">
          <!-- 省份选择 - 所有管理员角色都需要 -->
          <view class="form-item">
            <text class="form-label">省份</text>
            <picker :value="roleEditForm.provinceIndex"
                    :range="provinces"
                    range-key="name"
                    @change="onProvinceEditChange"
                    :disabled="isProvinceDisabled">
              <view :class="['form-input', 'picker-input', { disabled: isProvinceDisabled }]">
                {{ (roleEditForm.provinceIndex >= 0 && roleEditForm.provinceIndex < provinces.length) ? provinces[roleEditForm.provinceIndex]?.name : '请选择省份' }}
              </view>
            </picker>
          </view>

          <!-- 城市选择 - 市级和区县管理员需要 -->
          <view v-if="needsCitySelection"
                class="form-item">
            <text class="form-label">城市</text>
            <picker :value="roleEditForm.cityIndex"
                    :range="cities"
                    range-key="name"
                    @change="onCityEditChange"
                    :disabled="isCityDisabled">
              <view :class="['form-input', 'picker-input', { disabled: isCityDisabled }]">
                {{ (roleEditForm.cityIndex >= 0 && roleEditForm.cityIndex < cities.length) ? cities[roleEditForm.cityIndex]?.name : '请选择城市' }}
              </view>
            </picker>
          </view>

          <!-- 区县选择 - 只有区县管理员需要 -->
          <view v-if="needsDistrictSelection"
                class="form-item">
            <text class="form-label">区县</text>
            <picker :value="roleEditForm.districtIndex"
                    :range="districts"
                    range-key="name"
                    @change="onDistrictEditChange"
                    :disabled="isDistrictDisabled">
              <view :class="['form-input', 'picker-input', { disabled: isDistrictDisabled }]">
                {{ (roleEditForm.districtIndex >= 0 && roleEditForm.districtIndex < districts.length) ? districts[roleEditForm.districtIndex]?.name : '请选择区县' }}
              </view>
            </picker>
          </view>
        </view>

        <!-- 模块权限设置 - 只有超级管理员可见 -->
        <view v-if="currentUser?.role === 'super_admin' && selectedUser && selectedUser.role !== 'guest'"
              class="module-permissions-section">
          <view class="section-title">模块权限设置</view>
          <view class="permission-list">
            <view v-for="module in availableModules"
                  :key="module.key"
                  class="permission-item">
              <view class="permission-info">
                <text class="permission-name">{{ module.name }}</text>
                <text class="permission-desc">{{ module.description }}</text>
              </view>
              <switch :checked="modulePermissions[module.key]"
                      @change="onModulePermissionChange(module.key, $event)"
                      class="permission-switch" />
            </view>
          </view>
        </view>

        <view class="popup-actions">
          <button @click="closeRoleEdit"
                  class="cancel-btn">取消</button>
          <button @click="saveUserRole"
                  class="confirm-btn">保存</button>
        </view>
      </view>
    </view>

    <!-- 游客搜索弹窗 -->
    <view v-if="showGuestSearchPopup"
          class="popup-overlay"
          @click="closeGuestSearch">
      <view class="guest-search-popup"
            @click.stop>
        <view class="popup-header">
          <text class="popup-title">搜索游客用户</text>
          <text @click="closeGuestSearch"
                class="close-btn">×</text>
        </view>

        <view class="search-form">
          <view class="form-item">
            <text class="form-label">手机号（完整准确）</text>
            <input v-model="guestSearchForm.phone"
                   class="form-input"
                   placeholder="请输入完整的手机号"
                   type="tel"
                   maxlength="11" />
          </view>

          <view class="search-actions">
            <button @click="closeGuestSearch"
                    class="cancel-btn">取消</button>
            <button @click="searchGuests"
                    class="confirm-btn">搜索</button>
          </view>
        </view>

        <!-- 搜索结果 -->
        <view v-if="guestSearchResults.length > 0"
              class="search-results">
          <view class="results-header">
            <text>搜索结果 ({{ guestSearchTotal }}条)</text>
          </view>

          <view v-for="user in guestSearchResults"
                :key="user.id"
                class="guest-item">
            <view class="guest-avatar">
              <image v-if="user.avatar_url"
                     :src="user.avatar_url"
                     class="avatar-img" />
              <view v-else
                    class="avatar-placeholder">
                <text class="avatar-text">{{ getUserAvatarText(user) }}</text>
              </view>
            </view>

            <view class="guest-info">
              <view class="guest-name">{{ user.nickname || '未设置昵称' }}</view>
              <view v-if="user.phone"
                    class="guest-phone">{{ user.phone }}</view>
              <view class="guest-meta">
                ID: {{ user.id }} | {{ formatDate(user.created_at) }}
              </view>
            </view>

            <view class="guest-actions">
              <button @click="selectGuestForAuth(user)"
                      class="auth-btn">
                授权
              </button>
            </view>
          </view>

          <!-- 加载更多搜索结果 -->
          <view v-if="guestSearchHasMore"
                class="load-more"
                @click="loadMoreGuestSearch">
            <text>{{ guestSearchLoading ? '加载中...' : '加载更多' }}</text>
          </view>
        </view>

        <!-- 空搜索结果 -->
        <view v-else-if="hasSearchedGuests && !guestSearchLoading"
              class="empty-search">
          <text>未找到符合条件的游客用户</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  getUserList,
  updateUserRole,
  updateUserStatus,
  getRoleDisplayName,
  getGenderDisplayName,
  searchGuestUsers,
  getAvailableModules,
  getUserModulePermissions,
  updateUserModulePermissions,
  type UserListQuery,
  type UserRoleUpdateRequest,
  type SearchGuestQuery,
  type ModulePermissions,
  type ModuleInfo,
} from '@/api/users'
import { UserRole, type UserInfo } from '@/api/auth'
import { authManager } from '@/store/modules/auth'
import {
  getProvinces,
  getCities,
  getDistricts,
  type Province,
  type City,
  type District,
} from '@/api/admin_divisions'

// 弹窗引用（保留userDetailPopup以避免其他地方的引用错误）
const userDetailPopup = ref()
const roleEditPopup = ref()

// 数据状态
const loading = ref(false)
const userList = ref<UserInfo[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = 20
const hasMore = computed(() => userList.value.length < total.value)

// 选中的用户
const selectedUser = ref<UserInfo | null>(null)

// 弹窗显示状态
const showRoleEditPopup = ref(false)
const showUserDetailPopup = ref(false)
const showGuestSearchPopup = ref(false)

// 搜索查询
const searchQuery = reactive<UserListQuery>({
  page: 1,
  page_size: pageSize,
  search: '',
  role: undefined,
  province_id: undefined,
  city_id: undefined,
  district_id: undefined,
})

// 角色筛选
const roleFilterIndex = ref(0)
const roleFilterOptions = [
  { label: '全部角色', value: undefined },
  { label: '游客', value: UserRole.GUEST },
  { label: '区县管理员', value: UserRole.DISTRICT_ADMIN },
  { label: '市级管理员', value: UserRole.CITY_ADMIN },
  { label: '省级管理员', value: UserRole.PROVINCE_ADMIN },
  { label: '超级管理员', value: UserRole.SUPER_ADMIN },
]

// 角色编辑表单
const roleEditForm = reactive({
  userId: 0,
  roleIndex: 0,
  provinceIndex: -1,
  cityIndex: -1,
  districtIndex: -1,
})

// 可用角色选项（根据当前用户权限）
const availableRoles = computed(() => {
  const currentUser = authManager.currentUser
  if (!currentUser) return []

  const allRoles = [
    { label: '游客', value: UserRole.GUEST },
    { label: '区县管理员', value: UserRole.DISTRICT_ADMIN },
    { label: '市级管理员', value: UserRole.CITY_ADMIN },
    { label: '省级管理员', value: UserRole.PROVINCE_ADMIN },
    { label: '超级管理员', value: UserRole.SUPER_ADMIN },
  ]

  // 根据当前用户角色过滤可设置的角色
  if (currentUser.role === UserRole.SUPER_ADMIN) {
    return allRoles
  } else if (currentUser.role === UserRole.PROVINCE_ADMIN) {
    return allRoles.filter(
      (r) =>
        r.value !== UserRole.SUPER_ADMIN && r.value !== UserRole.PROVINCE_ADMIN
    )
  } else if (currentUser.role === UserRole.CITY_ADMIN) {
    return allRoles.filter(
      (r) =>
        ![
          UserRole.SUPER_ADMIN,
          UserRole.PROVINCE_ADMIN,
          UserRole.CITY_ADMIN,
        ].includes(r.value)
    )
  } else if (currentUser.role === UserRole.DISTRICT_ADMIN) {
    // 区县管理员可以设置游客和区县管理员角色
    return allRoles.filter((r) =>
      [UserRole.GUEST, UserRole.DISTRICT_ADMIN].includes(r.value)
    )
  } else {
    return [{ label: '游客', value: UserRole.GUEST }]
  }
})

// 是否需要选择管理区域
const needsRegionSelection = computed(() => {
  if (
    roleEditForm.roleIndex < 0 ||
    roleEditForm.roleIndex >= availableRoles.value.length
  ) {
    return false
  }
  const selectedRole = availableRoles.value[roleEditForm.roleIndex]
  return (
    selectedRole &&
    [
      UserRole.DISTRICT_ADMIN,
      UserRole.CITY_ADMIN,
      UserRole.PROVINCE_ADMIN,
    ].includes(selectedRole.value)
  )
})

// 是否需要选择城市
const needsCitySelection = computed(() => {
  if (
    roleEditForm.roleIndex < 0 ||
    roleEditForm.roleIndex >= availableRoles.value.length
  ) {
    return false
  }
  const selectedRole = availableRoles.value[roleEditForm.roleIndex]
  return (
    selectedRole &&
    [UserRole.CITY_ADMIN, UserRole.DISTRICT_ADMIN].includes(
      selectedRole.value
    ) &&
    cities.value.length > 0
  )
})

// 是否需要选择区县
const needsDistrictSelection = computed(() => {
  if (
    roleEditForm.roleIndex < 0 ||
    roleEditForm.roleIndex >= availableRoles.value.length
  ) {
    return false
  }
  const selectedRole = availableRoles.value[roleEditForm.roleIndex]
  return (
    selectedRole &&
    selectedRole.value === UserRole.DISTRICT_ADMIN &&
    districts.value.length > 0
  )
})

// 区域数据
const provinces = ref<Province[]>([])
const cities = ref<City[]>([])
const districts = ref<District[]>([])

// 游客搜索相关状态
const guestSearchForm = reactive<SearchGuestQuery>({
  phone: '',
  page: 1,
  page_size: 20,
})
const guestSearchResults = ref<UserInfo[]>([])
const guestSearchTotal = ref(0)
const guestSearchCurrentPage = ref(1)
const guestSearchLoading = ref(false)
const hasSearchedGuests = ref(false)
const guestSearchHasMore = computed(
  () => guestSearchResults.value.length < guestSearchTotal.value
)

// 模块权限相关状态
const availableModules = ref<ModuleInfo[]>([])
const modulePermissions = reactive<ModulePermissions>({
  ancient_books: false,
  paintings: false,
  archives: false,
  videos: false,
})

// 获取当前用户
const currentUser = computed(() => authManager.currentUser)

// 搜索防抖
let searchTimeout: any
const debounceSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 500)
}

// 页面初始化
onMounted(async () => {
  await loadUserList()
  await loadProvinces()
  await loadAvailableModules()
})

// 加载用户列表
const loadUserList = async (isLoadMore = false) => {
  try {
    loading.value = true

    if (!isLoadMore) {
      currentPage.value = 1
      searchQuery.page = 1
    }

    const response = await getUserList(searchQuery)

    if (isLoadMore) {
      userList.value.push(...response.users)
    } else {
      userList.value = response.users
    }

    total.value = response.total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (loading.value || !hasMore.value) return

  currentPage.value++
  searchQuery.page = currentPage.value
  await loadUserList(true)
}

// 搜索处理
const handleSearch = async () => {
  await loadUserList()
}

// 角色筛选变化
const onRoleFilterChange = (e: any) => {
  roleFilterIndex.value = parseInt(e.detail.value)
  searchQuery.role = roleFilterOptions[roleFilterIndex.value].value
  handleSearch()
}

// 显示区域筛选
const showRegionFilter = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

// 获取区域筛选文本
const getRegionFilterText = () => {
  // TODO: 根据searchQuery中的区域ID显示区域名称
  return '全部区域'
}

// 获取用户头像文字
const getUserAvatarText = (user: UserInfo) => {
  if (user.nickname && user.nickname.length > 0) {
    return user.nickname.charAt(0).toUpperCase()
  }
  return '用'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    '0'
  )}-${String(date.getDate()).padStart(2, '0')}`
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${formatDate(dateStr)} ${String(date.getHours()).padStart(
    2,
    '0'
  )}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 显示用户详情
const showUserDetail = (user: UserInfo) => {
  selectedUser.value = user
  showUserDetailPopup.value = true
}

// 关闭用户详情
const closeUserDetail = () => {
  showUserDetailPopup.value = false
  selectedUser.value = null
}

// 禁用用户相关功能已注释
// // 从详情页切换用户状态
// const toggleUserStatusFromDetail = async () => {
//   if (!selectedUser.value) return
//   await toggleUserStatus(selectedUser.value)
//   // 更新选中用户的状态
//   if (selectedUser.value) {
//     selectedUser.value.is_active = !selectedUser.value.is_active
//   }
// }

// // 从详情页编辑用户角色
// const editUserRoleFromDetail = () => {
//   if (!selectedUser.value) return
//   // 关闭详情弹窗
//   userDetailPopup.value?.close()
//   // 打开角色编辑弹窗
//   editUserRole(selectedUser.value)
// }

// // 切换用户状态
// const toggleUserStatus = async (user: UserInfo) => {
//   try {
//     uni.showLoading({ title: '处理中...' })

//     const updatedUser = await updateUserStatus(user.id, !user.is_active)

//     // 更新本地数据
//     const index = userList.value.findIndex((u) => u.id === user.id)
//     if (index !== -1) {
//       userList.value[index] = updatedUser
//     }

//     uni.showToast({
//       title: `用户已${updatedUser.is_active ? '启用' : '禁用'}`,
//       icon: 'success',
//     })
//   } catch (error) {
//     console.error('更新用户状态失败:', error)
//     uni.showToast({
//       title: '操作失败',
//       icon: 'none',
//     })
//   } finally {
//     uni.hideLoading()
//   }
// }

// 编辑用户角色
const editUserRole = async (user: UserInfo) => {
  roleEditForm.userId = user.id

  // 设置当前角色
  const roleIndex = availableRoles.value.findIndex((r) => r.value === user.role)
  roleEditForm.roleIndex = roleIndex >= 0 ? roleIndex : 0

  // 重置区域选择
  roleEditForm.provinceIndex = -1
  roleEditForm.cityIndex = -1
  roleEditForm.districtIndex = -1
  cities.value = []
  districts.value = []

  const currentUser = authManager.currentUser
  if (!currentUser) return

  // 根据当前管理员的级别自动设置区域限制
  if (currentUser.role === UserRole.PROVINCE_ADMIN && currentUser.province_id) {
    // 省级管理员：自动设置省份，且不可更改
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      // 加载该省的城市列表
      cities.value = await getCities(currentUser.province_id)
    }
  } else if (
    currentUser.role === UserRole.CITY_ADMIN &&
    currentUser.province_id &&
    currentUser.city_id
  ) {
    // 市级管理员：自动设置省份和城市，且不可更改
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      cities.value = await getCities(currentUser.province_id)

      const cityIndex = cities.value.findIndex(
        (c) => c.city_id === currentUser.city_id
      )
      if (cityIndex >= 0) {
        roleEditForm.cityIndex = cityIndex
        // 加载该市的区县列表
        districts.value = await getDistricts(
          currentUser.province_id,
          currentUser.city_id
        )
      }
    }
  } else if (
    currentUser.role === UserRole.DISTRICT_ADMIN &&
    currentUser.province_id &&
    currentUser.city_id &&
    currentUser.district_id
  ) {
    // 区县管理员：自动设置省份、城市和区县，且不可更改
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      cities.value = await getCities(currentUser.province_id)

      const cityIndex = cities.value.findIndex(
        (c) => c.city_id === currentUser.city_id
      )
      if (cityIndex >= 0) {
        roleEditForm.cityIndex = cityIndex
        districts.value = await getDistricts(
          currentUser.province_id,
          currentUser.city_id
        )

        const districtIndex = districts.value.findIndex(
          (d) => d.district_id === currentUser.district_id
        )
        if (districtIndex >= 0) {
          roleEditForm.districtIndex = districtIndex
        }
      }
    }
  } else {
    // 超级管理员或其他情况：如果用户已有区域信息，设置对应的选择器索引
    if (user.province_id) {
      const provinceIndex = provinces.value.findIndex(
        (p) => p.province_id === user.province_id
      )
      if (provinceIndex >= 0) {
        roleEditForm.provinceIndex = provinceIndex
        // 异步加载城市数据
        loadCitiesForEdit(user)
      }
    }
  }

  // 设置选中的用户，用于模块权限管理
  selectedUser.value = user

  // 如果是超级管理员且目标用户不是游客，加载模块权限
  if (
    currentUser.role === UserRole.SUPER_ADMIN &&
    user.role !== UserRole.GUEST
  ) {
    await loadUserModulePermissions(user.id)
  }

  showRoleEditPopup.value = true
}

// 关闭角色编辑
const closeRoleEdit = () => {
  showRoleEditPopup.value = false
}

// 角色选择变化
const onRoleEditChange = async (e: any) => {
  roleEditForm.roleIndex = parseInt(e.detail.value)

  const currentUser = authManager.currentUser
  if (!currentUser) return

  // 根据当前管理员的级别保持区域限制
  if (currentUser.role === UserRole.PROVINCE_ADMIN && currentUser.province_id) {
    // 省级管理员：保持省份设置
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      if (cities.value.length === 0) {
        cities.value = await getCities(currentUser.province_id)
      }
    }
    // 重置下级区域
    roleEditForm.cityIndex = -1
    roleEditForm.districtIndex = -1
    districts.value = []
  } else if (
    currentUser.role === UserRole.CITY_ADMIN &&
    currentUser.province_id &&
    currentUser.city_id
  ) {
    // 市级管理员：保持省份和城市设置
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      if (cities.value.length === 0) {
        cities.value = await getCities(currentUser.province_id)
      }

      const cityIndex = cities.value.findIndex(
        (c) => c.city_id === currentUser.city_id
      )
      if (cityIndex >= 0) {
        roleEditForm.cityIndex = cityIndex
        if (districts.value.length === 0) {
          districts.value = await getDistricts(
            currentUser.province_id,
            currentUser.city_id
          )
        }
      }
    }
    // 重置区县
    roleEditForm.districtIndex = -1
  } else if (
    currentUser.role === UserRole.DISTRICT_ADMIN &&
    currentUser.province_id &&
    currentUser.city_id &&
    currentUser.district_id
  ) {
    // 区县管理员：保持省份、城市和区县设置
    const provinceIndex = provinces.value.findIndex(
      (p) => p.province_id === currentUser.province_id
    )
    if (provinceIndex >= 0) {
      roleEditForm.provinceIndex = provinceIndex
      if (cities.value.length === 0) {
        cities.value = await getCities(currentUser.province_id)
      }

      const cityIndex = cities.value.findIndex(
        (c) => c.city_id === currentUser.city_id
      )
      if (cityIndex >= 0) {
        roleEditForm.cityIndex = cityIndex
        if (districts.value.length === 0) {
          districts.value = await getDistricts(
            currentUser.province_id,
            currentUser.city_id
          )
        }

        const districtIndex = districts.value.findIndex(
          (d) => d.district_id === currentUser.district_id
        )
        if (districtIndex >= 0) {
          roleEditForm.districtIndex = districtIndex
        }
      }
    }
  } else {
    // 超级管理员：完全重置区域选择
    roleEditForm.provinceIndex = -1
    roleEditForm.cityIndex = -1
    roleEditForm.districtIndex = -1
    cities.value = []
    districts.value = []
  }
}

// 省份选择变化
const onProvinceEditChange = async (e: any) => {
  roleEditForm.provinceIndex = parseInt(e.detail.value)
  roleEditForm.cityIndex = -1
  roleEditForm.districtIndex = -1
  districts.value = []

  if (roleEditForm.provinceIndex >= 0) {
    const province = provinces.value[roleEditForm.provinceIndex]
    cities.value = await getCities(province.province_id)
  } else {
    cities.value = []
  }
}

// 城市选择变化
const onCityEditChange = async (e: any) => {
  roleEditForm.cityIndex = parseInt(e.detail.value)
  roleEditForm.districtIndex = -1

  if (roleEditForm.cityIndex >= 0 && roleEditForm.provinceIndex >= 0) {
    const province = provinces.value[roleEditForm.provinceIndex]
    const city = cities.value[roleEditForm.cityIndex]
    districts.value = await getDistricts(province.province_id, city.city_id)
  } else {
    districts.value = []
  }
}

// 区县选择变化
const onDistrictEditChange = (e: any) => {
  roleEditForm.districtIndex = parseInt(e.detail.value)
}

// 保存用户角色
const saveUserRole = async () => {
  try {
    // 安全检查角色索引
    if (
      roleEditForm.roleIndex < 0 ||
      roleEditForm.roleIndex >= availableRoles.value.length
    ) {
      uni.showToast({
        title: '请选择角色',
        icon: 'none',
      })
      return
    }

    const selectedRole = availableRoles.value[roleEditForm.roleIndex]
    if (!selectedRole) {
      uni.showToast({
        title: '请选择角色',
        icon: 'none',
      })
      return
    }

    const updateData: UserRoleUpdateRequest = {
      role: selectedRole.value,
      // 默认清空所有区域ID
      province_id: undefined,
      city_id: undefined,
      district_id: undefined,
    }

    // 根据角色设置区域信息
    if (selectedRole.value === UserRole.PROVINCE_ADMIN) {
      // 省级管理员只需要省份ID，清空市和区县ID
      if (roleEditForm.provinceIndex < 0) {
        uni.showToast({
          title: '请选择省份',
          icon: 'none',
        })
        return
      }
      updateData.province_id =
        provinces.value[roleEditForm.provinceIndex].province_id
      // 明确清空下级区域ID
      updateData.city_id = undefined
      updateData.district_id = undefined
    } else if (selectedRole.value === UserRole.CITY_ADMIN) {
      // 市级管理员需要省份ID和城市ID，清空区县ID
      if (roleEditForm.provinceIndex < 0) {
        uni.showToast({
          title: '请选择省份',
          icon: 'none',
        })
        return
      }
      if (roleEditForm.cityIndex < 0) {
        uni.showToast({
          title: '请选择城市',
          icon: 'none',
        })
        return
      }
      updateData.province_id =
        provinces.value[roleEditForm.provinceIndex].province_id
      updateData.city_id = cities.value[roleEditForm.cityIndex].city_id
      // 明确清空区县ID
      updateData.district_id = undefined
    } else if (selectedRole.value === UserRole.DISTRICT_ADMIN) {
      // 区县管理员需要省份ID、城市ID和区县ID
      if (roleEditForm.provinceIndex < 0) {
        uni.showToast({
          title: '请选择省份',
          icon: 'none',
        })
        return
      }
      if (roleEditForm.cityIndex < 0) {
        uni.showToast({
          title: '请选择城市',
          icon: 'none',
        })
        return
      }
      if (roleEditForm.districtIndex < 0) {
        uni.showToast({
          title: '请选择区县',
          icon: 'none',
        })
        return
      }
      updateData.province_id =
        provinces.value[roleEditForm.provinceIndex].province_id
      updateData.city_id = cities.value[roleEditForm.cityIndex].city_id
      updateData.district_id =
        districts.value[roleEditForm.districtIndex].district_id
    }
    // 对于游客和超级管理员角色，保持默认的undefined值，清空所有区域ID

    uni.showLoading({ title: '保存中...' })
    console.log(updateData)
    const updatedUser = await updateUserRole(roleEditForm.userId, updateData)

    // 如果是超级管理员且目标用户不是游客，同时更新模块权限
    const currentUser = authManager.currentUser
    if (
      currentUser?.role === UserRole.SUPER_ADMIN &&
      selectedRole.value !== UserRole.GUEST
    ) {
      try {
        await updateUserModulePermissions(roleEditForm.userId, {
          module_permissions: { ...modulePermissions },
        })
      } catch (error) {
        console.error('更新模块权限失败:', error)
        // 权限更新失败不阻断主流程，但要提示用户
        uni.showToast({
          title: '角色更新成功，但模块权限更新失败',
          icon: 'none',
          duration: 3000,
        })
      }
    }

    // 更新本地数据
    // 1. 尝试更新主用户列表中的用户（如果存在）
    const userListIndex = userList.value.findIndex(
      (u) => u.id === roleEditForm.userId
    )
    if (userListIndex !== -1) {
      userList.value[userListIndex] = updatedUser
    }

    // 2. 尝试更新游客搜索结果中的用户（如果存在）
    const guestListIndex = guestSearchResults.value.findIndex(
      (u) => u.id === roleEditForm.userId
    )
    if (guestListIndex !== -1) {
      guestSearchResults.value[guestListIndex] = updatedUser
    }

    showRoleEditPopup.value = false

    uni.showToast({
      title: '角色更新成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('更新用户角色失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 加载省份列表
const loadProvinces = async () => {
  try {
    provinces.value = await getProvinces()
  } catch (error) {
    console.error('加载省份列表失败:', error)
  }
}

// 加载可用模块列表
const loadAvailableModules = async () => {
  try {
    const response = await getAvailableModules()
    availableModules.value = response.modules
  } catch (error) {
    console.error('加载可用模块列表失败:', error)
  }
}

// 加载用户模块权限
const loadUserModulePermissions = async (userId: number) => {
  try {
    const response = await getUserModulePermissions(userId)
    // 更新模块权限状态
    Object.assign(modulePermissions, response.module_permissions)
  } catch (error) {
    console.error('加载用户模块权限失败:', error)
    // 如果加载失败，重置为默认值
    Object.assign(modulePermissions, {
      ancient_books: false,
      paintings: false,
      archives: false,
      videos: false,
    })
  }
}

// 模块权限变化处理
const onModulePermissionChange = (moduleKey: string, event: any) => {
  const isEnabled = event.detail.value
  ;(modulePermissions as any)[moduleKey] = isEnabled
}

// 为编辑用户加载城市和区县数据
const loadCitiesForEdit = async (user: UserInfo) => {
  try {
    if (user.province_id) {
      cities.value = await getCities(user.province_id)

      if (user.city_id) {
        const cityIndex = cities.value.findIndex(
          (c) => c.city_id === user.city_id
        )
        if (cityIndex >= 0) {
          roleEditForm.cityIndex = cityIndex

          if (user.district_id) {
            districts.value = await getDistricts(user.province_id, user.city_id)
            const districtIndex = districts.value.findIndex(
              (d) => d.district_id === user.district_id
            )
            if (districtIndex >= 0) {
              roleEditForm.districtIndex = districtIndex
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('加载区域数据失败:', error)
  }
}

// 显示游客搜索
const showGuestSearch = () => {
  showGuestSearchPopup.value = true
  // 重置搜索表单
  guestSearchForm.phone = ''
  guestSearchForm.page = 1
  guestSearchResults.value = []
  guestSearchTotal.value = 0
  guestSearchCurrentPage.value = 1
  hasSearchedGuests.value = false
}

// 关闭游客搜索
const closeGuestSearch = () => {
  showGuestSearchPopup.value = false
}

// 搜索游客用户
const searchGuests = async () => {
  if (!guestSearchForm.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  try {
    guestSearchLoading.value = true
    guestSearchCurrentPage.value = 1
    guestSearchForm.page = 1

    const response = await searchGuestUsers(guestSearchForm)
    guestSearchResults.value = response.users
    guestSearchTotal.value = response.total
    hasSearchedGuests.value = true

    if (response.users.length === 0) {
      uni.showToast({
        title: '未找到符合条件的用户',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('搜索游客用户失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'none',
    })
  } finally {
    guestSearchLoading.value = false
  }
}

// 加载更多游客搜索结果
const loadMoreGuestSearch = async () => {
  if (guestSearchLoading.value || !guestSearchHasMore.value) return

  try {
    guestSearchLoading.value = true
    guestSearchCurrentPage.value++
    guestSearchForm.page = guestSearchCurrentPage.value

    const response = await searchGuestUsers(guestSearchForm)
    guestSearchResults.value.push(...response.users)
  } catch (error) {
    console.error('加载更多游客搜索结果失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    guestSearchLoading.value = false
  }
}

// 选择游客进行授权
const selectGuestForAuth = async (user: UserInfo) => {
  // 关闭搜索弹窗
  closeGuestSearch()
  // 打开角色编辑弹窗
  await editUserRole(user)
}

// 是否禁用省份选择器
const isProvinceDisabled = computed(() => {
  const currentUser = authManager.currentUser
  if (!currentUser) return false

  // 省级、市级、区县管理员不能更改省份
  return [
    UserRole.PROVINCE_ADMIN,
    UserRole.CITY_ADMIN,
    UserRole.DISTRICT_ADMIN,
  ].includes(currentUser.role as UserRole)
})

// 是否禁用城市选择器
const isCityDisabled = computed(() => {
  const currentUser = authManager.currentUser
  if (!currentUser) return false

  // 市级、区县管理员不能更改城市
  return [UserRole.CITY_ADMIN, UserRole.DISTRICT_ADMIN].includes(
    currentUser.role as UserRole
  )
})

// 是否禁用区县选择器
const isDistrictDisabled = computed(() => {
  const currentUser = authManager.currentUser
  if (!currentUser) return false

  // 区县管理员不能更改区县
  return currentUser.role === UserRole.DISTRICT_ADMIN
})

// 获取用户地区文本
const getUserRegionText = (user: UserInfo) => {
  if (user.role === UserRole.GUEST || user.role === UserRole.SUPER_ADMIN) {
    return '' // 游客和超级管理员不显示地区信息
  }

  // 使用后端返回的省市区名称
  let regionText = ''

  if (user.province_name) {
    regionText = user.province_name

    if (user.city_name) {
      regionText += ` ${user.city_name}`
    }

    if (user.district_name) {
      regionText += ` ${user.district_name}`
    }
  } else if (user.province_id) {
    return '未知地区'
  } else {
    return '未设置地区'
  }

  return regionText
}

// 检查用户是否有任何模块权限
const hasAnyModulePermission = (user: UserInfo) => {
  if (!user.module_permissions || user.role === UserRole.GUEST) {
    return false
  }

  return (
    user.module_permissions.ancient_books ||
    user.module_permissions.paintings ||
    user.module_permissions.archives ||
    user.module_permissions.videos
  )
}
</script>

<style scoped>
.users-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 搜索和筛选 */
.search-section {
  background-color: #ffffff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-bar {
  display: flex;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx 0 0 10rpx;
  font-size: 28rpx;
}

.search-btn {
  padding: 20rpx 30rpx;
  background-color: #c8161e;
  color: #ffffff;
  border: none;
  border-radius: 0 10rpx 10rpx 0;
  font-size: 28rpx;
}

.guest-search-btn {
  padding: 20rpx 30rpx;
  background-color: #c8161e;
  color: #ffffff;
  border: none;
  border-radius: 0 10rpx 10rpx 0;
  font-size: 28rpx;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
}

.filter-item {
  padding: 15rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #666;
}

/* 用户列表 */
.user-list {
  padding: 0 20rpx;
}

.user-item {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.user-item:active {
  transform: scale(0.98);
  background-color: #f8f8f8;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 30rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #c8161e;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  color: #c8161e;
  margin-bottom: 8rpx;
}

.user-region {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.user-meta {
  font-size: 22rpx;
  color: #999;
}

.user-actions {
  margin-left: 20rpx;
}

.auth-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  background-color: #c8161e;
  color: #ffffff;
  transition: all 0.2s;
}

.auth-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 详情页操作按钮 */
.detail-actions {
  margin-top: 40rpx;
  display: flex;
  justify-content: space-between;
}

.detail-action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.detail-action-btn:first-child {
  margin-right: 20rpx;
}

.detail-action-btn.disable-btn {
  background-color: #ff4444;
  color: #ffffff;
}

.detail-action-btn.enable-btn {
  background-color: #00cc66;
  color: #ffffff;
}

.detail-action-btn.auth-btn {
  background-color: #c8161e;
  color: #ffffff;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #666;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 角色编辑弹窗 - 确保在最顶层 */
.role-edit-overlay {
  z-index: 2000 !important;
  background-color: rgba(0, 0, 0, 0.7);
}

.role-edit-popup {
  width: 650rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 2100;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}

.detail-content {
  padding: 40rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 30rpx;
}

.detail-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 用户详情弹窗 */
.user-detail-popup {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
  width: 100%;
  margin-top: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.popup-overlay {
  align-items: flex-end;
}

/* 游客搜索弹窗overlay - 居中显示 */
.popup-overlay:not(.role-edit-overlay) {
  align-items: center;
}

/* 用户详情弹窗overlay - 底部显示 */
.popup-overlay:has(.user-detail-popup) {
  align-items: flex-end;
}

/* 角色编辑弹窗 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.picker-input {
  display: flex;
  align-items: center;
  color: #333;
}

.picker-input.disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.region-selection {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
  margin-top: 20rpx;
}

.popup-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #c8161e;
  color: #ffffff;
}

/* 游客搜索弹窗 */
.guest-search-popup {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
  width: 90%;
  max-width: 700rpx;
  z-index: 1000;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 模块权限管理样式 */
.module-permissions-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
  margin-top: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.permission-list {
  background-color: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-info {
  flex: 1;
  margin-right: 20rpx;
}

.permission-name {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.permission-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.permission-switch {
  transform: scale(0.8);
}

/* 用户权限标识样式 */
.user-permissions {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.permission-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.permission-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  line-height: 1.2;
}

.permission-tag.active {
  background-color: #e8f5e8;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.permission-tag.inactive {
  background-color: #f5f5f5;
  color: #999;
  border: 1rpx solid #d9d9d9;
}

.search-form {
  margin-bottom: 40rpx;
}

.search-actions {
  display: flex;
  justify-content: space-between;
}

.results-header {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.guest-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.guest-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 30rpx;
}

.guest-info {
  flex: 1;
}

.guest-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.guest-phone {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.guest-meta {
  font-size: 22rpx;
  color: #999;
}

.guest-actions {
  margin-left: 20rpx;
}

.empty-search {
  text-align: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
}
</style> 