"""
图片二进制处理工具函数
将图片转换为blob格式传输，前端接收后转换为URL显示
"""

import logging
from typing import Optional, List, Dict, Any
import json
import base64
from app.core.minio_client import MinioClient

logger = logging.getLogger(__name__)

# 初始化Minio客户端
minio_client = MinioClient()


def get_image_binary_data(image_url: Optional[str]) -> Optional[str]:
    """
    从MinIO获取图片的原始二进制数据，编码为base64用于传输
    前端接收后需要解码为blob并创建URL显示图片
    
    Args:
        image_url: 完整的MinIO图片URL
        
    Returns:
        base64编码的二进制数据（用于JSON传输），前端需解码为blob，如果失败则返回None
    """
    if not image_url:
        return None
    
    try:
        # 检查Minio服务是否可用
        if not minio_client._initialize_client():
            logger.error("MinIO服务不可用")
            return None
        
        # 从完整的MinIO URL提取object_name
        object_name = extract_object_name_from_url(image_url)
        if not object_name:
            logger.error(f"无法从URL提取对象名称: {image_url}")
            return None
        
        logger.info(f"正在获取图片: {object_name}")
        
        # 从MinIO获取图片数据
        response = minio_client.client.get_object(
            minio_client.bucket_name,
            object_name
        )
        
        # 读取原始二进制数据
        image_data = response.read()
        response.close()
        
        # 将二进制数据编码为base64字符串，用于JSON传输
        base64_data = base64.b64encode(image_data).decode('utf-8')
        return 'data:image/jpeg;base64,' + base64_data
        
    except Exception as e:
        logger.error(f"获取图片二进制数据失败: {image_url}, 错误: {e}")
        return None


def extract_object_name_from_url(image_url: str) -> Optional[str]:
    """
    从图片URL中提取MinIO对象名称
    
    Args:
        image_url: 图片URL
        
    Returns:
        MinIO对象名称，失败返回None
    """
    try:
        # 情况1: 完整的MinIO URL - http://192.168.8.96:9000/millennia-now/place/xxx.jpg
        if '/millennia-now/' in image_url:
            object_name = image_url.split('/millennia-now/')[-1]
            logger.debug(f"从MinIO URL提取对象名称: {image_url} -> {object_name}")
            return object_name
        
        # 情况2: 直接的对象名称 - place/xxx.jpg
        if not image_url.startswith('http') and '/' in image_url and '.' in image_url:
            logger.debug(f"直接对象名称: {image_url}")
            return image_url
        
        # 情况3: 静态文件路径，不处理
        if '/static/' in image_url:
            logger.debug(f"静态文件路径，跳过: {image_url}")
            return None
        
        logger.warning(f"无法识别的URL格式: {image_url}")
        return None
        
    except Exception as e:
        logger.error(f"提取对象名称失败: {image_url}, 错误: {e}")
        return None


def convert_image_list_to_binary_data(image_urls: Optional[List[str]]) -> List[str]:
    """
    将图片URL列表转换为base64编码的字符串列表（用于JSON传输）
    前端接收后需要解码为blob数据
    
    Args:
        image_urls: 图片URL列表
        
    Returns:
        base64编码的字符串列表（前端需解码为blob）
    """
    if not image_urls:
        return []
    
    result = []
    for url in image_urls:
        if url:
            binary_data = get_image_binary_data(url)
            if binary_data:
                result.append(binary_data)
            else:
                logger.warning(f"无法获取图片二进制数据: {url}")
    
    return result


def add_binary_data_to_heritage_place(place_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将地点图片转换为base64编码的字符串，替换原有的URL字段
    前端接收后需要解码为blob并创建URL显示
    
    Args:
        place_data: 地点数据字典
        
    Returns:
        包含base64编码字符串的数据字典
    """
    # 处理头部背景图：替换为base64编码的字符串
    if place_data.get('header_bg_image'):
        base64_data = get_image_binary_data(place_data['header_bg_image'])
        if base64_data:
            # 替换为base64编码的字符串（前端将解码为blob并创建URL）
            place_data['header_bg_image'] = base64_data
        else:
            # 如果获取失败，设为空
            place_data['header_bg_image'] = None
    
    return place_data


def add_binary_data_to_timeline_item(item_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将时间轴项目图片转换为base64编码的字符串
    前端接收后需要解码为blob并创建URL显示
    
    Args:
        item_data: 时间轴项目数据字典
        
    Returns:
        包含base64编码字符串的数据字典
    """
    # 处理主图片
    if item_data.get('image'):
        base64_data = get_image_binary_data(item_data['image'])
        if base64_data:
            item_data['image'] = base64_data
        else:
            item_data['image'] = None
    
    # 处理详细图片列表
    if item_data.get('detail_images'):
        detail_images = item_data['detail_images']
        
        # 如果是JSON字符串，先解析
        if isinstance(detail_images, str):
            try:
                detail_images = json.loads(detail_images)
            except (json.JSONDecodeError, TypeError):
                detail_images = []
        
        if isinstance(detail_images, list):
            base64_data_list = convert_image_list_to_binary_data(detail_images)
            item_data['detail_images'] = base64_data_list
    
    return item_data


def add_binary_data_to_heritage_item(item_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将文化遗产项目图片转换为base64编码的字符串
    前端接收后需要解码为blob并创建URL显示
    
    Args:
        item_data: 文化遗产项目数据字典
        
    Returns:
        包含base64编码字符串的数据字典
    """
    # 处理主图片
    if item_data.get('image'):
        base64_data = get_image_binary_data(item_data['image'])
        if base64_data:
            item_data['image'] = base64_data
        else:
            item_data['image'] = None
    
    # 处理详细图片列表
    if item_data.get('detail_images'):
        detail_images = item_data['detail_images']
        
        # 如果是JSON字符串，先解析
        if isinstance(detail_images, str):
            try:
                detail_images = json.loads(detail_images)
            except (json.JSONDecodeError, TypeError):
                detail_images = []
        
        if isinstance(detail_images, list):
            base64_data_list = convert_image_list_to_binary_data(detail_images)
            item_data['detail_images'] = base64_data_list
    
    return item_data


def add_binary_data_to_memory_item(item_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将城市记忆项目图片转换为base64编码的字符串
    前端接收后需要解码为blob并创建URL显示
    
    Args:
        item_data: 城市记忆项目数据字典
        
    Returns:
        包含base64编码字符串的数据字典
    """
    # 处理主图片
    if item_data.get('image'):
        base64_data = get_image_binary_data(item_data['image'])
        if base64_data:
            item_data['image'] = base64_data
        else:
            item_data['image'] = None
    
    # 处理详细图片列表
    if item_data.get('detail_images'):
        detail_images = item_data['detail_images']
        
        # 如果是JSON字符串，先解析
        if isinstance(detail_images, str):
            try:
                detail_images = json.loads(detail_images)
            except (json.JSONDecodeError, TypeError):
                detail_images = []
        
        if isinstance(detail_images, list):
            base64_data_list = convert_image_list_to_binary_data(detail_images)
            item_data['detail_images'] = base64_data_list
    
    return item_data 