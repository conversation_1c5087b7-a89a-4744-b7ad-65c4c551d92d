<template>
  <div class="gallery-container">
    <!-- 3D场景容器 -->
    <div class="scene-container">
      <canvas ref="sceneCanvas"></canvas>
    </div>

    <!-- 用户界面元素 -->
    <div class="ui-container">
      <!-- 使用小程序返回按钮组件 -->
      <MiniappBackButton />

      <!-- 普通返回按钮（当不是从小程序进入时显示） -->
      <div class="back-button"
           @click="goBack"
           v-if="!isFromMiniapp">
        <span class="icon">←</span>
        <span class="text">返回</span>
      </div>

      <!-- 展厅标题 -->
      <div class="gallery-title">
        <h1>当代记忆</h1>
        <p>数字时代的城市印记</p>
      </div>

      <!-- 加载进度指示器 -->
      <div class="loading-progress"
           v-if="loading">
        <div class="loading-text">加载中 {{ Math.floor(loadingProgress) }}%</div>
        <div class="progress-bar">
          <div class="progress"
               :style="{ width: loadingProgress + '%' }"></div>
        </div>
      </div>

      <!-- 展品信息面板 -->
      <div class="exhibit-panel"
           v-if="selectedExhibit"
           :class="{ 'active': selectedExhibit }">
        <div class="panel-header">
          <h2>{{ selectedExhibit.title }}</h2>
          <button class="close-btn"
                  @click="closeExhibitPanel">×</button>
        </div>
        <div class="panel-content">
          <!-- 图片轮播 - 固定在上方 -->
          <div class="image-carousel">
            <!-- 主图片 -->
            <img :src="currentImage"
                 alt="展品图片"
                 class="exhibit-image"
                 @click="showFullImage(currentImage)">

            <!-- 缩略图预览 -->
            <div class="thumbnail-container"
                 v-if="hasMultipleImages">
              <div class="thumbnails">
                <div v-for="(image, index) in allImages"
                     :key="index"
                     class="thumbnail"
                     :class="{ 'active': currentImageIndex === index }"
                     @click="setCurrentImage(index)">
                  <img :src="image"
                       :alt="`缩略图 ${index+1}`">
                </div>
              </div>
            </div>

            <!-- 轮播控制按钮 -->
            <div class="carousel-controls"
                 v-if="hasMultipleImages">
              <button class="control-btn prev"
                      @click="prevImage">&lt;</button>
              <button class="control-btn next"
                      @click="nextImage">&gt;</button>
            </div>
          </div>

          <!-- 展品信息区域 - 可独立滚动 -->
          <div class="exhibit-info-container">
            <div class="exhibit-info">
              <!-- 基本信息卡片 -->
              <div class="info-card basic-info">
                <div class="card-header">
                  <h3><i class="icon-time"></i>记忆信息</h3>
                </div>
                <div class="card-content">
                  <div class="info-row">
                    <span class="label">年代：</span>
                    <span class="value year-badge">{{ selectedExhibit.year }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">类型：</span>
                    <span class="value memory-type-badge">{{ selectedExhibit.memory_type || '城市记忆' }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">时期：</span>
                    <span class="value">{{ selectedExhibit.period }}</span>
                  </div>
                </div>
              </div>

              <!-- 记忆描述卡片 -->
              <div class="info-card description-info">
                <div class="card-header">
                  <h3><i class="icon-camera"></i>记忆描述</h3>
                </div>
                <div class="card-content">
                  <p class="memory-description">{{ selectedExhibit.description }}</p>
                </div>
              </div>

              <!-- 详细介绍卡片 -->
              <div class="info-card detail-info"
                   v-if="selectedExhibit.content">
                <div class="card-header">
                  <h3><i class="icon-detail"></i>详细介绍</h3>
                </div>
                <div class="card-content">
                  <div class="content-text"
                       v-html="formattedContent"></div>
                </div>
              </div>

              <!-- 记忆意义卡片 -->
              <div class="info-card significance-info"
                   v-if="selectedExhibit.significance">
                <div class="card-header">
                  <h3><i class="icon-heart"></i>记忆意义</h3>
                </div>
                <div class="card-content">
                  <p class="significance-text">{{ selectedExhibit.significance }}</p>
                </div>
              </div>

              <!-- 元数据信息 -->
              <div class="meta-info">
                <div class="meta-row"
                     v-if="selectedExhibit.created_at">
                  <span class="meta-label">创建时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.created_at) }}</span>
                </div>
                <div class="meta-row"
                     v-if="selectedExhibit.updated_at">
                  <span class="meta-label">更新时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作提示 -->
      <div class="controls-hint"
           v-if="!loading && !selectedExhibit">
        <p>使用 WASD 或方向键移动，鼠标左键点击展品查看详情</p>
      </div>

      <!-- 移动端移动摇杆控制 -->
      <div class="move-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="moveControl"
           @touchstart.prevent="handleMoveJoystickStart"
           @mousedown.prevent="handleMoveJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="moveJoystickHandle"
               :style="moveJoystickStyle"></div>
        </div>
      </div>

      <!-- 移动端视角摇杆控制 -->
      <div class="look-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="lookControl"
           @touchstart.prevent="handleLookJoystickStart"
           @mousedown.prevent="handleLookJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="lookJoystickHandle"
               :style="lookJoystickStyle"></div>
        </div>
      </div>
    </div>

    <!-- 全屏图片查看器 -->
    <div class="fullscreen-image-viewer"
         v-if="fullScreenImage"
         @click="closeFullImage">
      <div class="fullscreen-image-container"
           @click.stop>
        <img :src="fullScreenImage"
             alt="全屏图片">
        <button class="close-fullscreen-btn"
                @click="closeFullImage">×</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as THREE from 'three'
import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls'
import MiniappBackButton from '../components/MiniappBackButton.vue'
import {
  getMemoryData,
  getMemoryPageDataByRegion,
  generateDefaultMemoryData,
  getImageProxyUrl,
} from '../api/memory'
import {
  convertMemoryArrayToExhibits,
  sortExhibitsByYear,
  getDataStats,
  type ExhibitData,
  type MemoryDataItem,
} from '../utils/dataProcessor'
import { isDebugMode } from '../config/api'

const router = useRouter()
const route = useRoute()

// 小程序环境标记
const isFromMiniapp = ref(false)

// 添加区域信息状态
const regionName = ref('')
const provinceId = ref<number | null>(null)
const cityId = ref<number | null>(null)
const districtId = ref<number | null>(null)

// 尝试从route参数或localStorage中获取区域信息
const initRegionInfo = () => {
  // 检查是否从小程序进入
  const urlParams = new URLSearchParams(window.location.search)
  const fromParam = urlParams.get('from') === 'miniapp'
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true'

  isFromMiniapp.value = fromParam || fromStorage

  // 从路由参数中获取区域信息（支持两种命名格式）
  // 优先使用驼峰命名（从首页传递的参数）
  if (route.query.provinceId) {
    provinceId.value = Number(route.query.provinceId)
  } else if (route.query.province_id) {
    provinceId.value = Number(route.query.province_id)
  }

  if (route.query.cityId) {
    cityId.value = Number(route.query.cityId)
  } else if (route.query.city_id) {
    cityId.value = Number(route.query.city_id)
  }

  if (route.query.districtId) {
    districtId.value = Number(route.query.districtId)
  } else if (route.query.district_id) {
    districtId.value = Number(route.query.district_id)
  }

  // 同时获取区域名称
  if (route.query.regionName) {
    regionName.value = route.query.regionName as string
  }

  // 如果路由参数中没有，尝试从localStorage获取
  if (!provinceId.value && !cityId.value && !districtId.value) {
    const storedProvinceId = localStorage.getItem('selectedProvinceId')
    const storedCityId = localStorage.getItem('selectedCityId')
    const storedDistrictId = localStorage.getItem('selectedDistrictId')

    if (storedProvinceId) provinceId.value = Number(storedProvinceId)
    if (storedCityId) cityId.value = Number(storedCityId)
    if (storedDistrictId) districtId.value = Number(storedDistrictId)
  }

  // 输出接收到的参数信息
  console.log('🎯 当代记忆展厅接收到区域信息:', {
    路由参数: route.query,
    解析结果: {
      regionName: regionName.value,
      provinceId: provinceId.value,
      cityId: cityId.value,
      districtId: districtId.value,
    },
  })

  // 获取区域名称
  const storedRegionName = localStorage.getItem('selectedRegionName')
  if (storedRegionName) {
    regionName.value = storedRegionName
  }

  console.log('当代记忆展厅 - 初始化区域信息:', {
    provinceId: provinceId.value,
    cityId: cityId.value,
    districtId: districtId.value,
    regionName: regionName.value,
  })
}

// 场景相关变量
const sceneCanvas = ref<HTMLCanvasElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: PointerLockControls
let raycaster: THREE.Raycaster
let clock = new THREE.Clock()
let animationFrameId: number

// 视角控制相关引用（新的右侧摇杆系统）
const lookControl = ref<HTMLElement | null>(null)
// 旧的 joystickHandle 和 joystickStyle 已移除，现在使用 lookJoystickHandle 和 lookJoystickStyle

// 视角控制状态（新的右侧摇杆系统）
const lookActive = ref(false)
const lookStartPosition = { x: 0, y: 0 }
const lookCurrentPosition = { x: 0, y: 0 }
// 旧的 cameraRotation 已移除，现在使用 cameraYaw 和 cameraPitch

// 右侧视角摇杆相关变量（新增）
const lookJoystickHandle = ref<HTMLElement | null>(null)
const lookJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 触摸ID跟踪，确保多点触控正常工作
let moveTouchId: number | null = null
let lookTouchId: number | null = null

// 移动摇杆控制相关引用
const moveControl = ref<HTMLElement | null>(null)
const moveJoystickHandle = ref<HTMLElement | null>(null)
const moveJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 移动摇杆控制状态
const moveActive = ref(false)
const moveStartPosition = { x: 0, y: 0 }
const moveCurrentPosition = { x: 0, y: 0 }
const moveDirection = { x: 0, z: 0 } // 移动方向向量

// 加载状态
const loading = ref(true)
const loadingProgress = ref(0)

// 移动控制变量
const moveForward = ref(false)
const moveBackward = ref(false)
const moveLeft = ref(false)
const moveRight = ref(false)
const canJump = ref(false)

let velocity = new THREE.Vector3()
let direction = new THREE.Vector3()
let prevTime = performance.now()

// 展品数据
const exhibits = ref<any[]>([])
const exhibitObjects: THREE.Object3D[] = []
const selectedExhibit = ref<any>(null)

// 图片轮播相关
const currentImageIndex = ref(0)
const currentImage = computed(() => {
  if (!selectedExhibit.value) return ''
  return allImages.value[currentImageIndex.value]
})

const allImages = computed(() => {
  if (!selectedExhibit.value) return []

  // 主图片加上所有详情图片
  const images = [selectedExhibit.value.image]
  if (
    selectedExhibit.value.detail_images &&
    Array.isArray(selectedExhibit.value.detail_images)
  ) {
    images.push(...selectedExhibit.value.detail_images)
  }
  return images
})

const hasMultipleImages = computed(() => {
  return allImages.value.length > 1
})

const formattedContent = computed(() => {
  if (!selectedExhibit.value || !selectedExhibit.value.content) return ''
  // 将内容中的换行符转换为HTML换行
  return selectedExhibit.value.content.replace(/\n/g, '<br>')
})

// 格式化日期的方法
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return dateString
  }
}

// 全屏图片查看器相关
const fullScreenImage = ref<string | null>(null)

const showFullImage = (imageSrc: string) => {
  fullScreenImage.value = imageSrc
}

const closeFullImage = () => {
  fullScreenImage.value = null
}

// 图片轮播相关方法
const nextImage = () => {
  if (currentImageIndex.value < allImages.value.length - 1) {
    currentImageIndex.value++
  } else {
    currentImageIndex.value = 0 // 循环到第一张
  }
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  } else {
    currentImageIndex.value = allImages.value.length - 1 // 循环到最后一张
  }
}

const setCurrentImage = (index: number) => {
  currentImageIndex.value = index
}

// 初始化3D场景
const initScene = async () => {
  if (!sceneCanvas.value) return

  // 检测设备类型
  checkMobileDevice()
  console.log('是否为移动设备:', isMobileDevice.value)

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x0a1a2a) // 深蓝色背景，营造科技感
  scene.fog = new THREE.Fog(0x0a1a2a, 10, 50) // 匹配的雾色

  // 添加环境光和方向光 - 科技感的冷色调
  const ambientLight = new THREE.AmbientLight(0xc8e0ff, 0.4) // 冷蓝色调
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xe0f0ff, 0.8) // 明亮的白蓝色调
  directionalLight.position.set(5, 10, 7.5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 添加点光源 - 科技感的蓝色LED灯效果
  const pointLight1 = new THREE.PointLight(0x00a0ff, 1.5, 30) // 明亮的蓝色调
  pointLight1.position.set(0, 3, 0)
  scene.add(pointLight1)

  // 设置相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  )
  camera.position.set(0, 1.6, 0) // 人眼高度约1.6米

  // 设置渲染器
  renderer = new THREE.WebGLRenderer({
    canvas: sceneCanvas.value,
    antialias: true,
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true

  // 使用指针锁定控制
  controls = new PointerLockControls(camera, renderer.domElement)
  scene.add(controls.getObject())

  // 移动设备上禁用指针锁定（因为不支持），但保留控制功能
  if (isMobileDevice.value) {
    console.log('移动设备：禁用指针锁定')
    // 修改lock方法以适应移动设备
    const originalLock = controls.lock
    controls.lock = function () {
      // 在移动设备上，只需将isLocked设为true而不实际尝试锁定指针
      this.isLocked = true
      return this
    }

    // 自动"锁定"控制，以便移动键可以正常工作
    controls.lock()

    // 移动设备上确保锁定后再初始化相机角度
    setTimeout(() => {
      initializeCameraAngles()
    }, 100)
  } else {
    // 桌面设备立即初始化
    initializeCameraAngles()
  }

  // 覆盖PointerLockControls的鼠标移动处理
  const originalOnMouseMove = (controls as any).onMouseMove
  if (originalOnMouseMove) {
    ;(controls as any).onMouseMove = function (event: MouseEvent) {
      if (!controls.isLocked) return

      const movementX = event.movementX || 0
      const movementY = event.movementY || 0

      // 使用我们的标准FPS相机控制
      updateCameraRotation(movementX, movementY, mouseSensitivity)
    }
  }

  // 设置射线投射器用于交互
  raycaster = new THREE.Raycaster()

  // 添加键盘事件监听
  document.addEventListener('keydown', onKeyDown)
  document.addEventListener('keyup', onKeyUp)

  // 添加点击事件监听
  renderer.domElement.addEventListener('click', onMouseClick)

  // 移动设备上添加触摸事件
  if (isMobileDevice.value) {
    // 用于选择展品
    renderer.domElement.addEventListener('touchend', onTouchEnd)
    // 移动端禁用手指触摸控制视角，只通过右侧摇杆控制
    // renderer.domElement.addEventListener('touchstart', handleCanvasTouchStart)
  }

  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize)

  // 加载数据并创建展厅
  await loadMemoryData()
  createMemoryGallery()

  // 开始动画循环
  animate()

  // 确保相机角度正确初始化（延迟执行，确保所有初始化完成）
  setTimeout(() => {
    initializeCameraAngles()
    console.log('延迟初始化相机角度完成')
  }, 500)

  // 桌面设备才需要延迟锁定（移动设备已在初始化时处理）
  if (!isMobileDevice.value) {
    setTimeout(() => {
      controls.lock()
    }, 1000)
  }
}

// 加载当代记忆数据（优化版本）
const loadMemoryData = async () => {
  try {
    loading.value = true
    loadingProgress.value = 0
    console.log('🚀 正在加载当代记忆数据...')

    // 获取真实当代记忆数据，使用区域ID参数
    // 如果没有区域信息，使用默认的测试区域（四川省成都市锦江区）
    const requestParams = {
      province_id: provinceId.value || 51,
      city_id: cityId.value || 4,
      district_id: districtId.value || 21,
      page: 1,
      page_size: 50,
    }

    console.log('🎯 当代记忆展厅 - 请求参数:', requestParams)
    loadingProgress.value = 10

    // 首先尝试使用标准API
    loadingProgress.value = 20
    let memoryData = await getMemoryData(requestParams)

    // 如果标准API返回空数据，尝试使用完整页面数据API
    if (!memoryData.items || memoryData.items.length === 0) {
      console.log('📋 标准API返回空数据，尝试使用完整页面数据API')
      loadingProgress.value = 40

      const pageData = await getMemoryPageDataByRegion({
        province_id: requestParams.province_id,
        city_id: requestParams.city_id,
        district_id: requestParams.district_id,
      })

      if (pageData && pageData.memory_data && pageData.memory_data.length > 0) {
        console.log(
          '✅ 完整页面数据API返回数据:',
          pageData.memory_data.length,
          '条记录'
        )
        // 转换为标准格式
        memoryData = {
          items: pageData.memory_data,
          total: pageData.memory_data.length,
          page: 1,
          page_size: 50,
          total_pages: 1,
        }
      }
    }

    loadingProgress.value = 60

    // 如果所有API都返回空数据，使用默认数据
    if (!memoryData.items || memoryData.items.length === 0) {
      console.log('⚠️ 所有API都返回空数据，使用默认当代记忆数据')
      loadingProgress.value = 80

      const defaultData = generateDefaultMemoryData()
      exhibits.value = convertMemoryArrayToExhibits(defaultData)
    } else {
      // 转换为展品格式
      console.log('✅ 成功获取当代记忆数据:', memoryData.items.length, '条记录')
      loadingProgress.value = 80

      exhibits.value = convertMemoryArrayToExhibits(memoryData.items)
    }

    // 按照年代排序（从新到旧）
    exhibits.value = sortExhibitsByYear(exhibits.value)

    loadingProgress.value = 100

    // 输出数据统计信息
    if (isDebugMode()) {
      const stats = getDataStats(exhibits.value)
      console.log('📊 当代记忆数据统计:', {
        总数: stats.total,
        有图片: stats.withImages,
        有详细图片: stats.withDetailImages,
        年份范围: `${stats.yearRange.min}-${stats.yearRange.max}`,
      })
    }

    console.log('🎉 当代记忆数据加载成功:', exhibits.value.length, '条记录')

    // 延迟一点时间让用户看到100%的进度
    setTimeout(() => {
      loading.value = false
    }, 300)

    return exhibits.value
  } catch (error) {
    console.error('❌ 加载当代记忆数据失败:', error)
    // 如果API调用失败，使用默认数据
    console.log('🔄 API调用失败，使用默认当代记忆数据')

    const defaultData = generateDefaultMemoryData()
    exhibits.value = convertMemoryArrayToExhibits(defaultData)
    exhibits.value = sortExhibitsByYear(exhibits.value)

    loadingProgress.value = 100

    if (isDebugMode()) {
      const stats = getDataStats(exhibits.value)
      console.log('📊 默认数据统计:', stats)
    }

    setTimeout(() => {
      loading.value = false
    }, 300)

    return exhibits.value
  }
}

// 将年份字符串转换为可比较的数字
const parseYearToNumber = (yearStr: string): number => {
  if (!yearStr) return 0

  // 处理"公元前"年份
  if (yearStr.includes('公元前')) {
    const match = yearStr.match(/公元前(\d+)/)
    if (match && match[1]) {
      return -parseInt(match[1], 10)
    }
  }

  // 处理包含范围的年份，如"1911-1949年"，取前面的年份
  if (yearStr.includes('-')) {
    const match = yearStr.match(/(\d+)-\d+/)
    if (match && match[1]) {
      return parseInt(match[1], 10)
    }
  }

  // 处理普通年份
  const match = yearStr.match(/(\d+)/)
  if (match && match[1]) {
    return parseInt(match[1], 10)
  }

  return 0
}

// 创建当代记忆展厅
const createMemoryGallery = () => {
  if (!exhibits.value || exhibits.value.length === 0) return

  // 计算走廊长度 - 根据展品数量动态调整
  const wallSpacing = 10 // 每两根柱子间距10个单位

  // 计算每面墙可以容纳的展品数量
  const exhibitsPerWall = 4 // 每面墙最多放5个展品

  // 计算走廊长度 - 确保足够长以容纳所有展品
  const totalExhibits = exhibits.value.length
  const bothSidesExhibits = Math.ceil(totalExhibits / 2) // 两侧墙各自至少需要展示的展品数量
  const wallCount = Math.max(exhibitsPerWall, bothSidesExhibits) // 取较大值确保空间足够

  // 走廊总长度
  const corridorLength = wallCount * wallSpacing
  // 起始位置
  const startX = -corridorLength / 1.58

  // 创建地面 - 科技感高反光地板
  const floorGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const floorMaterial = new THREE.MeshStandardMaterial({
    color: 0x101820, // 深色反光地面
    roughness: 0.1,
    metalness: 0.9,
    envMapIntensity: 1.0, // 增加反射强度
  })
  const floor = new THREE.Mesh(floorGeometry, floorMaterial)
  floor.rotation.x = -Math.PI / 2
  floor.position.set(0, 0, 0)
  floor.receiveShadow = true
  scene.add(floor)

  // 创建天花板 - 现代科技风格
  const ceilingGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const ceilingMaterial = new THREE.MeshStandardMaterial({
    color: 0x202530, // 深蓝灰色，现代金属感
    roughness: 0.3,
    metalness: 0.8,
  })
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial)
  ceiling.rotation.x = Math.PI / 2
  ceiling.position.set(0, 4, 0)
  ceiling.receiveShadow = true
  scene.add(ceiling)

  // 添加装饰性光带 - 科技感线条
  const lightStripMaterial = new THREE.MeshStandardMaterial({
    color: 0x00a0ff, // 明亮的蓝色
    roughness: 0.1,
    metalness: 0.9,
    emissive: 0x00a0ff,
    emissiveIntensity: 0.7, // 发光效果
  })

  // 添加LED光带
  for (let i = -corridorLength / 2; i < corridorLength / 2; i += 5) {
    const lightStrip = new THREE.Mesh(
      new THREE.BoxGeometry(4, 0.05, 0.2),
      lightStripMaterial
    )
    lightStrip.position.set(i, 3.95, 0)
    lightStrip.castShadow = false
    scene.add(lightStrip)
  }

  // 创建左右墙壁 - 高科技面板墙
  const sideWallGeometry = new THREE.PlaneGeometry(corridorLength + 20, 4)
  const wallMaterial = new THREE.MeshStandardMaterial({
    color: 0x2a3040, // 深蓝灰色，现代科技感
    roughness: 0.3,
    metalness: 0.6,
    envMapIntensity: 0.8, // 增加光泽感
  })

  // 左墙
  const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  leftWall.position.set(0, 2, 5)
  leftWall.rotation.y = Math.PI
  leftWall.receiveShadow = true
  scene.add(leftWall)

  // 右墙
  const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  rightWall.position.set(0, 2, -5)
  rightWall.receiveShadow = true
  scene.add(rightWall)

  // 创建前后端实体墙
  // 前端墙 - 入口
  const frontWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const frontWallMaterial = new THREE.MeshStandardMaterial({
    color: 0x1a2432, // 深蓝色
    roughness: 0.3,
    metalness: 0.7,
    emissive: 0x003366, // 微弱的蓝色发光
    emissiveIntensity: 0.2,
  })
  const frontWall = new THREE.Mesh(frontWallGeometry, frontWallMaterial)
  frontWall.position.set(-corridorLength / 2 - 5, 2, 0)
  frontWall.castShadow = true
  frontWall.receiveShadow = true
  scene.add(frontWall)

  // 后端墙 - 尽头
  const endWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const endWallMaterial = new THREE.MeshStandardMaterial({
    color: 0x1a2432, // 深蓝色
    roughness: 0.3,
    metalness: 0.7,
    emissive: 0x003366, // 微弱的蓝色发光
    emissiveIntensity: 0.2,
  })
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial)
  endWall.position.set(corridorLength / 2.8, 2, 0) // 后墙位置 2.8是根据走廊长度计算的
  endWall.castShadow = true
  endWall.receiveShadow = true
  scene.add(endWall)

  // 创建柱子
  // 每个柱子的位置
  const pillarPositions: number[] = []
  for (let i = 0; i <= wallCount; i++) {
    pillarPositions.push(startX + i * wallSpacing)
  }

  // 添加柱子
  pillarPositions.forEach((x) => {
    createPillar(x, 0, 4.8) // 左侧柱子
    createPillar(x, 0, -4.8) // 右侧柱子
  })

  // 按顺序展示展品 - 先左侧墙再右侧墙
  exhibits.value.forEach((exhibit, index) => {
    // 计算展品位置
    const wallSide = index < exhibitsPerWall ? 'left' : 'right' // 左侧墙展示完再到右侧墙
    const wallIndex = wallSide === 'left' ? index : index - exhibitsPerWall // 在当前墙上的索引

    // 确保不超过墙的数量
    if (wallIndex >= wallCount) return

    // 计算展品位置 - 放在两个柱子之间的中点
    const leftPillarX = pillarPositions[wallIndex]
    const rightPillarX = pillarPositions[wallIndex + 1]
    const exhibitX = (leftPillarX + rightPillarX) / 2

    // 计算z位置 (左侧墙或右侧墙)
    const zPosition = wallSide === 'left' ? 4.9 : -4.9

    // 展示展品
    createExhibitOnWall(exhibitX, 0, zPosition, exhibit, wallSide)
  })

  // 添加中间一排天花板灯光
  for (let i = 0; i < pillarPositions.length - 1; i++) {
    const leftX = pillarPositions[i]
    const rightX = pillarPositions[i + 1]
    const centerX = (leftX + rightX) / 2

    // 在中央位置创建灯光
    createCeilingLight(centerX, 3.8, 0)
  }

  // 设置物理碰撞边界
  addPhysicalBoundaries(corridorLength)

  // 设置初始相机位置在走廊的最早时间点（最前端）
  controls.getObject().position.set(startX + 2, 1.6, 0)
}

// 添加物理碰撞边界
const addPhysicalBoundaries = (corridorLength: number) => {
  // 前墙碰撞检测边界
  const frontBoundary = new THREE.Vector3(-corridorLength / 2 - 4.5, 0, 0)
  // 后墙碰撞检测边界
  const endBoundary = new THREE.Vector3(corridorLength / 2.9, 0, 0)

  // 将边界保存到全局变量中，以便在animate函数中使用
  boundaries = {
    front: frontBoundary.x,
    end: endBoundary.x,
  }
}

// 全局边界变量
let boundaries: { front: number; end: number } = { front: 0, end: 0 }

// 墙面参数常量
const WALL_SPACING = 10 // 每两根柱子间距10个单位
const WALL_HEIGHT = 4 // 墙壁高度

// 在墙上创建展品
const createExhibitOnWall = (
  x: number,
  y: number,
  z: number,
  exhibit: any,
  side: 'left' | 'right'
) => {
  // 创建纹理
  const textureLoader = new THREE.TextureLoader()
  textureLoader.load(
    exhibit.image,
    (texture) => {
      const aspectRatio = texture.image.width / texture.image.height
      const imageWidth = 2
      const imageHeight = imageWidth / aspectRatio

      // 确定朝向 - 平行于墙面
      let wallRotationY = side === 'left' ? Math.PI : 0 // 左侧墙朝向走廊内部，右侧墙朝向走廊内部

      // 创建主展板(超薄，几乎就是一个平面)
      const boardDepth = 0.02 // 非常薄的展板
      // 计算展板尺寸 - 占两柱子间距的80%，墙高的80%
      const boardWidth = WALL_SPACING * 0.8 // 两柱子间距是10个单位，取80%
      const boardHeight = WALL_HEIGHT * 0.8 // 墙高4个单位，取80%
      const boardGeometry = new THREE.BoxGeometry(
        boardWidth,
        boardHeight,
        boardDepth
      )

      // 创建画布元素（将在展板前方直接显示）
      const canvasGeometry = new THREE.PlaneGeometry(imageWidth, imageHeight)

      // 获取原始图片宽高比
      const originalAspectRatio = texture.image.width / texture.image.height
      const canvasMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.FrontSide,
      })

      // 创建展板材质（底板为现代金属材质）
      const boardMaterial = new THREE.MeshStandardMaterial({
        color: 0x2a3040, // 深蓝灰色金属感
        roughness: 0.3,
        metalness: 0.7,
      })

      // 使用不同材质的数组创建展板
      const materials = [
        boardMaterial, // right
        boardMaterial, // left
        boardMaterial, // top
        boardMaterial, // bottom
        boardMaterial, // back (面向墙壁)
        boardMaterial, // front (面向走廊)
      ]

      // 创建展板
      const board = new THREE.Mesh(boardGeometry, materials)

      // 将展板放在墙上(稍微突出一点)
      const wallOffset = 0.1 // 从墙面稍微突出的距离
      board.position.set(
        x,
        y + 2,
        z - (side === 'left' ? -wallOffset : wallOffset)
      )
      board.rotation.y = wallRotationY
      // 为整个展板添加展品数据，使其可交互
      board.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(board)
      exhibitObjects.push(board) // 添加展板到可交互对象

      // 调整图片尺寸，使其适合展板左侧40%区域
      const imageAreaWidth = boardWidth * 0.4 // 图片区域占展板宽度的40%
      const adjustedImageWidth = imageAreaWidth * 0.9 // 图片宽度为图片区域的90%
      // 图片高度设为展板高度的80%，不再基于宽高比自适应
      const adjustedImageHeight = boardHeight * 0.8
      const adjustedCanvasGeometry = new THREE.PlaneGeometry(
        adjustedImageWidth,
        adjustedImageHeight
      )

      // 根据图片原始比例和目标比例调整纹理
      texture.center.set(0.5, 0.5) // 纹理中心点
      const targetAspectRatio = adjustedImageWidth / adjustedImageHeight

      if (originalAspectRatio > targetAspectRatio) {
        // 图片更宽，调整横向重复值
        texture.repeat.set(targetAspectRatio / originalAspectRatio, 1)
      } else {
        // 图片更高，调整纵向重复值
        texture.repeat.set(1, originalAspectRatio / targetAspectRatio)
      }

      // 计算图片水平偏移量 - 将其放置在左侧40%区域
      const horizontalOffset = -boardWidth / 2 + imageAreaWidth / 2 // 向左偏移到左侧40%区域的中心

      // 在展板前方放置图片
      const canvas = new THREE.Mesh(adjustedCanvasGeometry, canvasMaterial)
      const faceOffset = boardDepth / 2 + 0.001 // 稍微在展板前方
      canvas.position.set(
        x + horizontalOffset,
        y + 2,
        z - (side === 'left' ? -faceOffset : faceOffset)
      )
      canvas.rotation.y = wallRotationY
      canvas.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(canvas)
      exhibitObjects.push(canvas)

      // 创建详细信息面板 - 放置在右侧60%区域
      // 创建详细信息画布
      const infoCanvas = document.createElement('canvas')
      const infoContext = infoCanvas.getContext('2d')
      let infoTexture

      if (infoContext) {
        // 创建高分辨率画布以获得更清晰的文本
        infoCanvas.width = 1024
        infoCanvas.height = 1024

        // 设置科技感背景 - 添加淡蓝色渐变底色
        infoContext.clearRect(0, 0, infoCanvas.width, infoCanvas.height)
        const gradient = infoContext.createLinearGradient(
          0,
          0,
          0,
          infoCanvas.height
        )
        gradient.addColorStop(0, 'rgba(10, 30, 50, 0.7)')
        gradient.addColorStop(1, 'rgba(10, 30, 50, 0.3)')
        infoContext.fillStyle = gradient
        infoContext.fillRect(0, 0, infoCanvas.width, infoCanvas.height)

        // 添加科技感图案 - 数字线条
        infoContext.strokeStyle = 'rgba(0, 160, 255, 0.2)'
        infoContext.lineWidth = 1
        // 绘制水平线
        for (let i = 0; i < infoCanvas.height; i += 40) {
          infoContext.beginPath()
          infoContext.moveTo(0, i)
          infoContext.lineTo(infoCanvas.width, i)
          infoContext.stroke()
        }
        // 绘制垂直线
        for (let i = 0; i < infoCanvas.width; i += 40) {
          infoContext.beginPath()
          infoContext.moveTo(i, 0)
          infoContext.lineTo(i, infoCanvas.height)
          infoContext.stroke()
        }

        // 绘制标题 - 更现代的字体
        infoContext.font = 'bold 70px Arial, sans-serif'
        infoContext.fillStyle = '#80D0FF' // 蓝白色
        infoContext.textAlign = 'left'
        infoContext.fillText(exhibit.title, 40, 100)

        // 绘制分隔线 - 科技风格
        infoContext.strokeStyle = '#00A0FF' // 蓝色分隔线
        infoContext.lineWidth = 3
        infoContext.beginPath()
        infoContext.moveTo(40, 130)
        infoContext.lineTo(infoCanvas.width - 40, 130)
        infoContext.stroke()

        // 绘制年代与时期 - 更现代的字体
        infoContext.font = 'bold 52px Arial, sans-serif'
        infoContext.fillStyle = '#E0F0FF' // 亮蓝白色
        infoContext.fillText(`年代: ${exhibit.year}`, 40, 200)
        infoContext.fillText(`时期: ${exhibit.period}`, 40, 270)

        // 绘制描述文本 - 自动换行，科技感字体
        infoContext.font = '46px Arial, sans-serif'
        infoContext.fillStyle = '#D0E8FF' // 淡蓝色

        // 文本换行算法
        const maxWidth = infoCanvas.width - 100 // 留出更多边距
        const lineHeight = 60 // 增大行间距
        const description = exhibit.description || ''
        let y = 350 // 调整起始位置，给上面的标题和年代留出更多空间

        // 使用中文字符更适合的文本换行方式
        let words = description.split('')
        let line = ''

        for (let i = 0; i < words.length; i++) {
          let testLine = line + words[i]
          let metrics = infoContext.measureText(testLine)

          if (metrics.width > maxWidth && i > 0) {
            infoContext.fillText(line, 40, y)
            line = words[i]
            y += lineHeight

            // 防止文本超出画布底部
            if (y > infoCanvas.height - 40) {
              line += '...'
              infoContext.fillText(line, 40, y)
              break
            }
          } else {
            line = testLine
          }
        }

        // 绘制最后一行
        if (y <= infoCanvas.height - 40) {
          infoContext.fillText(line, 40, y)
        }

        // 创建纹理
        infoTexture = new THREE.CanvasTexture(infoCanvas)
        infoTexture.needsUpdate = true
      }

      // 创建右侧信息面板 - 科技风格数据面板
      if (infoTexture) {
        // 计算信息面板的尺寸和位置
        const infoAreaWidth = boardWidth * 0.6 // 右侧信息区域占展板宽度的60%
        const infoWidth = infoAreaWidth * 0.9 // 信息面板宽度为信息区域的90%
        const infoHeight = boardHeight * 0.85 // 信息面板高度为展板高度的85%

        const infoGeometry = new THREE.PlaneGeometry(infoWidth, infoHeight)
        const infoMaterial = new THREE.MeshBasicMaterial({
          map: infoTexture,
          side: THREE.FrontSide,
          transparent: true,
          opacity: 1.0, // 保持文字清晰
          color: 0xd0e5ff, // 添加淡蓝色色调
        })

        // 计算信息面板的水平偏移量 - 将其放置在右侧60%区域
        const infoHorizontalOffset = boardWidth / 2 - infoAreaWidth / 2

        const infoPanel = new THREE.Mesh(infoGeometry, infoMaterial)
        infoPanel.position.set(
          x + infoHorizontalOffset,
          y + 2,
          z - (side === 'left' ? -faceOffset : faceOffset)
        )
        infoPanel.rotation.y = wallRotationY
        infoPanel.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit } // 确保信息面板也添加展品数据
        scene.add(infoPanel)
        exhibitObjects.push(infoPanel) // 添加到可交互对象
      }

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
      scene.add(ambientLight)

      // 更新加载进度
      loadingProgress.value += 100 / exhibits.value.length
    },
    undefined,
    (error) => {
      console.error('加载纹理失败:', error)
    }
  )
}

// 创建柱子 - 现代科技感立柱
const createPillar = (x: number, y: number, z: number) => {
  // 柱身 - 方形金属柱
  const pillarGeometry = new THREE.BoxGeometry(0.6, 4, 0.6)
  const pillarMaterial = new THREE.MeshStandardMaterial({
    color: 0x303540, // 深灰色金属
    roughness: 0.2,
    metalness: 0.9,
  })
  const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial)
  pillar.position.set(x, y + 2, z)
  pillar.castShadow = true
  pillar.receiveShadow = true
  scene.add(pillar)

  // 柱子LED灯带 - 垂直发光条
  const ledStripGeometry = new THREE.BoxGeometry(0.05, 3.8, 0.08)
  const ledMaterial = new THREE.MeshStandardMaterial({
    color: 0x00a0ff, // 蓝色LED
    roughness: 0.1,
    metalness: 0.5,
    emissive: 0x00a0ff,
    emissiveIntensity: 1.0, // 发光效果
  })

  // 添加到柱子前后面
  const ledFront = new THREE.Mesh(ledStripGeometry, ledMaterial)
  ledFront.position.set(x, y + 2, z + 0.31)
  scene.add(ledFront)

  const ledBack = new THREE.Mesh(ledStripGeometry, ledMaterial)
  ledBack.position.set(x, y + 2, z - 0.31)
  scene.add(ledBack)

  // 柱顶装饰 - 金属盖板带LED照明
  const capGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8)
  const capMaterial = new THREE.MeshStandardMaterial({
    color: 0x505050, // 金属灰
    roughness: 0.2,
    metalness: 0.9,
  })
  const cap = new THREE.Mesh(capGeometry, capMaterial)
  cap.position.set(x, y + 4.1, z)
  cap.castShadow = true
  scene.add(cap)

  // 柱顶光源
  const capLight = new THREE.PointLight(0x00a0ff, 0.8, 3)
  capLight.position.set(x, y + 4.2, z)
  scene.add(capLight)

  // 柱底装饰
  const baseGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8)
  const baseMaterial = new THREE.MeshStandardMaterial({
    color: 0x303540, // 深灰色金属
    roughness: 0.3,
    metalness: 0.8,
  })
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.set(x, y + 0.05, z)
  base.receiveShadow = true
  scene.add(base)
}

// 创建天花板灯光 - 现代科技风格LED照明
const createCeilingLight = (x: number, y: number, z: number) => {
  // 灯具主体 - 现代悬吊式LED灯
  const fixtureGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1, 12)
  const fixtureMaterial = new THREE.MeshStandardMaterial({
    color: 0x303540, // 深灰色金属
    roughness: 0.2,
    metalness: 0.9,
  })
  const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial)
  fixture.position.set(x, y, z)
  fixture.castShadow = true
  scene.add(fixture)

  // LED灯面板
  const panelGeometry = new THREE.CircleGeometry(0.25, 16)
  const panelMaterial = new THREE.MeshStandardMaterial({
    color: 0xffffff, // 白色发光面板
    emissive: 0x00a0ff,
    emissiveIntensity: 0.9,
    roughness: 0.2,
    metalness: 0.8,
  })
  const panel = new THREE.Mesh(panelGeometry, panelMaterial)
  panel.rotation.x = Math.PI / 2 // 使圆形面板朝下
  panel.position.set(x, y - 0.06, z)
  scene.add(panel)

  // 悬挂装置 - 现代金属线
  const wireGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.4, 8)
  const wireMaterial = new THREE.MeshStandardMaterial({
    color: 0x909090, // 银灰色
    roughness: 0.3,
    metalness: 0.9,
  })
  const wire = new THREE.Mesh(wireGeometry, wireMaterial)
  wire.position.set(x, y + 0.25, z)
  scene.add(wire)

  // 顶部连接件
  const connectorGeometry = new THREE.BoxGeometry(0.1, 0.05, 0.1)
  const connectorMaterial = new THREE.MeshStandardMaterial({
    color: 0x505050, // 金属灰
    roughness: 0.3,
    metalness: 0.9,
  })
  const connector = new THREE.Mesh(connectorGeometry, connectorMaterial)
  connector.position.set(x, y + 0.45, z)
  scene.add(connector)

  // 点光源 - 冷色调的LED光
  const light = new THREE.PointLight(0x80d0ff, 2, 10) // 冷白带蓝的LED光
  light.position.set(x, y - 0.1, z)
  scene.add(light)

  // 添加一个小光晕效果 - 灯光照射区域的可见效果
  const glowGeometry = new THREE.CircleGeometry(0.4, 16)
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: 0x80d0ff,
    transparent: true,
    opacity: 0.15,
    side: THREE.DoubleSide,
  })
  const glow = new THREE.Mesh(glowGeometry, glowMaterial)
  glow.rotation.x = Math.PI / 2 // 平放在天花板下方
  glow.position.set(x, y - 0.08, z)
  scene.add(glow)
}

// 创建文本标签
const createTextLabel = (
  text: string,
  x: number,
  y: number,
  z: number,
  rotationY: number,
  scale: number = 1,
  bgColor: string = '#ffffff'
) => {
  // 创建画布并绘制文本
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return

  canvas.width = 256
  canvas.height = 64

  context.fillStyle = bgColor
  context.fillRect(0, 0, canvas.width, canvas.height)

  context.font = '24px KaiTi, STKaiti, serif'
  context.fillStyle = '#000000'
  context.textAlign = 'center'
  context.textBaseline = 'middle'
  context.fillText(text, canvas.width / 2, canvas.height / 2)

  // 创建纹理和材质
  const texture = new THREE.CanvasTexture(canvas)
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.DoubleSide,
    transparent: true,
  })

  // 创建平面几何体
  const geometry = new THREE.PlaneGeometry(1.5 * scale, 0.4 * scale)
  const mesh = new THREE.Mesh(geometry, material)
  mesh.position.set(x, y, z)
  mesh.rotation.y = rotationY

  scene.add(mesh)
  exhibitObjects.push(mesh)
}

// 键盘按下事件处理
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = true
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = true
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = true
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = true
      break
    case 'Space':
      if (canJump.value) {
        velocity.y += 350
      }
      canJump.value = false
      break
    case 'Escape':
      controls.unlock()
      break
  }
}

// 键盘释放事件处理
const onKeyUp = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = false
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = false
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = false
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = false
      break
  }
}

// 鼠标点击事件处理
const onMouseClick = () => {
  if (!controls.isLocked && !isMobileDevice.value) {
    controls.lock()
    return
  }

  checkExhibitInteraction()
}

// 触摸结束事件处理（移动设备）
const onTouchEnd = (event: TouchEvent) => {
  // 防止长按弹出菜单等行为
  event.preventDefault()

  if (event.touches.length > 0 || event.changedTouches.length === 0) return

  // 获取触摸位置
  const touch = event.changedTouches[0]
  checkExhibitInteraction({
    clientX: touch.clientX,
    clientY: touch.clientY,
  })
}

// 检测与展品的交互
const checkExhibitInteraction = (touchPoint?: {
  clientX: number
  clientY: number
}) => {
  // 使用射线检测点击的对象
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()

  // 如果是触摸事件，计算相对坐标
  if (touchPoint) {
    mouse.x = (touchPoint.clientX / window.innerWidth) * 2 - 1
    mouse.y = -(touchPoint.clientY / window.innerHeight) * 2 + 1
  }

  // 设置射线起点和方向（从相机发出）
  raycaster.setFromCamera(mouse, camera)

  // 检测与展品的交叉
  const intersects = raycaster.intersectObjects(exhibitObjects)

  if (intersects.length > 0) {
    const object = intersects[0].object
    if (object.userData && object.userData.isExhibit) {
      // 显示展品详情
      selectedExhibit.value = object.userData.exhibit
      currentImageIndex.value = 0 // 重置图片索引为第一张

      // 桌面设备才需要解锁
      if (!isMobileDevice.value) {
        controls.unlock()
      }
    }
  }
}

// 窗口大小变化处理
const onWindowResize = () => {
  if (!camera || !renderer) return

  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)

  if (controls.isLocked) {
    // 计算时间增量
    const time = performance.now()
    const delta = (time - prevTime) / 1000

    // 应用阻尼
    velocity.x -= velocity.x * 10.0 * delta
    velocity.z -= velocity.z * 10.0 * delta
    velocity.y -= 9.8 * 100.0 * delta // 应用重力

    // 根据按键状态计算方向
    direction.z = Number(moveForward.value) - Number(moveBackward.value)
    direction.x = Number(moveRight.value) - Number(moveLeft.value)
    direction.normalize() // 确保对角线移动不会更快

    // 应用移动速度（减缓移动速率）
    if (moveForward.value || moveBackward.value)
      velocity.z -= direction.z * 250.0 * delta
    if (moveLeft.value || moveRight.value)
      velocity.x -= direction.x * 250.0 * delta

    // 移动控制器 - 仅在XZ平面上移动，确保Y轴位置固定
    const oldY = controls.getObject().position.y // 保存当前Y轴位置
    controls.moveRight(-velocity.x * delta)
    controls.moveForward(-velocity.z * delta)
    controls.getObject().position.y = oldY // 恢复Y轴位置，确保不会"飞起来"

    // 限制移动范围（保持在走廊内）
    const position = controls.getObject().position

    // 前后端墙碰撞检测
    if (position.x < boundaries.front) position.x = boundaries.front
    if (position.x > boundaries.end) position.x = boundaries.end

    // Z轴限制（走廊宽度）
    if (position.z < -4.5) position.z = -4.5
    if (position.z > 4.5) position.z = 4.5

    // Y轴限制（高度）
    if (position.y < 1.6) {
      velocity.y = 0
      position.y = 1.6
      canJump.value = true
    }

    prevTime = time
  }

  renderer.render(scene, camera)
}

// 返回首页
const goBack = () => {
  router.push('/')
}

// 关闭展品面板
const closeExhibitPanel = () => {
  selectedExhibit.value = null
  currentImageIndex.value = 0 // 重置图片索引

  // 保存当前控制器位置和旋转，避免视角变化
  const currentPosition = controls.getObject().position.clone()
  const currentRotation = new THREE.Euler().copy(controls.getObject().rotation)

  if (controls && !isMobileDevice.value) {
    controls.lock() // 只在非移动设备上重新锁定控制

    // 恢复之前的位置和旋转
    setTimeout(() => {
      controls.getObject().position.copy(currentPosition)
      controls.getObject().rotation.copy(currentRotation)
    }, 10)
  }
}

// 检测是否为移动设备
const isMobileDevice = ref(false)

// 检测设备类型
const checkMobileDevice = () => {
  isMobileDevice.value =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
}

// 处理移动摇杆触摸开始
const handleMoveJoystickStart = (event: TouchEvent | MouseEvent) => {
  console.log('移动摇杆触摸开始!', event.type)
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸
  moveActive.value = true

  let clientX, clientY

  if ('touches' in event) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else {
    clientX = (event as MouseEvent).clientX
    clientY = (event as MouseEvent).clientY
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveStartPosition.x = clientX - joystickRect.left
  moveStartPosition.y = clientY - joystickRect.top
  moveCurrentPosition.x = moveStartPosition.x
  moveCurrentPosition.y = moveStartPosition.y

  // 更新摇杆位置
  updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)

  // 添加移动和结束事件监听
  document.addEventListener('mousemove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleMoveJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleMoveJoystickEnd, {
    passive: false,
  })
}

// 处理移动摇杆移动
const handleMoveJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!moveActive.value) return
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸

  let clientX, clientY

  if ('touches' in event && event.touches.length > 0) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else if ('changedTouches' in event && event.changedTouches.length > 0) {
    clientX = event.changedTouches[0].clientX
    clientY = event.changedTouches[0].clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveCurrentPosition.x = clientX - joystickRect.left
  moveCurrentPosition.y = clientY - joystickRect.top

  // 限制在基座范围内
  const maxDistance = 50
  const dx = moveCurrentPosition.x - moveStartPosition.x
  const dy = moveCurrentPosition.y - moveStartPosition.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance > maxDistance) {
    moveCurrentPosition.x = moveStartPosition.x + (dx * maxDistance) / distance
    moveCurrentPosition.y = moveStartPosition.y + (dy * maxDistance) / distance
  }

  // 更新摇杆位置
  updateMoveJoystickPosition(moveCurrentPosition.x, moveCurrentPosition.y)

  // 计算移动方向
  moveDirection.x = (moveCurrentPosition.x - moveStartPosition.x) / maxDistance // 左右移动，-1到1
  moveDirection.z = (moveStartPosition.y - moveCurrentPosition.y) / maxDistance // 前后移动，-1到1

  // 根据摇杆位置设置移动状态
  moveForward.value = moveDirection.z > 0.2
  moveBackward.value = moveDirection.z < -0.2
  moveLeft.value = moveDirection.x < -0.2
  moveRight.value = moveDirection.x > 0.2
}

// 处理移动摇杆结束
const handleMoveJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  moveActive.value = false

  // 停止所有移动
  moveForward.value = false
  moveBackward.value = false
  moveLeft.value = false
  moveRight.value = false

  // 复位方向向量
  moveDirection.x = 0
  moveDirection.z = 0

  // 复位摇杆位置（动画效果）
  const joystickBase = moveControl.value?.querySelector('.joystick-base')
  if (joystickBase) {
    const baseRect = joystickBase.getBoundingClientRect()
    const centerX = baseRect.width / 2
    const centerY = baseRect.height / 2
    updateMoveJoystickPosition(centerX, centerY)
  } else {
    updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)
  }

  // 移除事件监听
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)
}

// 更新移动摇杆位置
const updateMoveJoystickPosition = (x: number, y: number) => {
  moveJoystickStyle.value = {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  }
  console.log('更新移动摇杆位置:', x, y)
}

// 右侧视角摇杆事件处理
const handleLookJoystickStart = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  lookActive.value = true

  // 记录触摸ID
  if ('touches' in event) {
    const touch = event.touches[0]
    lookTouchId = touch.identifier
  } else {
    lookTouchId = null // 鼠标事件不需要ID
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 重置摇杆位置
  lookStartPosition.x = joystickRect.width / 2
  lookStartPosition.y = joystickRect.height / 2
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0

  // 更新摇杆位置
  updateLookJoystickPosition(0, 0)

  // 添加事件监听
  document.addEventListener('mousemove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleLookJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleLookJoystickEnd, {
    passive: false,
  })
}

const handleLookJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!lookActive.value) return
  event.preventDefault()

  let clientX: number, clientY: number

  if ('touches' in event && event.touches.length > 0) {
    // 查找匹配的触摸点
    let foundTouch = null
    for (let i = 0; i < event.touches.length; i++) {
      if (lookTouchId === null || event.touches[i].identifier === lookTouchId) {
        foundTouch = event.touches[i]
        break
      }
    }
    if (!foundTouch) return
    clientX = foundTouch.clientX
    clientY = foundTouch.clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 计算相对于摇杆中心的偏移
  const centerX = joystickRect.left + joystickRect.width / 2
  const centerY = joystickRect.top + joystickRect.height / 2
  const offsetX = clientX - centerX
  const offsetY = clientY - centerY

  // 限制摇杆移动范围
  const maxDistance = 40 // 最大移动距离
  const distance = Math.sqrt(offsetX * offsetX + offsetY * offsetY)

  if (distance <= maxDistance) {
    lookCurrentPosition.x = offsetX
    lookCurrentPosition.y = offsetY
  } else {
    // 限制在圆形范围内
    const angle = Math.atan2(offsetY, offsetX)
    lookCurrentPosition.x = Math.cos(angle) * maxDistance
    lookCurrentPosition.y = Math.sin(angle) * maxDistance
  }

  // 更新摇杆位置
  updateLookJoystickPosition(lookCurrentPosition.x, lookCurrentPosition.y)

  // 应用视角旋转
  const pixelMultiplier = 0.1 // 降低灵敏度
  const deltaX = lookCurrentPosition.x * pixelMultiplier
  const deltaY = lookCurrentPosition.y * pixelMultiplier

  // 使用标准FPS相机控制
  updateCameraRotation(deltaX, deltaY, touchSensitivity)
}

const handleLookJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  lookActive.value = false

  // 重置触摸ID
  lookTouchId = null

  // 复位摇杆位置
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0
  updateLookJoystickPosition(0, 0)

  // 移除事件监听
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)
}

const updateLookJoystickPosition = (x: number, y: number) => {
  if (lookJoystickHandle.value) {
    lookJoystickStyle.value = {
      left: '50%',
      top: '50%',
      transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`,
    }
  }
}

// 组件挂载时初始化场景
onMounted(() => {
  // 初始化区域信息
  initRegionInfo()

  // 初始化3D场景
  initScene()

  // 旧的摇杆事件绑定已移除，现在使用新的右侧视角摇杆系统
  // 新系统通过模板中的 @touchstart 和 @mousedown 直接绑定
})

// 移动设备屏幕滑动控制视角的相关变量（已禁用，改为使用右侧摇杆）
// let touchStartX = 0
// let touchStartY = 0
// let touchPreviousX = 0
// let touchPreviousY = 0
// let touchMoving = false
// let activeLookTouchId: number | null = null

// 标准FPS相机控制变量
let cameraYaw = 0 // 水平旋转角度（绕Y轴）
let cameraPitch = 0 // 垂直旋转角度（绕X轴）
const maxPitch = Math.PI / 2 - 0.1 // 最大俯仰角（89度），防止万向锁
const minPitch = -Math.PI / 2 + 0.1 // 最小俯仰角（-89度）
const mouseSensitivity = 0.002 // 鼠标灵敏度
const touchSensitivity = 0.006 // 触摸灵敏度（稍高一些）

// 标准FPS相机更新函数
const updateCameraRotation = (
  deltaX: number,
  deltaY: number,
  sensitivity: number
) => {
  if (!controls) return

  // 在移动设备上，我们不检查 isLocked 状态，因为我们总是希望能够控制视角
  // 在桌面设备上，只有锁定时才允许控制
  if (!isMobileDevice.value && !controls.isLocked) return

  // 更新欧拉角
  cameraYaw -= deltaX * sensitivity
  cameraPitch -= deltaY * sensitivity

  // 限制俯仰角，防止翻转和万向锁
  cameraPitch = Math.max(minPitch, Math.min(maxPitch, cameraPitch))

  // 应用旋转到相机
  const camera = controls.getObject()

  // 重置相机旋转
  camera.rotation.set(0, 0, 0)

  // 按正确顺序应用旋转：先Y轴（偏航），再X轴（俯仰）
  camera.rotateY(cameraYaw)
  camera.rotateX(cameraPitch)

  console.log('相机旋转更新:', { cameraYaw, cameraPitch, deltaX, deltaY })
}

// 初始化相机角度（从当前相机状态获取）
const initializeCameraAngles = () => {
  if (!controls) {
    console.log('初始化相机角度失败: controls 不存在')
    return
  }

  const camera = controls.getObject()
  console.log('相机当前状态:', {
    position: camera.position,
    rotation: camera.rotation,
    quaternion: camera.quaternion,
  })

  const euler = new THREE.Euler().setFromQuaternion(camera.quaternion, 'YXZ')
  console.log('从四元数转换的欧拉角:', { x: euler.x, y: euler.y, z: euler.z })

  // 如果相机没有旋转，直接设置为0
  if (Math.abs(euler.x) < 0.001 && Math.abs(euler.y) < 0.001) {
    cameraYaw = 0
    cameraPitch = 0
    console.log('相机无旋转，设置角度为0')
  } else {
    cameraYaw = euler.y
    cameraPitch = euler.x

    // 确保俯仰角在有效范围内
    cameraPitch = Math.max(minPitch, Math.min(maxPitch, cameraPitch))
    console.log('从相机状态获取角度')
  }

  console.log('最终初始化相机角度:', { cameraYaw, cameraPitch })
}

// 移动端手指触摸控制视角已禁用，改为使用右侧摇杆控制
// 以下函数已不再使用，保留注释作为参考
/*
const handleCanvasTouchStart = (event: TouchEvent) => { ... }
const handleCanvasTouchMove = (event: TouchEvent) => { ... }
const handleCanvasTouchEnd = (event: TouchEvent) => { ... }
*/

// 旧的触摸控制函数已移除，现在使用右侧摇杆控制视角

// 旧的触摸移动和结束函数已移除

// 组件卸载前清理资源
onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown)
  document.removeEventListener('keyup', onKeyUp)
  // 旧的摇杆事件监听器已移除
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)

  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener('click', onMouseClick)
    if (isMobileDevice.value) {
      renderer.domElement.removeEventListener('touchend', onTouchEnd)
      // 移动端已禁用手指触摸控制视角，不需要移除这些事件监听器
      // renderer.domElement.removeEventListener('touchstart', handleCanvasTouchStart)
      // renderer.domElement.removeEventListener('touchmove', handleCanvasTouchMove)
      // renderer.domElement.removeEventListener('touchend', handleCanvasTouchEnd)
    }
  }

  window.removeEventListener('resize', onWindowResize)

  cancelAnimationFrame(animationFrameId)

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }
})
</script>

<style scoped lang="scss">
.gallery-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.scene-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;

  & > * {
    pointer-events: auto;
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .icon {
    margin-right: 5px;
    font-size: 1.2rem;
  }
}

.gallery-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

  h1 {
    font-size: 2rem;
    margin: 0 0 5px 0;
    font-weight: bold;
  }

  p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }
}

.loading-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;

  .loading-text {
    margin-bottom: 10px;
    font-size: 1.2rem;
  }

  .progress-bar {
    width: 300px;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;

    .progress {
      height: 100%;
      background-color: #0066cc;
      transition: width 0.3s ease;
    }
  }
}

.exhibit-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 900px;
  max-height: 85vh;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;

  &.active {
    opacity: 1;
    visibility: visible;
  }

  .panel-header {
    padding: 15px 20px;
    background-color: #0066cc;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 4px solid rgba(0, 0, 0, 0.2);
    flex-shrink: 0;

    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      font-family: 'STKaiti', 'KaiTi', serif;
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 1.8rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
      }
    }
  }

  .panel-content {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .image-carousel {
      flex-shrink: 0;
      margin-bottom: 20px;
      height: 380px; /* 固定高度确保可见性 */
    }

    .exhibit-info-container {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;

      .exhibit-info {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .info-card {
          background: linear-gradient(
            135deg,
            rgba(240, 248, 255, 0.95),
            rgba(230, 240, 250, 0.9)
          );
          border-radius: 12px;
          border: 1px solid rgba(0, 102, 204, 0.15);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
          }

          .card-header {
            background: linear-gradient(135deg, #0066cc, #0080ff);
            color: white;
            padding: 12px 16px;

            h3 {
              margin: 0;
              font-size: 1.1rem;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;
              font-family: 'Microsoft YaHei', sans-serif;

              i {
                font-size: 1rem;
                opacity: 0.9;
              }
            }
          }

          .card-content {
            padding: 16px;

            .info-row {
              display: flex;
              align-items: flex-start;
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                font-weight: 600;
                color: #0066cc;
                min-width: 80px;
                font-size: 0.95rem;
              }

              .value {
                flex: 1;
                color: #333;
                font-size: 0.95rem;
                line-height: 1.5;

                &.year-badge {
                  background: linear-gradient(135deg, #ff6b35, #ff8c42);
                  color: white;
                  padding: 4px 12px;
                  border-radius: 20px;
                  font-size: 0.85rem;
                  font-weight: 500;
                  display: inline-block;
                }

                &.memory-type-badge {
                  background: linear-gradient(135deg, #4caf50, #66bb6a);
                  color: white;
                  padding: 3px 10px;
                  border-radius: 15px;
                  font-size: 0.8rem;
                  font-weight: 500;
                  display: inline-block;
                }
              }
            }

            .memory-description {
              font-size: 1rem;
              line-height: 1.7;
              color: #333;
              font-family: 'Microsoft YaHei', sans-serif;
              text-align: justify;
              margin: 0;
            }

            .content-text {
              font-size: 0.95rem;
              line-height: 1.7;
              color: #444;
              text-align: justify;

              h4 {
                color: #0066cc;
                font-size: 1rem;
                margin: 16px 0 8px 0;
                font-weight: 600;
                border-left: 4px solid #0066cc;
                padding-left: 12px;
              }

              p {
                margin: 8px 0;
              }
            }

            .significance-text {
              font-size: 0.95rem;
              line-height: 1.6;
              color: #444;
              text-align: justify;
              margin: 0;
              font-style: italic;
            }
          }
        }

        .meta-info {
          margin-top: 20px;
          padding: 12px 16px;
          background: rgba(230, 240, 250, 0.5);
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.1);

          .meta-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.85rem;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              font-weight: 500;
              color: #666;
              min-width: 70px;
            }

            .meta-value {
              color: #888;
            }
          }
        }
      }
    }
  }

  .panel-actions {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    .detail-btn {
      padding: 10px 25px;
      background-color: #0066cc;
      color: white;
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.3s ease;
      border: none;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

      &:hover {
        background-color: darken(#0066cc, 10%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 768px) {
    width: 95%;
    max-height: 90vh;

    .panel-content {
      padding: 15px;

      .image-carousel {
        height: 300px;
      }
    }
  }

  @media screen and (max-width: 480px) {
    .panel-content {
      .image-carousel {
        height: 220px;
      }

      .exhibit-info-container .exhibit-info {
        .description {
          font-size: 1rem;
        }
      }
    }

    .panel-header h2 {
      font-size: 1.3rem;
    }
  }
}

.controls-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.move-joystick {
  position: absolute;
  bottom: 80px;
  left: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      pointer-events: none;
      transition: transform 0.05s ease-out;
    }
  }
}

.look-joystick {
  position: absolute;
  bottom: 80px;
  right: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 100, 100, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 100, 100, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 120, 120, 0.9);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      pointer-events: none;
      transition: transform 0.05s ease-out;
    }
  }
}

.image-carousel {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  .exhibit-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: rgba(0, 0, 0, 0.05);
    transition: opacity 0.3s ease;
  }

  .thumbnail-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 0;

    .thumbnails {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 100%;
      padding: 0 15px;
      overflow-x: auto;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        height: 5px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 3px;
      }

      .thumbnail {
        flex: 0 0 auto;
        width: 60px;
        height: 50px;
        border: 2px solid transparent;
        border-radius: 3px;
        overflow: hidden;
        transition: all 0.2s ease;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        &:hover {
          transform: translateY(-2px);
        }

        &.active {
          border-color: #0066cc;
          transform: translateY(-3px);
        }
      }
    }
  }

  .carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 10;
    padding: 0 15px;

    .control-btn {
      background-color: rgba(0, 0, 0, 0.6);
      border: none;
      color: white;
      font-size: 1.8rem;
      font-weight: bold;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 102, 204, 0.8);
        transform: scale(1.1);
      }
    }
  }
}

.detail-content {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 5px;

  h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #0066cc;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 5px;
  }

  .content-text {
    font-size: 1rem;
    line-height: 1.8;
    color: #333;
    text-align: justify;
  }
}

.fullscreen-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;

  .fullscreen-image-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;

    img {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
      border: 2px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    }

    .close-fullscreen-btn {
      position: absolute;
      top: -20px;
      right: -20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid white;
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #0066cc;
        transform: rotate(90deg);
      }
    }
  }
}

/* 图标样式 */
.icon-time::before {
  content: '⏰';
}
.icon-camera::before {
  content: '📷';
}
.icon-detail::before {
  content: '📋';
}
.icon-heart::before {
  content: '💝';
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .exhibit-panel {
    .panel-content {
      .exhibit-info-container .exhibit-info {
        gap: 15px;

        .info-card {
          .card-header h3 {
            font-size: 1rem;
          }

          .card-content {
            padding: 12px;

            .info-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .label {
                min-width: auto;
                font-size: 0.9rem;
              }

              .value {
                font-size: 0.9rem;
              }
            }

            .memory-description {
              font-size: 0.95rem;
            }

            .content-text {
              font-size: 0.9rem;

              h4 {
                font-size: 0.95rem;
              }
            }
          }
        }

        .meta-info {
          padding: 10px 12px;

          .meta-row {
            font-size: 0.8rem;

            .meta-label {
              min-width: 60px;
            }
          }
        }
      }
    }
  }
}

/* 自定义滚动条样式 */
.exhibit-info-container::-webkit-scrollbar {
  width: 6px;
}

.exhibit-info-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.exhibit-info-container::-webkit-scrollbar-thumb {
  background: rgba(0, 102, 204, 0.5);
  border-radius: 3px;
}

.exhibit-info-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 102, 204, 0.7);
}
</style>

