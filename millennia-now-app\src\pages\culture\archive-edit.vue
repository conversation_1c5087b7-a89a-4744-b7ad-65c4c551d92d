<template>
  <view class="archive-edit-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 类型指示器 -->
      <view class="type-header">
        <text class="type-icon">{{ getTypeIcon(archiveType) }}</text>
        <text class="type-title">{{ isEdit ? '编辑' : '新建' }}{{ getTypeName(archiveType) }}</text>
      </view>

      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <view class="form-item">
          <text class="form-label">{{ getItemTitleLabel(archiveType) }}</text>
          <input v-model="formData.title"
                 class="form-input"
                 :placeholder="`请输入${getItemTitleLabel(archiveType)}`"
                 maxlength="200" />
        </view>

        <!-- 古籍特有字段 -->
        <template v-if="archiveType === 'books'">
          <view class="form-item">
            <text class="form-label">作者</text>
            <input v-model="formData.author"
                   class="form-input"
                   placeholder="请输入作者姓名"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">朝代</text>
            <picker :value="dynastyIndex"
                    :range="dynastyOptions"
                    @change="onDynastyChange">
              <view class="form-input">
                {{ formData.dynasty || '请选择朝代' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">版本</text>
            <input v-model="formData.edition"
                   class="form-input"
                   placeholder="请输入版本信息"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">技术特性</text>
            <view class="checkbox-group">
              <label class="checkbox-item">
                <checkbox :checked="formData.has_ocr"
                          @change="e => formData.has_ocr = e.detail.value" />
                <text>OCR识别</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_annotation"
                          @change="e => formData.has_annotation = e.detail.value" />
                <text>AI标注</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_scan"
                          @change="e => formData.has_scan = e.detail.value" />
                <text>高清扫描</text>
              </label>
            </view>
          </view>
        </template>

        <!-- 书画特有字段 -->
        <template v-if="archiveType === 'calligraphy'">
          <view class="form-item">
            <text class="form-label">艺术家</text>
            <input v-model="formData.artist"
                   class="form-input"
                   placeholder="请输入艺术家姓名"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">年代</text>
            <input v-model="formData.period"
                   class="form-input"
                   placeholder="请输入创作年代"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">风格</text>
            <picker :value="styleIndex"
                    :range="styleOptions"
                    @change="onStyleChange">
              <view class="form-input">
                {{ formData.style || '请选择风格' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">分析特性</text>
            <view class="checkbox-group">
              <label class="checkbox-item">
                <checkbox :checked="formData.has_style_analysis"
                          @change="e => formData.has_style_analysis = e.detail.value" />
                <text>风格分析</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_technique"
                          @change="e => formData.has_technique = e.detail.value" />
                <text>技法解读</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_history"
                          @change="e => formData.has_history = e.detail.value" />
                <text>历史溯源</text>
              </label>
            </view>
          </view>
        </template>

        <!-- 档案特有字段 -->
        <template v-if="archiveType === 'archives'">
          <view class="form-item">
            <text class="form-label">档案类型</text>
            <picker :value="archiveTypeIndex"
                    :range="archiveTypeOptions"
                    @change="onArchiveTypeChange">
              <view class="form-input">
                {{ formData.type || '请选择档案类型' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">时间</text>
            <input v-model="formData.date"
                   class="form-input"
                   placeholder="请输入档案时间"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">数字化特性</text>
            <view class="checkbox-group">
              <label class="checkbox-item">
                <checkbox :checked="formData.has_digital"
                          @change="e => formData.has_digital = e.detail.value" />
                <text>数字档案</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_ai_story"
                          @change="e => formData.has_ai_story = e.detail.value" />
                <text>AI故事生成</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_context"
                          @change="e => formData.has_context = e.detail.value" />
                <text>历史背景</text>
              </label>
            </view>
          </view>
        </template>

        <!-- 建筑特有字段 -->
        <template v-if="archiveType === 'architecture'">
          <view class="form-item">
            <text class="form-label">建筑类型</text>
            <picker :value="buildingTypeIndex"
                    :range="buildingTypeOptions"
                    @change="onBuildingTypeChange">
              <view class="form-input">
                {{ formData.type || '请选择建筑类型' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">建造年代</text>
            <input v-model="formData.period"
                   class="form-input"
                   placeholder="请输入建造年代"
                   maxlength="100" />
          </view>

          <view class="form-item">
            <text class="form-label">地理位置</text>
            <input v-model="formData.location"
                   class="form-input"
                   placeholder="请输入地理位置"
                   maxlength="200" />
          </view>

          <view class="form-item">
            <text class="form-label">记录特性</text>
            <view class="checkbox-group">
              <label class="checkbox-item">
                <checkbox :checked="formData.has_3d_scan"
                          @change="e => formData.has_3d_scan = e.detail.value" />
                <text>三维扫描</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_aerial"
                          @change="e => formData.has_aerial = e.detail.value" />
                <text>航拍记录</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_structure"
                          @change="e => formData.has_structure = e.detail.value" />
                <text>结构分析</text>
              </label>
              <label class="checkbox-item">
                <checkbox :checked="formData.has_history"
                          @change="e => formData.has_history = e.detail.value" />
                <text>历史考证</text>
              </label>
            </view>
          </view>
        </template>

        <view class="form-item">
          <text class="form-label">简介</text>
          <textarea v-model="formData.brief"
                    class="form-textarea"
                    placeholder="请输入简介"
                    maxlength="1000"
                    :auto-height="true"
                    :min-height="120"
                    :show-count="true" />
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">图片和内容</view>

        <!-- 主图片 -->
        <view class="form-item">
          <text class="form-label">封面图片</text>
          <view class="image-upload">
            <view class="image-preview"
                  v-if="formData.image">
              <image :src="formData.image"
                     mode="aspectFill"
                     class="preview-image" />
              <view class="image-actions">
                <text class="action-btn"
                      @click="changeMainImage">更换</text>
                <text class="action-btn delete"
                      @click="removeMainImage">删除</text>
              </view>
            </view>
            <view class="upload-btn"
                  v-else
                  @click="uploadMainImage"
                  :class="{ disabled: uploading }">
              <text class="upload-icon">{{ uploading ? '...' : '+' }}</text>
              <text class="upload-text">{{ uploading ? '上传中' : '上传封面图片' }}</text>
            </view>
          </view>
        </view>

        <!-- 详细图片 -->
        <view class="form-item">
          <text class="form-label">详细图片 ({{ formData.detail_images.length }}/9)</text>
          <view class="detail-images-container">
            <view class="detail-image-item"
                  v-for="(image, index) in formData.detail_images"
                  :key="index">
              <image :src="image"
                     mode="aspectFill"
                     class="detail-image" />
              <view class="delete-detail-image"
                    @click="removeDetailImage(index)">
                <text class="delete-icon">×</text>
              </view>
            </view>
            <view class="add-detail-image"
                  v-if="formData.detail_images.length < 9"
                  @click="addDetailImage"
                  :class="{ disabled: uploading }">
              <text class="add-icon">+</text>
            </view>
          </view>
        </view>

        <!-- 详细内容 -->
        <view class="form-item">
          <text class="form-label">详细内容</text>
          <textarea v-model="formData.detail_content"
                    class="form-textarea large"
                    placeholder="请输入详细内容，支持HTML格式"
                    maxlength="5000"
                    :auto-height="true"
                    :min-height="200"
                    :show-count="true" />
        </view>

        <!-- AI分析内容 -->
        <view class="form-item">
          <text class="form-label">AI分析内容</text>
          <textarea v-model="formData.ai_analysis"
                    class="form-textarea large"
                    placeholder="请输入AI分析内容，支持HTML格式"
                    maxlength="3000"
                    :auto-height="true"
                    :min-height="150"
                    :show-count="true" />
        </view>
      </view>

      <!-- 状态设置 -->
      <view class="form-section">
        <view class="section-title">设置</view>

        <view class="form-item">
          <text class="form-label">启用状态</text>
          <switch :checked="formData.is_active"
                  @change="onActiveChange"
                  color="#8B4513" />
          <text class="form-desc">关闭后不会在前台显示</text>
        </view>

        <view class="form-item">
          <text class="form-label">排序权重</text>
          <input v-model.number="formData.sort_order"
                 class="form-input"
                 type="number"
                 placeholder="数值越大越靠前"
                 maxlength="10" />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="cancel-btn"
                @click="cancelEdit">取消</button>
        <button class="save-btn"
                @click="saveArchive"
                :disabled="isSaving">
          {{ isSaving ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getImageProxyUrl } from '../../utils/image'

// 页面状态
const isEdit = ref(false)
const isLoading = ref(false)
const isSaving = ref(false)
const uploading = ref(false)
const loadingText = ref('加载中...')
const archiveType = ref('')
const archiveId = ref<number | null>(null)

// 类型映射
const typeMap = {
  books: { name: '古籍典藏', icon: '📜', titleLabel: '古籍名称' },
  calligraphy: { name: '书画珍品', icon: '🖌️', titleLabel: '作品名称' },
  archives: { name: '档案故事', icon: '📄', titleLabel: '档案标题' },
  architecture: { name: '建筑遗构', icon: '🏛️', titleLabel: '建筑名称' },
}

// 选择器相关
const dynastyIndex = ref(-1)
const dynastyOptions = ref([
  '先秦',
  '秦汉',
  '魏晋南北朝',
  '隋唐',
  '五代十国',
  '宋',
  '元',
  '明',
  '清',
  '近现代',
])

const styleIndex = ref(-1)
const styleOptions = ref([
  '楷书',
  '行书',
  '草书',
  '隶书',
  '篆书',
  '山水画',
  '花鸟画',
  '人物画',
  '其他',
])

const archiveTypeIndex = ref(-1)
const archiveTypeOptions = ref([
  '房产契约',
  '官府文书',
  '地方志',
  '族谱',
  '商业账册',
  '信件',
  '其他',
])

const buildingTypeIndex = ref(-1)
const buildingTypeOptions = ref([
  '祭祀建筑',
  '民居建筑',
  '商业建筑',
  '教育建筑',
  '宗教建筑',
  '园林建筑',
  '其他',
])

// 表单数据
const formData = ref({
  title: '',
  brief: '',
  image: '',
  detail_content: '',
  ai_analysis: '',
  detail_images: [] as string[],
  is_active: true,
  sort_order: 0,

  // 古籍特有字段
  author: '',
  dynasty: '',
  edition: '',
  has_ocr: false,
  has_annotation: false,
  has_scan: false,

  // 书画特有字段
  artist: '',
  period: '',
  style: '',
  has_style_analysis: false,
  has_technique: false,
  has_history: false,

  // 档案特有字段
  type: '',
  date: '',
  has_digital: false,
  has_ai_story: false,
  has_context: false,

  // 建筑特有字段
  location: '',
  has_3d_scan: false,
  has_aerial: false,
  has_structure: false,
})

// 初始化页面
onMounted(async () => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}

  if (options.type) {
    archiveType.value = options.type
  }

  if (options.mode === 'edit' && options.id) {
    isEdit.value = true
    archiveId.value = parseInt(options.id)
    await loadArchiveData()
  }

  // 设置导航栏
  uni.setNavigationBarTitle({
    title: isEdit.value
      ? `编辑${getTypeName(archiveType.value)}`
      : `新建${getTypeName(archiveType.value)}`,
  })

  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#8B4513',
  })
})

// 加载档案数据
const loadArchiveData = async () => {
  if (!archiveId.value) return

  try {
    isLoading.value = true
    loadingText.value = '加载数据中...'

    // 这里应该调用相应的API获取数据
    // 目前使用模拟数据
    await loadMockEditData()
  } catch (error: any) {
    console.error('加载档案数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 加载模拟编辑数据
const loadMockEditData = async () => {
  await new Promise((resolve) => setTimeout(resolve, 500))

  // 根据类型生成模拟数据
  switch (archiveType.value) {
    case 'books':
      formData.value = {
        ...formData.value,
        title: '四库全书总目提要',
        author: '纪昀等',
        dynasty: '清',
        edition: '武英殿版',
        brief: '中国古代目录学的集大成之作，收录古籍约44000种',
        image: '/static/images/books/siku.jpg',
        has_ocr: true,
        has_annotation: true,
        has_scan: true,
        detail_content: '<p>详细的古籍介绍内容...</p>',
        ai_analysis: '<p>AI分析结果...</p>',
        detail_images: ['/static/images/books/siku-1.jpg'],
        is_active: true,
        sort_order: 1,
      }
      dynastyIndex.value = dynastyOptions.value.indexOf('清')
      break

    // 其他类型的模拟数据...
  }
}

// 获取类型图标
const getTypeIcon = (type: string) => {
  return typeMap[type as keyof typeof typeMap]?.icon || '📄'
}

// 获取类型名称
const getTypeName = (type: string) => {
  return typeMap[type as keyof typeof typeMap]?.name || '档案'
}

// 获取标题标签
const getItemTitleLabel = (type: string) => {
  return typeMap[type as keyof typeof typeMap]?.titleLabel || '标题'
}

// 选择器变化事件
const onDynastyChange = (e: any) => {
  dynastyIndex.value = e.detail.value
  formData.value.dynasty = dynastyOptions.value[e.detail.value]
}

const onStyleChange = (e: any) => {
  styleIndex.value = e.detail.value
  formData.value.style = styleOptions.value[e.detail.value]
}

const onArchiveTypeChange = (e: any) => {
  archiveTypeIndex.value = e.detail.value
  formData.value.type = archiveTypeOptions.value[e.detail.value]
}

const onBuildingTypeChange = (e: any) => {
  buildingTypeIndex.value = e.detail.value
  formData.value.type = buildingTypeOptions.value[e.detail.value]
}

const onActiveChange = (e: any) => {
  formData.value.is_active = e.detail.value
}

// 图片上传相关函数
const uploadImageToServer = async (
  filePath: string,
  type: string = 'main'
): Promise<string> => {
  uploading.value = true

  try {
    const token = uni.getStorageSync('access_token')
    const uploadRes = await uni.uploadFile({
      url: `http://192.168.8.96:8000/api/v1/upload/archive/image`,
      filePath: filePath,
      name: 'file',
      header: {
        Authorization: `Bearer ${token}`,
      },
    })

    const data = JSON.parse(uploadRes.data)
    if (data.success) {
      const imageUrl = getImageProxyUrl(data.data.url)

      if (type === 'main') {
        formData.value.image = imageUrl
        uni.showToast({
          title: '封面图片上传成功',
          icon: 'success',
          duration: 1500,
        })
      }

      return imageUrl
    } else {
      throw new Error(data.message || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    })
    throw error
  } finally {
    uploading.value = false
  }
}

// 上传主图片
const uploadMainImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      await uploadImageToServer(res.tempFilePaths[0], 'main')
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 更换主图片
const changeMainImage = () => {
  uploadMainImage()
}

// 删除主图片
const removeMainImage = () => {
  formData.value.image = ''
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 添加详细图片
const addDetailImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 9 - formData.value.detail_images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      uni.showLoading({
        title: '上传中...',
      })

      try {
        for (const filePath of res.tempFilePaths) {
          const imageUrl = await uploadImageToServer(filePath, 'detail')
          formData.value.detail_images.push(imageUrl)
        }

        uni.showToast({
          title: `成功上传${res.tempFilePaths.length}张图片`,
          icon: 'success',
        })
      } finally {
        uni.hideLoading()
      }
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 删除详细图片
const removeDetailImage = (index: number) => {
  formData.value.detail_images.splice(index, 1)
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 保存档案
const saveArchive = async () => {
  // 表单验证
  if (!formData.value.title.trim()) {
    uni.showToast({
      title: `请输入${getItemTitleLabel(archiveType.value)}`,
      icon: 'none',
    })
    return
  }

  if (!formData.value.brief.trim()) {
    uni.showToast({
      title: '请输入简介',
      icon: 'none',
    })
    return
  }

  try {
    isSaving.value = true

    // 这里应该调用相应的API保存数据
    // 目前使用模拟保存
    await new Promise((resolve) => setTimeout(resolve, 1000))

    uni.showToast({
      title: isEdit.value ? '修改成功' : '创建成功',
      icon: 'success',
    })

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error: any) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
    })
  } finally {
    isSaving.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消编辑吗？未保存的更改将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}
</script>

<style scoped>
.archive-edit-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 类型头部 */
.type-header {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.type-icon {
  font-size: 48rpx;
}

.type-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
}

/* 表单容器 */
.form-container {
  padding: 0 20rpx;
}

.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx 0;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8b4513;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: #8b4513;
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  resize: none;
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  border-color: #8b4513;
}

.form-textarea.large {
  min-height: 200rpx;
}

.form-desc {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 25rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.checkbox-item:active {
  background: #e8f4fd;
  border-color: #8b4513;
}

.checkbox-item text {
  font-size: 26rpx;
  color: #333;
}

/* 图片上传 */
.image-upload {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 16rpx;
  display: flex;
  justify-content: space-around;
}

.action-btn {
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.2);
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.8);
}

.upload-btn {
  width: 100%;
  height: 100%;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-btn:active {
  border-color: #8b4513;
  background: #f0f8ff;
}

.upload-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 详细图片 */
.detail-images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.detail-image-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.detail-image {
  width: 100%;
  height: 100%;
}

.delete-detail-image {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

.add-detail-image {
  width: 150rpx;
  height: 150rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.add-detail-image:active {
  border-color: #8b4513;
  background: #f0f8ff;
}

.add-detail-image.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.add-icon {
  font-size: 36rpx;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 30rpx;
  display: flex;
  gap: 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.cancel-btn {
  flex: 1;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 25rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background: #e9ecef;
}

.save-btn {
  flex: 2;
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-btn:active {
  transform: translateY(2rpx);
}

.save-btn:disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8b4513;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .checkbox-group {
    flex-direction: column;
  }

  .detail-images-container {
    justify-content: space-between;
  }

  .detail-image-item,
  .add-detail-image {
    width: calc(50% - 10rpx);
  }
}
</style>
</code_block_to_apply_changes_from>