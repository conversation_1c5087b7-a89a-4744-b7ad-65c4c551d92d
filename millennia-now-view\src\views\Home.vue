<template>
  <div class="home-container">
    <div class="home-content">
      <h1 class="title">{{ regionName }}虚拟文化馆</h1>
      <p class="subtitle">传统文化数字保护展示</p>

      <div class="gallery-selection">
        <div class="gallery-card history"
             @click="navigateToGallery('history')">
          <div class="card-content">
            <h2>历史文脉</h2>
            <p>探索地区发展历史</p>
          </div>
          <div class="card-overlay"></div>
        </div>

        <div class="gallery-card culture"
             @click="navigateToGallery('culture')">
          <div class="card-content">
            <h2>文化传承</h2>
            <p>传统文化数字保护</p>
          </div>
          <div class="card-overlay"></div>
        </div>

        <div class="gallery-card memory"
             @click="navigateToGallery('memory')">
          <div class="card-content">
            <h2>当代记忆</h2>
            <p>城市影像数字档案</p>
          </div>
          <div class="card-overlay"></div>
        </div>
      </div>
    </div>

    <div class="background-scene">
      <!-- 3D背景场景 -->
      <canvas ref="sceneCanvas"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, provide, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import MiniappBackButton from '../components/MiniappBackButton.vue'

// 为微信小程序环境添加类型声明
declare global {
  interface Window {
    __wxjs_environment?: string
    wx?: {
      miniProgram?: {
        navigateBack: (options?: { delta?: number }) => void
        redirectTo: (options: { url: string }) => void
      }
    }
  }
}

const router = useRouter()
const route = useRoute()

// 小程序环境标记
const isFromMiniapp = ref(false)

// 区域信息
const regionName = ref('文源纪')
const provinceId = ref('')
const cityId = ref('')
const districtId = ref('')

// 提供区域信息为全局可访问的状态
provide('regionInfo', {
  regionName,
  provinceId,
  cityId,
  districtId,
})

// 解析URL参数
const parseUrlParams = () => {
  // 获取URL参数
  const urlParams = new URLSearchParams(window.location.search)
  const fromMiniapp = urlParams.get('from') === 'miniapp'
  isFromMiniapp.value = fromMiniapp

  if (fromMiniapp) {
    localStorage.setItem('fromMiniapp', 'true')
  }

  const urlRegionName = route.query.regionName as string
  const urlProvinceId = route.query.provinceId as string
  const urlCityId = route.query.cityId as string
  const urlDistrictId = route.query.districtId as string

  console.log('接收到参数:', {
    regionName: urlRegionName,
    provinceId: urlProvinceId,
    cityId: urlCityId,
    districtId: urlDistrictId,
  })

  // 如果URL中有区域名称参数，则使用它，否则尝试从localStorage获取
  if (urlRegionName) {
    regionName.value = urlRegionName
  } else {
    const storedRegionName = localStorage.getItem('regionName')
    if (storedRegionName) regionName.value = storedRegionName
  }

  // 保存省市区ID，优先使用URL参数，其次使用localStorage
  if (urlProvinceId) {
    provinceId.value = urlProvinceId
  } else {
    const storedProvinceId = localStorage.getItem('provinceId')
    if (storedProvinceId) provinceId.value = storedProvinceId
  }

  if (urlCityId) {
    cityId.value = urlCityId
  } else {
    const storedCityId = localStorage.getItem('cityId')
    if (storedCityId) cityId.value = storedCityId
  }

  if (urlDistrictId) {
    districtId.value = urlDistrictId
  } else {
    const storedDistrictId = localStorage.getItem('districtId')
    if (storedDistrictId) districtId.value = storedDistrictId
  }

  // 保存到localStorage，方便其他页面使用
  try {
    localStorage.setItem('regionName', regionName.value)
    localStorage.setItem('provinceId', provinceId.value)
    localStorage.setItem('cityId', cityId.value)
    localStorage.setItem('districtId', districtId.value)
  } catch (e) {
    console.error('无法保存区域信息到localStorage:', e)
  }
}

// 监听路由变化，重新解析参数
watch(
  () => route.fullPath, // 也可以用 route.query
  () => {
    parseUrlParams()
  }
)

// 场景相关变量
const sceneCanvas = ref<HTMLCanvasElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let animationFrameId: number

// 初始化3D场景
const initScene = () => {
  if (!sceneCanvas.value) return

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x000000)

  // 添加环境光和方向光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(5, 10, 7.5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 设置相机
  const aspect = window.innerWidth / window.innerHeight
  camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000)
  camera.position.set(0, 1.5, 5)

  // 设置渲染器
  renderer = new THREE.WebGLRenderer({
    canvas: sceneCanvas.value,
    antialias: true,
    alpha: true,
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true

  // 添加轨道控制
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.enableZoom = false
  controls.autoRotate = true
  controls.autoRotateSpeed = 0.5

  // 创建背景场景
  createBackgroundScene()

  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize)

  // 开始动画循环
  animate()
}

// 创建背景场景
const createBackgroundScene = () => {
  // 创建星空背景
  const starsGeometry = new THREE.BufferGeometry()
  const starsMaterial = new THREE.PointsMaterial({
    color: 0xffffff,
    size: 1,
    transparent: true,
    opacity: 0.8,
    sizeAttenuation: true,
  })

  const starsVertices = []
  for (let i = 0; i < 1000; i++) {
    const x = (Math.random() - 0.5) * 2000
    const y = (Math.random() - 0.5) * 2000
    const z = (Math.random() - 0.5) * 2000
    starsVertices.push(x, y, z)
  }

  starsGeometry.setAttribute(
    'position',
    new THREE.Float32BufferAttribute(starsVertices, 3)
  )
  const stars = new THREE.Points(starsGeometry, starsMaterial)
  scene.add(stars)

  // 添加三个代表不同展厅的几何体
  const historyGeometry = new THREE.BoxGeometry(2, 3, 0.2)
  const historyMaterial = new THREE.MeshStandardMaterial({
    color: 0xc8161e,
    metalness: 0.3,
    roughness: 0.4,
  })
  const historyMesh = new THREE.Mesh(historyGeometry, historyMaterial)
  historyMesh.position.set(-3, 0, -3)
  historyMesh.rotation.y = Math.PI / 6
  scene.add(historyMesh)

  const cultureGeometry = new THREE.SphereGeometry(1.5, 32, 32)
  const cultureMaterial = new THREE.MeshStandardMaterial({
    color: 0x8b4513,
    metalness: 0.5,
    roughness: 0.3,
  })
  const cultureMesh = new THREE.Mesh(cultureGeometry, cultureMaterial)
  cultureMesh.position.set(0, 0, -5)
  scene.add(cultureMesh)

  const memoryGeometry = new THREE.CylinderGeometry(1, 1.5, 3, 32)
  const memoryMaterial = new THREE.MeshStandardMaterial({
    color: 0x333333,
    metalness: 0.7,
    roughness: 0.2,
  })
  const memoryMesh = new THREE.Mesh(memoryGeometry, memoryMaterial)
  memoryMesh.position.set(3, 0, -3)
  memoryMesh.rotation.y = -Math.PI / 6
  scene.add(memoryMesh)
}

// 窗口大小变化处理
const onWindowResize = () => {
  if (!camera || !renderer) return

  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)

  if (controls) {
    controls.update()
  }

  renderer.render(scene, camera)
}

// 导航到不同的展厅
const navigateToGallery = (type: 'history' | 'culture' | 'memory') => {
  // 构建查询参数
  const query = {
    regionName: regionName.value,
    provinceId: provinceId.value,
    cityId: cityId.value,
    districtId: districtId.value,
  }

  console.log(`导航到${type}展厅，参数:`, query)

  // 根据类型跳转到不同路由，并传递区域参数
  if (type === 'history') {
    router.push({
      path: '/history-gallery',
      query,
    })
  } else if (type === 'culture') {
    router.push({
      path: '/culture-gallery',
      query,
    })
  } else if (type === 'memory') {
    router.push({
      path: '/memory-gallery',
      query,
    })
  }
}

// 组件挂载时初始化场景并解析URL参数
onMounted(() => {
  parseUrlParams()
  initScene()
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  window.removeEventListener('resize', onWindowResize)
  cancelAnimationFrame(animationFrameId)

  if (renderer) {
    renderer.dispose()
  }

  if (controls) {
    controls.dispose()
  }
})
</script>

<style scoped lang="scss">
.home-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(to bottom, #000000, #1a1a1a);
}

.background-scene {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.home-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  z-index: 2;
  padding: 20px;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(200, 22, 30, 0.8);
  animation: glow 2s ease-in-out infinite alternate;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 4rem;
  opacity: 0.8;
}

.gallery-selection {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  max-width: 1200px;
  width: 100%;
}

.gallery-card {
  position: relative;
  width: 300px;
  height: 200px;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);

  &:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);

    .card-overlay {
      opacity: 0.6;
    }

    .card-content {
      transform: translateY(-5px);

      h2 {
        transform: scale(1.1);
      }
    }
  }

  &.history {
    background: url('http://192.168.101.3:8000/static/image/wenmai.png')
      center/cover;
  }

  &.culture {
    background: url('http://192.168.101.3:8000/static/image/chuancheng.png')
      center/cover;
  }

  &.memory {
    background: url('http://192.168.101.3:8000/static/image/memory.jpg')
      center/cover;
  }

  .card-content {
    position: relative;
    z-index: 2;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    transition: transform 0.3s ease;

    h2 {
      font-size: 1.8rem;
      margin: 0 0 5px 0;
      transition: transform 0.3s ease;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.8);
    }

    p {
      font-size: 1rem;
      margin: 0;
      opacity: 0.8;
      text-shadow: 0 2px 5px rgba(0, 0, 0, 0.8);
    }
  }

  .card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px rgba(200, 22, 30, 0.8);
  }
  to {
    text-shadow: 0 0 20px rgba(200, 22, 30, 1), 0 0 30px rgba(200, 22, 30, 0.6);
  }
}

@media (max-width: 992px) {
  .gallery-selection {
    gap: 20px;
  }

  .gallery-card {
    width: 250px;
    height: 180px;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
  }

  .gallery-card {
    width: 100%;
    max-width: 400px;
    height: 150px;
  }
}
</style> 