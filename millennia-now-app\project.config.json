{"description": "项目配置文件", "setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": ["typescript", "sass"], "minifyWXML": true, "minifyWXSS": true, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "localPlugins": false, "disableUseStrict": false, "condition": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "checkInvalidKey": true, "showShadowRootInWxmlPanel": true, "compileHotReLoad": true, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wx7827d9f8bd827e3f", "projectname": "millennia-now-app", "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "libVersion": "3.8.10", "condition": {}}