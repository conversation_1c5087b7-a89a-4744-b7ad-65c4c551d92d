// API配置文件 - 统一管理所有后端服务的IP地址
// 如果需要修改IP地址，只需在这里修改即可

/**
 * API环境配置
 */
export interface ApiConfig {
  baseURL: string
  timeout: number
}

/**
 * 环境类型
 */
export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing', 
  PRODUCTION = 'production'
}

/**
 * 不同环境的API配置
 * 修改IP地址请在这里修改对应环境的baseURL
 */
const API_CONFIGS: Record<Environment, ApiConfig> = {
  [Environment.DEVELOPMENT]: {
    baseURL: 'http://**************:8001/api',  // 使用 /api 前缀
    // baseURL:'http://*************:8000/api',
    // baseURL:'https://luckyzyn.top/millennia-api/api',
    timeout: 30000  
  },
  [Environment.TESTING]: {
    baseURL: 'https://test-api.millennia-now.com/api',  // 使用 /api 前缀
    timeout: 20000
  },
  [Environment.PRODUCTION]: {
    baseURL: 'https://api.millennia-now.com/api',  // 使用 /api 前缀
    timeout: 15000
  }
}

/**
 * 获取当前环境 - 支持多平台
 */
function getCurrentEnvironment(): Environment {
  // #ifdef MP-WEIXIN
  // 微信小程序环境
  try {
    const accountInfo = uni.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion

    switch (envVersion) {
      case 'develop':
        return Environment.DEVELOPMENT
      case 'trial':
        return Environment.TESTING
      case 'release':
        return Environment.PRODUCTION
      default:
        return Environment.DEVELOPMENT
    }
  } catch (error) {
    console.warn('获取小程序环境信息失败，使用开发环境配置:', error)
    return Environment.DEVELOPMENT
  }
  // #endif

  // #ifdef APP-PLUS
  // App环境（Android/iOS）
  try {
    // 可以通过编译时环境变量判断
    // @ts-ignore
    if (typeof __PRODUCTION__ !== 'undefined' && __PRODUCTION__) {
      return Environment.PRODUCTION
    }
    // @ts-ignore
    if (typeof __TESTING__ !== 'undefined' && __TESTING__) {
      return Environment.TESTING
    }
    // 默认开发环境
    return Environment.DEVELOPMENT
  } catch (error) {
    console.warn('获取App环境信息失败，使用开发环境配置:', error)
    return Environment.DEVELOPMENT
  }
  // #endif

  // #ifdef H5
  // H5环境
  try {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname
      if (hostname.includes('localhost') || hostname.includes('127.0.0.1') || hostname.includes('192.168')) {
        return Environment.DEVELOPMENT
      } else if (hostname.includes('test') || hostname.includes('staging')) {
        return Environment.TESTING
      } else {
        return Environment.PRODUCTION
      }
    }
  } catch (error) {
    console.warn('获取H5环境信息失败，使用开发环境配置:', error)
  }
  // #endif

  // 其他平台或无法判断时，默认使用开发环境
  return Environment.DEVELOPMENT
}

/**
 * 手动设置的环境（用于调试）
 */
let manualEnvironment: Environment | null = null

/**
 * 获取当前平台类型
 */
function getCurrentPlatform(): string {
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif

  // #ifdef APP-PLUS
  return 'app-plus'
  // #endif

  // #ifdef H5
  return 'h5'
  // #endif

  return 'unknown'
}

/**
 * 获取当前API配置
 */
export function getApiConfig(): ApiConfig {
  const env = manualEnvironment || getCurrentEnvironment()
  const config = API_CONFIGS[env]
  const platform = getCurrentPlatform()
  return config
}

/**
 * 获取基础URL
 * 这是主要的导出函数，所有API文件都应该使用这个函数获取BASE_URL
 */
export function getBaseURL(): string {
  return getApiConfig().baseURL
}

/**
 * 获取超时时间
 */
export function getTimeout(): number {
  return getApiConfig().timeout
}

/**
 * 手动设置环境（用于调试）
 */
export function setEnvironment(env: Environment | null): void {
  manualEnvironment = env
  const platform = getCurrentPlatform()
  console.log(`[API Config] 平台: ${platform}, 手动设置环境为: ${env || '自动检测'}`)
}

/**
 * 重置环境设置
 */
export function resetEnvironment(): void {
  manualEnvironment = null
  const platform = getCurrentPlatform()
  console.log(`[API Config] 平台: ${platform}, 重置环境设置，将自动检测环境`)
}

/**
 * 获取当前平台和环境信息（用于调试）
 */
export function getDebugInfo() {
  const platform = getCurrentPlatform()
  const env = manualEnvironment || getCurrentEnvironment()
  const config = API_CONFIGS[env]

  return {
    platform,
    environment: env,
    manualEnvironment,
    baseURL: config.baseURL,
    timeout: config.timeout
  }
}

/**
 * 构建完整的API URL
 */
export function buildApiUrl(endpoint: string): string {
  const baseURL = getBaseURL()
  return endpoint.startsWith('http') ? endpoint : `${baseURL}${endpoint}`
}

// 默认导出基础URL获取函数，方便直接导入使用
export default getBaseURL
