/**
 * HTML模板配置文件
 * 用于时间轴编辑器和其他需要HTML模板的组件
 */

// 定义模板接口类型
export interface TemplateItem {
  name: string
  description: string
  preview: string
  content: string
  category?: string
  tags?: string[]
}

// HTML模板列表
export const htmlTemplates: TemplateItem[] = [
  {
    name: '基础段落模板',
    description: '适用于一般文字描述',
    preview: '<p>这是一个段落...</p>',
    category: '基础',
    tags: ['段落', '文字', '基础'],
    content: `<p>在这里输入您的内容...</p>
<p>您可以添加多个段落来组织内容。</p>`,
  },
  {
    name: '要点列表模板',
    description: '适用于列举要点',
    preview: '• 要点一 • 要点二',
    category: '列表',
    tags: ['要点', '列表', '归纳'],
    content: `<p>主要特点：</p>
<p>• 第一个要点</p>
<p>• 第二个要点</p>
<p>• 第三个要点</p>`,
  },
  {
    name: '时间发展模板',
    description: '适用于描述历史发展过程',
    preview: '年份 - 事件描述',
    category: '历史',
    tags: ['时间', '发展', '历史'],
    content: `<p>这一时期的重要发展：</p>
<p>• 初期：发展背景和起源</p>
<p>• 中期：重要变化和发展</p>
<p>• 后期：成果和影响</p>
<p>这些发展为后来的历史奠定了重要基础。</p>`,
  },
  {
    name: '文化特色模板',
    description: '适用于介绍文化特色',
    preview: '文化特色介绍...',
    category: '文化',
    tags: ['文化', '特色', '介绍'],
    content: `<p>这一时期的文化特色：</p>
<p>• 建筑风格：描述建筑特点</p>
<p>• 生活方式：描述生活特点</p>
<p>• 艺术表现：描述艺术特点</p>
<p>• 社会制度：描述社会特点</p>
<p>这些文化特色体现了当时的时代特征。</p>`,
  },
  {
    name: '影响意义模板',
    description: '适用于总结历史影响和意义',
    preview: '历史影响和意义...',
    category: '分析',
    tags: ['影响', '意义', '总结'],
    content: `<p>这一时期的历史意义：</p>
<p>• 政治影响：对政治制度的影响</p>
<p>• 经济影响：对经济发展的影响</p>
<p>• 文化影响：对文化传承的影响</p>
<p>• 社会影响：对社会发展的影响</p>
<p>这些影响对后世产生了深远的意义。</p>`,
  },
  {
    name: '强调重点模板',
    description: '用于突出重要内容',
    preview: '【重要】突出显示的内容',
    category: '强调',
    tags: ['重要', '强调', '突出'],
    content: `<p style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; border-radius: 4px;">
<strong>【重要提示】</strong>这里是需要特别强调的重要内容，会以醒目的样式显示。
</p>
<p>普通内容可以继续在这里描述...</p>`,
  },
  {
    name: '引用说明模板',
    description: '用于引用文献或说明出处',
    preview: '引用内容和出处说明',
    category: '引用',
    tags: ['引用', '文献', '出处'],
    content: `<p style="border-left: 3px solid #007aff; padding-left: 15px; margin: 15px 0; font-style: italic; color: #666;">
"这里是引用的原文内容，可以是古籍记载、史料描述等重要文献资料。"
</p>
<p style="text-align: right; font-size: 14px; color: #999; margin-top: 5px;">
—— 出处：《文献名称》或相关史料
</p>
<p>基于以上史料记载，我们可以了解到...</p>`,
  },
  {
    name: '时间轴详细模板',
    description: '适用于详细的时间线描述',
    preview: '时间节点 → 事件详情',
    category: '时间轴',
    tags: ['时间轴', '时间线', '详细'],
    content: `<div style="border-left: 2px solid #007aff; padding-left: 20px;">
<p style="margin-bottom: 15px;"><strong style="color: #007aff;">公元XXX年</strong></p>
<p style="margin-left: 15px;">• 重要事件一：事件详细描述</p>
<p style="margin-left: 15px;">• 重要事件二：事件详细描述</p>
<p style="margin-left: 15px;">• 重要事件三：事件详细描述</p>
</div>
<div style="border-left: 2px solid #28a745; padding-left: 20px; margin-top: 20px;">
<p style="margin-bottom: 15px;"><strong style="color: #28a745;">公元XXX年</strong></p>
<p style="margin-left: 15px;">• 重要事件一：事件详细描述</p>
<p style="margin-left: 15px;">• 重要事件二：事件详细描述</p>
</div>`,
  },
  {
    name: '对比分析模板',
    description: '用于对比不同时期或方面',
    preview: '对比项目A vs 对比项目B',
    category: '分析',
    tags: ['对比', '分析', '比较'],
    content: `<div style="display: flex; gap: 20px; margin: 15px 0;">
<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007aff;">
<h4 style="color: #007aff; margin-bottom: 10px;">前期特征</h4>
<p>• 特征描述一</p>
<p>• 特征描述二</p>
<p>• 特征描述三</p>
</div>
<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
<h4 style="color: #28a745; margin-bottom: 10px;">后期特征</h4>
<p>• 特征描述一</p>
<p>• 特征描述二</p>
<p>• 特征描述三</p>
</div>
</div>
<p>通过对比可以看出...</p>`,
  },
  {
    name: '数据统计模板',
    description: '用于展示数据和统计信息',
    preview: '数据项：具体数值',
    category: '数据',
    tags: ['数据', '统计', '展示'],
    content: `<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;">
<h4 style="text-align: center; color: #333; margin-bottom: 15px;">重要数据统计</h4>
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
<div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px;">
<div style="font-size: 24px; font-weight: bold; color: #007aff;">XXX</div>
<div style="font-size: 14px; color: #666;">数据项目一</div>
</div>
<div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px;">
<div style="font-size: 24px; font-weight: bold; color: #28a745;">XXX</div>
<div style="font-size: 14px; color: #666;">数据项目二</div>
</div>
</div>
</div>
<p>数据说明：以上统计数据反映了...</p>`,
  },
  {
    name: '成就展示模板',
    description: '用于展示重要成就和贡献',
    preview: '成就标题 + 详细说明',
    category: '展示',
    tags: ['成就', '展示', '贡献'],
    content: `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin: 15px 0;">
<h3 style="margin-bottom: 15px; text-align: center;">重要成就</h3>
<div style="background-color: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
<p><strong>📍 成就一：</strong>具体成就描述</p>
<p><strong>🏆 成就二：</strong>具体成就描述</p>
<p><strong>⭐ 成就三：</strong>具体成就描述</p>
</div>
</div>
<p>这些成就的历史意义在于...</p>`,
  },
  {
    name: '人物介绍模板',
    description: '用于介绍重要历史人物',
    preview: '人物姓名 + 生平简介',
    category: '人物',
    tags: ['人物', '介绍', '历史'],
    content: `<div style="border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; margin: 15px 0; background-color: #fafafa;">
<div style="display: flex; align-items: center; margin-bottom: 15px;">
<div style="width: 4px; height: 40px; background-color: #007aff; margin-right: 15px;"></div>
<h3 style="color: #333; margin: 0;">人物姓名（生卒年份）</h3>
</div>
<p><strong>身份职位：</strong>具体身份描述</p>
<p><strong>主要贡献：</strong>重要贡献和成就</p>
<p><strong>历史评价：</strong>后世对其的评价和影响</p>
</div>
<p>该人物在这一时期的作用...</p>`,
  },
  {
    name: '事件记录模板',
    description: '用于详细记录重要历史事件',
    preview: '事件名称 + 过程描述',
    category: '事件',
    tags: ['事件', '记录', '历史'],
    content: `<div style="background-color: #fff; border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; margin: 15px 0;">
<div style="background-color: #007aff; color: white; padding: 15px;">
<h3 style="margin: 0;">重要历史事件</h3>
</div>
<div style="padding: 20px;">
<p><strong>🕐 发生时间：</strong>具体时间</p>
<p><strong>📍 发生地点：</strong>具体地点</p>
<p><strong>👥 参与人物：</strong>相关人物</p>
<p><strong>📋 事件经过：</strong></p>
<div style="margin-left: 20px; border-left: 3px solid #007aff; padding-left: 15px;">
<p>1. 事件起因和背景</p>
<p>2. 事件发展过程</p>
<p>3. 事件结果和影响</p>
</div>
</div>
</div>`,
  },
  {
    name: '总结回顾模板',
    description: '用于总结和回顾历史时期',
    preview: '时期总结 + 历史地位',
    category: '总结',
    tags: ['总结', '回顾', '历史'],
    content: `<div style="background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%); padding: 2px; border-radius: 12px; margin: 20px 0;">
<div style="background-color: white; padding: 20px; border-radius: 10px;">
<h3 style="text-align: center; color: #333; margin-bottom: 20px;">历史时期总结</h3>
<div style="border-top: 2px solid #f5576c; padding-top: 15px;">
<p><strong>🔸 时代特征：</strong>这一时期的主要特征和特点</p>
<p><strong>🔸 重要成就：</strong>取得的主要成就和进步</p>
<p><strong>🔸 历史地位：</strong>在历史长河中的重要地位</p>
<p><strong>🔸 后世影响：</strong>对后续历史发展的深远影响</p>
</div>
</div>
</div>
<p style="text-align: center; font-style: italic; color: #666;">
这一时期在历史发展中承前启后，具有重要的历史意义。
</p>`,
  },
  {
    name: '表格数据模板',
    description: '用于展示结构化数据',
    preview: '表格形式的数据展示',
    category: '数据',
    tags: ['表格', '数据', '结构化'],
    content: `<div style="overflow-x: auto; margin: 15px 0;">
<table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
<thead style="background-color: #f8f9fa;">
<tr>
<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">项目</th>
<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">内容</th>
<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">备注</th>
</tr>
</thead>
<tbody>
<tr>
<td style="border: 1px solid #dee2e6; padding: 12px;">数据项目一</td>
<td style="border: 1px solid #dee2e6; padding: 12px;">具体内容</td>
<td style="border: 1px solid #dee2e6; padding: 12px;">相关说明</td>
</tr>
<tr style="background-color: #f8f9fa;">
<td style="border: 1px solid #dee2e6; padding: 12px;">数据项目二</td>
<td style="border: 1px solid #dee2e6; padding: 12px;">具体内容</td>
<td style="border: 1px solid #dee2e6; padding: 12px;">相关说明</td>
</tr>
</tbody>
</table>
</div>
<p>表格说明：以上数据展示了...</p>`,
  },
  {
    name: '步骤流程模板',
    description: '用于描述发展过程或操作步骤',
    preview: '步骤1 → 步骤2 → 步骤3',
    category: '流程',
    tags: ['步骤', '流程', '过程'],
    content: `<div style="margin: 20px 0;">
<div style="display: flex; align-items: center; margin-bottom: 15px;">
<div style="width: 30px; height: 30px; background-color: #007aff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">1</div>
<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">
<strong>第一步：</strong>步骤描述和具体内容
</div>
</div>
<div style="display: flex; align-items: center; margin-bottom: 15px;">
<div style="width: 30px; height: 30px; background-color: #28a745; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">2</div>
<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">
<strong>第二步：</strong>步骤描述和具体内容
</div>
</div>
<div style="display: flex; align-items: center; margin-bottom: 15px;">
<div style="width: 30px; height: 30px; background-color: #ffc107; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">3</div>
<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">
<strong>第三步：</strong>步骤描述和具体内容
</div>
</div>
</div>
<p>通过以上步骤，最终形成了...</p>`,
  },
  {
    name: '注意提醒模板',
    description: '用于重要提醒和注意事项',
    preview: '⚠️ 注意事项提醒',
    category: '提醒',
    tags: ['注意', '提醒', '警告'],
    content: `<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0;">
<div style="display: flex; align-items: center; margin-bottom: 10px;">
<span style="font-size: 20px; margin-right: 10px;">⚠️</span>
<strong style="color: #856404;">注意事项</strong>
</div>
<p style="margin: 0; color: #856404;">这里是需要特别注意的重要信息，请仔细阅读和理解。</p>
</div>
<p>除了上述注意事项外...</p>`,
  },
  {
    name: '小贴士模板',
    description: '用于提供有用的补充信息',
    preview: '💡 实用小贴士',
    category: '提示',
    tags: ['贴士', '提示', '补充'],
    content: `<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin: 15px 0;">
<div style="display: flex; align-items: center; margin-bottom: 10px;">
<span style="font-size: 20px; margin-right: 10px;">💡</span>
<strong style="color: #0c5460;">小贴士</strong>
</div>
<p style="margin: 0; color: #0c5460;">这里提供一些有用的补充信息和背景知识，帮助更好地理解相关内容。</p>
</div>
<p>了解这些背景信息后...</p>`,
  },
]

/**
 * 工具函数：根据关键词搜索模板
 * @param keyword 搜索关键词
 * @param templates 模板列表（可选，默认使用全部模板）
 * @returns 匹配的模板列表
 */
export function searchTemplates(keyword: string, templates: TemplateItem[] = htmlTemplates): TemplateItem[] {
  if (!keyword.trim()) {
    return templates
  }

  const lowerKeyword = keyword.toLowerCase().trim()
  
  return templates.filter(template => 
    template.name.toLowerCase().includes(lowerKeyword) ||
    template.description.toLowerCase().includes(lowerKeyword) ||
    template.preview.toLowerCase().includes(lowerKeyword) ||
    template.category?.toLowerCase().includes(lowerKeyword) ||
    template.tags?.some(tag => tag.toLowerCase().includes(lowerKeyword))
  )
}

/**
 * 工具函数：根据分类筛选模板
 * @param category 分类名称
 * @param templates 模板列表（可选，默认使用全部模板）
 * @returns 指定分类的模板列表
 */
export function getTemplatesByCategory(category: string, templates: TemplateItem[] = htmlTemplates): TemplateItem[] {
  return templates.filter(template => template.category === category)
}

/**
 * 工具函数：获取所有分类
 * @param templates 模板列表（可选，默认使用全部模板）
 * @returns 分类列表
 */
export function getTemplateCategories(templates: TemplateItem[] = htmlTemplates): string[] {
  const categories = new Set<string>()
  templates.forEach(template => {
    if (template.category) {
      categories.add(template.category)
    }
  })
  return Array.from(categories).sort()
}

/**
 * 工具函数：根据标签筛选模板
 * @param tag 标签名称
 * @param templates 模板列表（可选，默认使用全部模板）
 * @returns 包含指定标签的模板列表
 */
export function getTemplatesByTag(tag: string, templates: TemplateItem[] = htmlTemplates): TemplateItem[] {
  return templates.filter(template => 
    template.tags?.includes(tag)
  )
}

/**
 * 工具函数：获取所有标签
 * @param templates 模板列表（可选，默认使用全部模板）
 * @returns 标签列表
 */
export function getAllTemplateTags(templates: TemplateItem[] = htmlTemplates): string[] {
  const tags = new Set<string>()
  templates.forEach(template => {
    template.tags?.forEach(tag => tags.add(tag))
  })
  return Array.from(tags).sort()
}

/**
 * 默认导出HTML模板列表
 */
export default htmlTemplates 