// Store modules type declarations
declare module '@/store/modules/location' {
  export interface LocationInfo {
    province: string
    city: string
    district: string
    street: string
    fullAddress: string
    longitude: number
    latitude: number
  }

  export const locationManager: {
    currentLocation: LocationInfo
    fullAddress: string
    isLocating: boolean
    hasLocationPermission: boolean
    setLocation: (location: Partial<LocationInfo>) => void
    clearLocation: () => void
    getCurrentLocation: () => Promise<LocationInfo>
    updatePermissionStatus: (granted: boolean) => void
  }

  export const useLocationStore: () => typeof locationManager
}

declare module '@/store/modules/region' {
  export interface RegionIds {
    provinceId: number
    provinceName: string
    cityId: number
    cityName: string
    districtId: number
    districtName: string
  }

  export interface RegionState {
    regionIds: RegionIds
    isLoading: boolean
    heritagePlace: any | null
    heritageLoading: boolean
  }

  export const regionManager: {
    currentRegionIds: RegionIds
    isLoading: boolean
    setRegionIds: (regionIds: Partial<RegionIds>) => void
    clearRegionIds: () => void
    matchRegionIdsByLocation: (locationInfo: {
      province: string
      city: string
      district: string
    }) => Promise<RegionIds | null>
    setSelectedRegionIds: (province: any, city: any, district?: any) => void
    fullRegionName: string
    hasCompleteRegion: boolean
    hasDistrict: boolean
    currentHeritagePlace: any | null
    isHeritageLoading: boolean
    fetchHeritagePlace: () => Promise<any | null>
    clearHeritagePlace: () => void
    updateRegionAndFetchHeritage: (regionIds: Partial<RegionIds>) => Promise<any | null>
  }
}

declare module '@/store/modules/auth' {
  export const authManager: {
    // Add auth manager type definitions here
    [key: string]: any
  }
}

export {} 