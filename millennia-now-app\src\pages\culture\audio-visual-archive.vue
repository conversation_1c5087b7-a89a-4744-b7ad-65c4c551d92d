<template>
  <view class="archive-container">
    <!-- 页面顶部 -->
    <view class="archive-header">
      <image class="header-bg"
             src="/static/images/archive-bg.jpg"
             mode="aspectFill">
      </image>
      <view class="header-overlay">
        <view class="header-content">
          <text class="page-title">声像文藏</text>
          <text class="page-subtitle">数字化文化遗产典藏平台</text>
          <text class="page-desc">传承千年文脉，守护文化记忆</text>
        </view>
      </view>
    </view>

    <!-- 功能板块导航 -->
    <view class="section-nav">
      <view class="nav-item ancient-books"
            @click="navigateToSection('ancient-books')">
        <view class="nav-icon">
          <image src="/static/icons/ancient-book.png"
                 mode="aspectFit"></image>
        </view>
        <view class="nav-content">
          <text class="nav-title">古籍典藏</text>
          <text class="nav-desc">珍贵古籍数字化保护</text>
          <text class="nav-features">图像扫描 • OCR识别 • AI标注</text>
        </view>
        <view class="nav-arrow">
          <image src="/static/icons/arrow-right.svg"
                 mode="aspectFit"></image>
        </view>
      </view>

      <view class="nav-item paintings"
            @click="navigateToSection('paintings')">
        <view class="nav-icon">
          <image src="/static/icons/painting.png"
                 mode="aspectFit"></image>
        </view>
        <view class="nav-content">
          <text class="nav-title">书画珍品</text>
          <text class="nav-desc">名人字画艺术传承</text>
          <text class="nav-features">高清图像 • 技法分析 • 风格溯源</text>
        </view>
        <view class="nav-arrow">
          <image src="/static/icons/arrow-right.svg"
                 mode="aspectFit"></image>
        </view>
      </view>

      <view class="nav-item archives"
            @click="navigateToSection('archives')">
        <view class="nav-icon">
          <image src="/static/icons/archive.png"
                 mode="aspectFit"></image>
        </view>
        <view class="nav-content">
          <text class="nav-title">档案故事</text>
          <text class="nav-desc">历史档案文献记录</text>
          <text class="nav-features">契约文书 • 地方志 • 历史背景</text>
        </view>
        <view class="nav-arrow">
          <image src="/static/icons/arrow-right.svg"
                 mode="aspectFit"></image>
        </view>
      </view>

      <view class="nav-item videos"
            @click="navigateToSection('videos')">
        <view class="nav-icon">
          <image src="/static/icons/video.png"
                 mode="aspectFit"></image>
        </view>
        <view class="nav-content">
          <text class="nav-title">影像文献</text>
          <text class="nav-desc">珍贵影音资料收藏</text>
          <text class="nav-features">历史影像 • 口述历史 • 纪录资料</text>
        </view>
        <view class="nav-arrow">
          <image src="/static/icons/arrow-right.svg"
                 mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 精选推荐 -->
    <view class="featured-section">
      <view class="section-header">
        <text class="section-title">精选推荐</text>
        <text class="section-subtitle">Curated Collection</text>
      </view>

      <swiper class="featured-swiper"
              :indicator-dots="true"
              :autoplay="true"
              :interval="5000"
              indicator-color="rgba(255,255,255,0.3)"
              indicator-active-color="#C8161E">
        <swiper-item v-for="(item, index) in featuredItems"
                     :key="index">
          <view class="featured-item"
                @click="viewFeaturedItem(item)">
            <image :src="item.image"
                   mode="aspectFill"
                   class="featured-image"></image>
            <view class="featured-overlay">
              <view class="featured-content">
                <text class="featured-category">{{ item.category }}</text>
                <text class="featured-title">{{ item.title }}</text>
                <text class="featured-desc">{{ item.description }}</text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 最新收藏 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最新收藏</text>
        <text class="more-btn"
              @click="viewMore">查看更多</text>
      </view>

      <view class="recent-grid">
        <view v-for="(item, index) in recentItems"
              :key="index"
              class="recent-item"
              @click="viewRecentItem(item)">
          <image :src="item.image"
                 mode="aspectFill"
                 class="recent-image"></image>
          <view class="recent-info">
            <text class="recent-title">{{ item.title }}</text>
            <text class="recent-type">{{ item.type }}</text>
            <text class="recent-date">{{ item.date }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ stats.ancientBooks }}</text>
          <text class="stat-label">古籍典藏</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.paintings }}</text>
          <text class="stat-label">书画珍品</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.archives }}</text>
          <text class="stat-label">档案故事</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.videos }}</text>
          <text class="stat-label">影像文献</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AudioVisualArchive',
  data () {
    return {
      featuredItems: [
        {
          id: 1,
          category: '古籍典藏',
          title: '明代地方志珍本',
          description: '记录明朝时期地方历史文化的珍贵文献',
          image: '/static/images/featured/ancient-book-1.jpg',
          type: 'ancient-books'
        },
        {
          id: 2,
          category: '书画珍品',
          title: '清代名家山水画卷',
          description: '清代著名画家的山水画作品，展现传统绘画技法',
          image: '/static/images/featured/painting-1.jpg',
          type: 'paintings'
        },
        {
          id: 3,
          category: '档案故事',
          title: '民国时期土地契约',
          description: '见证历史变迁的珍贵土地买卖契约文书',
          image: '/static/images/featured/archive-1.jpg',
          type: 'archives'
        }
      ],
      recentItems: [
        {
          id: 1,
          title: '宋代诗词手抄本',
          type: '古籍典藏',
          date: '2024-01-15',
          image: '/static/images/recent/book-1.jpg'
        },
        {
          id: 2,
          title: '明代花鸟画',
          type: '书画珍品',
          date: '2024-01-12',
          image: '/static/images/recent/painting-1.jpg'
        },
        {
          id: 3,
          title: '清代官府公文',
          type: '档案故事',
          date: '2024-01-10',
          image: '/static/images/recent/archive-1.jpg'
        },
        {
          id: 4,
          title: '1950年代纪录片',
          type: '影像文献',
          date: '2024-01-08',
          image: '/static/images/recent/video-1.jpg'
        }
      ],
      stats: {
        ancientBooks: '1,234',
        paintings: '567',
        archives: '890',
        videos: '123'
      }
    }
  },
  methods: {
    navigateToSection (section) {
      const routes = {
        'ancient-books': '/pages/culture/ancient-books',
        'paintings': '/pages/culture/paintings',
        'archives': '/pages/culture/archives',
        'videos': '/pages/culture/videos'
      }

      uni.navigateTo({
        url: routes[section]
      })
    },

    viewFeaturedItem (item) {
      uni.navigateTo({
        url: `/pages/culture/${item.type}-detail?id=${item.id}`
      })
    },

    viewRecentItem (item) {
      // 根据类型导航到相应的详情页
      uni.navigateTo({
        url: `/pages/culture/item-detail?id=${item.id}&type=${item.type}`
      })
    },

    viewMore () {
      uni.navigateTo({
        url: '/pages/culture/recent-collections'
      })
    }
  },

  onLoad () {
    // 加载数据
    this.loadData()
  },

  async loadData () {
    try {
      // 这里可以调用API获取真实数据
      console.log('加载声像文藏数据')
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }
}
</script>

<style scoped>
.archive-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 页面顶部 */
.archive-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(200, 22, 30, 0.8),
    rgba(200, 22, 30, 0.6)
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  text-align: center;
  color: white;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 28rpx;
  display: block;
  margin-bottom: 12rpx;
  opacity: 0.9;
}

.page-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 功能板块导航 */
.section-nav {
  padding: 40rpx 30rpx;
  background: white;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.nav-item:last-child {
  border-bottom: none;
}

.nav-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(200, 22, 30, 0.15);
}

.nav-icon image {
  width: 100%;
  height: 100%;
}

.nav-content {
  flex: 1;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.nav-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.nav-features {
  font-size: 22rpx;
  color: #c8161e;
  display: block;
}

.nav-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.nav-arrow image {
  width: 100%;
  height: 100%;
}

/* 精选推荐 */
.featured-section {
  margin-top: 20rpx;
  background: white;
  padding: 40rpx 30rpx;
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.featured-swiper {
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.featured-item {
  position: relative;
  height: 100%;
  border-radius: 16rpx;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  height: 100%;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
}

.featured-content {
  color: white;
}

.featured-category {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.featured-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.featured-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 最新收藏 */
.recent-section {
  margin-top: 20rpx;
  background: white;
  padding: 40rpx 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.more-btn {
  font-size: 26rpx;
  color: #c8161e;
}

.recent-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.recent-item {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.recent-item:active {
  transform: translateY(-4rpx);
}

.recent-image {
  width: 100%;
  height: 200rpx;
}

.recent-info {
  padding: 20rpx;
}

.recent-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-type {
  font-size: 22rpx;
  color: #c8161e;
  display: block;
  margin-bottom: 6rpx;
}

.recent-date {
  font-size: 22rpx;
  color: #999;
}

/* 数据统计 */
.stats-section {
  margin: 20rpx 30rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, #c8161e, #e03a44);
  border-radius: 16rpx;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 适配深色主题 */
@media (prefers-color-scheme: dark) {
  .archive-container {
    background: #1a1a1a;
  }

  .section-nav,
  .featured-section,
  .recent-section {
    background: #2a2a2a;
  }

  .nav-title,
  .section-title {
    color: #e0e0e0;
  }

  .nav-desc {
    color: #a0a0a0;
  }
}
</style> 