from fastapi import HTTPException, status


class CityApiException(HTTPException):
    """城市API服务的基础异常类"""
    def __init__(
        self,
        status_code: int,
        error_code: str,
        message: str,
    ):
        self.error_code = error_code
        self.message = message
        super().__init__(status_code=status_code, detail={"error_code": error_code, "message": message})


class ResourceNotFoundException(CityApiException):
    """资源未找到异常"""
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="RESOURCE_NOT_FOUND",
            message=f"{resource_type} with id {resource_id} not found"
        )


class InvalidParameterException(CityApiException):
    """参数无效异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="INVALID_PARAMETER",
            message=message
        )


class DatabaseException(CityApiException):
    """数据库异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="DATABASE_ERROR",
            message=message
        )


class MinioNotAvailableError(CityApiException):
    """Minio服务不可用异常"""
    def __init__(self, message: str = "图片服务暂时不可用"):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="MINIO_NOT_AVAILABLE",
            message=message
        )