from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


# 基础模式
class HeritageTagsResponse(BaseModel):
    """文化遗产标签响应"""
    tags: List[str] = Field(..., description="标签列表")


# 地点相关模式
class HeritagePlaceBase(BaseModel):
    """地点基础模式"""
    place_name: str = Field(..., min_length=1, max_length=100, description="地点名称")
    place_desc: Optional[str] = Field(None, max_length=255, description="地点描述")
    header_bg_image: Optional[str] = Field(None, description="头部背景图片URL或base64数据")
    introduction: Optional[str] = Field(None, description="简介")
    footer_text: Optional[str] = Field(None, max_length=500, description="底部文本")
    province_id: Optional[int] = Field(None, description="省份ID")
    city_id: Optional[int] = Field(None, description="城市ID")
    district_id: Optional[int] = Field(None, description="区县ID")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")


class HeritagePlaceCreate(HeritagePlaceBase):
    """创建地点请求"""
    pass


class HeritagePlaceUpdate(BaseModel):
    """更新地点请求"""
    place_name: Optional[str] = Field(None, min_length=1, max_length=100, description="地点名称")
    place_desc: Optional[str] = Field(None, max_length=255, description="地点描述")
    header_bg_image: Optional[str] = Field(None, description="头部背景图片URL或base64数据")
    introduction: Optional[str] = Field(None, description="简介")
    footer_text: Optional[str] = Field(None, max_length=500, description="底部文本")
    is_active: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序")


class HeritagePlaceResponse(HeritagePlaceBase):
    """地点响应"""
    id: int = Field(..., description="地点ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 时间轴相关模式
class TimelineItemBase(BaseModel):
    """时间轴项目基础模式"""
    period: str = Field(..., min_length=1, max_length=100, description="时期名称")
    year: str = Field(..., min_length=1, max_length=100, description="年份范围")
    title: str = Field(..., min_length=1, max_length=200, description="标题")
    description: str = Field(..., min_length=1, description="描述")
    image: Optional[str] = Field(None, max_length=500, description="主图片URL")
    has_detail: bool = Field(False, description="是否有详细内容")
    detail: Optional[str] = Field(None, description="详细内容（HTML格式）")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    heritage_tags: Optional[List[str]] = Field(None, description="文化遗产标签")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")


class TimelineItemCreate(TimelineItemBase):
    """创建时间轴项目请求"""
    pass  # place_id 从URL路径参数获取，不需要在请求体中


class TimelineItemUpdate(BaseModel):
    """更新时间轴项目请求"""
    period: Optional[str] = Field(None, min_length=1, max_length=100, description="时期名称")
    year: Optional[str] = Field(None, min_length=1, max_length=100, description="年份范围")
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="标题")
    description: Optional[str] = Field(None, min_length=1, description="描述")
    image: Optional[str] = Field(None, max_length=500, description="主图片URL")
    has_detail: Optional[bool] = Field(None, description="是否有详细内容")
    detail: Optional[str] = Field(None, description="详细内容（HTML格式）")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    heritage_tags: Optional[List[str]] = Field(None, description="文化遗产标签")
    is_active: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序")


class TimelineItemResponse(TimelineItemBase):
    """时间轴项目响应"""
    id: int = Field(..., description="项目ID")
    place_id: int = Field(..., description="关联地点ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 文化遗产相关模式
class HeritageItemBase(BaseModel):
    """文化遗产项目基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="遗产名称")
    type: str = Field(..., min_length=1, max_length=100, description="遗产类型")
    brief: str = Field(..., min_length=1, description="简介")
    image: Optional[str] = Field(None, max_length=500, description="图片URL")
    detail_content: Optional[str] = Field(None, description="详细内容")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")


class HeritageItemCreate(HeritageItemBase):
    """创建文化遗产项目请求"""
    pass  # place_id 从URL路径参数获取，不需要在请求体中


class HeritageItemUpdate(BaseModel):
    """更新文化遗产项目请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="遗产名称")
    type: Optional[str] = Field(None, min_length=1, max_length=100, description="遗产类型")
    brief: Optional[str] = Field(None, min_length=1, description="简介")
    image: Optional[str] = Field(None, max_length=500, description="图片URL")
    detail_content: Optional[str] = Field(None, description="详细内容")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    is_active: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序")


class HeritageItemResponse(HeritageItemBase):
    """文化遗产项目响应"""
    id: int = Field(..., description="项目ID")
    place_id: int = Field(..., description="关联地点ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 城市记忆相关模式
class MemoryItemBase(BaseModel):
    """城市记忆项目基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="记忆标题")
    year: str = Field(..., min_length=1, max_length=50, description="年份")
    image: Optional[str] = Field(None, max_length=500, description="图片URL")
    description: Optional[str] = Field(None, description="简要描述")
    detail_content: Optional[str] = Field(None, description="详细内容（HTML格式）")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")


class MemoryItemCreate(MemoryItemBase):
    """创建城市记忆项目请求"""
    pass  # place_id 从URL路径参数获取，不需要在请求体中


class MemoryItemUpdate(BaseModel):
    """更新城市记忆项目请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="记忆标题")
    year: Optional[str] = Field(None, min_length=1, max_length=50, description="年份")
    image: Optional[str] = Field(None, max_length=500, description="图片URL")
    description: Optional[str] = Field(None, description="简要描述")
    detail_content: Optional[str] = Field(None, description="详细内容（HTML格式）")
    detail_images: Optional[List[str]] = Field(None, description="详细图片URLs")
    is_active: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序")


class MemoryItemResponse(MemoryItemBase):
    """城市记忆项目响应"""
    id: int = Field(..., description="项目ID")
    place_id: int = Field(..., description="关联地点ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 完整页面数据响应
class HeritagePageResponse(BaseModel):
    """文化遗产页面完整数据响应"""
    place_info: HeritagePlaceResponse = Field(..., description="地点信息")
    timeline_data: List[TimelineItemResponse] = Field(..., description="时间轴数据")
    heritage_data: List[HeritageItemResponse] = Field(..., description="文化遗产数据")
    memory_data: List[MemoryItemResponse] = Field(..., description="城市记忆数据")


# 列表响应
class HeritagePlaceListResponse(BaseModel):
    """地点列表响应"""
    places: List[HeritagePlaceResponse] = Field(..., description="地点列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")


class TimelineItemListResponse(BaseModel):
    """时间轴项目列表响应"""
    items: List[TimelineItemResponse] = Field(..., description="时间轴项目列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")


class HeritageItemListResponse(BaseModel):
    """文化遗产项目列表响应"""
    items: List[HeritageItemResponse] = Field(..., description="文化遗产项目列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")


class MemoryItemListResponse(BaseModel):
    """城市记忆项目列表响应"""
    items: List[MemoryItemResponse] = Field(..., description="城市记忆项目列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量") 