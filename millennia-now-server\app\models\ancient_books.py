from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base

class AncientBook(Base):
    """古籍典藏主表 - 一部古籍的基本信息"""
    __tablename__ = "ancient_books"

    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="古籍ID")
    title = Column(String(200), nullable=False, index=True, comment="古籍名称")
    author = Column(String(100), nullable=True, index=True, comment="责任者")
    dynasty = Column(String(50), nullable=True, index=True, comment="朝代")
    
    # 版本信息
    edition = Column(Text, nullable=True, comment="版本项")
    publication = Column(Text, nullable=True, comment="出版发行项")
    bibliographic_note = Column(Text, nullable=True, comment="版本书目史注")
    
    # 分类编号
    sibu_category = Column(String(10), nullable=True, index=True, comment="四部分类号（A经部、B史部、C子部、D集部）")
    rare_book_number = Column(String(50), nullable=True, unique=True, index=True, comment="善本书号")
    
    # 内容描述
    description = Column(Text, nullable=True, comment="简介")
    content_summary = Column(Text, nullable=True, comment="内容摘要")
    keywords = Column(String(500), nullable=True, comment="关键词")
    
    # 物理信息（总体信息）
    total_volumes = Column(Integer, nullable=True, comment="总册数")
    size_info = Column(String(100), nullable=True, comment="尺寸信息")
    preservation_status = Column(String(50), nullable=True, comment="保存状态")
    
    # 基础数字化信息
    cover_image = Column(String(500), nullable=True, comment="封面图片URL")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览次数")
    favorite_count = Column(Integer, default=0, comment="收藏次数")
    
    # 管理信息
    status = Column(String(20), default="active", comment="状态（active/inactive/archived）")
    visibility = Column(String(20), default="public", comment="可见性（public/researcher）")
    created_by = Column(Integer, nullable=True, comment="创建者ID")
    updated_by = Column(Integer, nullable=True, comment="更新者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    volumes = relationship("AncientBookVolume", back_populates="book", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<AncientBook(id={self.id}, title='{self.title}', author='{self.author}')>"
    
    @property
    def is_rare_book(self):
        """是否为善本"""
        return self.rare_book_number is not None
    
    @property
    def sibu_category_name(self):
        """四部分类名称"""
        category_map = {
            'A': '经部',
            'B': '史部', 
            'C': '子部',
            'D': '集部'
        }
        return category_map.get(self.sibu_category, '未分类')
    
    @property
    def available_volumes_count(self):
        """可用册数"""
        return len([v for v in self.volumes if v.status == 'active'])
    
    @property
    def total_pages(self):
        """总页数"""
        return sum(v.total_pages or 0 for v in self.volumes if v.status == 'published')


# AncientBookVolume 模型已移动到 ancient_book_volumes.py 文件中


class UserBookVolumeFavorite(Base):
    """用户卷册收藏表"""
    __tablename__ = "user_book_volume_favorites"
    
    id = Column(Integer, primary_key=True, index=True, comment="收藏ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    volume_id = Column(Integer, ForeignKey("ancient_book_volumes.id"), nullable=False, comment="卷册ID")
    created_at = Column(DateTime, default=func.now(), comment="收藏时间")
    
    # 关联关系
    volume = relationship("AncientBookVolume")
    
    def __repr__(self):
        return f"<UserBookVolumeFavorite(user_id={self.user_id}, volume_id={self.volume_id})>"


class BookVolumeViewHistory(Base):
    """卷册浏览历史表"""
    __tablename__ = "book_volume_view_history"
    
    id = Column(Integer, primary_key=True, index=True, comment="浏览记录ID")
    user_id = Column(Integer, nullable=True, comment="用户ID（可为空，支持匿名浏览）")
    volume_id = Column(Integer, ForeignKey("ancient_book_volumes.id"), nullable=False, comment="卷册ID")
    view_time = Column(DateTime, default=func.now(), comment="浏览时间")
    view_duration = Column(Integer, nullable=True, comment="浏览时长（秒）")
    last_page = Column(Integer, nullable=True, comment="最后浏览的页码")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 关联关系
    volume = relationship("AncientBookVolume")
    
    def __repr__(self):
        return f"<BookVolumeViewHistory(volume_id={self.volume_id}, view_time={self.view_time})>" 