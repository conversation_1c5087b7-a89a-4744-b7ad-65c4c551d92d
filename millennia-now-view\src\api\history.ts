import axios from 'axios';
import { getBaseURL } from '../config/api';

// API基础URL - 使用统一配置
const BASE_URL = getBaseURL();

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 历史文脉项目接口（基于服务器的TimelineItem）
export interface HistoryItem {
  id: number;
  place_id: number;
  period: string;
  year: string;
  title: string;
  description: string;
  image?: string;
  has_detail: boolean;
  detail?: string;
  detail_images?: string[];
  heritage_tags?: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 历史文脉数据响应接口（基于服务器的TimelineItemListResponse）
export interface HistoryResponse {
  items: HistoryItem[];
  total: number;
  page: number;
  page_size: number;
}

/**
 * 获取模拟历史文脉数据
 * 当真实API调用失败时使用
 */
export const getMockHistoryData = (): { items: any[] } => {
  return {
    items: [
      {
        id: 1,
        title: '古代北京城的建立',
        description: '北京最早的城市形态可以追溯到西周时期，当时称为蓟城。',
        image: 'https://picsum.photos/seed/101/800/600',
        year: '公元前11世纪',
        period: '远古时期',
        type: '历史遗迹',
        source: '历史文献',
        content: '北京的历史可以追溯到3000年前。西周时期，周武王封召公奭于此，为蓟国，建都蓟城，这是北京建城的开始。战国时期属燕国。秦统一中国后，秦始皇设立广阳郡，治所亦在蓟县。汉朝时为幽州蓟县。'
      },
      {
        id: 2,
        title: '元大都的建设',
        description: '1271年，忽必烈定国号为元，建都大都（今北京），开始了北京作为全国政治中心的历史。',
        image: 'https://picsum.photos/seed/102/800/600',
        year: '1271年',
        period: '元朝',
        type: '古都',
        source: '元史',
        content: '元世祖忽必烈建国后，决定迁都燕京（今北京），并于1267年至1293年主持修建了规模宏大的元大都。元大都城呈方形，有城门11座，南北长约2300多米，东西宽约1900余米。城内街道交织成棋盘状，城中有宫城、太庙、社稷坛等重要建筑。'
      },
      {
        id: 3,
        title: '明清北京城的发展',
        description: '明永乐帝朱棣迁都北京，清朝沿用明代北京城格局，进一步完善了城市布局。',
        image: 'https://picsum.photos/seed/103/800/600',
        year: '1420年',
        period: '明清时期',
        type: '古都',
        source: '明史、清史稿',
        content: '明朝初期，明太祖朱元璋建都南京。1402年，朱棣即位，次年开始营建北京宫殿，1420年迁都北京。明清时期的北京城由内城和外城组成，内城又称"紫禁城"，是皇家宫殿所在地。清朝入关后沿用了明代北京城的基本格局，并进一步扩建和美化，使北京城成为世界上保存最完整、规模最大的古代城市规划体系。'
      },
      {
        id: 4,
        title: '近代北京的变迁',
        description: '1911年辛亥革命后，北京经历了从帝国首都到民国政治中心的转变。',
        image: 'https://picsum.photos/seed/104/800/600',
        year: '1911-1949年',
        period: '民国时期',
        type: '近代史迹',
        source: '民国档案',
        content: '1911年辛亥革命爆发，次年清帝退位，中华民国成立。1928年，国民政府定都南京，北京更名为北平。这一时期的北京逐渐由封建帝国的政治中心转变为文化教育中心，北京大学、清华大学等高等学府在此发展壮大。同时，西方文化开始大量传入，使北京的城市风貌和市民生活都发生了深刻变化。'
      },
      {
        id: 5,
        title: '现代北京的发展',
        description: '1949年新中国成立，北京重新成为首都，迎来了现代化建设的新纪元。',
        image: 'https://picsum.photos/seed/105/800/600',
        year: '1949年至今',
        period: '当代',
        type: '现代发展',
        source: '当代史料',
        content: '1949年10月1日，中华人民共和国中央人民政府在北京正式成立，北平恢复北京名称，并定为首都。此后，北京开始了大规模的现代化建设，先后建成了人民大会堂、国家博物馆等标志性建筑，举办了2008年奥运会等国际盛事，城市面貌发生了翻天覆地的变化。如今的北京，既是一座具有悠久历史文化的古都，也是一个充满现代活力的国际大都市。'
      }
    ]
  };
};

/**
 * 获取历史文脉列表
 * @param params 查询参数
 */
export const fetchHistoryItems = (params = {}) => {
  return api.get('/history/items', { params });
};

/**
 * 获取历史文脉详情
 * @param id 历史文脉ID
 */
export const fetchHistoryDetail = (id: number | string) => {
  return api.get(`/history/items/${id}`);
};

/**
 * 获取历史时间轴数据
 */
export const fetchHistoryTimeline = () => {
  return api.get('/history/timeline');
};

/**
 * 获取历史地点数据
 */
export const fetchHistoryPlaces = () => {
  return api.get('/history/places');
};

/**
 * 获取真实历史文脉数据（使用服务器的timeline API）
 * @param params 查询参数
 */
export async function getHistoryData(params: {
  province_id?: number;
  city_id?: number;
  district_id?: number;
  page?: number;
  page_size?: number;
}): Promise<HistoryResponse> {
  try {
    // 首先获取地点信息
    const placeResponse = await axios.get(`${BASE_URL}/heritage/places/by-region`, { 
      params: {
        province_id: params.province_id,
        city_id: params.city_id,
        district_id: params.district_id
      }
    });
    
    // 获取该地点的时间轴数据（历史文脉）
    const placeId = placeResponse.data.id;
    const timelineResponse = await axios.get(`${BASE_URL}/heritage/places/${placeId}/timeline`, {
      params: {
        page: params.page || 1,
        page_size: params.page_size || 50
      }
    });
    
    return {
      items: timelineResponse.data.items,
      total: timelineResponse.data.total,
      page: timelineResponse.data.page,
      page_size: timelineResponse.data.page_size
    };
  } catch (error) {
    console.error('获取历史文脉数据失败:', error);
    // 如果API调用失败，返回模拟数据
    const mockData = getMockHistoryData();
    
    // 将模拟数据转换为符合HistoryItem接口的格式
    const historyItems: HistoryItem[] = mockData.items.map((item: any, index: number) => ({
      id: item.id,
      place_id: 1, // 默认地点ID
      period: item.period,
      year: item.year,
      title: item.title,
      description: item.description,
      image: item.image,
      has_detail: true,
      detail: item.content,
      detail_images: [],
      heritage_tags: [item.type],
      is_active: true,
      sort_order: index,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    return {
      items: historyItems,
      total: historyItems.length,
      page: 1,
      page_size: historyItems.length
    };
  }
}

/**
 * 获取历史文脉项目详情
 * @param id 项目ID
 */
export async function getHistoryItemDetail(id: number): Promise<HistoryItem | null> {
  try {
    // 使用服务器的timeline API获取详情
    const response = await axios.get(`${BASE_URL}/heritage/timeline/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取历史文脉项目详情失败:', error);
    
    const mockData = getMockHistoryData();
    const item = mockData.items.find(item => item.id === id);
    
    if (!item) return null;
    
    // 将模拟数据转换为符合HistoryItem接口的格式
    return {
      id: item.id,
      place_id: 1, // 默认地点ID
      period: item.period,
      year: item.year,
      title: item.title,
      description: item.description,
      image: item.image,
      has_detail: true,
      detail: item.content,
      detail_images: [],
      heritage_tags: [item.type],
      is_active: true,
      sort_order: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
}

export default api; 