// 行政区划API接口
// 导入统一的API配置
import { getBaseURL } from '@/config/api'

const BASE_URL = getBaseURL()

// 省份信息接口
export interface Province {
  province_id: number
  name: string
  abbr?: string
  alias?: string
  acronym?: string
}

// 城市信息接口
export interface City {
  province_id: number
  city_id: number
  name: string
  acronym?: string
}

// 区县信息接口
export interface District {
  province_id: number
  city_id: number
  district_id: number
  name: string
  acronym?: string
  postal_code?: string
}

// 街道信息接口
export interface Street {
  province_id: number
  city_id: number
  district_id: number
  street_id: number
  name: string
  acronym?: string
  postal_code?: string
}

// 完整地址信息接口
export interface AddressInfo {
  province_id: number
  province_name: string
  city_id?: number
  city_name?: string
  district_id?: number
  district_name?: string
  street_id?: number
  street_name?: string
  postal_code?: string
}

// API响应接口
export interface ApiResponse<T> {
  total: number
  items: T[]
}

// 获取所有省份
export const getProvinces = async (): Promise<Province[]> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/provinces`,
      method: 'GET'
    })
    
    if (response.statusCode === 200) {
      return response.data.items
    } else {
      throw new Error('获取省份列表失败')
    }
  } catch (error) {
    console.error('获取省份列表失败:', error)
    throw error
  }
}

// 获取指定省份下的城市
export const getCities = async (provinceId: number): Promise<City[]> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/cities`,
      method: 'GET',
      data: {
        province_id: provinceId
      }
    })
    
    if (response.statusCode === 200) {
      return response.data.items
    } else {
      throw new Error('获取城市列表失败')
    }
  } catch (error) {
    console.error('获取城市列表失败:', error)
    throw error
  }
}

// 获取指定城市下的区县
export const getDistricts = async (provinceId: number, cityId: number): Promise<District[]> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/districts`,
      method: 'GET',
      data: {
        province_id: provinceId,
        city_id: cityId
      }
    })
    
    if (response.statusCode === 200) {
      return response.data.items
    } else {
      throw new Error('获取区县列表失败')
    }
  } catch (error) {
    console.error('获取区县列表失败:', error)
    throw error
  }
}

// 在指定省份下搜索区县
export const searchDistrictsByName = async (provinceId: number, districtName: string): Promise<District[]> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/districts/search`,
      method: 'GET',
      data: {
        province_id: provinceId,
        district_name: districtName
      }
    })
    
    if (response.statusCode === 200) {
      return response.data.items
    } else {
      throw new Error('搜索区县失败')
    }
  } catch (error) {
    console.error('搜索区县失败:', error)
    throw error
  }
}

// 获取指定区县下的街道
export const getStreets = async (provinceId: number, cityId: number, districtId: number): Promise<Street[]> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/streets`,
      method: 'GET',
      data: {
        province_id: provinceId,
        city_id: cityId,
        district_id: districtId
      }
    })
    
    if (response.statusCode === 200) {
      return response.data.items
    } else {
      throw new Error('获取街道列表失败')
    }
  } catch (error) {
    console.error('获取街道列表失败:', error)
    throw error
  }
}

// 获取完整地址信息
export const getAddressInfo = async (
  provinceId: number,
  cityId?: number,
  districtId?: number,
  streetId?: number
): Promise<AddressInfo> => {
  try {
    const params: any = { province_id: provinceId }
    if (cityId) params.city_id = cityId
    if (districtId) params.district_id = districtId
    if (streetId) params.street_id = streetId
    
    const response = await uni.request({
      url: `${BASE_URL}/address-info`,
      method: 'GET',
      data: params
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error('获取地址信息失败')
    }
  } catch (error) {
    console.error('获取地址信息失败:', error)
    throw error
  }
} 