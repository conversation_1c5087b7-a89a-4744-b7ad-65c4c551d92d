/**
 * API配置管理
 */

// 环境类型
export enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test'
}

// API配置接口
export interface ApiConfig {
  baseURL: string
  timeout: number
  imageProxyEnabled: boolean
  debugMode: boolean
}

// 不同环境的API配置 - 使用 /millennia-api 前缀
const API_CONFIGS: Record<Environment, ApiConfig> = {
  [Environment.DEVELOPMENT]: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://luckyzyn.top/millennia-api',
    timeout: 30000,
    imageProxyEnabled: import.meta.env.VITE_IMAGE_PROXY_ENABLED === 'true',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true'
  },
  [Environment.PRODUCTION]: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://luckyzyn.top/millennia-api',
    timeout: 30000,
    imageProxyEnabled: import.meta.env.VITE_IMAGE_PROXY_ENABLED === 'true',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true'
  },
  [Environment.TEST]: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://luckyzyn.top/millennia-api',
    timeout: 30000,
    imageProxyEnabled: true,
    debugMode: true
  }
}

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  const env = import.meta.env.MODE
  
  switch (env) {
    case 'production':
      return Environment.PRODUCTION
    case 'test':
      return Environment.TEST
    default:
      return Environment.DEVELOPMENT
  }
}

/**
 * 获取API配置
 */
export function getApiConfig(): ApiConfig {
  const currentEnv = getCurrentEnvironment()
  const config = API_CONFIGS[currentEnv]
  return config
}

/**
 * 获取基础URL
 */
export function getBaseURL(): string {
  return getApiConfig().baseURL
}

/**
 * 获取图片代理URL
 */
export function getImageProxyUrl(imagePath: string): string {
  if (!imagePath) return ''
  
  const config = getApiConfig()
  
  // 如果已经是完整的HTTP/HTTPS URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果禁用了图片代理，返回原始路径
  if (!config.imageProxyEnabled) {
    return imagePath
  }
  
  // 如果是相对路径，转换为代理URL
  const baseUrl = config.baseURL.replace('/millennia-api', '')
  return `${baseUrl}/proxy/image?url=${encodeURIComponent(imagePath)}`
}

/**
 * 是否启用调试模式
 */
export function isDebugMode(): boolean {
  return getApiConfig().debugMode
}

/**
 * 获取请求超时时间
 */
export function getTimeout(): number {
  return getApiConfig().timeout
}
