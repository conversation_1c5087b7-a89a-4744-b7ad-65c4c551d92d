<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 地点名称 -->
        <view class="form-item">
          <text class="form-label">地点名称 * (当前值: {{ formData.place_name }})</text>
          <input class="form-input"
                 v-model="formData.place_name"
                 placeholder="请输入地点名称"
                 maxlength="100" />
        </view>

        <!-- 地点描述 -->
        <view class="form-item">
          <text class="form-label">地点描述 (当前值: {{ formData.place_desc }})</text>
          <textarea class="form-textarea short"
                    v-model="formData.place_desc"
                    placeholder="请输入地点描述"
                    maxlength="255"
                    :auto-height="true"
                    :min-height="80" />
        </view>

        <!-- 区域信息 -->
        <view class="form-item"
              v-if="!isEdit">
          <text class="form-label">管理区域</text>
          <view class="region-display">
            <text class="region-text">{{ getRegionText() }}</text>
          </view>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">页面内容</view>

        <!-- 头部背景图 -->
        <view class="form-item">
          <text class="form-label">头部背景图</text>
          <view class="upload-tip">
            <text class="tip-text">建议尺寸：1200x600像素，支持JPG、PNG格式</text>
          </view>
          <view class="simple-image-upload">
            <view v-if="formData.header_bg_image"
                  class="image-preview">
              <image :src="formData.header_bg_image"
                     class="preview-img"
                     mode="aspectFill" />
              <view class="image-actions">
                <text class="action-btn"
                      @click="changeHeaderImage">更换</text>
                <text class="action-btn delete"
                      @click="removeHeaderImage">删除</text>
              </view>
            </view>
            <view v-else
                  class="upload-btn"
                  @click="uploadHeaderImage">
              <text class="upload-text">+ 选择图片</text>
            </view>
            <view v-if="uploading"
                  class="upload-progress">
              <text>上传中...</text>
            </view>
          </view>
        </view>

        <!-- 简介 -->
        <view class="form-item">
          <text class="form-label">简介</text>
          <textarea class="form-textarea"
                    v-model="formData.introduction"
                    placeholder="请输入地点简介，将显示在页面顶部"
                    maxlength="2000"
                    :show-count="true"
                    :auto-height="true"
                    :min-height="120" />
        </view>

        <!-- 底部文本 -->
        <view class="form-item">
          <text class="form-label">底部文本</text>
          <textarea class="form-textarea short"
                    v-model="formData.footer_text"
                    placeholder="请输入底部文本，如座右铭、标语等"
                    maxlength="500"
                    :auto-height="true"
                    :min-height="80" />
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">其他设置</view>

        <!-- 是否启用 -->
        <view class="form-item">
          <text class="form-label">启用状态</text>
          <switch :checked="formData.is_active"
                  @change="onActiveChange"
                  color="#007aff" />
          <text class="form-desc">关闭后，用户将无法访问该地点页面</text>
        </view>

        <!-- 排序 -->
        <view class="form-item">
          <text class="form-label">排序</text>
          <input class="form-input"
                 v-model.number="formData.sort_order"
                 placeholder="数字越小排序越靠前"
                 type="number" />
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-section">
        <button class="save-button"
                @click="savePlace"
                :disabled="isLoading">
          {{ isLoading ? (isEdit ? '保存中...' : '创建中...') : (isEdit ? '保存' : '创建') }}
        </button>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import {
  createHeritagePlace,
  updateHeritagePlace,
  getHeritagePlace,
  type HeritagePlaceCreateRequest,
  type HeritagePlaceUpdateRequest,
  type HeritagePlace,
} from '../../api/heritage'
import { getImageProxyUrl } from '../../utils/image'
import { uploadImage } from '../../utils/upload'

// 页面状态
const isEdit = ref(false)
const isLoading = ref(false)
const loadingText = ref('加载中...')
const placeId = ref<number | null>(null)
const uploading = ref(false)

// 用户信息和区域信息
const userInfo = ref({
  role: '',
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
})

const currentManageRegion = ref({
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
})

// 表单数据
const formData = ref({
  place_name: '',
  place_desc: '',
  header_bg_image: '',
  introduction: '',
  footer_text: '',
  province_id: 0,
  city_id: 0,
  district_id: 0,
  is_active: true,
  sort_order: 0,
})

// 初始化页面
const initPage = () => {
  const app = getApp()
  // 检查是否存在全局数据
  if (app.globalData && app.globalData.placeEditData) {
    // 从全局数据获取参数
    const options = app.globalData.placeEditData
    console.log('🔍 place-edit 接收到的全局数据:', options)

    // 获取用户信息
    userInfo.value = {
      role: options.role || '',
      province_name: options.province_name || '',
      province_id: options.province_id || '',
      city_name: options.city_name || '',
      city_id: options.city_id || '',
      district_name: options.district_name || '',
      district_id: options.district_id || '',
    }

    // 获取当前管理区域
    currentManageRegion.value = {
      province_name: options.manage_province_name || '',
      province_id: options.manage_province_id || '',
      city_name: options.manage_city_name || '',
      city_id: options.manage_city_id || '',
      district_name: options.manage_district_name || '',
      district_id: options.manage_district_id || '',
    }

    // 检查是否为编辑模式
    if (options.place_id) {
      isEdit.value = true
      placeId.value = parseInt(options.place_id)

      // 直接使用全局数据中的地点信息，避免重新加载
      console.log('🔍 使用全局数据填充表单')

      // 逐个设置属性，确保响应式更新
      formData.value.place_name = options.place_name || ''
      formData.value.place_desc = options.place_desc || ''
      formData.value.header_bg_image = options.header_bg_image || ''
      formData.value.introduction = options.introduction || ''
      formData.value.footer_text = options.footer_text || ''
      formData.value.province_id = parseInt(options.manage_province_id) || 0
      formData.value.city_id = parseInt(options.manage_city_id) || 0
      formData.value.district_id = parseInt(options.manage_district_id) || 0
      formData.value.is_active = true
      formData.value.sort_order = 0

      console.log('🔍 表单数据已填充:', formData.value)

      // 强制触发响应式更新
      nextTick(() => {
        console.log('🔍 nextTick后的表单数据:', formData.value)
      })
    } else {
      // 创建模式：设置区域信息
      const region = currentManageRegion.value
      formData.value.province_id = parseInt(region.province_id) || 0
      formData.value.city_id = parseInt(region.city_id) || 0
      formData.value.district_id = parseInt(region.district_id) || 0
    }

    // 使用后清除全局数据
    // app.globalData.placeEditData = null
  } else {
    // 没有全局数据，可能是直接访问页面，尝试从URL获取简单参数
    console.error('未找到全局参数数据')
    uni.showToast({
      title: '参数加载失败',
      icon: 'none',
    })

    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

// 添加onLoad生命周期函数
const onLoad = (options: any) => {
  console.log('🔍 place-edit onLoad 接收到的参数:', options)

  // 设置导航栏标题（先设置一个默认标题）
  uni.setNavigationBarTitle({
    title: '地点信息编辑',
  })
}

// 注册onLoad到组件实例
defineExpose({
  onLoad,
})

// 处理页面参数
const processPageParams = (options: any) => {
  // 获取用户信息
  userInfo.value = {
    role: options.role || '',
    province_name:
      typeof options.province_name === 'string'
        ? decodeURIComponent(options.province_name)
        : options.province_name || '',
    province_id: options.province_id || '',
    city_name:
      typeof options.city_name === 'string'
        ? decodeURIComponent(options.city_name)
        : options.city_name || '',
    city_id: options.city_id || '',
    district_name:
      typeof options.district_name === 'string'
        ? decodeURIComponent(options.district_name)
        : options.district_name || '',
    district_id: options.district_id || '',
  }

  // 获取当前管理区域
  currentManageRegion.value = {
    province_name:
      typeof options.manage_province_name === 'string'
        ? decodeURIComponent(options.manage_province_name)
        : options.manage_province_name || '',
    province_id: options.manage_province_id || '',
    city_name:
      typeof options.manage_city_name === 'string'
        ? decodeURIComponent(options.manage_city_name)
        : options.manage_city_name || '',
    city_id: options.manage_city_id || '',
    district_name:
      typeof options.manage_district_name === 'string'
        ? decodeURIComponent(options.manage_district_name)
        : options.manage_district_name || '',
    district_id: options.manage_district_id || '',
  }

  // 检查是否为编辑模式
  if (options.place_id) {
    isEdit.value = true
    placeId.value = parseInt(options.place_id)
    loadPlaceData()
  } else {
    // 创建模式：设置区域信息
    const region = currentManageRegion.value
    formData.value.province_id = parseInt(region.province_id) || 0
    formData.value.city_id = parseInt(region.city_id) || 0
    formData.value.district_id = parseInt(region.district_id) || 0
  }
}

// 加载地点数据（编辑模式）
const loadPlaceData = async () => {
  if (!placeId.value) return

  isLoading.value = true
  loadingText.value = '加载地点信息...'

  try {
    const place = await getHeritagePlace(placeId.value)
    if (place) {
      formData.value = {
        place_name: place.place_name,
        place_desc: place.place_desc || '',
        header_bg_image: place.header_bg_image || '',
        introduction: place.introduction || '',
        footer_text: place.footer_text || '',
        province_id: place.province_id || 0,
        city_id: place.city_id || 0,
        district_id: place.district_id || 0,
        is_active: place.is_active,
        sort_order: place.sort_order,
      }
    } else {
      uni.showToast({
        title: '地点信息不存在',
        icon: 'none',
      })
      uni.navigateBack()
    }
  } catch (error) {
    console.error('加载地点信息失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 获取区域文本
const getRegionText = () => {
  const region = currentManageRegion.value
  if (region.district_name) {
    return `${region.province_name} ${region.city_name} ${region.district_name}`
  } else if (region.city_name) {
    return `${region.province_name} ${region.city_name}`
  } else if (region.province_name) {
    return region.province_name
  }
  return '未选择区域'
}

// 头部背景图上传成功
const onHeaderImageUploadSuccess = (imageUrl: string) => {
  console.log('头部背景图上传成功:', imageUrl)
  formData.value.header_bg_image = imageUrl
  uni.showToast({
    title: '图片上传成功',
    icon: 'success',
    duration: 1500,
  })
}

// 头部背景图上传失败
const onHeaderImageUploadError = (error: string) => {
  console.error('头部背景图上传失败:', error)
  uni.showToast({
    title: error || '图片上传失败，请重试',
    icon: 'none',
    duration: 2000,
  })
}

// 上传头部背景图
const uploadHeaderImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      await uploadImageToServer(res.tempFilePaths[0])
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 更换头部背景图
const changeHeaderImage = () => {
  uploadHeaderImage()
}

// 删除头部背景图
const removeHeaderImage = () => {
  formData.value.header_bg_image = ''
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 上传图片到服务器 - 使用统一的上传工具
const uploadImageToServer = async (filePath: string) => {
  uploading.value = true

  try {
    // 使用统一的上传工具，自动处理URL配置
    const result = await uploadImage(filePath, 'place')

    if (result.success && result.url) {
      // 使用工具函数处理图片URL
      const imageUrl = getImageProxyUrl(result.url)

      formData.value.header_bg_image = imageUrl
      onHeaderImageUploadSuccess(imageUrl)
    } else {
      onHeaderImageUploadError(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    onHeaderImageUploadError('上传失败，请重试')
  } finally {
    uploading.value = false
  }
}

// 启用状态变化
const onActiveChange = (e: any) => {
  formData.value.is_active = e.detail.value
}

// 保存地点
const savePlace = async () => {
  // 验证必填字段
  if (!formData.value.place_name.trim()) {
    uni.showToast({
      title: '请输入地点名称',
      icon: 'none',
    })
    return
  }

  if (!isEdit.value && !formData.value.province_id) {
    uni.showToast({
      title: '请选择管理区域',
      icon: 'none',
    })
    return
  }

  isLoading.value = true
  loadingText.value = isEdit.value ? '保存中...' : '创建中...'

  try {
    if (isEdit.value && placeId.value) {
      // 编辑模式
      const updateData: HeritagePlaceUpdateRequest = {
        place_name: formData.value.place_name.trim(),
        place_desc: formData.value.place_desc.trim() || undefined,
        header_bg_image: formData.value.header_bg_image.trim() || null, // 明确传递 null 来清空图片
        introduction: formData.value.introduction.trim() || undefined,
        footer_text: formData.value.footer_text.trim() || undefined,
        is_active: formData.value.is_active,
        sort_order: formData.value.sort_order || 0,
      }

      console.log('保存时的表单数据:', formData.value)
      console.log('发送给后端的数据:', updateData)

      await updateHeritagePlace(placeId.value, updateData)

      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    } else {
      // 创建模式
      const createData: HeritagePlaceCreateRequest = {
        place_name: formData.value.place_name.trim(),
        place_desc: formData.value.place_desc.trim() || undefined,
        header_bg_image: formData.value.header_bg_image.trim() || undefined, // 创建时如果为空则不传递
        introduction: formData.value.introduction.trim() || undefined,
        footer_text: formData.value.footer_text.trim() || undefined,
        province_id: formData.value.province_id,
        city_id: formData.value.city_id || undefined,
        district_id: formData.value.district_id || undefined,
        is_active: formData.value.is_active,
        sort_order: formData.value.sort_order || 0,
      }

      await createHeritagePlace(createData)

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 保存成功后返回上一页，并通过URL参数标识需要刷新
    setTimeout(() => {
      uni.navigateBack({
        delta: 1,
      })
    }, 1500)
  } catch (error: any) {
    console.error('保存地点失败:', error)

    let errorMessage = '保存失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  // 在onMounted中处理数据初始化，确保页面完全加载
  console.log('🔍 place-edit onMounted 开始初始化')
  initPage()

  // 设置导航栏颜色
  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#C8161E',
  })

  // 更新导航栏标题
  uni.setNavigationBarTitle({
    title: isEdit.value ? '编辑地点信息' : '创建地点信息',
  })
})
</script>

<style>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 表单容器 */
.form-container {
  padding-bottom: 40rpx;
}

.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
  line-height: 1.6;
}

.form-textarea.short {
  min-height: 80rpx;
}

.form-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  margin-left: 20rpx;
}

/* 区域显示 */
.region-display {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1px solid #e9ecef;
}

.region-text {
  font-size: 28rpx;
  color: #666;
}

/* 上传提示 */
.upload-tip {
  margin-bottom: 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 简单图片上传 */
.simple-image-upload {
  width: 100%;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: 80rpx;
  text-align: center;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.8);
}

.upload-btn {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-text {
  color: #999;
  font-size: 28rpx;
}

.upload-progress {
  margin-top: 20rpx;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}

/* 通用图片上传 */
.image-upload {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

/* 保存按钮 */
.save-section {
  padding: 30rpx 20rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.save-button:disabled {
  background-color: #ccc;
  color: #999;
}

/* 加载中 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 