# 千年今朝项目环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ====== Minio对象存储配置 ======
MINIO_ENDPOINT=************:9000
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=12345678
MINIO_SECURE=false
MINIO_BUCKET_NAME=millennia-now

# 文件上传配置
MINIO_MAX_FILE_SIZE=10485760  # 10MB = 10 * 1024 * 1024
MINIO_ALLOWED_EXTENSIONS=[".jpg", ".jpeg", ".png", ".gif", ".webp"]

# 图片处理配置
MINIO_MAX_WIDTH=1920
MINIO_QUALITY=85

# ====== 数据库配置 ======
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=millennia_now
DB_CHARSET=utf8mb4

# 连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ====== Redis配置 ======
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 连接配置
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# ====== 安全配置 ======
SECURITY_SECRET_KEY=your-secret-key-change-this-in-production
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7

# 微信小程序配置
SECURITY_WECHAT_APP_ID=your-wechat-app-id
SECURITY_WECHAT_APP_SECRET=your-wechat-app-secret

# ====== 应用配置 ======
APP_NAME=千年今朝
APP_VERSION=1.0.0
APP_DEBUG=true

# API配置
APP_API_PREFIX=/api/v1
APP_DOCS_URL=/docs
APP_REDOC_URL=/redoc

# 跨域配置
APP_CORS_ORIGINS=["*"]
APP_CORS_METHODS=["*"]
APP_CORS_HEADERS=["*"]

# 日志配置
APP_LOG_LEVEL=INFO
APP_LOG_FILE=app.log 