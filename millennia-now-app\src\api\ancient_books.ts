// 古籍相关API接口
import { request } from '../utils/request'

// 基础配置
const BASE_URL = 'http://*************:8000/api/v1'

// 古籍数据结构
export interface AncientBookVolume {
  id: number
  book_id: number
  volume_number: number
  volume_title?: string
  page_count?: number
  start_page?: string
  end_page?: string
  cover_image?: string
  images?: string[]
  is_digitized: boolean
  digitization_quality?: string
  content_description?: string
  chapter_info?: any[]
  view_count: number
  favorite_count: number
  status: string
  availability: string
  created_at: string
  updated_at: string
  display_title: string
  image_list: string[]
  chapter_list: any[]
}

export interface AncientBook {
  id: number
  title: string
  author?: string
  dynasty?: string
  edition?: string
  publication?: string
  bibliographic_note?: string
  sibu_category?: string
  rare_book_number?: string
  description?: string
  content_summary?: string
  keywords?: string
  total_volumes?: number
  size_info?: string
  preservation_status?: string
  cover_image?: string
  view_count: number
  favorite_count: number
  status: string
  visibility: string
  created_by?: number
  updated_by?: number
  created_at: string
  updated_at: string
  is_rare_book: boolean
  sibu_category_name: string
  available_volumes_count: number
  total_pages: number
  volumes: AncientBookVolume[]
}

export interface AncientBookCreateRequest {
  title: string
  author?: string
  dynasty?: string
  edition?: string
  publication?: string
  bibliographic_note?: string
  sibu_category?: string
  rare_book_number?: string
  description?: string
  content_summary?: string
  keywords?: string
  total_volumes?: number
  size_info?: string
  preservation_status?: string
  cover_image?: string
  visibility?: string
  volumes?: AncientBookVolumeCreateRequest[]
}

export interface AncientBookUpdateRequest {
  title?: string
  author?: string
  dynasty?: string
  edition?: string
  publication?: string
  bibliographic_note?: string
  sibu_category?: string
  rare_book_number?: string
  description?: string
  content_summary?: string
  keywords?: string
  total_volumes?: number
  size_info?: string
  preservation_status?: string
  cover_image?: string
  visibility?: string
}

export interface AncientBookVolumeCreateRequest {
  volume_number: number
  volume_title?: string
  page_count?: number
  start_page?: string
  end_page?: string
  cover_image?: string
  images?: string[]
  is_digitized?: boolean
  digitization_quality?: string
  content_description?: string
  chapter_info?: any[]
}

export interface AncientBookListResponse {
  items: AncientBook[]
  total: number
  skip: number
  limit: number
}

// 统一请求方法
function ancientBookRequest<T = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  includeBinaryUrls?: boolean
}): Promise<T> {
  return new Promise((resolve, reject) => {
    // 构建完整URL
    const url = config.url.startsWith('http') 
      ? config.url 
      : `${BASE_URL}${config.url}`

    // 合并请求头
    const header: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.header
    }

    // 获取存储的token
    try {
      const token = uni.getStorageSync('access_token')
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }
    } catch (error) {
      console.warn('获取token失败:', error)
    }

    // 如果需要包含二进制图片URL，添加查询参数
    let requestData = config.data
    if (config.includeBinaryUrls && config.method === 'GET') {
      requestData = {
        ...requestData,
        include_binary_urls: true
      }
    }

    // 发起请求
    uni.request({
      url,
      method: config.method || 'GET',
      data: requestData,
      header,
      timeout: 30000,
      success: (response: any) => {
        console.log(`[${config.method || 'GET'}] ${url}:`, response)
        
        if (response.statusCode >= 200 && response.statusCode < 300) {
          resolve(response.data)
        } else {
          console.error('请求失败:', response)
          reject(new Error(`HTTP ${response.statusCode}: ${response.data?.message || '请求失败'}`))
        }
      },
      fail: (error: any) => {
        console.error(`[${config.method || 'GET'}] ${url} 请求失败:`, error)
        reject(new Error(error.errMsg || '网络请求失败'))
      }
    })
  })
}

/**
 * 获取古籍列表
 */
export async function getAncientBookList(params: {
  skip?: number
  limit?: number
  search?: string
  sibu_category?: string
  dynasty?: string
  is_rare?: boolean
  is_digitized?: boolean
} = {}): Promise<AncientBookListResponse | null> {
  try {
    const response = await ancientBookRequest<AncientBookListResponse>({
      url: '/ancient-books/',
      method: 'GET',
      data: params,
      includeBinaryUrls: true
    })
    return response
  } catch (error) {
    console.error('获取古籍列表失败:', error)
    return null
  }
}

/**
 * 获取古籍详情
 */
export async function getAncientBookDetail(bookId: number): Promise<AncientBook | null> {
  try {
    const response = await ancientBookRequest<AncientBook>({
      url: `/ancient-books/${bookId}`,
      method: 'GET',
      includeBinaryUrls: true
    })
    return response
  } catch (error) {
    console.error('获取古籍详情失败:', error)
    return null
  }
}

/**
 * 创建古籍
 */
export async function createAncientBook(data: AncientBookCreateRequest): Promise<AncientBook | null> {
  try {
    const response = await ancientBookRequest<AncientBook>({
      url: '/ancient-books/',
      method: 'POST',
      data
    })
    return response
  } catch (error) {
    console.error('创建古籍失败:', error)
    return null
  }
}

/**
 * 更新古籍
 */
export async function updateAncientBook(bookId: number, data: AncientBookUpdateRequest): Promise<AncientBook | null> {
  try {
    const response = await ancientBookRequest<AncientBook>({
      url: `/ancient-books/${bookId}`,
      method: 'PUT',
      data
    })
    return response
  } catch (error) {
    console.error('更新古籍失败:', error)
    return null
  }
}

/**
 * 删除古籍
 */
export async function deleteAncientBook(bookId: number): Promise<boolean> {
  try {
    await ancientBookRequest({
      url: `/ancient-books/${bookId}`,
      method: 'DELETE'
    })
    return true
  } catch (error) {
    console.error('删除古籍失败:', error)
    return false
  }
}

// 四部分类选项
export const SIBU_CATEGORIES = [
  { value: 'A', label: '经部' },
  { value: 'B', label: '史部' },
  { value: 'C', label: '子部' },
  { value: 'D', label: '集部' }
]

// 数字化质量选项
export const DIGITIZATION_QUALITY_OPTIONS = [
  { value: 'high', label: '高质量' },
  { value: 'medium', label: '中等质量' },
  { value: 'low', label: '低质量' }
]

// 保存状态选项
export const PRESERVATION_STATUS_OPTIONS = [
  { value: 'excellent', label: '完好' },
  { value: 'good', label: '良好' },
  { value: 'fair', label: '一般' },
  { value: 'poor', label: '较差' },
  { value: 'damaged', label: '损坏' }
]

// 可见性选项
export const VISIBILITY_OPTIONS = [
  { value: 'public', label: '公众可见' },
  { value: 'researcher', label: '研究员可见' }
] 