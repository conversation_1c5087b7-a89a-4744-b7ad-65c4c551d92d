// 文化遗产API接口
import axios from 'axios';
import { getBaseURL } from '../config/api';

// API基础URL - 使用统一配置
const BASE_URL = getBaseURL();

// 文化遗产地点信息接口
export interface HeritagePlace {
  id: number;
  place_name: string;
  place_desc?: string;
  header_bg_image?: string;
  introduction?: string;
  footer_text?: string;
  province_id?: number;
  city_id?: number;
  district_id?: number;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 文化遗产项目接口
export interface HeritageItem {
  id: number;
  title: string;
  description: string;
  image: string;
  year?: string;
  type: string;
  source?: string;
  content?: string;
  detail_images?: string[];
  // 添加文化传承项目特有字段
  place_id?: number;
  brief?: string;
  detail_content?: string;
  is_active?: boolean;
  sort_order?: number;
  created_at?: string;
  updated_at?: string;
}

// 完整页面数据接口
export interface HeritagePageData {
  place_info?: HeritagePlace;
  heritage_data?: HeritageItem[];
  timeline_data?: any[];
  memory_data?: any[];
}

// API响应接口
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

/**
 * 根据省市区ID获取完整的文化遗产页面数据
 * @param params 查询参数
 */
export async function getHeritagePageDataByRegion(params: {
  province_id?: number;
  city_id?: number;
  district_id?: number;
}): Promise<{ heritage_data: HeritageItem[] } | null> {
  try {
    // 1. 获取 place 信息
    const placeResponse = await axios.get(`${BASE_URL}/heritage/places/by-region`, {
      params: {
        province_id: params.province_id,
        city_id: params.city_id,
        district_id: params.district_id
      }
    });
    const placeId = placeResponse.data.id;
    if (!placeId) return { heritage_data: [] };

    // 2. 获取文化传承数据
    const heritageResponse = await axios.get(`${BASE_URL}/heritage/places/${placeId}/heritage`, {
      params: {
        page: 1,
        page_size: 50
      }
    });

    return { heritage_data: heritageResponse.data.items || [] };
  } catch (error) {
    console.error('获取文化传承数据失败:', error);
    return null;
  }
}

/**
 * 获取文化遗产数据
 */
export async function fetchHeritageItems(): Promise<ApiResponse<HeritageItem[]>> {
  try {
    // 实际项目中，这里应该调用真实的API
    // const response = await axios.get(`${BASE_URL}/heritage`);
    // return response.data;
    
    // 模拟API响应
    const mockItems: HeritageItem[] = Array(20).fill(null).map((_, index) => ({
      id: index + 1,
      title: `文化展品 ${index + 1}`,
      description: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。这件展品具有深厚的历史意义和艺术价值，是中华文明的重要组成部分。',
      image: `https://picsum.photos/seed/${index + 100}/800/600`,
      year: `${1000 + Math.floor(Math.random() * 1000)}年`,
      type: ['绘画', '雕塑', '陶瓷', '书法', '织物'][Math.floor(Math.random() * 5)],
      source: ['故宫博物院', '上海博物馆', '陕西历史博物馆', '南京博物院', '湖南省博物馆'][Math.floor(Math.random() * 5)]
    }));
    
    return {
      data: mockItems,
      status: 200
    };
  } catch (error) {
    console.error('获取文化遗产数据失败:', error);
    throw error;
  }
}

/**
 * 获取文化遗产项目详情
 * @param id 项目ID
 */
export async function getHeritageItemDetail(id: number): Promise<HeritageItem | null> {
  try {
    // 实际项目中，这里应该调用真实的API
    // const response = await axios.get(`${BASE_URL}/heritage/${id}`);
    // return response.data;
    
    // 模拟API响应
    const mockItem: HeritageItem = {
      id,
      title: `文化展品 ${id}`,
      description: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。这件展品具有深厚的历史意义和艺术价值，是中华文明的重要组成部分。',
      image: `https://picsum.photos/seed/${id + 100}/800/600`,
      year: `${1000 + Math.floor(Math.random() * 1000)}年`,
      type: ['绘画', '雕塑', '陶瓷', '书法', '织物'][Math.floor(Math.random() * 5)],
      source: ['故宫博物院', '上海博物馆', '陕西历史博物馆', '南京博物院', '湖南省博物馆'][Math.floor(Math.random() * 5)],
      content: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。这件展品具有深厚的历史意义和艺术价值，是中华文明的重要组成部分。通过这件展品，我们可以了解到中国传统文化的独特魅力和深厚底蕴。这件展品的制作工艺精湛，体现了古代工匠的智慧和创造力。它不仅是物质文化遗产，更是中华民族精神的载体。',
      detail_images: [
        `https://picsum.photos/seed/${id + 200}/800/600`,
        `https://picsum.photos/seed/${id + 201}/800/600`,
        `https://picsum.photos/seed/${id + 202}/800/600`
      ]
    };
    
    return mockItem;
  } catch (error) {
    console.error('获取文化遗产项目详情失败:', error);
    return null;
  }
} 