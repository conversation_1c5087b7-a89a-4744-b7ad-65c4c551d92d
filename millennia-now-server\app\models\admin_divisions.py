from sqlalchemy import Column, Integer, String, ForeignKeyConstraint, Index
from sqlalchemy.ext.declarative import declarative_base
from app.database.db import Base

class Province(Base):
    __tablename__ = "province"
    
    province_id = Column(Integer, primary_key=True, comment="省份编码")
    name = Column(String(50), nullable=False, comment="省份名称")
    abbr = Column(String(20), comment="省份简称")
    alias = Column(String(50), comment="省份别名")
    acronym = Column(String(10), comment="省份首字母缩写")

    def __repr__(self):
        return f"<Province(province_id='{self.province_id}', name='{self.name}')>"


class City(Base):
    __tablename__ = "city"
    
    province_id = Column(Integer, primary_key=True, comment="省份编码")
    city_id = Column(Integer, primary_key=True, comment="城市编码")
    name = Column(String(50), nullable=False, comment="城市名称")
    acronym = Column(String(10), comment="城市首字母缩写")
    
    __table_args__ = (
        ForeignKeyConstraint(['province_id'], ['province.province_id']),
        Index('idx_province_city', 'province_id', 'city_id'),
    )

    def __repr__(self):
        return f"<City(province_id='{self.province_id}', city_id='{self.city_id}', name='{self.name}')>"


class District(Base):
    __tablename__ = "district"
    
    province_id = Column(Integer, primary_key=True, comment="省份编码")
    city_id = Column(Integer, primary_key=True, comment="城市编码")
    district_id = Column(Integer, primary_key=True, comment="区县编码")
    name = Column(String(50), nullable=False, comment="区县名称")
    acronym = Column(String(10), comment="区县首字母缩写")
    postal_code = Column(String(6), comment="邮政编码")
    
    __table_args__ = (
        ForeignKeyConstraint(['province_id', 'city_id'], ['city.province_id', 'city.city_id']),
        Index('idx_province_city_district', 'province_id', 'city_id', 'district_id'),
    )

    def __repr__(self):
        return f"<District(province_id='{self.province_id}', city_id='{self.city_id}', district_id='{self.district_id}', name='{self.name}')>"


class Street(Base):
    __tablename__ = "street"
    
    province_id = Column(Integer, primary_key=True, comment="省份编码")
    city_id = Column(Integer, primary_key=True, comment="城市编码")
    district_id = Column(Integer, primary_key=True, comment="区县编码")
    street_id = Column(Integer, primary_key=True, comment="街道编码")
    name = Column(String(50), nullable=False, comment="街道名称")
    acronym = Column(String(10), comment="街道首字母缩写")
    postal_code = Column(String(6), comment="邮政编码")
    
    __table_args__ = (
        ForeignKeyConstraint(['province_id', 'city_id', 'district_id'], ['district.province_id', 'district.city_id', 'district.district_id']),
        Index('idx_province_city_district_street', 'province_id', 'city_id', 'district_id', 'street_id'),
    )

    def __repr__(self):
        return f"<Street(province_id='{self.province_id}', city_id='{self.city_id}', district_id='{self.district_id}', street_id='{self.street_id}', name='{self.name}')>"