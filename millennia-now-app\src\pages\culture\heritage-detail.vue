<template>
  <view class="heritage-detail-container">
    <!-- 加载状态 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>

    <!-- 详细内容 -->
    <view class="detail-content"
          v-else-if="heritageData">
      <!-- 头部图片 -->
      <view class="header-image-container"
            v-if="heritageData.image">
        <image :src="heritageData.image"
               mode="aspectFill"
               class="header-image" />
        <view class="image-overlay">
          <text class="heritage-title">{{ heritageData.title }}</text>
          <text class="heritage-type">{{ heritageData.type }}</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title"
              v-if="!heritageData.image">
          <text class="title-text">{{ heritageData.title }}</text>
          <text class="type-text">{{ heritageData.type }}</text>
        </view>

        <view class="brief-content">
          <text class="brief-text">{{ heritageData.brief }}</text>
        </view>
      </view>

      <!-- 详细内容 -->
      <view class="detail-section"
            v-if="heritageData.detail_content">
        <view class="section-header">
          <text class="section-title-text">详细介绍</text>
        </view>
        <view class="detail-text-content">
          <rich-text :nodes="heritageData.detail_content"></rich-text>
        </view>
      </view>

      <!-- 详细图片 -->
      <view class="images-section"
            v-if="heritageData.detail_images && heritageData.detail_images.length > 0">
        <view class="section-header">
          <text class="section-title-text">相关图片</text>
        </view>
        <view class="images-grid">
          <view class="image-item"
                v-for="(image, index) in heritageData.detail_images"
                :key="index"
                @click="previewImage(heritageData.detail_images, index)">
            <image :src="image"
                   mode="aspectFill"
                   class="grid-image" />
          </view>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <text class="update-time">最后更新：{{ formatDate(heritageData.updated_at) }}</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container"
          v-else>
      <text class="error-text">内容加载失败</text>
      <button class="retry-btn"
              @click="loadHeritageDetail">重试</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHeritageItemDetail, type HeritageItem } from '@/api/heritage'

// 页面状态
const isLoading = ref(true)
const loadingText = ref('加载中...')
const heritageData = ref<HeritageItem | null>(null)
const heritageId = ref<number | null>(null)

// 初始化页面
onMounted(async () => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}

  if (options.heritage_id) {
    heritageId.value = parseInt(options.heritage_id)
    await loadHeritageDetail()
  } else {
    isLoading.value = false
    uni.showToast({
      title: '缺少遗产ID',
      icon: 'none',
    })
  }

  // 设置导航栏
  uni.setNavigationBarTitle({
    title: heritageData.value?.title || '文化遗产详情',
  })
})

// 加载文化遗产详情
const loadHeritageDetail = async () => {
  if (!heritageId.value) {
    uni.showToast({
      title: '缺少遗产ID',
      icon: 'none',
    })
    return
  }

  try {
    isLoading.value = true
    loadingText.value = '加载中...'

    const data = await getHeritageItemDetail(heritageId.value)
    if (data) {
      heritageData.value = data

      // 更新导航栏标题
      uni.setNavigationBarTitle({
        title: data.title,
      })
    } else {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    }
  } catch (error: any) {
    console.error('加载文化遗产详情失败:', error)

    let errorMessage = '加载失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: current,
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}
</script>

<style scoped>
.heritage-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 头部图片 */
.header-image-container {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
}

.heritage-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.heritage-type {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 6rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: inline-block;
}

/* 基本信息 */
.info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  margin-bottom: 30rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.type-text {
  display: inline-block;
  font-size: 24rpx;
  color: #007aff;
  padding: 8rpx 20rpx;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 20rpx;
}

.brief-content {
  line-height: 1.8;
}

.brief-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
}

/* 详细内容 */
.detail-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.detail-text-content {
  line-height: 1.8;
  font-size: 28rpx;
  color: #333;
}

/* 图片区域 */
.images-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.image-item {
  aspect-ratio: 4/3;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.grid-image {
  width: 100%;
  height: 100%;
}

/* 底部信息 */
.footer-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 适配不同屏幕 */
@media (min-width: 750rpx) {
  .images-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 