"""
模块权限检查工具

用于验证用户对特定文化模块的管理权限
"""

from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import Callable

from app.models.users import User, UserRole, ModulePermission
from app.core.dependencies import get_current_user


def require_module_permission(module: ModulePermission):
    """
    创建一个依赖项，用于检查用户是否有指定模块的管理权限
    
    Args:
        module: 要检查的模块权限
        
    Returns:
        一个FastAPI依赖项函数
        
    Example:
        @router.post("/ancient-books/")
        async def create_ancient_book(
            data: CreateBookRequest,
            current_user: User = Depends(require_module_permission(ModulePermission.ANCIENT_BOOKS))
        ):
            # 只有有古籍管理权限的用户才能访问
            pass
    """
    def permission_dependency(current_user: User = Depends(get_current_user)) -> User:
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未登录"
            )
        
        if not current_user.has_module_permission(module):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"无权访问{get_module_name(module)}模块"
            )
        
        return current_user
    
    return permission_dependency


def check_module_permission(user: User, module: ModulePermission) -> bool:
    """
    检查用户是否有指定模块的管理权限
    
    Args:
        user: 用户对象
        module: 要检查的模块权限
        
    Returns:
        bool: 是否有权限
    """
    if not user:
        return False
    
    return user.has_module_permission(module)


def get_module_name(module: ModulePermission) -> str:
    """
    获取模块的中文名称
    
    Args:
        module: 模块权限枚举
        
    Returns:
        str: 模块中文名称
    """
    module_names = {
        ModulePermission.ANCIENT_BOOKS: "古籍典藏",
        ModulePermission.PAINTINGS: "书画珍品",
        ModulePermission.ARCHIVES: "档案故事",
        ModulePermission.VIDEOS: "影像文献"
    }
    return module_names.get(module, module.value)


def get_user_accessible_modules(user: User) -> list[str]:
    """
    获取用户可以访问的所有模块列表
    
    Args:
        user: 用户对象
        
    Returns:
        list[str]: 用户可访问的模块键名列表
    """
    if not user:
        return []
    
    accessible_modules = []
    for module in ModulePermission:
        if user.has_module_permission(module):
            accessible_modules.append(module.value)
    
    return accessible_modules


class ModulePermissionChecker:
    """模块权限检查器类"""
    
    def __init__(self, user: User):
        self.user = user
    
    def can_manage_ancient_books(self) -> bool:
        """检查是否可以管理古籍"""
        return check_module_permission(self.user, ModulePermission.ANCIENT_BOOKS)
    
    def can_manage_paintings(self) -> bool:
        """检查是否可以管理书画"""
        return check_module_permission(self.user, ModulePermission.PAINTINGS)
    
    def can_manage_archives(self) -> bool:
        """检查是否可以管理档案"""
        return check_module_permission(self.user, ModulePermission.ARCHIVES)
    
    def can_manage_videos(self) -> bool:
        """检查是否可以管理影像"""
        return check_module_permission(self.user, ModulePermission.VIDEOS)
    
    def can_manage_any_module(self) -> bool:
        """检查是否可以管理任何模块"""
        return self.user.has_any_management_permission()
    
    def get_accessible_modules(self) -> list[str]:
        """获取可访问的模块列表"""
        return get_user_accessible_modules(self.user)


# 便捷的依赖项函数，可以直接在路由中使用
require_ancient_books_permission = require_module_permission(ModulePermission.ANCIENT_BOOKS)
require_paintings_permission = require_module_permission(ModulePermission.PAINTINGS)
require_archives_permission = require_module_permission(ModulePermission.ARCHIVES)
require_videos_permission = require_module_permission(ModulePermission.VIDEOS)


def require_any_module_permission():
    """
    要求用户至少有一个模块的管理权限
    """
    def permission_dependency(current_user: User = Depends(get_current_user)) -> User:
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未登录"
            )
        
        if not current_user.has_any_management_permission():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无管理权限"
            )
        
        return current_user
    
    return permission_dependency 