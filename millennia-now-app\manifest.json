{"name": "千载·今知", "appid": "__UNI__5CBF086", "description": "文化传承小程序", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "Gallery": {}, "Geolocation": {}, "Share": {}, "Storage": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_LOGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-feature android:name=\"android.hardware.camera.autofocus\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "<uses-feature android:name=\"android.hardware.camera\" />", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "targetSdkVersion": 33, "minSdkVersion": 21, "compileSdkVersion": 33}, "ios": {"dSYMs": false}, "sdkConfigs": {"geolocation": {"system": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"appkey_ios": "", "appkey_android": ""}}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}}, "splashscreen": {"androidStyle": "default", "android": {"hdpi": "C:/Users/<USER>/Desktop/image/480.png", "xhdpi": "C:/Users/<USER>/Desktop/image/720.png", "xxhdpi": "C:/Users/<USER>/Desktop/image/1080.png"}}}}, "mp-weixin": {"appid": "wx7827d9f8bd827e3f", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "bigPackageSizeSupport": true, "ignoreDevUnusedFiles": true, "checkInvalidKey": true, "uploadWithSourceMap": true, "enhance": true, "showShadowRootInWxmlPanel": true, "compileHotReLoad": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXML": true, "minifyWXSS": true, "useStaticServer": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": ["typescript", "sass"], "ignoreUploadUnusedFiles": true}, "usingComponents": true, "lazyCodeLoading": "requiredComponents", "permission": {"scope.userLocation": {"desc": "您的位置信息将用于为您提供更精准的服务"}, "scope.userInfo": {"desc": "您的用户信息将用于提供个性化的文旅服务"}, "scope.camera": {"desc": "相机权限用于拍摄图片上传"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation", "<PERSON><PERSON><PERSON><PERSON>"], "optimization": {"subPackages": true}, "plugins": {}, "resizable": false, "functionalPages": false, "preloadRule": {"pages/culture/heritage": {"network": "all", "packages": ["__APP__"]}}, "darkmode": false, "sitemapLocation": "sitemap.json", "useExtendedLib": {"weui": true}, "securitySetting": {"enableScopedRun": true, "reportUnsupportedCall": true}, "performance": {"autoSplitChunk": true}, "openAbility": {"shareTimeline": true, "shareAppMessage": true}}, "vueVersion": "3"}