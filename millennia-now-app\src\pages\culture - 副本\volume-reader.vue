<template>
  <view class="volume-reader">
    <!-- 头部导航 -->
    <view class="header-section">
      <view class="nav-bar">
        <view class="nav-left"
              @click="goBack">
          <uni-icons type="left"
                     size="20"
                     color="#333" />
          <text class="nav-text">返回</text>
        </view>
        <view class="nav-center">
          <text class="nav-title">{{ volumeDetail?.display_title || '阅读器' }}</text>
          <text class="nav-subtitle">{{ currentPage }}/{{ totalPages }}</text>
        </view>
        <view class="nav-right">
          <uni-icons type="more"
                     size="24"
                     color="#333"
                     @click="showTools = !showTools" />
        </view>
      </view>
    </view>

    <!-- 阅读内容区域 -->
    <view class="reader-content"
          :class="{ 'tools-visible': showTools }">
      <swiper v-if="!loading && volumeDetail"
              :current="currentPageIndex"
              @change="onPageChange"
              :disable-touch="false"
              class="page-swiper">
        <swiper-item v-for="(page, index) in pages"
                     :key="index"
                     class="page-item">
          <scroll-view scroll-y="true"
                       class="page-scroll"
                       :scroll-top="scrollTop"
                       @scroll="onScroll">
            <view class="page-content">
              <image v-if="page.image_url"
                     :src="page.image_url"
                     mode="widthFix"
                     class="page-image"
                     @load="onImageLoad"
                     @error="onImageError" />
              <view v-else
                    class="page-placeholder">
                <uni-icons type="image"
                           size="60"
                           color="#ccc" />
                <text class="placeholder-text">页面图片加载失败</text>
              </view>

              <!-- 文字内容（如果有OCR识别结果） -->
              <view v-if="page.text_content"
                    class="page-text">
                <text class="text-content">{{ page.text_content }}</text>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>

      <!-- 加载状态 -->
      <view v-if="loading"
            class="loading-state">
        <uni-load-more status="loading" />
        <text class="loading-text">正在加载卷册内容...</text>
      </view>

      <!-- 错误状态 -->
      <view v-if="error"
            class="error-state">
        <image src="/static/images/error-image.svg"
               class="error-image" />
        <text class="error-text">加载失败，请重试</text>
        <button class="retry-btn"
                @click="loadVolumeDetail">重新加载</button>
      </view>
    </view>

    <!-- 工具栏 -->
    <view v-if="showTools"
          class="tools-panel">
      <view class="tools-content">
        <!-- 页面导航 -->
        <view class="tool-section">
          <text class="tool-title">页面导航</text>
          <view class="page-controls">
            <button class="control-btn"
                    @click="prevPage"
                    :disabled="currentPageIndex === 0">
              <uni-icons type="left"
                         size="16"
                         color="#fff" />
              <text>上一页</text>
            </button>

            <view class="page-input">
              <input v-model="pageInput"
                     type="number"
                     :placeholder="`第${currentPage}页`"
                     class="page-number-input"
                     @confirm="goToPage" />
              <text class="page-total">/ {{ totalPages }}</text>
            </view>

            <button class="control-btn"
                    @click="nextPage"
                    :disabled="currentPageIndex === totalPages - 1">
              <text>下一页</text>
              <uni-icons type="right"
                         size="16"
                         color="#fff" />
            </button>
          </view>
        </view>

        <!-- 阅读设置 -->
        <view class="tool-section">
          <text class="tool-title">阅读设置</text>
          <view class="reading-settings">
            <view class="setting-item">
              <text class="setting-label">亮度</text>
              <slider :value="brightness"
                      @change="setBrightness"
                      min="20"
                      max="100"
                      block-size="20"
                      backgroundColor="#e0e0e0"
                      activeColor="#007AFF"
                      class="setting-slider" />
              <text class="setting-value">{{ brightness }}%</text>
            </view>

            <view class="setting-item">
              <text class="setting-label">缩放</text>
              <view class="zoom-controls">
                <button class="zoom-btn"
                        @click="zoomOut">
                  <uni-icons type="minus"
                             size="16"
                             color="#666" />
                </button>
                <text class="zoom-value">{{ Math.round(zoomScale * 100) }}%</text>
                <button class="zoom-btn"
                        @click="zoomIn">
                  <uni-icons type="plus"
                             size="16"
                             color="#666" />
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 快捷功能 -->
        <view class="tool-section">
          <text class="tool-title">功能</text>
          <view class="quick-actions">
            <button class="action-btn"
                    @click="toggleBookmark">
              <uni-icons :type="isBookmarked ? 'star-filled' : 'star'"
                         size="20"
                         :color="isBookmarked ? '#FFD700' : '#666'" />
              <text>{{ isBookmarked ? '已书签' : '书签' }}</text>
            </button>

            <button class="action-btn"
                    @click="shareVolume">
              <uni-icons type="redo"
                         size="20"
                         color="#666" />
              <text>分享</text>
            </button>

            <button class="action-btn"
                    @click="showVolumeInfo">
              <uni-icons type="info"
                         size="20"
                         color="#666" />
              <text>信息</text>
            </button>
          </view>
        </view>
      </view>

      <view class="tools-close"
            @click="showTools = false">
        <uni-icons type="down"
                   size="20"
                   color="#666" />
      </view>
    </view>

    <!-- 底部状态栏 -->
    <view v-if="!showTools"
          class="status-bar">
      <view class="status-left">
        <text class="status-text">{{ volumeDetail?.display_title }}</text>
      </view>
      <view class="status-center">
        <text class="page-indicator">{{ currentPage }} / {{ totalPages }}</text>
      </view>
      <view class="status-right">
        <text class="reading-time">{{ formatReadingTime(readingTime) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'VolumeReader',
  setup () {
    const volumeDetail = ref(null)
    const pages = ref([])
    const loading = ref(false)
    const error = ref(false)
    const showTools = ref(false)
    const currentPageIndex = ref(0)
    const scrollTop = ref(0)
    const brightness = ref(80)
    const zoomScale = ref(1)
    const isBookmarked = ref(false)
    const pageInput = ref('')
    const readingTime = ref(0)
    const readingTimer = ref(null)

    // 计算属性
    const currentPage = computed(() => currentPageIndex.value + 1)
    const totalPages = computed(() => pages.value.length)

    // 模拟数据
    const mockVolumeDetail = {
      id: 1,
      display_title: '第1册 - 大学章句',
      page_count: 168,
      is_digitized: true,
      content_description: '包含《大学》原文及朱熹的章句注释，阐述修身齐家治国平天下的理念。'
    }

    // 生成模拟页面数据
    const generateMockPages = (count) => {
      const mockPages = []
      for (let i = 1; i <= count; i++) {
        mockPages.push({
          page_number: i,
          image_url: `/static/images/volume-page-${i}.jpg`,
          text_content: i % 5 === 0 ? `第${i}页内容示例：这里是古籍文字的OCR识别结果，包含了古代文献的原始内容。` : null
        })
      }
      return mockPages
    }

    // 方法
    const loadVolumeDetail = async () => {
      loading.value = true
      error.value = false
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))
        volumeDetail.value = mockVolumeDetail
        pages.value = generateMockPages(20) // 生成20页用于演示

        // 恢复阅读进度
        const savedProgress = uni.getStorageSync(`reading_progress_${volumeDetail.value.id}`)
        if (savedProgress) {
          currentPageIndex.value = savedProgress.page || 0
        }

        // 恢复书签状态
        const bookmarks = uni.getStorageSync('volume_bookmarks') || []
        isBookmarked.value = bookmarks.includes(volumeDetail.value.id)

      } catch (error) {
        console.error('加载卷册详情失败:', error)
        error.value = true
      } finally {
        loading.value = false
      }
    }

    const onPageChange = (e) => {
      currentPageIndex.value = e.detail.current
      scrollTop.value = 0
      saveReadingProgress()
    }

    const onScroll = (e) => {
      scrollTop.value = e.detail.scrollTop
    }

    const onImageLoad = () => {
      // 图片加载成功
    }

    const onImageError = () => {
      // 图片加载失败
    }

    const prevPage = () => {
      if (currentPageIndex.value > 0) {
        currentPageIndex.value--
        saveReadingProgress()
      }
    }

    const nextPage = () => {
      if (currentPageIndex.value < totalPages.value - 1) {
        currentPageIndex.value++
        saveReadingProgress()
      }
    }

    const goToPage = () => {
      const page = parseInt(pageInput.value)
      if (page >= 1 && page <= totalPages.value) {
        currentPageIndex.value = page - 1
        saveReadingProgress()
      }
      pageInput.value = ''
    }

    const setBrightness = (e) => {
      brightness.value = e.detail.value
      // 这里可以调用系统API设置屏幕亮度
    }

    const zoomIn = () => {
      if (zoomScale.value < 3) {
        zoomScale.value += 0.2
      }
    }

    const zoomOut = () => {
      if (zoomScale.value > 0.5) {
        zoomScale.value -= 0.2
      }
    }

    const toggleBookmark = () => {
      isBookmarked.value = !isBookmarked.value

      let bookmarks = uni.getStorageSync('volume_bookmarks') || []
      if (isBookmarked.value) {
        if (!bookmarks.includes(volumeDetail.value.id)) {
          bookmarks.push(volumeDetail.value.id)
        }
        uni.showToast({
          title: '添加书签成功',
          icon: 'success'
        })
      } else {
        bookmarks = bookmarks.filter(id => id !== volumeDetail.value.id)
        uni.showToast({
          title: '移除书签成功',
          icon: 'success'
        })
      }

      uni.setStorageSync('volume_bookmarks', bookmarks)
    }

    const shareVolume = () => {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `pages/culture/volume-reader?id=${volumeDetail.value.id}`,
        title: volumeDetail.value.display_title,
        summary: volumeDetail.value.content_description,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        }
      })
    }

    const showVolumeInfo = () => {
      uni.showModal({
        title: '卷册信息',
        content: `标题：${volumeDetail.value.display_title}\n总页数：${volumeDetail.value.page_count}\n简介：${volumeDetail.value.content_description}`,
        showCancel: false
      })
    }

    const saveReadingProgress = () => {
      if (volumeDetail.value) {
        const progress = {
          page: currentPageIndex.value,
          timestamp: Date.now()
        }
        uni.setStorageSync(`reading_progress_${volumeDetail.value.id}`, progress)
      }
    }

    const startReadingTimer = () => {
      readingTimer.value = setInterval(() => {
        readingTime.value++
      }, 1000)
    }

    const stopReadingTimer = () => {
      if (readingTimer.value) {
        clearInterval(readingTimer.value)
        readingTimer.value = null
      }
    }

    const formatReadingTime = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const goBack = () => {
      saveReadingProgress()
      uni.navigateBack()
    }

    // 生命周期
    onMounted(() => {
      // 获取传入的卷册ID
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const volumeId = currentPage.options?.id || 1

      loadVolumeDetail()
      startReadingTimer()
    })

    onUnmounted(() => {
      stopReadingTimer()
      saveReadingProgress()
    })

    return {
      volumeDetail,
      pages,
      loading,
      error,
      showTools,
      currentPageIndex,
      currentPage,
      totalPages,
      scrollTop,
      brightness,
      zoomScale,
      isBookmarked,
      pageInput,
      readingTime,
      loadVolumeDetail,
      onPageChange,
      onScroll,
      onImageLoad,
      onImageError,
      prevPage,
      nextPage,
      goToPage,
      setBrightness,
      zoomIn,
      zoomOut,
      toggleBookmark,
      shareVolume,
      showVolumeInfo,
      formatReadingTime,
      goBack
    }
  }
}
</script>

<style scoped>
.volume-reader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 头部导航 */
.header-section {
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  height: 88rpx;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 100rpx;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.nav-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.nav-subtitle {
  font-size: 22rpx;
  color: #666;
}

.nav-right {
  min-width: 100rpx;
  display: flex;
  justify-content: flex-end;
}

/* 阅读内容区域 */
.reader-content {
  flex: 1;
  position: relative;
  transition: all 0.3s ease;
}

.reader-content.tools-visible {
  filter: brightness(0.8);
}

.page-swiper {
  height: 100%;
}

.page-item {
  height: 100%;
}

.page-scroll {
  height: 100%;
}

.page-content {
  padding: 20rpx;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-image {
  width: 100%;
  max-width: 100%;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.page-placeholder {
  width: 100%;
  height: 800rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

.page-text {
  width: 100%;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.text-content {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  text-align: justify;
}

/* 工具栏 */
.tools-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 200;
  max-height: 70vh;
}

.tools-content {
  padding: 30rpx;
  max-height: calc(70vh - 100rpx);
  overflow-y: auto;
}

.tool-section {
  margin-bottom: 40rpx;
}

.tool-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
}

.control-btn:disabled {
  background: #ccc;
  color: #999;
}

.page-input {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
  justify-content: center;
}

.page-number-input {
  width: 120rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 24rpx;
}

.page-total {
  font-size: 24rpx;
  color: #666;
}

.reading-settings {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.setting-label {
  font-size: 26rpx;
  color: #333;
  min-width: 80rpx;
}

.setting-slider {
  flex: 1;
}

.setting-value {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  justify-content: center;
}

.zoom-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-value {
  font-size: 24rpx;
  color: #333;
  min-width: 80rpx;
  text-align: center;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 10rpx;
  background: #f8f9fa;
  border: none;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #333;
}

.tools-close {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e0e0e0;
}

/* 状态栏 */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid #e0e0e0;
}

.status-left,
.status-center,
.status-right {
  flex: 1;
}

.status-center {
  text-align: center;
}

.status-right {
  text-align: right;
}

.status-text,
.page-indicator,
.reading-time {
  font-size: 22rpx;
  color: #666;
}

/* 加载和错误状态 */
.loading-state,
.error-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
  display: block;
}

.error-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

.retry-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
</style> 