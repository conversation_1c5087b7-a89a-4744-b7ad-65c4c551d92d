<template>
  <view class="heritage-content-page">
    <!-- 顶部封面区域 -->
    <view class="cover-section">
      <image class="cover-image"
             :src="headerBgImage"
             mode="aspectFill"></image>
      <view class="cover-overlay"></view>
      <view class="cover-content">
        <text class="place-name">{{ placeName }}</text>
        <text class="place-desc">{{ placeDesc }}</text>
      </view>

    </view>

    <!-- 简介 -->
    <view class="intro-section"
          v-if="introduction">
      <text class="intro-text">{{ introduction }}</text>
    </view>

    <!-- 文化传承内容 -->
    <view class="modern-section">
      <view class="section-title">
        <text>当代文化传承</text>
      </view>

      <view class="heritage-list">
        <!-- 无数据提示 -->
        <view class="empty-tip"
              v-if="!heritageItems || heritageItems.length === 0">
          <text class="empty-text">暂无文化传承数据</text>
        </view>

        <view class="heritage-item"
              v-for="(item, index) in heritageItems"
              :key="index"
              @click="navigateToDetail(item.id)"
              :style="{ animationDelay: `${index * 0.2}s` }">
          <image :src="getImageSrc(item.image)"
                 mode="aspectFill"
                 class="heritage-image"
                 @error="onImageError"></image>
          <view class="heritage-info">
            <text class="heritage-title">{{ item.title }}</text>
            <text class="heritage-type">{{ item.type }}</text>
            <text class="heritage-brief">{{ item.brief }}</text>
          </view>

          <!-- 墨滴动画效果 -->
          <view class="ink-animation">
            <view class="ink-drop"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer"
          v-if="footerText">
      <text class="footer-text">{{ footerText }}</text>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="ink-loading">
        <view class="ink-circle"></view>
        <view class="ink-splash"></view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 动画背景元素 -->
    <view class="animated-bg">
      <view class="ink-wash"
            v-for="n in 5"
            :key="n"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { HeritageItem, getHeritagePageDataByRegion } from '../../api/heritage'
import { getStaticImageUrl, getImageProxyUrl } from '../../utils/image'
import { regionManager } from '../../store/modules/region'
import { getBaseURL } from '../../config/api'

// 页面状态
const isLoading = ref(true)
const heritageItems = ref<HeritageItem[]>([])

// 地点信息
const placeName = ref('')
const placeDesc = ref('')
const headerBgImage = ref(
  `${getBaseURL().replace('/api/', '')}/static/image/chuancheng.png`
)
const introduction = ref('')
const footerText = ref('')

// 初始化页面
onMounted(async () => {
  // 获取区域的文化遗产数据
  await loadRegionHeritageData()
})

// 加载特定区域的文化遗产列表
const loadRegionHeritageData = async () => {
  isLoading.value = true
  try {
    const regionIds = regionManager.currentRegionIds

    if (regionIds.provinceId) {
      const pageData = await getHeritagePageDataByRegion({
        province_id: regionIds.provinceId,
        city_id: regionIds.cityId,
        district_id: regionIds.districtId,
      })

      if (pageData) {
        // 设置地点信息
        placeName.value =
          pageData.place_info?.place_name ||
          regionManager.fullRegionName ||
          '文化传承'
        placeDesc.value = pageData.place_info?.place_desc || '探索传统文化之美'
        headerBgImage.value = pageData.place_info?.header_bg_image
          ? getImageProxyUrl(pageData.place_info.header_bg_image)
          : `${getBaseURL().replace('/api/', '')}/static/image/chuancheng.png`
        introduction.value = pageData.place_info?.introduction || ''
        footerText.value = pageData.place_info?.footer_text || ''

        // 设置文化遗产项列表，按 sort_order 从小到大排序
        if (pageData.heritage_data && pageData.heritage_data.length > 0) {
          heritageItems.value = pageData.heritage_data.sort(
            (a, b) => (a.sort_order || 0) - (b.sort_order || 0)
          )
        }
      }
    }
  } catch (error) {
    console.error('获取区域文化遗产数据失败:', error)
  } finally {
    isLoading.value = false

    // 延迟启动水墨动画，确保页面已经渲染
    setTimeout(() => {
      startInkAnimations()
    }, 500)
  }
}

// 启动水墨动画
const startInkAnimations = () => {
  // 水墨动画是通过CSS实现的，这里可以添加一些动态类或者其他控制逻辑
  console.log('水墨动画已启动')
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  return getStaticImageUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event: any) => {
  // 将图片源替换为错误占位图
  const target = event.target || event.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}

// 导航到具体文化遗产详情
const navigateToDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/culture/heritage-detail?heritage_id=${id}`,
  })
}
</script>

<style>
.heritage-content-page {
  background-color: #f9f9f9;
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 动画背景元素 */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.ink-wash {
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.5);
}

.ink-wash:nth-child(1) {
  top: 10%;
  left: 20%;
  animation: inkWash 15s ease-in-out 0s infinite;
}

.ink-wash:nth-child(2) {
  top: 50%;
  left: 80%;
  animation: inkWash 18s ease-in-out 2s infinite;
}

.ink-wash:nth-child(3) {
  top: 80%;
  left: 15%;
  animation: inkWash 20s ease-in-out 5s infinite;
}

.ink-wash:nth-child(4) {
  top: 30%;
  left: 60%;
  animation: inkWash 17s ease-in-out 7s infinite;
}

.ink-wash:nth-child(5) {
  top: 70%;
  left: 40%;
  animation: inkWash 22s ease-in-out 10s infinite;
}

@keyframes inkWash {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  40% {
    opacity: 0.5;
  }
  80% {
    opacity: 0;
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
}

/* 顶部封面区域 */
.cover-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
  animation: fadeIn 1.5s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.cover-image {
  width: 100%;
  height: 100%;
  transform: scale(1.1);
  animation: zoomOut 2s ease-out forwards;
}

@keyframes zoomOut {
  0% {
    transform: scale(1.1);
    filter: brightness(0.8) saturate(1.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
  }
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

.cover-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 1.5s ease-out forwards;
  animation-delay: 0.5s;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.place-name {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 10rpx;
}

.place-desc {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 简介样式 */
.intro-section {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 1.5s ease-out forwards;
  animation-delay: 0.8s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.intro-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  animation: inkFade 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.intro-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  position: relative;
  z-index: 1;
}

/* 文化传承区域样式 */
.modern-section {
  padding: 30rpx 20rpx;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 1s;
  position: relative;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  width: 0;
  height: 6rpx;
  background-color: #c8161e;
  border-radius: 3rpx;
  transform: translateX(-50%);
  animation: lineExpand 1.5s ease-out forwards;
  animation-delay: 1.5s;
}

@keyframes lineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 80rpx;
  }
}

/* 墨滴效果 */
.section-title::before {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(200, 22, 30, 0.7) 0%,
    rgba(200, 22, 30, 0) 70%
  );
  border-radius: 50%;
  transform: translateX(-50%);
  opacity: 0;
  z-index: 0;
  animation: inkDrop 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkDrop {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    width: 100rpx;
    height: 100rpx;
    opacity: 0;
  }
}

.heritage-list {
  padding: 0 20rpx;
}

.empty-tip {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 文化传承项目卡片样式 */
.heritage-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transform: translateY(30px);
  opacity: 0;
  animation: cardFadeIn 0.8s ease-out forwards;
  transition: transform 0.3s, box-shadow 0.3s;
}

.heritage-item:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

@keyframes cardFadeIn {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.heritage-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 30rpx;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.heritage-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.heritage-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.heritage-type {
  font-size: 24rpx;
  color: #c8161e;
  background-color: #fff0f0;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  display: inline-block;
  margin-bottom: 16rpx;
}

.heritage-brief {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 墨滴动画效果 */
.ink-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  overflow: hidden;
}

.ink-drop {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  opacity: 0;
  animation: inkSpread 3s ease-out infinite;
}

@keyframes inkSpread {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  30% {
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 60rpx 0;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 2s;
}

.footer-text {
  font-size: 28rpx;
  color: #999;
}

/* 水墨风格加载动画 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.ink-loading {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.ink-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 6rpx solid #f0f0f0;
  border-top: 6rpx solid #c8161e;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.ink-splash {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(
    circle,
    rgba(200, 22, 30, 0.7) 0%,
    rgba(200, 22, 30, 0) 70%
  );
  border-radius: 50%;
  animation: pulsate 1.5s ease-in-out infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulsate {
  0% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.2;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.2;
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  font-family: 'KaiTi', 'STKaiti', serif;
  letter-spacing: 2rpx;
}
</style>