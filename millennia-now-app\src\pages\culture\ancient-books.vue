<template>
  <view class="ancient-books-page">
    <!-- 头部搜索区域 -->
    <view class="header-section">
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search"
                     size="20"
                     color="#999" />
          <input v-model="searchQuery"
                 placeholder="搜索古籍名称、作者或关键词..."
                 class="search-input"
                 @input="onSearchInput" />
        </view>
      </view>

      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view v-for="category in categories"
              :key="category.code"
              :class="['filter-tab', { active: selectedCategory === category.code }]"
              @click="selectCategory(category.code)">
          {{ category.name }}
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ stats.total_books || 0 }}</text>
        <text class="stat-label">古籍总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.total_volumes || 0 }}</text>
        <text class="stat-label">卷册总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.digitized_volumes || 0 }}</text>
        <text class="stat-label">已数字化</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.rare_books || 0 }}</text>
        <text class="stat-label">善本典藏</text>
      </view>
    </view>

    <!-- 古籍列表 -->
    <view class="books-container">
      <view v-for="book in books"
            :key="book.id"
            class="book-card"
            @click="viewBookDetail(book)">
        <!-- 古籍封面和基本信息 -->
        <view class="book-header">
          <view class="book-cover">
            <image :src="book.cover_image || '/static/images/no-image.svg'"
                   mode="aspectFit"
                   class="cover-image" />
            <!-- 善本标识 -->
            <view v-if="book.is_rare_book"
                  class="rare-badge">
              <uni-icons type="star-filled"
                         size="12"
                         color="#FFD700" />
              <text class="rare-text">善本</text>
            </view>
          </view>

          <view class="book-info">
            <view class="book-title">{{ book.title }}</view>

            <!-- 基本信息 -->
            <view class="book-meta-grid">
              <view class="meta-row">
                <text class="meta-label">作者：</text>
                <text class="meta-value">{{ book.author || '佚名' }}</text>
              </view>
              <view class="meta-row">
                <text class="meta-label">朝代：</text>
                <text class="meta-value">{{ book.dynasty || '未知' }}</text>
              </view>
              <view class="meta-row">
                <text class="meta-label">分类：</text>
                <text class="meta-value category-highlight">{{ book.sibu_category_name }}</text>
              </view>
              <view v-if="book.rare_book_number"
                    class="meta-row">
                <text class="meta-label">善本号：</text>
                <text class="meta-value rare-highlight">{{ book.rare_book_number }}</text>
              </view>
            </view>

            <view class="book-description">
              {{ book.description || '暂无简介' }}
            </view>
          </view>
        </view>

        <!-- 卷册信息 -->
        <view class="volumes-section">
          <view class="volumes-header">
            <view class="volumes-info">
              <text class="volumes-title">卷册信息</text>
              <text class="volumes-subtitle">
                共{{ book.available_volumes_count }}册 · {{ book.total_pages }}页
              </text>
            </view>
            <text class="digitized-status"
                  :class="{ digitized: hasDigitizedVolumes(book) }">
              {{ hasDigitizedVolumes(book) ? '已数字化' : '未数字化' }}
            </text>
          </view>

          <!-- 卷册列表 -->
          <view class="volumes-list">
            <view v-for="volume in book.volumes.slice(0, showAllVolumes[book.id] ? book.volumes.length : 3)"
                  :key="volume.id"
                  class="volume-item"
                  @click.stop="viewVolume(volume)">
              <view class="volume-cover">
                <image :src="volume.cover_image || book.cover_image || '/static/images/no-image.svg'"
                       mode="aspectFit"
                       class="volume-image" />
                <view v-if="volume.is_digitized"
                      class="digital-badge">
                  <uni-icons type="checkmarkempty"
                             size="10"
                             color="#fff" />
                </view>
              </view>

              <view class="volume-info">
                <text class="volume-title">{{ volume.display_title }}</text>
                <view class="volume-meta">
                  <text class="volume-pages">页数：{{ volume.page_count || 0 }}页</text>
                  <text class="volume-status"
                        :class="{ digitized: volume.is_digitized }">
                    {{ volume.is_digitized ? '已数字化' : '未数字化' }}
                  </text>
                </view>
              </view>

              <!-- 操作按钮区域 -->
              <view class="volume-actions">
                <button class="action-btn read-btn"
                        @click.stop="readVolume(volume)"
                        :disabled="!volume.is_digitized">
                  <uni-icons type="eye"
                             size="14"
                             color="#fff" />
                  <text>阅读</text>
                </button>
                <button class="action-btn favorite-btn"
                        :class="{ favorited: isVolumeFavorited(volume.id) }"
                        @click.stop="toggleVolumeFavorite(volume)">
                  <uni-icons :type="isVolumeFavorited(volume.id) ? 'heart-filled' : 'heart'"
                             size="14"
                             :color="isVolumeFavorited(volume.id) ? '#FF6B6B' : '#666'" />
                  <text>{{ isVolumeFavorited(volume.id) ? '已收藏' : '收藏' }}</text>
                </button>
              </view>
            </view>

            <!-- 展开/收起按钮 -->
            <view v-if="book.volumes.length > 3"
                  class="expand-btn"
                  @click.stop="toggleShowAllVolumes(book.id)">
              <text class="expand-text">
                {{ showAllVolumes[book.id] ? '收起' : `展开全部${book.volumes.length}册` }}
              </text>
              <uni-icons :type="showAllVolumes[book.id] ? 'up' : 'down'"
                         size="14"
                         color="#007AFF" />
            </view>
          </view>
        </view>

        <!-- 古籍统计信息 -->
        <view class="book-stats">
          <view class="stat-item">
            <uni-icons type="eye"
                       size="12"
                       color="#999" />
            <text class="stat-text">浏览 {{ book.view_count }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="heart"
                       size="12"
                       color="#999" />
            <text class="stat-text">收藏 {{ book.favorite_count }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="calendar"
                       size="12"
                       color="#999" />
            <text class="stat-text">{{ formatDate(book.created_at) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading"
          class="loading-container">
      <uni-load-more :status="loadingStatus" />
    </view>

    <!-- 空状态 -->
    <view v-if="!loading && books.length === 0"
          class="empty-state">
      <image src="/static/images/no-image.svg"
             class="empty-image" />
      <text class="empty-text">暂无古籍数据</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'

export default {
  name: 'AncientBooks',
  setup () {
    // 响应式数据
    const books = ref([])
    const stats = ref({})
    const loading = ref(false)
    const loadingStatus = ref('more')
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const showAllVolumes = ref({})
    const favoriteVolumes = ref(new Set())

    // 分类数据
    const categories = ref([
      { code: '', name: '全部' },
      { code: 'A', name: '经部' },
      { code: 'B', name: '史部' },
      { code: 'C', name: '子部' },
      { code: 'D', name: '集部' }
    ])

    // 分页参数
    const pagination = reactive({
      skip: 0,
      limit: 20,
      total: 0
    })

    // 模拟数据
    const mockBooks = [
      {
        id: 1,
        title: '四书章句集注',
        author: '朱熹',
        dynasty: '宋',
        sibu_category: 'A',
        sibu_category_name: '经部',
        rare_book_number: 'SB001',
        description: '朱熹对《大学》《中庸》《论语》《孟子》的注释，是理学的重要典籍。此书融合了宋代理学思想，对后世儒学发展产生了深远影响。',
        cover_image: '/static/images/book-cover-1.jpg',
        is_rare_book: true,
        available_volumes_count: 4,
        total_pages: 680,
        view_count: 1250,
        favorite_count: 89,
        created_at: '2024-01-15T10:30:00Z',
        volumes: [
          {
            id: 1,
            display_title: '第1册 - 大学章句',
            page_count: 168,
            is_digitized: true,
            cover_image: '/static/images/volume-1.jpg'
          },
          {
            id: 2,
            display_title: '第2册 - 中庸章句',
            page_count: 172,
            is_digitized: true,
            cover_image: '/static/images/volume-2.jpg'
          },
          {
            id: 3,
            display_title: '第3册 - 论语集注',
            page_count: 186,
            is_digitized: true,
            cover_image: '/static/images/volume-3.jpg'
          },
          {
            id: 4,
            display_title: '第4册 - 孟子集注',
            page_count: 154,
            is_digitized: false,
            cover_image: '/static/images/volume-4.jpg'
          }
        ]
      },
      {
        id: 2,
        title: '史记',
        author: '司马迁',
        dynasty: '汉',
        sibu_category: 'B',
        sibu_category_name: '史部',
        rare_book_number: 'SB002',
        description: '中国历史上第一部纪传体通史，记载了从上古传说中的黄帝时代到汉武帝的三千多年历史。司马迁以其深厚的史学功底和文学才华，创造了史学与文学完美结合的典范。',
        cover_image: '/static/images/book-cover-2.jpg',
        is_rare_book: true,
        available_volumes_count: 10,
        total_pages: 1520,
        view_count: 2180,
        favorite_count: 156,
        created_at: '2024-01-10T14:20:00Z',
        volumes: [
          {
            id: 5,
            display_title: '第1册 - 本纪',
            page_count: 152,
            is_digitized: true,
            cover_image: '/static/images/volume-5.jpg'
          },
          {
            id: 6,
            display_title: '第2册 - 世家上',
            page_count: 148,
            is_digitized: true,
            cover_image: '/static/images/volume-6.jpg'
          },
          {
            id: 7,
            display_title: '第3册 - 世家下',
            page_count: 156,
            is_digitized: false,
            cover_image: '/static/images/volume-7.jpg'
          }
        ]
      },
      {
        id: 3,
        title: '老子道德经',
        author: '老子',
        dynasty: '春秋',
        sibu_category: 'C',
        sibu_category_name: '子部',
        description: '道家哲学思想的重要源头，阐述了道的概念和无为而治的思想。全书共八十一章，言简意赅，蕴含着深刻的哲学智慧。',
        cover_image: '/static/images/book-cover-3.jpg',
        is_rare_book: false,
        available_volumes_count: 1,
        total_pages: 86,
        view_count: 980,
        favorite_count: 67,
        created_at: '2024-01-08T09:15:00Z',
        volumes: [
          {
            id: 8,
            display_title: '第1册 - 道德经',
            page_count: 86,
            is_digitized: true,
            cover_image: '/static/images/volume-8.jpg'
          }
        ]
      }
    ]

    const mockStats = {
      total_books: 156,
      total_volumes: 324,
      digitized_volumes: 198,
      rare_books: 89
    }

    // 计算属性
    const filteredBooks = computed(() => {
      let result = books.value

      // 按分类筛选
      if (selectedCategory.value) {
        result = result.filter(book => book.sibu_category === selectedCategory.value)
      }

      // 按搜索关键词筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(book =>
          book.title.toLowerCase().includes(query) ||
          (book.author && book.author.toLowerCase().includes(query)) ||
          (book.description && book.description.toLowerCase().includes(query))
        )
      }

      return result
    })

    // 方法
    const loadBooks = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        books.value = mockBooks
        stats.value = mockStats
        pagination.total = mockBooks.length
      } catch (error) {
        console.error('加载古籍数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        loading.value = false
      }
    }

    const onSearchInput = (e) => {
      searchQuery.value = e.detail.value
    }

    const selectCategory = (code) => {
      selectedCategory.value = selectedCategory.value === code ? '' : code
    }

    const hasDigitizedVolumes = (book) => {
      return book.volumes.some(volume => volume.is_digitized)
    }

    const toggleShowAllVolumes = (bookId) => {
      showAllVolumes.value[bookId] = !showAllVolumes.value[bookId]
    }

    const isVolumeFavorited = (volumeId) => {
      return favoriteVolumes.value.has(volumeId)
    }

    const viewBookDetail = (book) => {
      uni.navigateTo({
        url: `/pages/culture/ancient-book-detail?id=${book.id}`
      })
    }

    const viewVolume = (volume) => {
      if (!volume.is_digitized) {
        uni.showToast({
          title: '该卷册尚未数字化',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/volume-detail?id=${volume.id}`
      })
    }

    const readVolume = (volume) => {
      if (!volume.is_digitized) {
        uni.showToast({
          title: '该卷册尚未数字化',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/volume-reader?id=${volume.id}`
      })
    }

    const toggleVolumeFavorite = async (volume) => {
      try {
        if (isVolumeFavorited(volume.id)) {
          favoriteVolumes.value.delete(volume.id)
          uni.showToast({
            title: '取消收藏成功',
            icon: 'success'
          })
        } else {
          favoriteVolumes.value.add(volume.id)
          uni.showToast({
            title: '收藏成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }

    // 生命周期
    onMounted(() => {
      loadBooks()
    })

    return {
      books: filteredBooks,
      stats,
      loading,
      loadingStatus,
      searchQuery,
      selectedCategory,
      categories,
      showAllVolumes,
      favoriteVolumes,
      onSearchInput,
      selectCategory,
      hasDigitizedVolumes,
      toggleShowAllVolumes,
      isVolumeFavorited,
      viewBookDetail,
      viewVolume,
      readVolume,
      toggleVolumeFavorite,
      formatDate
    }
  }
}
</script>

<style scoped>
.ancient-books-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部搜索区域 */
.header-section {
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-container {
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-input {
  flex: 1;
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
}

.filter-tab {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #007aff;
  color: #fff;
}

/* 统计信息 */
.stats-section {
  display: flex;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 古籍列表 */
.books-container {
  padding: 0 20rpx 20rpx;
}

.book-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.book-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 古籍头部 */
.book-header {
  display: flex;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.book-cover {
  position: relative;
  width: 120rpx;
  height: 160rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.rare-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(255, 215, 0, 0.9);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.rare-text {
  font-size: 20rpx;
  color: #8b4513;
  font-weight: bold;
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.book-meta-grid {
  margin-bottom: 16rpx;
}

.meta-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-label {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  margin-right: 8rpx;
}

.meta-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.category-highlight {
  color: #1976d2;
  font-weight: 500;
}

.rare-highlight {
  color: #f57c00;
  font-weight: 500;
  font-family: monospace;
}

.book-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卷册区域 */
.volumes-section {
  padding: 24rpx;
  background: #fafbfc;
}

.volumes-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.volumes-info {
  flex: 1;
}

.volumes-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.volumes-subtitle {
  font-size: 22rpx;
  color: #666;
}

.digitized-status {
  font-size: 22rpx;
  color: #999;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}

.digitized-status.digitized {
  background: #e8f5e8;
  color: #4caf50;
}

.volumes-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.volume-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.volume-item:active {
  transform: scale(0.98);
}

.volume-cover {
  position: relative;
  width: 80rpx;
  height: 100rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.volume-image {
  width: 100%;
  height: 100%;
}

.digital-badge {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-info {
  flex: 1;
  margin-right: 16rpx;
}

.volume-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.volume-meta {
  display: flex;
  gap: 16rpx;
}

.volume-pages {
  font-size: 22rpx;
  color: #666;
}

.volume-status {
  font-size: 22rpx;
  color: #999;
}

.volume-status.digitized {
  color: #4caf50;
}

.volume-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 120rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.read-btn {
  background: #007aff;
  color: #fff;
}

.read-btn:disabled {
  background: #ccc;
  color: #999;
}

.favorite-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.favorite-btn.favorited {
  background: #ffebee;
  color: #ff6b6b;
  border-color: #ff6b6b;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-top: 8rpx;
}

.expand-text {
  font-size: 24rpx;
  color: #007aff;
}

/* 统计信息 */
.book-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #f0f0f0;
}

.book-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 加载和空状态 */
.loading-container {
  padding: 40rpx;
  text-align: center;
}

.empty-state {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 