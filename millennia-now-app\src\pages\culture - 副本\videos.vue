<template>
  <view class="videos-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <image class="header-bg"
             src="/static/images/videos-bg.jpg"
             mode="aspectFill">
      </image>
      <view class="header-overlay">
        <text class="header-title">影像文献</text>
        <text class="header-subtitle">珍贵影音资料收藏</text>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-nav">
      <scroll-view class="nav-scroll"
                   scroll-x="true"
                   show-scrollbar="false">
        <view class="nav-list">
          <view v-for="category in categories"
                :key="category.value"
                :class="['nav-item', { active: currentCategory === category.value }]"
                @click="switchCategory(category.value)">
            <image :src="category.icon"
                   class="nav-icon"></image>
            <text class="nav-label">{{ category.label }}</text>
            <text class="nav-count">{{ category.count }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索和排序 -->
    <view class="control-section">
      <view class="search-box">
        <image src="/static/icons/search.svg"
               class="search-icon"></image>
        <input v-model="searchKeyword"
               placeholder="搜索影像标题、年代、主题..."
               class="search-input"
               @input="onSearchInput" />
        <text v-if="searchKeyword"
              @click="clearSearch"
              class="clear-btn">×</text>
      </view>

      <view class="sort-controls">
        <picker :value="sortIndex"
                :range="sortOptions"
                range-key="label"
                @change="onSortChange">
          <view class="sort-btn">
            <text class="sort-text">{{ sortOptions[sortIndex].label }}</text>
            <image src="/static/icons/arrow-down.svg"
                   class="sort-arrow"></image>
          </view>
        </picker>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <view v-if="loading"
            class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else
            class="videos-list">
        <view v-for="video in filteredVideos"
              :key="video.id"
              class="video-item"
              @click="playVideo(video)">

          <!-- 视频缩略图 -->
          <view class="video-thumb">
            <image :src="video.thumbnail"
                   mode="aspectFill"
                   class="thumb-image"
                   @error="onImageError">
            </image>

            <!-- 播放按钮覆层 -->
            <view class="play-overlay">
              <view class="play-button">
                <image src="/static/icons/play.svg"
                       class="play-icon"></image>
              </view>
              <view class="video-duration">{{ video.duration }}</view>
            </view>

            <!-- 视频类型标签 -->
            <view class="video-type">{{ video.type }}</view>

            <!-- 高清标记 -->
            <view v-if="video.isHD"
                  class="hd-badge">
              <text>HD</text>
            </view>
          </view>

          <!-- 视频信息 -->
          <view class="video-info">
            <text class="video-title">{{ video.title }}</text>
            <text class="video-year">{{ video.year }}</text>
            <text class="video-desc">{{ video.description }}</text>

            <!-- 视频标签 -->
            <view class="video-tags">
              <text v-for="tag in video.tags"
                    :key="tag"
                    class="video-tag">{{ tag }}</text>
            </view>

            <!-- 统计信息 -->
            <view class="video-stats">
              <view class="stat-item">
                <image src="/static/icons/play-count.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ video.playCount }}次播放</text>
              </view>
              <view class="stat-item">
                <image src="/static/icons/duration.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ video.duration }}</text>
              </view>
              <view class="stat-item">
                <image src="/static/icons/date.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ video.uploadDate }}</text>
              </view>
            </view>

            <!-- 历史背景 -->
            <view class="historical-context"
                  v-if="video.historicalContext">
              <text class="context-label">历史背景</text>
              <text class="context-text">{{ video.historicalContext }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="video-actions">
            <view class="action-btn primary"
                  @click.stop="playVideo(video)">
              <image src="/static/icons/play.svg"
                     class="action-icon"></image>
              <text>播放</text>
            </view>
            <view class="action-btn secondary"
                  @click.stop="downloadVideo(video)">
              <image src="/static/icons/download.svg"
                     class="action-icon"></image>
              <text>下载</text>
            </view>
            <view class="action-btn tertiary"
                  @click.stop="showTranscript(video)">
              <image src="/static/icons/transcript.svg"
                     class="action-icon"></image>
              <text>字幕</text>
            </view>
            <view class="action-btn favorite"
                  :class="{ active: video.isFavorited }"
                  @click.stop="toggleFavorite(video)">
              <image :src="video.isFavorited ? '/static/icons/heart-filled.svg' : '/static/icons/heart.svg'"
                     class="action-icon"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && filteredVideos.length === 0"
            class="empty-state">
        <image src="/static/icons/empty-videos.svg"
               class="empty-icon"></image>
        <text class="empty-title">暂无影像文献</text>
        <text class="empty-desc">{{ getEmptyStateText() }}</text>
      </view>
    </view>

    <!-- 视频播放器浮层 -->
    <view v-if="showPlayer"
          class="player-overlay"
          @click="closePlayer">
      <view class="player-container"
            @click.stop>
        <view class="player-header">
          <text class="player-title">{{ currentVideo.title }}</text>
          <text @click="closePlayer"
                class="close-btn">×</text>
        </view>

        <view class="player-content">
          <video :src="currentVideo.videoUrl"
                 :poster="currentVideo.thumbnail"
                 class="video-player"
                 controls
                 autoplay
                 @timeupdate="onTimeUpdate"
                 @ended="onVideoEnded">
          </video>
        </view>

        <view class="player-controls">
          <view class="control-item"
                @click="toggleCC">
            <image :src="showCC ? '/static/icons/cc-on.svg' : '/static/icons/cc-off.svg'"
                   class="control-icon"></image>
            <text>字幕</text>
          </view>
          <view class="control-item"
                @click="changeSpeed">
            <image src="/static/icons/speed.svg"
                   class="control-icon"></image>
            <text>{{ currentSpeed }}x</text>
          </view>
          <view class="control-item"
                @click="toggleFullscreen">
            <image src="/static/icons/fullscreen.svg"
                   class="control-icon"></image>
            <text>全屏</text>
          </view>
        </view>

        <!-- 字幕显示 -->
        <view v-if="showCC && currentSubtitle"
              class="subtitle-overlay">
          <text class="subtitle-text">{{ currentSubtitle }}</text>
        </view>
      </view>
    </view>

    <!-- 字幕浮层 -->
    <view v-if="showTranscriptModal"
          class="transcript-overlay"
          @click="closeTranscript">
      <view class="transcript-panel"
            @click.stop>
        <view class="panel-header">
          <text class="panel-title">{{ currentVideo.title }} - 字幕文稿</text>
          <text @click="closeTranscript"
                class="close-btn">×</text>
        </view>

        <scroll-view class="transcript-content"
                     scroll-y="true">
          <view v-for="(subtitle, index) in currentVideo.subtitles"
                :key="index"
                class="subtitle-item"
                @click="seekToTime(subtitle.startTime)">
            <text class="subtitle-time">{{ formatTime(subtitle.startTime) }}</text>
            <text class="subtitle-content">{{ subtitle.text }}</text>
          </view>
        </scroll-view>

        <view class="transcript-actions">
          <button @click="exportTranscript"
                  class="export-btn">导出字幕</button>
          <button @click="searchInTranscript"
                  class="search-btn">搜索内容</button>
        </view>
      </view>
    </view>

    <!-- 下载确认浮层 -->
    <view v-if="showDownloadModal"
          class="download-overlay"
          @click="closeDownload">
      <view class="download-panel"
            @click.stop>
        <view class="panel-header">
          <text class="panel-title">下载选项</text>
          <text @click="closeDownload"
                class="close-btn">×</text>
        </view>

        <view class="download-options">
          <view v-for="option in downloadOptions"
                :key="option.quality"
                class="download-option"
                @click="startDownload(option)">
            <view class="option-info">
              <text class="option-quality">{{ option.quality }}</text>
              <text class="option-size">{{ option.size }}</text>
            </view>
            <text class="option-format">{{ option.format }}</text>
            <image src="/static/icons/download.svg"
                   class="option-icon"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部工具栏 -->
    <view class="toolbar">
      <view class="tool-item"
            @click="showCollections">
        <image src="/static/icons/collection.svg"
               class="tool-icon"></image>
        <text class="tool-text">专题</text>
      </view>
      <view class="tool-item"
            @click="showTimeline">
        <image src="/static/icons/timeline.svg"
               class="tool-icon"></image>
        <text class="tool-text">时间线</text>
      </view>
      <view class="tool-item"
            @click="showHistory">
        <image src="/static/icons/history.svg"
               class="tool-icon"></image>
        <text class="tool-text">观看记录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Videos',
  data () {
    return {
      loading: true,
      searchKeyword: '',
      currentCategory: 'all',
      sortIndex: 0,
      showPlayer: false,
      showTranscriptModal: false,
      showDownloadModal: false,
      currentVideo: null,
      currentTime: 0,
      currentSubtitle: '',
      showCC: false,
      currentSpeed: 1,

      categories: [
        { label: '全部', value: 'all', icon: '/static/icons/all.svg', count: 89 },
        { label: '历史影像', value: 'historical', icon: '/static/icons/historical.svg', count: 34 },
        { label: '口述历史', value: 'oral', icon: '/static/icons/oral.svg', count: 23 },
        { label: '纪录片', value: 'documentary', icon: '/static/icons/documentary.svg', count: 18 },
        { label: '新闻资料', value: 'news', icon: '/static/icons/news.svg', count: 14 }
      ],

      sortOptions: [
        { label: '最新上传', value: 'latest' },
        { label: '最多播放', value: 'popular' },
        { label: '年代排序', value: 'chronological' },
        { label: '时长排序', value: 'duration' }
      ],

      downloadOptions: [
        { quality: '高清', size: '1.2GB', format: 'MP4', url: '' },
        { quality: '标清', size: '680MB', format: 'MP4', url: '' },
        { quality: '音频', size: '45MB', format: 'MP3', url: '' }
      ],

      videos: [
        {
          id: 1,
          title: '1949年开国大典珍贵影像',
          year: '1949年',
          type: '历史影像',
          description: '记录中华人民共和国成立时的历史时刻，包含开国大典完整过程的珍贵影像资料。',
          thumbnail: '/static/images/videos/founding-ceremony-thumb.jpg',
          videoUrl: '/static/videos/founding-ceremony.mp4',
          duration: '42:35',
          isHD: true,
          playCount: 1234,
          uploadDate: '2024-01-15',
          isFavorited: false,
          tags: ['开国大典', '历史时刻', '珍贵影像'],
          historicalContext: '1949年10月1日，中华人民共和国中央人民政府成立，毛泽东主席在天安门城楼上庄严宣告中华人民共和国成立。',
          subtitles: [
            { startTime: 0, endTime: 5, text: '这里是天安门广场，1949年10月1日' },
            { startTime: 5, endTime: 10, text: '中华人民共和国中央人民政府今天成立了' },
            { startTime: 10, endTime: 15, text: '毛泽东主席在天安门城楼上庄严宣告' }
          ]
        },
        {
          id: 2,
          title: '老北京胡同生活口述历史',
          year: '1980年代',
          type: '口述历史',
          description: '采访老北京胡同居民，记录传统生活方式和文化记忆的珍贵口述历史资料。',
          thumbnail: '/static/images/videos/hutong-life-thumb.jpg',
          videoUrl: '/static/videos/hutong-life.mp4',
          duration: '28:15',
          isHD: false,
          playCount: 567,
          uploadDate: '2024-01-12',
          isFavorited: true,
          tags: ['胡同文化', '北京历史', '民俗传统'],
          historicalContext: '1980年代的北京，胡同生活还保持着传统的样貌，这些口述历史记录了即将消失的生活方式。',
          subtitles: [
            { startTime: 0, endTime: 8, text: '我从小就住在这个胡同里，已经70多年了' },
            { startTime: 8, endTime: 15, text: '那时候邻里之间都很熟悉，大家互相帮助' }
          ]
        },
        {
          id: 3,
          title: '改革开放40年纪录片',
          year: '2018年',
          type: '纪录片',
          description: '全面回顾改革开放40年来中国社会经济发展的历程，展现伟大变革的历史进程。',
          thumbnail: '/static/images/videos/reform-40-thumb.jpg',
          videoUrl: '/static/videos/reform-40.mp4',
          duration: '85:20',
          isHD: true,
          playCount: 2156,
          uploadDate: '2024-01-10',
          isFavorited: false,
          tags: ['改革开放', '经济发展', '社会变迁'],
          historicalContext: '1978年开始的改革开放政策，开启了中国特色社会主义建设的新时期。',
          subtitles: []
        }
      ]
    }
  },

  computed: {
    filteredVideos () {
      let result = this.videos

      // 按分类筛选
      if (this.currentCategory !== 'all') {
        const categoryMap = {
          'historical': '历史影像',
          'oral': '口述历史',
          'documentary': '纪录片',
          'news': '新闻资料'
        }
        result = result.filter(video => video.type === categoryMap[this.currentCategory])
      }

      // 按关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(video =>
          video.title.toLowerCase().includes(keyword) ||
          video.description.toLowerCase().includes(keyword) ||
          video.type.toLowerCase().includes(keyword) ||
          video.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      }

      // 排序
      const sortOption = this.sortOptions[this.sortIndex]
      switch (sortOption.value) {
        case 'latest':
          result.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
          break
        case 'popular':
          result.sort((a, b) => b.playCount - a.playCount)
          break
        case 'chronological':
          result.sort((a, b) => new Date(a.year) - new Date(b.year))
          break
        case 'duration':
          result.sort((a, b) => this.parseDuration(b.duration) - this.parseDuration(a.duration))
          break
      }

      return result
    }
  },

  methods: {
    onSearchInput () {
      // 搜索防抖
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.loadVideos()
      }, 300)
    },

    clearSearch () {
      this.searchKeyword = ''
      this.loadVideos()
    },

    switchCategory (category) {
      this.currentCategory = category
      this.loadVideos()
    },

    onSortChange (e) {
      this.sortIndex = e.detail.value
    },

    playVideo (video) {
      this.currentVideo = video
      this.showPlayer = true

      // 增加播放次数
      video.playCount++
    },

    closePlayer () {
      this.showPlayer = false
      this.currentTime = 0
      this.currentSubtitle = ''
    },

    onTimeUpdate (e) {
      this.currentTime = e.detail.currentTime
      this.updateSubtitle()
    },

    onVideoEnded () {
      console.log('视频播放结束')
    },

    updateSubtitle () {
      if (!this.showCC || !this.currentVideo.subtitles) return

      const subtitle = this.currentVideo.subtitles.find(sub =>
        this.currentTime >= sub.startTime && this.currentTime <= sub.endTime
      )

      this.currentSubtitle = subtitle ? subtitle.text : ''
    },

    toggleCC () {
      this.showCC = !this.showCC
      if (!this.showCC) {
        this.currentSubtitle = ''
      }
    },

    changeSpeed () {
      const speeds = [1, 1.25, 1.5, 2]
      const currentIndex = speeds.indexOf(this.currentSpeed)
      this.currentSpeed = speeds[(currentIndex + 1) % speeds.length]
    },

    toggleFullscreen () {
      // 全屏播放功能
      console.log('切换全屏模式')
    },

    downloadVideo (video) {
      this.currentVideo = video
      this.showDownloadModal = true
    },

    closeDownload () {
      this.showDownloadModal = false
    },

    startDownload (option) {
      uni.showLoading({
        title: '准备下载...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '下载开始',
          icon: 'success'
        })
        this.closeDownload()
      }, 2000)
    },

    showTranscript (video) {
      this.currentVideo = video
      this.showTranscriptModal = true
    },

    closeTranscript () {
      this.showTranscriptModal = false
    },

    seekToTime (time) {
      this.currentTime = time
      // 这里应该控制视频播放器跳转到指定时间
      console.log(`跳转到 ${time} 秒`)
    },

    formatTime (seconds) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    exportTranscript () {
      uni.showToast({
        title: '字幕导出功能开发中',
        icon: 'none'
      })
    },

    searchInTranscript () {
      uni.showToast({
        title: '字幕搜索功能开发中',
        icon: 'none'
      })
    },

    toggleFavorite (video) {
      video.isFavorited = !video.isFavorited

      uni.showToast({
        title: video.isFavorited ? '已收藏' : '已取消收藏',
        icon: video.isFavorited ? 'success' : 'none'
      })
    },

    showCollections () {
      uni.navigateTo({
        url: '/pages/culture/video-collections'
      })
    },

    showTimeline () {
      uni.navigateTo({
        url: '/pages/culture/video-timeline'
      })
    },

    showHistory () {
      uni.navigateTo({
        url: '/pages/culture/watch-history'
      })
    },

    getEmptyStateText () {
      if (this.searchKeyword) {
        return `未找到包含"${this.searchKeyword}"的影像`
      } else if (this.currentCategory !== 'all') {
        const category = this.categories.find(c => c.value === this.currentCategory)
        return `当前分类"${category?.label}"下暂无影像`
      }
      return '暂无影像文献'
    },

    parseDuration (duration) {
      // 将 "42:35" 格式转换为秒数
      const parts = duration.split(':')
      return parseInt(parts[0]) * 60 + parseInt(parts[1])
    },

    onImageError () {
      console.log('图片加载失败')
    },

    async loadVideos () {
      this.loading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.loading = false
      } catch (error) {
        console.error('加载影像数据失败:', error)
        this.loading = false
      }
    }
  },

  onLoad () {
    this.loadVideos()
  }
}
</script>

<style scoped>
.videos-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 页面头部 */
.page-header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(155, 89, 182, 0.8),
    rgba(142, 68, 173, 0.6)
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 分类导航 */
.category-nav {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.nav-scroll {
  height: 140rpx;
}

.nav-list {
  display: flex;
  gap: 24rpx;
  padding: 0 30rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.nav-item.active {
  background: #f3e5f5;
  color: #9b59b6;
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.nav-label {
  font-size: 22rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.nav-count {
  font-size: 18rpx;
  color: #999;
}

/* 搜索和排序 */
.control-section {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 24rpx;
}

.search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 26rpx;
  color: #333;
}

.clear-btn {
  font-size: 36rpx;
  color: #999;
  padding: 0 8rpx;
}

.sort-controls {
  min-width: 140rpx;
}

.sort-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  padding: 0 20rpx;
  background: #f0f0f0;
  border-radius: 35rpx;
  gap: 8rpx;
}

.sort-text {
  font-size: 24rpx;
  color: #666;
}

.sort-arrow {
  width: 20rpx;
  height: 20rpx;
}

/* 内容区域 */
.content-section {
  padding: 20rpx 30rpx;
  padding-bottom: 200rpx;
}

.loading-container {
  padding: 100rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 视频列表 */
.videos-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.video-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.video-item:active {
  transform: translateY(-2rpx);
}

/* 视频缩略图 */
.video-thumb {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.thumb-image {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-button:active {
  transform: scale(0.95);
}

.play-icon {
  width: 60rpx;
  height: 60rpx;
  margin-left: 8rpx;
}

.video-duration {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.video-type {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(155, 89, 182, 0.9);
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.hd-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

/* 视频信息 */
.video-info {
  padding: 30rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.video-year {
  font-size: 24rpx;
  color: #9b59b6;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.video-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 视频标签 */
.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.video-tag {
  font-size: 20rpx;
  background: #f3e5f5;
  color: #9b59b6;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

/* 统计信息 */
.video-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #999;
}

/* 历史背景 */
.historical-context {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #9b59b6;
}

.context-label {
  font-size: 22rpx;
  color: #9b59b6;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.context-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.video-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  flex: 1;
  background: #9b59b6;
  color: white;
}

.action-btn.secondary {
  flex: 1;
  background: #e0e0e0;
  color: #333;
}

.action-btn.tertiary {
  flex: 1;
  background: #f0f0f0;
  color: #666;
}

.action-btn.favorite {
  background: #f5f5f5;
  padding: 16rpx;
}

.action-btn.favorite.active {
  background: #ffebee;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 视频播放器浮层 */
.player-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.player-title {
  font-size: 28rpx;
  font-weight: 600;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

.close-btn {
  font-size: 40rpx;
  color: white;
  padding: 0 10rpx;
}

.player-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 70vh;
}

.player-controls {
  display: flex;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  gap: 40rpx;
  justify-content: center;
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  gap: 8rpx;
}

.control-icon {
  width: 40rpx;
  height: 40rpx;
}

.subtitle-overlay {
  position: absolute;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  max-width: 80%;
}

.subtitle-text {
  font-size: 28rpx;
  line-height: 1.4;
  text-align: center;
}

/* 字幕浮层 */
.transcript-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.transcript-panel {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

.transcript-content {
  height: 50vh;
  padding: 30rpx;
}

.subtitle-item {
  display: flex;
  gap: 20rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.subtitle-item:active {
  background-color: #f8f9fa;
}

.subtitle-time {
  font-size: 22rpx;
  color: #9b59b6;
  font-weight: 600;
  min-width: 100rpx;
}

.subtitle-content {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.transcript-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.export-btn,
.search-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.export-btn {
  background: #9b59b6;
  color: white;
}

.search-btn {
  background: #f0f0f0;
  color: #666;
}

/* 下载浮层 */
.download-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.download-panel {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
}

.download-options {
  padding: 30rpx;
}

.download-option {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.download-option:last-child {
  border-bottom: none;
}

.download-option:active {
  background-color: #f8f9fa;
}

.option-info {
  flex: 1;
}

.option-quality {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}

.option-size {
  font-size: 22rpx;
  color: #999;
}

.option-format {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.option-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 底部工具栏 */
.toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.tool-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  transition: background-color 0.3s ease;
}

.tool-item:active {
  background-color: #f8f9fa;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 22rpx;
  color: #666;
}
</style> 