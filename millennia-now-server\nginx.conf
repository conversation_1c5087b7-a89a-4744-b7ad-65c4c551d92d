events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # WebSocket连接升级映射
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器
    upstream app_backend {
        server app:8000;  # 容器内部端口是8000
    }



    # Dify服务上游定义 (使用IP地址，避免DNS解析问题)
    upstream dify_api {
        server **********:5001;                # Dify API服务 (主网络IP)
        server **********:5001 backup;         # SSRF代理网络IP
        server host.docker.internal:5001 backup;  # 备用地址1
        server **********:5001 backup;         # 备用地址2
    }

    upstream dify_web {
        server **********:3000;                # Dify Web服务 (主网络IP)
        server host.docker.internal:3000 backup;  # 备用地址1
        server **********:3000 backup;         # 备用地址2
    }

    upstream dify_plugin_daemon {
        server **********:5003;                # Dify Plugin Daemon (主网络IP)
        server host.docker.internal:5003 backup;  # 备用地址1
        server **********:5003 backup;         # 备用地址2
    }

    # HTTP服务器 - 重定向到HTTPS
    server {
        listen 80;
        server_name luckyzyn.top www.luckyzyn.top;

        # 重定向所有HTTP请求到HTTPS
        return 301 https://$server_name$request_uri;
    }

    # HTTPS服务器
    server {
        listen 443 ssl http2;
        server_name luckyzyn.top www.luckyzyn.top;

        # SSL证书配置
        ssl_certificate /etc/nginx/ssl/luckyzyn.top.pem;
        ssl_certificate_key /etc/nginx/ssl/luckyzyn.top.key;

        # SSL安全配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 客户端最大请求体大小
        client_max_body_size 100M;

        # 静态文件
        location /static/ {
            alias /var/www/static/;
            expires 30d;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }

        # Dify服务路由 (优先级高，放在前面)
        location /console/api {
            proxy_pass http://dify_api;
            include /etc/nginx/proxy.conf;
        }

        location /v1 {
            proxy_pass http://dify_api;
            include /etc/nginx/proxy.conf;
        }

        location /files {
            proxy_pass http://dify_api;
            include /etc/nginx/proxy.conf;
        }

        location /explore {
            proxy_pass http://dify_web;
            include /etc/nginx/proxy.conf;
        }

        location /e/ {
            proxy_pass http://dify_plugin_daemon;
            proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
            include /etc/nginx/proxy.conf;
        }

        location /mcp {
            proxy_pass http://dify_api;
            include /etc/nginx/proxy.conf;
        }

        # Millennia Now API代理 (使用 /millennia-api/ 前缀避免冲突)
        location /millennia-api/ {
            # 处理OPTIONS预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Language,Content-Language";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }

            proxy_pass http://app_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 隐藏后端的CORS头部，避免重复
            proxy_hide_header Access-Control-Allow-Origin;
            proxy_hide_header Access-Control-Allow-Methods;
            proxy_hide_header Access-Control-Allow-Headers;

            # 只在nginx层添加CORS头部
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Language,Content-Language";
        }

        # Dify API路由 (保持原有的 /api 路径给Dify使用)
        location /api {
            proxy_pass http://dify_api;
            include /etc/nginx/proxy.conf;
        }





        # 图片代理
        location /proxy/image {
            proxy_pass http://app_backend/proxy/image;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            proxy_pass http://app_backend/health;
            access_log off;
        }

        # Millennia Now API文档 (使用特定路径)
        location /millennia-docs {
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 根路径指向Dify Web服务
        location / {
            proxy_pass http://dify_web;
            include /etc/nginx/proxy.conf;
        }
    }
}
