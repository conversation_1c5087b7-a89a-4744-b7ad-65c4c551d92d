<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-left" @tap="goBack">
        <text class="back-icon">←</text>
        <text class="nav-title">云游文化</text>
      </view>
    </view>
    
    <!-- WebView 组件 -->
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: '',
      statusBarHeight: 20 // 默认状态栏高度
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    this.getStatusBarHeight();
    
    if (options && options.url) {
      try {
        this.url = decodeURIComponent(options.url);
        console.log('加载外部页面:', this.url);
      } catch (e) {
        console.error('URL解码失败:', e);
        this.url = options.url;
      }
    } else {
      console.error('未提供URL参数');
      // 如果没有提供URL，显示错误信息并返回
      uni.showToast({
        title: '链接无效',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    handleMessage(event) {
      console.log('接收到web-view消息:', event);
    },
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    // 获取状态栏高度
    getStatusBarHeight() {
      // #ifdef APP-PLUS || MP-WEIXIN
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;
      // #endif
    }
  }
}
</script>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏样式 */
.custom-nav {
  background-color: #ba0001;
  display: flex;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 10px;
  position: relative;
  z-index: 100;
}

.nav-left {
  display: flex;
  align-items: center;
  color: white;
  height: 44px;
}

.back-icon {
  font-size: 20px;
  margin-right: 5px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 16px;
  font-weight: bold;
}

/* WebView 样式 */
web-view {
  flex: 1;
  width: 100%;
}
</style> 