/**
 * 数据处理工具函数
 */

import { getImageProxyUrl } from '../config/api'

/**
 * 当代记忆数据项接口
 */
export interface MemoryDataItem {
  id: number
  title: string
  description?: string
  image?: string
  year?: string | number
  detail_content?: string
  detail_images?: string[]
  created_at?: string
  updated_at?: string
  place_id?: number
  is_active?: boolean
  sort_order?: number
  memory_type?: string
  significance?: string
}

/**
 * 展品数据接口
 */
export interface ExhibitData {
  id: number
  title: string
  description: string
  image: string
  year: string | number
  period: string
  content: string
  detail_images: string[]
  memory_type: string
  significance: string
  created_at?: string
  updated_at?: string
  place_id?: number
  is_active?: boolean
  sort_order?: number
}

/**
 * 验证当代记忆数据项
 */
export function validateMemoryItem(item: any): boolean {
  if (!item || typeof item !== 'object') {
    return false
  }
  
  // 必需字段检查
  if (!item.id || !item.title) {
    return false
  }
  
  return true
}

/**
 * 处理图片URL
 */
export function processImageUrl(imageUrl: string | undefined): string {
  if (!imageUrl) {
    // 返回默认占位图
    return 'https://picsum.photos/800/600?random=1'
  }
  
  return getImageProxyUrl(imageUrl)
}

/**
 * 处理图片数组
 */
export function processImageArray(images: string[] | undefined): string[] {
  if (!images || !Array.isArray(images)) {
    return []
  }
  
  return images.map(img => getImageProxyUrl(img)).filter(Boolean)
}

/**
 * 解析年份为数字
 */
export function parseYearToNumber(year: string | number | undefined): number {
  if (!year) return 0
  
  if (typeof year === 'number') {
    return year
  }
  
  if (typeof year === 'string') {
    // 提取年份数字
    const match = year.match(/(\d{4})/)
    if (match) {
      return parseInt(match[1], 10)
    }
  }
  
  return 0
}

/**
 * 转换当代记忆数据为展品格式
 */
export function convertMemoryToExhibit(item: MemoryDataItem): ExhibitData {
  // 验证数据
  if (!validateMemoryItem(item)) {
    throw new Error(`无效的当代记忆数据项: ${JSON.stringify(item)}`)
  }
  
  return {
    id: item.id,
    title: item.title,
    description: item.description || '这是一段珍贵的当代记忆',
    image: processImageUrl(item.image),
    year: item.year || new Date().getFullYear(),
    period: '当代',
    content: item.detail_content || item.description || '这段记忆记录了城市发展的重要时刻',
    detail_images: processImageArray(item.detail_images),
    memory_type: item.memory_type || '城市记忆',
    significance: item.significance || '记录城市发展变迁的重要时刻',
    created_at: item.created_at,
    updated_at: item.updated_at,
    place_id: item.place_id,
    is_active: item.is_active,
    sort_order: item.sort_order,
  }
}

/**
 * 批量转换当代记忆数据
 */
export function convertMemoryArrayToExhibits(items: MemoryDataItem[]): ExhibitData[] {
  if (!Array.isArray(items)) {
    console.warn('传入的数据不是数组:', items)
    return []
  }
  
  const validItems = items.filter(validateMemoryItem)
  
  if (validItems.length !== items.length) {
    console.warn(`过滤了 ${items.length - validItems.length} 个无效数据项`)
  }
  
  return validItems.map(convertMemoryToExhibit)
}

/**
 * 按年份排序展品（从新到旧）
 */
export function sortExhibitsByYear(exhibits: ExhibitData[]): ExhibitData[] {
  return [...exhibits].sort((a, b) => {
    const yearA = parseYearToNumber(a.year)
    const yearB = parseYearToNumber(b.year)
    return yearB - yearA // 倒序排列，新的在前
  })
}

/**
 * 生成唯一的展品ID（如果数据中没有ID）
 */
export function generateExhibitId(index: number, title: string): number {
  // 使用标题的哈希值和索引生成唯一ID
  let hash = 0
  for (let i = 0; i < title.length; i++) {
    const char = title.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return Math.abs(hash) + index + 1000 // 确保是正数且不与现有ID冲突
}

/**
 * 数据统计信息
 */
export interface DataStats {
  total: number
  withImages: number
  withDetailImages: number
  yearRange: {
    min: number
    max: number
  }
}

/**
 * 获取数据统计信息
 */
export function getDataStats(exhibits: ExhibitData[]): DataStats {
  const years = exhibits
    .map(item => parseYearToNumber(item.year))
    .filter(year => year > 0)
  
  return {
    total: exhibits.length,
    withImages: exhibits.filter(item => item.image && !item.image.includes('picsum')).length,
    withDetailImages: exhibits.filter(item => item.detail_images.length > 0).length,
    yearRange: {
      min: years.length > 0 ? Math.min(...years) : 0,
      max: years.length > 0 ? Math.max(...years) : 0,
    }
  }
}
