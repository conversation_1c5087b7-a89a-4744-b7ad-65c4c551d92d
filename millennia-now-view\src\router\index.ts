import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: {
      title: '文源纪 3D展厅 - 首页'
    }
  },
  {
    path: '/gallery',
    name: 'Gallery',
    component: () => import('../views/Gallery.vue'),
    meta: {
      title: '文源纪 3D展厅 - 走廊展厅'
    }
  },
  {
    path: '/history-gallery',
    name: 'HistoryGallery',
    component: () => import('../views/HistoryGallery.vue'),
    meta: {
      title: '文源纪 3D展厅 - 历史文脉展厅'
    }
  },  
  {
    path: '/culture-gallery',
    name: 'CultureGallery',
    component: () => import('../views/CultureGallery.vue'),
    meta: {
      title: '文源纪 3D展厅 - 文化传承展厅'
    }
  },  {
    path: '/memory-gallery',  
    name: 'M emoryGallery',
    component: () => import('../views/MemoryGallery.vue'),
    meta: {
      title: '文源纪 3D展厅 - 当代记忆展厅'
    }
  },
  {
    path: '/detail/:id',
    name: 'Detail',
    component: () => import('../views/Detail.vue'),
    meta: {
      title: '文源纪 3D展厅 - 详情'
    }
  },
  // 轨道交通路线图路由
  {
    path: '/rail-transit',
    name: 'RailTransit',
    redirect: '/rail-transit/chongqing',
    meta: {
      title: '轨道交通线路图'
    }
  },
  {
    path: '/rail-transit/chongqing',
    name: 'ChongqingMetro',
    component: () => import('../views/rail_transit/cq.vue'),
    meta: {
      title: '乘着轨道游重庆'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫，设置页面标题
router.beforeEach((to, from, next) => {
  // 检查是否是从小程序外链进入
  const urlParams = new URLSearchParams(window.location.search);
  const fromMiniapp = urlParams.get('from') === 'miniapp';
  if (fromMiniapp) {
    document.title = '云游文化';
    // 保存来源信息到 localStorage，以便其他页面使用
    localStorage.setItem('fromMiniapp', 'true');
  } else {
    document.title = to.meta.title as string || '文源纪 3D展厅';
  }
  next();
})

export default router 