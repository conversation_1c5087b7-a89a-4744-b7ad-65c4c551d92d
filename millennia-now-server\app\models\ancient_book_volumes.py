from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, DECIMAL, CheckConstraint, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.db import Base

class AncientBookVolume(Base):
    """古籍卷册模型"""
    __tablename__ = "ancient_book_volumes"
    
    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("ancient_books.id", ondelete="CASCADE"), nullable=False, index=True)
    volume_number = Column(Integer, nullable=False)
    volume_title = Column(String(200))
    total_pages = Column(Integer, default=0)
    
    # 版本信息
    start_page = Column(String(20))
    end_page = Column(String(20))
    
    # 内容描述
    content_description = Column(Text)
    notes = Column(Text)
    
    # 状态管理
    status = Column(String(20), default="draft", index=True)  # draft, published, archived
    
    # 元数据
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    book = relationship("AncientBook", back_populates="volumes")
    pages = relationship("AncientBookPage", back_populates="volume", cascade="all, delete-orphan")
    chapters = relationship("AncientBookChapter", back_populates="volume", cascade="all, delete-orphan")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    
    # 约束
    __table_args__ = (
        UniqueConstraint('book_id', 'volume_number', name='uq_book_volume_number'),
        CheckConstraint('volume_number > 0', name='ck_volume_number_positive'),
        CheckConstraint('total_pages >= 0', name='ck_total_pages_non_negative'),
    )

class AncientBookPage(Base):
    """古籍页面模型"""
    __tablename__ = "ancient_book_pages"
    
    id = Column(Integer, primary_key=True, index=True)
    volume_id = Column(Integer, ForeignKey("ancient_book_volumes.id", ondelete="CASCADE"), nullable=False, index=True)
    page_number = Column(Integer, nullable=False)
    page_label = Column(String(50))
    
    # 图片信息
    image_url = Column(String(500), nullable=False)
    image_width = Column(Integer)
    image_height = Column(Integer)
    image_size = Column(Integer)  # 文件大小（字节）
    
    # OCR文字识别
    ocr_text = Column(Text)
    ocr_confidence = Column(DECIMAL(5, 4))  # 0-1
    ocr_processed_at = Column(DateTime(timezone=True))
    ocr_status = Column(String(20), default="pending", index=True)  # pending, processing, completed, failed
    
    # 人工校对
    corrected_text = Column(Text)
    is_corrected = Column(Boolean, default=False)
    corrected_by = Column(Integer, ForeignKey("users.id"))
    corrected_at = Column(DateTime(timezone=True))
    
    # 页面类型
    page_type = Column(String(20), default="content", index=True)  # cover, contents, preface, content, appendix, colophon
    
    # 元数据
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    volume = relationship("AncientBookVolume", back_populates="pages")
    ocr_tasks = relationship("OCRTask", back_populates="page", cascade="all, delete-orphan")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    corrector = relationship("User", foreign_keys=[corrected_by])
    
    # 约束
    __table_args__ = (
        UniqueConstraint('volume_id', 'page_number', name='uq_volume_page_number'),
        CheckConstraint('page_number > 0', name='ck_page_number_positive'),
        CheckConstraint('ocr_confidence >= 0 AND ocr_confidence <= 1', name='ck_ocr_confidence_range'),
    )

class AncientBookChapter(Base):
    """古籍章节模型"""
    __tablename__ = "ancient_book_chapters"
    
    id = Column(Integer, primary_key=True, index=True)
    volume_id = Column(Integer, ForeignKey("ancient_book_volumes.id", ondelete="CASCADE"), nullable=False, index=True)
    chapter_number = Column(Integer, nullable=False)
    chapter_title = Column(String(200), nullable=False)
    start_page_id = Column(Integer, ForeignKey("ancient_book_pages.id"))
    end_page_id = Column(Integer, ForeignKey("ancient_book_pages.id"))
    
    # 内容信息
    description = Column(Text)
    
    # 元数据
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    volume = relationship("AncientBookVolume", back_populates="chapters")
    start_page = relationship("AncientBookPage", foreign_keys=[start_page_id])
    end_page = relationship("AncientBookPage", foreign_keys=[end_page_id])
    creator = relationship("User", foreign_keys=[created_by])
    
    # 约束
    __table_args__ = (
        UniqueConstraint('volume_id', 'chapter_number', name='uq_volume_chapter_number'),
        CheckConstraint('chapter_number > 0', name='ck_chapter_number_positive'),
    )

class OCRTask(Base):
    """OCR处理任务模型"""
    __tablename__ = "ocr_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    page_id = Column(Integer, ForeignKey("ancient_book_pages.id", ondelete="CASCADE"), nullable=False, index=True)
    task_status = Column(String(20), default="pending", index=True)  # pending, processing, completed, failed
    
    # 任务信息
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    
    # OCR服务信息
    ocr_provider = Column(String(50))  # 使用的OCR服务提供商
    processing_time_ms = Column(Integer)  # 处理时间（毫秒）
    
    # 结果统计
    character_count = Column(Integer)  # 识别的字符数
    confidence_avg = Column(DECIMAL(5, 4))  # 平均置信度
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    page = relationship("AncientBookPage", back_populates="ocr_tasks")

# 页面类型枚举
PAGE_TYPES = [
    ("cover", "封面"),
    ("contents", "目录"),
    ("preface", "序言"),
    ("content", "正文"),
    ("appendix", "附录"),
    ("colophon", "版权页"),
]

# OCR状态枚举
OCR_STATUSES = [
    ("pending", "待处理"),
    ("processing", "处理中"),
    ("completed", "已完成"),
    ("failed", "处理失败"),
]

# 卷册状态枚举
VOLUME_STATUSES = [
    ("draft", "草稿"),
    ("published", "已发布"),
    ("archived", "已归档"),
] 