// 用户认证状态管理
import { reactive, computed } from 'vue'
import type { UserInfo, LoginResponse } from '@/api/auth'
import {
  wechatLogin,
  getCurrentUser,
  logout as apiLogout,
  loginWithPassword,
  registerUser,
  type PasswordLoginRequest,
  type RegisterRequest
} from '@/api/auth'

// 存储键名
const TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_INFO_KEY = 'user_info'

// 认证状态
const state = reactive({
  user: null as UserInfo | null,
  isAuthenticated: false,
  isLoading: false
})

// 保存认证信息到本地存储
function saveAuthData(loginResponse: LoginResponse) {
  try {
    uni.setStorageSync(TOKEN_KEY, loginResponse.access_token)
    uni.setStorageSync(REFRESH_TOKEN_KEY, loginResponse.refresh_token)
    uni.setStorageSync(USER_INFO_KEY, JSON.stringify(loginResponse.user))
    
    state.user = loginResponse.user
    state.isAuthenticated = true
  } catch (error) {
    console.error('保存认证信息失败:', error)
  }
}

// 从本地存储加载认证信息
function loadAuthData() {
  try {
    const token = uni.getStorageSync(TOKEN_KEY)
    const userInfoStr = uni.getStorageSync(USER_INFO_KEY)
    
    if (token && userInfoStr) {
      const userInfo = JSON.parse(userInfoStr)
      console.log('🔍 从本地存储加载的用户信息:', userInfo)
      console.log('🔍 本地存储的模块权限:', userInfo.module_permissions)
      state.user = userInfo
      state.isAuthenticated = true
    }
  } catch (error) {
    console.error('加载认证信息失败:', error)
    clearAuthData()
  }
}

// 清除认证信息
function clearAuthData() {
  try {
    uni.removeStorageSync(TOKEN_KEY)
    uni.removeStorageSync(REFRESH_TOKEN_KEY)
    uni.removeStorageSync(USER_INFO_KEY)
    
    state.user = null
    state.isAuthenticated = false
  } catch (error) {
    console.error('清除认证信息失败:', error)
  }
}

// 认证管理器
export const authManager = {
  // 获取当前用户信息
  get currentUser(): UserInfo | null {
    return state.user
  },

  // 获取认证状态
  get isAuthenticated(): boolean {
    return state.isAuthenticated
  },

  // 获取加载状态
  get isLoading(): boolean {
    return state.isLoading
  },

  // 检查是否为管理员
  get isAdmin(): boolean {
    if (!state.user) return false
    return ['DISTRICT_ADMIN', 'CITY_ADMIN', 'PROVINCE_ADMIN', 'SUPER_ADMIN'].includes(state.user.role)
  },

  // 检查是否为超级管理员
  get isSuperAdmin(): boolean {
    return state.user?.role === 'SUPER_ADMIN'
  },

  // 获取用户角色显示名称
  get userRoleDisplayName(): string {
    if (!state.user) return '未登录'
    const roleMap = {
      'GUEST': '游客',
      'DISTRICT_ADMIN': '区县管理员',
      'CITY_ADMIN': '市级管理员',
      'PROVINCE_ADMIN': '省级管理员',
      'SUPER_ADMIN': '超级管理员'
    }
    return roleMap[state.user.role as keyof typeof roleMap] || state.user.role
  },

  // 初始化认证状态
  async init(): Promise<void> {
    loadAuthData()
    
    // 如果有token，尝试获取最新用户信息
    if (state.isAuthenticated) {
      try {
        state.isLoading = true
        const userInfo = await getCurrentUser()
        console.log('🔍 AuthManager 获取的用户信息:', userInfo)
        console.log('🔍 模块权限字段:', userInfo.module_permissions)
        state.user = userInfo
        uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo))
      } catch (error) {
        console.warn('获取用户信息失败，可能token已过期:', error)
        clearAuthData()
      } finally {
        state.isLoading = false
      }
    }
  },

  // 微信登录
  async loginWithWechat(code: string, encryptedData?: string, iv?: string): Promise<void> {
    try {
      state.isLoading = true
      const loginResponse = await wechatLogin({
        code,
        encrypted_data: encryptedData,
        iv
      })
      
      saveAuthData(loginResponse)
      console.log('微信登录成功:', loginResponse.user)
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    } finally {
      state.isLoading = false
    }
  },

  // 登出
  async logout(): Promise<void> {
    try {
      const refreshToken = uni.getStorageSync(REFRESH_TOKEN_KEY)
      if (refreshToken) {
        await apiLogout(refreshToken)
      }
    } catch (error) {
      console.warn('服务端登出失败:', error)
    } finally {
      clearAuthData()
      console.log('用户已登出')
    }
  },

  // 账号密码登录
  async loginWithPassword(phone: string, password: string): Promise<void> {
    state.isLoading = true

    try {
      const request: PasswordLoginRequest = { phone, password }
      const loginResponse = await loginWithPassword(request)
      saveAuthData(loginResponse)
      console.log('账号密码登录成功')
    } catch (error) {
      console.error('账号密码登录失败:', error)
      throw error
    } finally {
      state.isLoading = false
    }
  },

  // 用户注册
  async register(phone: string, password: string, nickname?: string): Promise<void> {
    state.isLoading = true

    try {
      const request: RegisterRequest = { phone, password, nickname }
      const loginResponse = await registerUser(request)
      saveAuthData(loginResponse)
      console.log('用户注册成功')
    } catch (error) {
      console.error('用户注册失败:', error)
      throw error
    } finally {
      state.isLoading = false
    }
  },

  // 更新用户信息
  updateUser(userInfo: UserInfo): void {
    state.user = userInfo
    uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo))
  },

  // 检查是否有权限访问某个功能
  hasPermission(requiredRole: string): boolean {
    if (!state.user) return false

    const roleHierarchy = {
      'GUEST': 0,
      'DISTRICT_ADMIN': 1,
      'CITY_ADMIN': 2,
      'PROVINCE_ADMIN': 3,
      'SUPER_ADMIN': 4
    }

    const currentLevel = roleHierarchy[state.user.role as keyof typeof roleHierarchy] || 0
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

    return currentLevel >= requiredLevel
  },

  // 获取管理区域信息
  get managementArea(): string {
    if (!state.user || !this.isAdmin) return ''
    
    // 这里可以根据province_id, city_id, district_id获取具体的区域名称
    // 暂时返回角色信息
    return this.userRoleDisplayName
  }
}

// 初始化认证状态
authManager.init()

export default authManager