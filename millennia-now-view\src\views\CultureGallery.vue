<template>
  <div class="gallery-container">
    <!-- 3D场景容器 -->
    <div class="scene-container">
      <canvas ref="sceneCanvas"></canvas>
    </div>

    <!-- 用户界面元素 -->
    <div class="ui-container">
      <!-- 使用小程序返回按钮组件 -->
      <MiniappBackButton />

      <!-- 普通返回按钮（当不是从小程序进入时显示） -->
      <div class="back-button"
           @click="goBack"
           v-if="!isFromMiniapp">
        <span class="icon">←</span>
        <span class="text">返回</span>
      </div>

      <!-- 展厅标题 -->
      <div class="gallery-title">
        <h1>{{ regionName }}文化传承</h1>
        <p>传统文化数字保护</p>
      </div>

      <!-- 加载进度指示器 -->
      <div class="loading-progress"
           v-if="loading">
        <div class="loading-text">加载中 {{ Math.floor(loadingProgress) }}%</div>
        <div class="progress-bar">
          <div class="progress"
               :style="{ width: loadingProgress + '%' }"></div>
        </div>
      </div>

      <!-- 展品信息面板 -->
      <div class="exhibit-panel"
           v-if="selectedExhibit"
           :class="{ 'active': selectedExhibit }">
        <div class="panel-header">
          <h2>{{ selectedExhibit.title }}</h2>
          <p class="heritage-type">{{ selectedExhibit.type }}</p>
          <button class="close-btn"
                  @click="closeExhibitPanel">×</button>
        </div>
        <div class="panel-content">
          <!-- 图片轮播 - 主图+详情图 -->
          <div class="image-carousel">
            <img :src="currentImage"
                 alt="展品图片"
                 class="exhibit-image"
                 @click="showFullImage(currentImage)">
            <div class="thumbnail-container"
                 v-if="hasMultipleImages">
              <div class="thumbnails">
                <div v-for="(image, index) in allImages"
                     :key="index"
                     class="thumbnail"
                     :class="{ 'active': currentImageIndex === index }"
                     @click="setCurrentImage(index)">
                  <img :src="image"
                       :alt="`缩略图 ${index+1}`">
                </div>
              </div>
            </div>
            <div class="carousel-controls"
                 v-if="hasMultipleImages">
              <button class="control-btn prev"
                      @click="prevImage">&lt;</button>
              <button class="control-btn next"
                      @click="nextImage">&gt;</button>
            </div>
          </div>
          <!-- 展品信息区域 -->
          <div class="exhibit-info-container">
            <div class="exhibit-info">
              <!-- 基本信息卡片 -->
              <div class="info-card basic-info">
                <div class="card-header">
                  <h3><i class="icon-info"></i>基本信息</h3>
                </div>
                <div class="card-content">
                  <div class="info-row">
                    <span class="label">类型：</span>
                    <span class="value heritage-type-badge">{{ selectedExhibit.type }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.year">
                    <span class="label">年代：</span>
                    <span class="value">{{ selectedExhibit.year }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.source">
                    <span class="label">来源：</span>
                    <span class="value">{{ selectedExhibit.source }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.sort_order !== undefined">
                    <span class="label">展示序号：</span>
                    <span class="value">{{ selectedExhibit.sort_order }}</span>
                  </div>
                </div>
              </div>

              <!-- 简介卡片 -->
              <div class="info-card brief-info">
                <div class="card-header">
                  <h3><i class="icon-book"></i>简介</h3>
                </div>
                <div class="card-content">
                  <p class="heritage-brief">{{ selectedExhibit.brief || selectedExhibit.description }}</p>
                </div>
              </div>

              <!-- 详细介绍卡片 -->
              <div class="info-card detail-info"
                   v-if="selectedExhibit.detail_content || selectedExhibit.content">
                <div class="card-header">
                  <h3><i class="icon-detail"></i>详细介绍</h3>
                </div>
                <div class="card-content">
                  <div class="content-text"
                       v-html="formattedContent"></div>
                </div>
              </div>

              <!-- 文化价值卡片 -->
              <div class="info-card cultural-value"
                   v-if="selectedExhibit.cultural_value || selectedExhibit.heritage_tags">
                <div class="card-header">
                  <h3><i class="icon-star"></i>文化价值</h3>
                </div>
                <div class="card-content">
                  <div v-if="selectedExhibit.heritage_tags && selectedExhibit.heritage_tags.length > 0"
                       class="heritage-tags">
                    <span v-for="tag in selectedExhibit.heritage_tags"
                          :key="tag"
                          class="heritage-tag">{{ tag }}</span>
                  </div>
                  <p v-if="selectedExhibit.cultural_value"
                     class="cultural-value-text">{{ selectedExhibit.cultural_value }}</p>
                </div>
              </div>

              <!-- 保护状况卡片 -->
              <div class="info-card protection-status"
                   v-if="selectedExhibit.protection_level || selectedExhibit.conservation_status">
                <div class="card-header">
                  <h3><i class="icon-shield"></i>保护状况</h3>
                </div>
                <div class="card-content">
                  <div class="info-row"
                       v-if="selectedExhibit.protection_level">
                    <span class="label">保护级别：</span>
                    <span class="value protection-level">{{ selectedExhibit.protection_level }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.conservation_status">
                    <span class="label">保存状况：</span>
                    <span class="value">{{ selectedExhibit.conservation_status }}</span>
                  </div>
                </div>
              </div>

              <!-- 相关信息卡片 -->
              <div class="info-card related-info"
                   v-if="selectedExhibit.location || selectedExhibit.dimensions || selectedExhibit.materials">
                <div class="card-header">
                  <h3><i class="icon-location"></i>相关信息</h3>
                </div>
                <div class="card-content">
                  <div class="info-row"
                       v-if="selectedExhibit.location">
                    <span class="label">地理位置：</span>
                    <span class="value">{{ selectedExhibit.location }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.dimensions">
                    <span class="label">尺寸规格：</span>
                    <span class="value">{{ selectedExhibit.dimensions }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.materials">
                    <span class="label">材质工艺：</span>
                    <span class="value">{{ selectedExhibit.materials }}</span>
                  </div>
                </div>
              </div>

              <!-- 元数据信息 -->
              <div class="meta-info">
                <div class="meta-row"
                     v-if="selectedExhibit.created_at">
                  <span class="meta-label">创建时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.created_at) }}</span>
                </div>
                <div class="meta-row"
                     v-if="selectedExhibit.updated_at">
                  <span class="meta-label">更新时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作提示 -->
      <div class="controls-hint"
           v-if="!loading && !selectedExhibit">
        <p>使用 WASD 或方向键移动，鼠标左键点击展品查看详情</p>
      </div>

      <!-- 移动端移动摇杆控制 -->
      <div class="move-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="moveControl"
           @touchstart.prevent="handleMoveJoystickStart"
           @mousedown.prevent="handleMoveJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="moveJoystickHandle"
               :style="moveJoystickStyle"></div>
        </div>
      </div>

      <!-- 移动端视角摇杆控制 -->
      <div class="look-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="lookControl"
           @touchstart.prevent="handleLookJoystickStart"
           @mousedown.prevent="handleLookJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="lookJoystickHandle"
               :style="lookJoystickStyle"></div>
        </div>
      </div>
    </div>

    <!-- 全屏图片查看器 -->
    <div class="fullscreen-image-viewer"
         v-if="fullScreenImage"
         @click="closeFullImage">
      <div class="fullscreen-image-container"
           @click.stop>
        <img :src="fullScreenImage"
             alt="全屏图片">
        <button class="close-fullscreen-btn"
                @click="closeFullImage">×</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as THREE from 'three'
import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls'
import MiniappBackButton from '../components/MiniappBackButton.vue'
// 导入正确的 API 模块
import { getHeritagePageDataByRegion, HeritageItem } from '@/api/heritage'

const router = useRouter()
const route = useRoute()

// 场景相关变量
const sceneCanvas = ref<HTMLCanvasElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: PointerLockControls
let raycaster: THREE.Raycaster
let clock = new THREE.Clock()
let animationFrameId: number

// 视角控制相关引用
const lookControl = ref<HTMLElement | null>(null)
const joystickHandle = ref<HTMLElement | null>(null)
const joystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 视角控制状态
const lookActive = ref(false)
const lookStartPosition = { x: 0, y: 0 }
const lookCurrentPosition = { x: 0, y: 0 }
const cameraRotation = { x: 0, y: 0 }

// 移动摇杆控制相关引用
const moveControl = ref<HTMLElement | null>(null)
const moveJoystickHandle = ref<HTMLElement | null>(null)
const moveJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 移动摇杆控制状态
const moveActive = ref(false)
const moveStartPosition = { x: 0, y: 0 }
const moveCurrentPosition = { x: 0, y: 0 }
const moveDirection = { x: 0, z: 0 } // 移动方向向量

// 右侧视角摇杆相关变量（新增）
const lookJoystickHandle = ref<HTMLElement | null>(null)
const lookJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 触摸ID跟踪，确保多点触控正常工作
let moveTouchId: number | null = null
let lookTouchId: number | null = null

// 加载状态
const loading = ref(true)
const loadingProgress = ref(0)

// 移动控制变量
const moveForward = ref(false)
const moveBackward = ref(false)
const moveLeft = ref(false)
const moveRight = ref(false)
const canJump = ref(false)

let velocity = new THREE.Vector3()
let direction = new THREE.Vector3()
let prevTime = performance.now()

// 展品数据
const exhibits = ref<any[]>([])
const exhibitObjects: THREE.Object3D[] = []
const selectedExhibit = ref<any>(null)

// 图片轮播相关
const currentImageIndex = ref(0)
const currentImage = computed(() => {
  if (!selectedExhibit.value) return ''
  // 详情图片优先，主图为第一张
  const allImgs = [
    selectedExhibit.value.image,
    ...(selectedExhibit.value.detail_images || []),
  ].filter(Boolean)
  return allImgs[currentImageIndex.value] || ''
})

const allImages = computed(() => {
  if (!selectedExhibit.value) return []
  return [
    selectedExhibit.value.image,
    ...(selectedExhibit.value.detail_images || []),
  ].filter(Boolean)
})

const hasMultipleImages = computed(() => {
  return allImages.value.length > 1
})

const formattedContent = computed(() => {
  if (!selectedExhibit.value) return ''
  return (
    selectedExhibit.value.detail_content || selectedExhibit.value.content || ''
  )
})

// 格式化日期的方法
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return dateString
  }
}

// 全屏图片查看器相关
const fullScreenImage = ref<string | null>(null)

const showFullImage = (imageSrc: string) => {
  fullScreenImage.value = imageSrc
}

const closeFullImage = () => {
  fullScreenImage.value = null
}

// 图片轮播相关方法
const nextImage = () => {
  if (currentImageIndex.value < allImages.value.length - 1) {
    currentImageIndex.value++
  } else {
    currentImageIndex.value = 0 // 循环到第一张
  }
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  } else {
    currentImageIndex.value = allImages.value.length - 1 // 循环到最后一张
  }
}

const setCurrentImage = (index: number) => {
  currentImageIndex.value = index
}

// 初始化3D场景
const initScene = async () => {
  if (!sceneCanvas.value) return

  // 检测设备类型
  checkMobileDevice()
  console.log('是否为移动设备:', isMobileDevice.value)

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x1a0f0d) // 更深沉的褐色调背景，营造古色古香的氛围
  scene.fog = new THREE.Fog(0x1a0f0d, 10, 50) // 匹配的雾色

  // 添加环境光和方向光 - 温暖的色调更符合传统文化氛围
  const ambientLight = new THREE.AmbientLight(0xf8e0c0, 0.4) // 温暖的米黄色调
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffe0b3, 0.7) // 柔和的黄褐色调
  directionalLight.position.set(5, 10, 7.5)
  directionalLight.castShadow = false // 关闭投射阴影
  scene.add(directionalLight)

  // 添加点光源 - 带有红色调的灯光营造传统喜庆氛围
  const pointLight1 = new THREE.PointLight(0xffc2a3, 1.2, 25) // 朱砂红色调
  pointLight1.position.set(0, 3, 0)
  scene.add(pointLight1)

  // 设置相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  )
  camera.position.set(0, 1.6, 0) // 人眼高度约1.6米

  // 设置渲染器
  renderer = new THREE.WebGLRenderer({
    canvas: sceneCanvas.value,
    antialias: true,
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = false // 完全关闭阴影贴图，提高性能

  // 使用指针锁定控制
  controls = new PointerLockControls(camera, renderer.domElement)
  scene.add(controls.getObject())

  // 移动设备上禁用指针锁定（因为不支持），但保留控制功能
  if (isMobileDevice.value) {
    console.log('移动设备：禁用指针锁定')
    // 修改lock方法以适应移动设备
    const originalLock = controls.lock
    controls.lock = function () {
      // 在移动设备上，只需将isLocked设为true而不实际尝试锁定指针
      this.isLocked = true
      return this
    }

    // 自动"锁定"控制，以便移动键可以正常工作
    controls.lock()

    // 移动设备上确保锁定后再初始化相机角度
    setTimeout(() => {
      initializeCameraAngles()
    }, 100)
  } else {
    // 桌面设备立即初始化
    initializeCameraAngles()
  }

  // 覆盖PointerLockControls的鼠标移动处理
  const originalOnMouseMove = (controls as any).onMouseMove
  if (originalOnMouseMove) {
    ;(controls as any).onMouseMove = function (event: MouseEvent) {
      if (!controls.isLocked) return

      const movementX = event.movementX || 0
      const movementY = event.movementY || 0

      // 使用我们的标准FPS相机控制
      updateCameraRotation(movementX, movementY, mouseSensitivity)
    }
  }

  // 设置射线投射器用于交互
  raycaster = new THREE.Raycaster()

  // 添加键盘事件监听
  document.addEventListener('keydown', onKeyDown)
  document.addEventListener('keyup', onKeyUp)

  // 添加点击事件监听
  renderer.domElement.addEventListener('click', onMouseClick)

  // 移动设备上添加触摸事件
  if (isMobileDevice.value) {
    // 用于选择展品
    renderer.domElement.addEventListener('touchend', onTouchEnd)
    // 移动端禁用手指触摸控制视角，只通过右侧摇杆控制
    // renderer.domElement.addEventListener('touchstart', handleCanvasTouchStart)
  }

  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize)

  // 加载数据并创建展厅
  await loadHistoryData()
  createHistoryGallery()

  // 开始动画循环
  animate()

  // 确保相机角度正确初始化（延迟执行，确保所有初始化完成）
  setTimeout(() => {
    initializeCameraAngles()
    console.log('延迟初始化相机角度完成')
  }, 500)

  // 桌面设备才需要延迟锁定（移动设备已在初始化时处理）
  if (!isMobileDevice.value) {
    setTimeout(() => {
      controls.lock()
    }, 1000)
  }
}

// 加载文化传承数据
const loadHistoryData = async () => {
  try {
    loading.value = true
    console.log('正在加载文化传承数据...')

    // 获取真实文化传承数据，使用区域ID参数
    const pageData = await getHeritagePageDataByRegion({
      province_id: provinceId.value || undefined,
      city_id: cityId.value || undefined,
      district_id: districtId.value || undefined,
    })

    if (pageData && pageData.heritage_data) {
      // 转换为展品格式
      exhibits.value = pageData.heritage_data.map((item: HeritageItem) => ({
        id: item.id,
        title: item.title,
        description: item.brief || '',
        image:
          item.image || `https://picsum.photos/seed/${item.id + 100}/800/600`, // 如果没有图片，使用占位图
        type: item.type || '文化传承',
        content: item.detail_content || item.brief || '',
        detail_images: item.detail_images || [],
        created_at: item.created_at, // 添加创建时间
        updated_at: item.updated_at, // 添加更新时间
      }))

      console.log('文化传承数据加载成功:', exhibits.value.length)
      console.log('第一个展品数据示例:', exhibits.value[0])
    } else {
      console.log('未找到文化传承数据，使用默认数据')
      exhibits.value = generateDefaultExhibits()
    }

    loading.value = false
    return exhibits.value
  } catch (error) {
    console.error('加载文化传承数据失败:', error)

    // 加载失败时使用默认数据
    exhibits.value = generateDefaultExhibits()

    loading.value = false
    return exhibits.value
  }
}

// 生成默认展品数据（当API调用失败时使用）
const generateDefaultExhibits = () => {
  const types = [
    '非物质文化遗产',
    '传统工艺',
    '民间艺术',
    '传统戏曲',
    '传统医药',
  ]
  const years = [
    '唐代（618-907年）',
    '宋代（960-1279年）',
    '明代（1368-1644年）',
    '清代（1644-1912年）',
    '近现代',
  ]
  const sources = [
    '故宫博物院',
    '中国国家博物馆',
    '上海博物馆',
    '陕西历史博物馆',
    '湖南省博物馆',
  ]
  const protectionLevels = ['国家级', '省级', '市级', '县级', '']
  const locations = ['北京', '西安', '苏州', '杭州', '成都']

  return Array(8)
    .fill(null)
    .map((_, index) => ({
      id: index + 1,
      title: `文化传承展品 ${index + 1}`,
      description:
        '这是一个传统文化展品，代表了中国传统文化的精髓和传承。通过这件展品，我们可以了解到中国传统文化的独特魅力和深厚底蕴。',
      brief:
        '这是一个传统文化展品，代表了中国传统文化的精髓和传承。通过这件展品，我们可以了解到中国传统文化的独特魅力和深厚底蕴。',
      image: `https://picsum.photos/seed/${index + 200}/800/600`,
      type: types[index % 5],
      year: years[index % 5],
      source: sources[index % 5],
      protection_level: protectionLevels[index % 5],
      location: locations[index % 5],
      dimensions:
        index % 2 === 0
          ? `长${20 + index * 5}cm，宽${15 + index * 3}cm，高${
              10 + index * 2
            }cm`
          : '',
      materials:
        index % 3 === 0
          ? ['丝绸', '陶瓷', '木材', '金属', '纸张'][index % 5]
          : '',
      conservation_status: ['完好', '良好', '一般', '需修复'][index % 4],
      cultural_value:
        '具有重要的历史价值和文化意义，是中华文明的重要组成部分，对研究古代社会、经济、文化具有重要价值。',
      heritage_tags: [
        ['传统技艺', '文化传承'],
        ['历史文物', '艺术价值'],
        ['民俗文化', '地方特色'],
        ['宗教文化', '精神象征'],
        ['科技成就', '工艺精湛'],
      ][index % 5],
      content:
        '这是一个传统文化展品，代表了中国传统文化的精髓和传承。通过这件展品，我们可以了解到中国传统文化的独特魅力和深厚底蕴。这件展品的制作工艺精湛，体现了古代工匠的智慧和创造力。它不仅是物质文化遗产，更是中华民族精神的载体。\n\n传统文化是一个民族的根和魂，是维系民族团结和国家统一的精神纽带。保护和传承传统文化，对于增强民族凝聚力、提高国家文化软实力具有重要意义。',
      detail_content: `<h4>历史背景</h4><p>这件文化传承展品承载着深厚的历史文化内涵，见证了中华文明的发展历程。</p><h4>制作工艺</h4><p>采用传统手工技艺制作，工艺精湛，体现了古代工匠的智慧和技艺水平。</p><h4>文化意义</h4><p>不仅具有实用价值，更承载着丰富的文化内涵和精神寄托，是中华优秀传统文化的重要载体。</p>`,
      detail_images: [
        `https://picsum.photos/seed/${index + 300}/800/600`,
        `https://picsum.photos/seed/${index + 301}/800/600`,
        `https://picsum.photos/seed/${index + 302}/800/600`,
      ],
      sort_order: index + 1,
      created_at: new Date(
        Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
      ).toISOString(),
      updated_at: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
    }))
}

// 创建文化传承展厅
const createHistoryGallery = () => {
  if (!exhibits.value || exhibits.value.length === 0) return

  // 计算走廊长度 - 根据展品数量动态调整
  const wallSpacing = 10 // 每两根柱子间距10个单位

  // 计算每面墙可以容纳的展品数量
  const exhibitsPerWall = 4 // 每面墙最多放5个展品

  // 计算走廊长度 - 确保足够长以容纳所有展品
  const totalExhibits = exhibits.value.length
  const bothSidesExhibits = Math.ceil(totalExhibits / 2) // 两侧墙各自至少需要展示的展品数量
  const wallCount = Math.max(exhibitsPerWall, bothSidesExhibits) // 取较大值确保空间足够

  // 走廊总长度
  const corridorLength = wallCount * wallSpacing
  // 起始位置
  const startX = -corridorLength / 1.58

  // 创建地面 - 传统青石板地面
  const floorGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const floorMaterial = new THREE.MeshStandardMaterial({
    color: 0x697785, // 青灰色石材，更符合传统建筑风格
    roughness: 0.9,
    metalness: 0.1,
  })
  const floor = new THREE.Mesh(floorGeometry, floorMaterial)
  floor.rotation.x = -Math.PI / 2
  floor.position.set(0, 0, 0)
  floor.receiveShadow = false // 关闭地面阴影折射
  scene.add(floor)

  // 创建天花板 - 传统斗拱藻井风格
  const ceilingGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const ceilingMaterial = new THREE.MeshStandardMaterial({
    color: 0x8a3324, // 深红木色，代表传统建筑的梁架
    roughness: 0.7,
    metalness: 0.3,
  })
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial)
  ceiling.rotation.x = Math.PI / 2
  ceiling.position.set(0, 4, 0)
  ceiling.receiveShadow = false
  scene.add(ceiling)

  // 添加装饰性横梁 - 模拟传统斗拱结构
  const beamMaterial = new THREE.MeshStandardMaterial({
    color: 0x8b2500, // 更深的红木色
    roughness: 0.8,
    metalness: 0.2,
  })

  // 每隔一定距离添加横梁
  for (let i = -corridorLength / 2; i < corridorLength / 2; i += 5) {
    const beam = new THREE.Mesh(
      new THREE.BoxGeometry(0.4, 0.3, 10),
      beamMaterial
    )
    beam.position.set(i, 3.8, 0)
    beam.castShadow = false
    scene.add(beam)
  }

  // 创建左右墙壁 - 传统中国风格的墙壁
  const sideWallGeometry = new THREE.PlaneGeometry(corridorLength + 20, 4)
  const wallMaterial = new THREE.MeshStandardMaterial({
    color: 0xf5efe0, // 象牙白色，更接近传统中式建筑的墙色
    roughness: 0.9,
    metalness: 0.1,
  })

  // 左墙
  const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  leftWall.position.set(0, 2, 5)
  leftWall.rotation.y = Math.PI
  leftWall.receiveShadow = false
  scene.add(leftWall)

  // 右墙
  const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  rightWall.position.set(0, 2, -5)
  rightWall.receiveShadow = false
  scene.add(rightWall)

  // 创建前后端实体墙
  // 前端墙 - 入口
  const frontWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const frontWallMaterial = new THREE.MeshStandardMaterial({
    color: 0xf5f5dc, // 米色
    roughness: 0.8,
    metalness: 0.2,
  })
  const frontWall = new THREE.Mesh(frontWallGeometry, frontWallMaterial)
  frontWall.position.set(-corridorLength / 2 - 5, 2, 0)
  frontWall.castShadow = false
  frontWall.receiveShadow = false
  scene.add(frontWall)

  // 后端墙 - 尽头
  const endWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const endWallMaterial = new THREE.MeshStandardMaterial({
    color: 0xf5f5dc, // 米色
    roughness: 0.8,
    metalness: 0.2,
  })
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial)
  endWall.position.set(corridorLength / 2.8, 2, 0) // 后墙位置 2.8是根据走廊长度计算的
  endWall.castShadow = false
  endWall.receiveShadow = false
  scene.add(endWall)

  // 创建柱子
  // 每个柱子的位置
  const pillarPositions: number[] = []
  for (let i = 0; i <= wallCount; i++) {
    pillarPositions.push(startX + i * wallSpacing)
  }

  // 添加柱子
  pillarPositions.forEach((x) => {
    createPillar(x, 0, 4.8) // 左侧柱子
    createPillar(x, 0, -4.8) // 右侧柱子
  })

  // 按顺序展示展品 - 先左侧墙再右侧墙
  exhibits.value.forEach((exhibit, index) => {
    // 计算展品位置
    const wallSide = index < exhibitsPerWall ? 'left' : 'right' // 左侧墙展示完再到右侧墙
    const wallIndex = wallSide === 'left' ? index : index - exhibitsPerWall // 在当前墙上的索引

    // 确保不超过墙的数量
    if (wallIndex >= wallCount) return

    // 计算展品位置 - 放在两个柱子之间的中点
    const leftPillarX = pillarPositions[wallIndex]
    const rightPillarX = pillarPositions[wallIndex + 1]
    const exhibitX = (leftPillarX + rightPillarX) / 2

    // 计算z位置 (左侧墙或右侧墙)
    const zPosition = wallSide === 'left' ? 4.9 : -4.9

    // 展示展品
    createExhibitOnWall(exhibitX, 0, zPosition, exhibit, wallSide)
  })

  // 添加中间一排天花板灯光
  for (let i = 0; i < pillarPositions.length - 1; i++) {
    const leftX = pillarPositions[i]
    const rightX = pillarPositions[i + 1]
    const centerX = (leftX + rightX) / 2

    // 在中央位置创建灯光
    createCeilingLight(centerX, 3.8, 0)
  }

  // 设置物理碰撞边界
  addPhysicalBoundaries(corridorLength)

  // 设置初始相机位置在走廊的最早时间点（最前端）
  controls.getObject().position.set(startX + 2, 1.6, 0)
}

// 添加物理碰撞边界
const addPhysicalBoundaries = (corridorLength: number) => {
  // 前墙碰撞检测边界
  const frontBoundary = new THREE.Vector3(-corridorLength / 2 - 4.5, 0, 0)
  // 后墙碰撞检测边界
  const endBoundary = new THREE.Vector3(corridorLength / 2.9, 0, 0)

  // 将边界保存到全局变量中，以便在animate函数中使用
  boundaries = {
    front: frontBoundary.x,
    end: endBoundary.x,
  }
}

// 全局边界变量
let boundaries: { front: number; end: number } = { front: 0, end: 0 }

// 墙面参数常量
const WALL_SPACING = 10 // 每两根柱子间距10个单位
const WALL_HEIGHT = 4 // 墙壁高度

// 在墙上创建展品
const createExhibitOnWall = (
  x: number,
  y: number,
  z: number,
  exhibit: any,
  side: 'left' | 'right'
) => {
  // 创建纹理
  const textureLoader = new THREE.TextureLoader()
  textureLoader.load(
    exhibit.image,
    (texture) => {
      const aspectRatio = texture.image.width / texture.image.height
      const imageWidth = 2
      const imageHeight = imageWidth / aspectRatio

      // 确定朝向 - 平行于墙面
      let wallRotationY = side === 'left' ? Math.PI : 0 // 左侧墙朝向走廊内部，右侧墙朝向走廊内部

      // 创建主展板(超薄，几乎就是一个平面)
      const boardDepth = 0.02 // 非常薄的展板
      // 计算展板尺寸 - 占两柱子间距的80%，墙高的80%
      const boardWidth = WALL_SPACING * 0.8 // 两柱子间距是10个单位，取80%
      const boardHeight = WALL_HEIGHT * 0.8 // 墙高4个单位，取80%
      const boardGeometry = new THREE.BoxGeometry(
        boardWidth,
        boardHeight,
        boardDepth
      )

      // 创建画布元素（将在展板前方直接显示）
      const canvasGeometry = new THREE.PlaneGeometry(imageWidth, imageHeight)

      // 获取原始图片宽高比
      const originalAspectRatio = texture.image.width / texture.image.height
      const canvasMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.FrontSide,
      })

      // 创建展板材质（底板为传统红木材质）
      const boardMaterial = new THREE.MeshStandardMaterial({
        color: 0x8b2500, // 深红木色，传统中国红木色调
        roughness: 0.7,
        metalness: 0.3,
      })

      // 使用不同材质的数组创建展板
      const materials = [
        boardMaterial, // right
        boardMaterial, // left
        boardMaterial, // top
        boardMaterial, // bottom
        boardMaterial, // back (面向墙壁)
        boardMaterial, // front (面向走廊)
      ]

      // 创建展板
      const board = new THREE.Mesh(boardGeometry, materials)

      // 将展板放在墙上(稍微突出一点)
      const wallOffset = 0.1 // 从墙面稍微突出的距离
      board.position.set(
        x,
        y + 2,
        z - (side === 'left' ? -wallOffset : wallOffset)
      )
      board.rotation.y = wallRotationY
      // 为整个展板添加展品数据，使其可交互
      board.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(board)
      exhibitObjects.push(board) // 添加展板到可交互对象

      // 调整图片尺寸，使其适合展板左侧40%区域
      const imageAreaWidth = boardWidth * 0.4 // 图片区域占展板宽度的40%
      const adjustedImageWidth = imageAreaWidth * 0.9 // 图片宽度为图片区域的90%
      // 图片高度设为展板高度的80%，不再基于宽高比自适应
      const adjustedImageHeight = boardHeight * 0.8
      const adjustedCanvasGeometry = new THREE.PlaneGeometry(
        adjustedImageWidth,
        adjustedImageHeight
      )

      // 根据图片原始比例和目标比例调整纹理
      texture.center.set(0.5, 0.5) // 纹理中心点
      const targetAspectRatio = adjustedImageWidth / adjustedImageHeight

      if (originalAspectRatio > targetAspectRatio) {
        // 图片更宽，调整横向重复值
        texture.repeat.set(targetAspectRatio / originalAspectRatio, 1)
      } else {
        // 图片更高，调整纵向重复值
        texture.repeat.set(1, originalAspectRatio / targetAspectRatio)
      }

      // 计算图片水平偏移量 - 将其放置在左侧40%区域
      const horizontalOffset = -boardWidth / 2 + imageAreaWidth / 2 // 向左偏移到左侧40%区域的中心

      // 在展板前方放置图片
      const canvas = new THREE.Mesh(adjustedCanvasGeometry, canvasMaterial)
      const faceOffset = boardDepth / 2 + 0.001 // 稍微在展板前方
      canvas.position.set(
        x + horizontalOffset,
        y + 2,
        z - (side === 'left' ? -faceOffset : faceOffset)
      )
      canvas.rotation.y = wallRotationY
      canvas.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(canvas)
      exhibitObjects.push(canvas)

      // 创建详细信息面板 - 放置在右侧60%区域
      // 创建详细信息画布
      const infoCanvas = document.createElement('canvas')
      const infoContext = infoCanvas.getContext('2d')
      let infoTexture

      if (infoContext) {
        // 创建高分辨率画布以获得更清晰的文本
        infoCanvas.width = 1024
        infoCanvas.height = 1024

        // 设置古典卷轴风格背景
        const gradient = infoContext.createLinearGradient(
          0,
          0,
          0,
          infoCanvas.height
        )
        gradient.addColorStop(0, 'rgba(242, 232, 212, 0.9)') // 浅米黄色，类似宣纸，提高不透明度
        gradient.addColorStop(1, 'rgba(230, 220, 200, 0.9)') // 略深的米色，提高不透明度
        infoContext.fillStyle = gradient
        infoContext.fillRect(0, 0, infoCanvas.width, infoCanvas.height)

        // 添加淡淡的纹理
        infoContext.strokeStyle = 'rgba(150, 120, 100, 0.05)' // 更淡的棕色线条，减少对文字可读性的干扰
        infoContext.lineWidth = 1

        // 横向纹理线条
        for (let i = 0; i < infoCanvas.height; i += 25) {
          infoContext.beginPath()
          infoContext.moveTo(0, i)
          infoContext.lineTo(infoCanvas.width, i)
          infoContext.stroke()
        }

        // 绘制标题 - 更大更鲜明的字体
        infoContext.font = 'bold 70px KaiTi, STKaiti, serif'
        infoContext.fillStyle = '#5C0000' // 加深的红木色，更加醒目
        infoContext.textAlign = 'left'
        infoContext.fillText(exhibit.title, 40, 100)

        // 绘制分隔线 - 典雅中国风样式
        infoContext.strokeStyle = '#800000' // 深红色分隔线，更加醒目
        infoContext.lineWidth = 5 // 更粗的线条
        infoContext.beginPath()
        infoContext.moveTo(40, 130)
        infoContext.lineTo(infoCanvas.width - 40, 130)
        infoContext.stroke()

        // 添加装饰图案
        const patternSize = 25 // 稍微增大装饰图案
        infoContext.fillStyle = '#800000' // 深红色，与分隔线匹配
        infoContext.fillRect(
          40,
          130 - patternSize / 2,
          patternSize,
          patternSize
        )
        infoContext.fillRect(
          infoCanvas.width - 40 - patternSize,
          130 - patternSize / 2,
          patternSize,
          patternSize
        )

        // 绘制年代与时期 - 更大更醒目的字体
        infoContext.font = 'bold 52px KaiTi, STKaiti, serif'
        infoContext.fillStyle = '#6B3300' // 深棕色，更加醒目

        infoContext.fillText(`类型: ${exhibit.type}`, 40, 270)

        // 绘制描述文本 - 自动换行，更大更醒目的字体
        infoContext.font = 'bold 46px KaiTi, STKaiti, serif' // 添加粗体
        infoContext.fillStyle = '#302010' // 更深的棕黑色，提高对比度

        // 文本换行算法
        const maxWidth = infoCanvas.width - 100 // 留出更多边距
        const lineHeight = 60 // 增大行间距
        const description = exhibit.description || ''
        let y = 350 // 调整起始位置，给上面的标题和年代留出更多空间

        // 使用中文字符更适合的文本换行方式
        let words = description.split('')
        let line = ''

        for (let i = 0; i < words.length; i++) {
          let testLine = line + words[i]
          let metrics = infoContext.measureText(testLine)

          if (metrics.width > maxWidth && i > 0) {
            infoContext.fillText(line, 40, y)
            line = words[i]
            y += lineHeight

            // 防止文本超出画布底部
            if (y > infoCanvas.height - 40) {
              line += '...'
              infoContext.fillText(line, 40, y)
              break
            }
          } else {
            line = testLine
          }
        }

        // 绘制最后一行
        if (y <= infoCanvas.height - 40) {
          infoContext.fillText(line, 40, y)
        }

        // 创建纹理
        infoTexture = new THREE.CanvasTexture(infoCanvas)
        infoTexture.needsUpdate = true
      }

      // 创建右侧信息面板
      if (infoTexture) {
        // 计算信息面板的尺寸和位置
        const infoAreaWidth = boardWidth * 0.6 // 右侧信息区域占展板宽度的60%
        const infoWidth = infoAreaWidth * 0.9 // 信息面板宽度为信息区域的90%
        const infoHeight = boardHeight * 0.85 // 信息面板高度为展板高度的85%

        const infoGeometry = new THREE.PlaneGeometry(infoWidth, infoHeight)
        const infoMaterial = new THREE.MeshBasicMaterial({
          map: infoTexture,
          side: THREE.FrontSide,
          transparent: true,
          opacity: 1.0, // 完全不透明，提高可读性
          color: 0xfffbf0, // 更亮的米黄色调，提高对比度
        })

        // 计算信息面板的水平偏移量 - 将其放置在右侧60%区域
        const infoHorizontalOffset = boardWidth / 2 - infoAreaWidth / 2

        const infoPanel = new THREE.Mesh(infoGeometry, infoMaterial)
        infoPanel.position.set(
          x + infoHorizontalOffset,
          y + 2,
          z - (side === 'left' ? -faceOffset : faceOffset)
        )
        infoPanel.rotation.y = wallRotationY
        infoPanel.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit } // 确保信息面板也添加展品数据
        scene.add(infoPanel)
        exhibitObjects.push(infoPanel) // 添加到可交互对象
      }

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
      scene.add(ambientLight)

      // 更新加载进度
      loadingProgress.value += 100 / exhibits.value.length
    },
    undefined,
    (error) => {
      console.error('加载纹理失败:', error)
    }
  )
}

// 创建柱子
const createPillar = (x: number, y: number, z: number) => {
  // 柱身
  const pillarGeometry = new THREE.CylinderGeometry(0.4, 0.4, 4, 16)
  const pillarMaterial = new THREE.MeshStandardMaterial({
    color: 0x8b4513, // 深棕色木柱
    roughness: 0.8,
    metalness: 0.2,
  })
  const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial)
  pillar.position.set(x, y + 2, z)
  pillar.castShadow = false
  pillar.receiveShadow = false
  scene.add(pillar)

  // 柱顶装饰
  const capGeometry = new THREE.BoxGeometry(1, 0.2, 1)
  const capMaterial = new THREE.MeshStandardMaterial({
    color: 0xc8161e, // 中国红
    roughness: 0.7,
    metalness: 0.3,
  })
  const cap = new THREE.Mesh(capGeometry, capMaterial)
  cap.position.set(x, y + 4.1, z)
  cap.castShadow = false
  scene.add(cap)

  // 柱底装饰
  const baseGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8)
  const baseMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000, // 黑色底座
    roughness: 0.5,
    metalness: 0.5,
  })
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.set(x, y + 0.05, z)
  base.receiveShadow = false
  scene.add(base)
}

// 创建天花板灯光 - 传统宫灯风格
const createCeilingLight = (x: number, y: number, z: number) => {
  // 灯笼主体 - 六角宫灯造型
  const lanternGeometry = new THREE.BoxGeometry(0.4, 0.6, 0.4)
  const lanternMaterial = new THREE.MeshStandardMaterial({
    color: 0xc8161e, // 中国红
    emissive: 0xc8161e,
    emissiveIntensity: 0.6,
    roughness: 0.6,
    metalness: 0.4,
  })
  const lantern = new THREE.Mesh(lanternGeometry, lanternMaterial)
  lantern.position.set(x, y, z)
  lantern.castShadow = false
  scene.add(lantern)

  // 灯笼顶部
  const topGeometry = new THREE.ConeGeometry(0.25, 0.2, 6)
  const topMaterial = new THREE.MeshStandardMaterial({
    color: 0xffd700, // 金色
    roughness: 0.4,
    metalness: 0.8,
  })
  const top = new THREE.Mesh(topGeometry, topMaterial)
  top.position.set(x, y + 0.4, z)
  top.castShadow = false
  scene.add(top)

  // 灯笼底部
  const bottomGeometry = new THREE.CylinderGeometry(0.1, 0.2, 0.15, 6)
  const bottomMaterial = new THREE.MeshStandardMaterial({
    color: 0xffd700, // 金色
    roughness: 0.4,
    metalness: 0.8,
  })
  const bottom = new THREE.Mesh(bottomGeometry, bottomMaterial)
  bottom.position.set(x, y - 0.35, z)
  bottom.castShadow = false
  scene.add(bottom)

  // 灯笼挂绳 - 红色挂绳
  const stringGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.6, 8)
  const stringMaterial = new THREE.MeshStandardMaterial({
    color: 0xa52a2a, // 棕红色
    roughness: 0.7,
    metalness: 0.3,
  })
  const string = new THREE.Mesh(stringGeometry, stringMaterial)
  string.position.set(x, y + 0.7, z)
  scene.add(string)

  // 点光源 - 更温暖的色调
  const light = new THREE.PointLight(0xffb366, 1.5, 10) // 更暖的橘黄色
  light.position.set(x, y, z)
  scene.add(light)
}

// 创建文本标签
const createTextLabel = (
  text: string,
  x: number,
  y: number,
  z: number,
  rotationY: number,
  scale: number = 1,
  bgColor: string = '#ffffff'
) => {
  // 创建画布并绘制文本
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return

  canvas.width = 256
  canvas.height = 64

  context.fillStyle = bgColor
  context.fillRect(0, 0, canvas.width, canvas.height)

  context.font = '24px KaiTi, STKaiti, serif'
  context.fillStyle = '#000000'
  context.textAlign = 'center'
  context.textBaseline = 'middle'
  context.fillText(text, canvas.width / 2, canvas.height / 2)

  // 创建纹理和材质
  const texture = new THREE.CanvasTexture(canvas)
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.DoubleSide,
    transparent: true,
  })

  // 创建平面几何体
  const geometry = new THREE.PlaneGeometry(1.5 * scale, 0.4 * scale)
  const mesh = new THREE.Mesh(geometry, material)
  mesh.position.set(x, y, z)
  mesh.rotation.y = rotationY

  scene.add(mesh)
  exhibitObjects.push(mesh)
}

// 键盘按下事件处理
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = true
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = true
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = true
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = true
      break
    case 'Space':
      if (canJump.value) {
        velocity.y += 350
      }
      canJump.value = false
      break
    case 'Escape':
      controls.unlock()
      break
  }
}

// 键盘释放事件处理
const onKeyUp = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = false
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = false
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = false
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = false
      break
  }
}

// 鼠标点击事件处理
const onMouseClick = () => {
  if (!controls.isLocked && !isMobileDevice.value) {
    controls.lock()
    return
  }

  checkExhibitInteraction()
}

// 触摸结束事件处理（移动设备）
const onTouchEnd = (event: TouchEvent) => {
  // 防止长按弹出菜单等行为
  event.preventDefault()

  if (event.touches.length > 0 || event.changedTouches.length === 0) return

  // 获取触摸位置
  const touch = event.changedTouches[0]
  checkExhibitInteraction({
    clientX: touch.clientX,
    clientY: touch.clientY,
  })
}

// 检测与展品的交互
const checkExhibitInteraction = (touchPoint?: {
  clientX: number
  clientY: number
}) => {
  // 使用射线检测点击的对象
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()

  // 如果是触摸事件，计算相对坐标
  if (touchPoint) {
    mouse.x = (touchPoint.clientX / window.innerWidth) * 2 - 1
    mouse.y = -(touchPoint.clientY / window.innerHeight) * 2 + 1
  }

  // 设置射线起点和方向（从相机发出）
  raycaster.setFromCamera(mouse, camera)

  // 检测与展品的交叉
  const intersects = raycaster.intersectObjects(exhibitObjects)

  if (intersects.length > 0) {
    const object = intersects[0].object
    if (object.userData && object.userData.isExhibit) {
      // 显示展品详情
      selectedExhibit.value = object.userData.exhibit
      currentImageIndex.value = 0 // 重置图片索引为第一张

      // 调试：检查展品数据结构
      console.log('选中的展品数据:', selectedExhibit.value)
      console.log('展品创建时间:', selectedExhibit.value.created_at)
      console.log('展品更新时间:', selectedExhibit.value.updated_at)

      // 桌面设备才需要解锁
      if (!isMobileDevice.value) {
        controls.unlock()
      }
    }
  }
}

// 窗口大小变化处理
const onWindowResize = () => {
  if (!camera || !renderer) return

  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)

  if (controls.isLocked) {
    // 计算时间增量
    const time = performance.now()
    const delta = (time - prevTime) / 1000

    // 应用阻尼
    velocity.x -= velocity.x * 10.0 * delta
    velocity.z -= velocity.z * 10.0 * delta
    velocity.y -= 9.8 * 100.0 * delta // 应用重力

    // 根据按键状态计算方向
    direction.z = Number(moveForward.value) - Number(moveBackward.value)
    direction.x = Number(moveRight.value) - Number(moveLeft.value)
    direction.normalize() // 确保对角线移动不会更快

    // 应用移动速度（减缓移动速率）
    if (moveForward.value || moveBackward.value)
      velocity.z -= direction.z * 250.0 * delta
    if (moveLeft.value || moveRight.value)
      velocity.x -= direction.x * 250.0 * delta

    // 移动控制器 - 仅在XZ平面上移动，确保Y轴位置固定
    const oldY = controls.getObject().position.y // 保存当前Y轴位置
    controls.moveRight(-velocity.x * delta)
    controls.moveForward(-velocity.z * delta)
    controls.getObject().position.y = oldY // 恢复Y轴位置，确保不会"飞起来"

    // 限制移动范围（保持在走廊内）
    const position = controls.getObject().position

    // 前后端墙碰撞检测
    if (position.x < boundaries.front) position.x = boundaries.front
    if (position.x > boundaries.end) position.x = boundaries.end

    // Z轴限制（走廊宽度）
    if (position.z < -4.5) position.z = -4.5
    if (position.z > 4.5) position.z = 4.5

    // Y轴限制（高度）
    if (position.y < 1.6) {
      velocity.y = 0
      position.y = 1.6
      canJump.value = true
    }

    prevTime = time
  }

  renderer.render(scene, camera)
}

// 返回首页
const goBack = () => {
  router.push('/')
}

// 关闭展品面板
const closeExhibitPanel = () => {
  selectedExhibit.value = null
  currentImageIndex.value = 0 // 重置图片索引

  // 保存当前控制器位置和旋转，避免视角变化
  const currentPosition = controls.getObject().position.clone()
  const currentRotation = new THREE.Euler().copy(controls.getObject().rotation)

  if (controls && !isMobileDevice.value) {
    controls.lock() // 只在非移动设备上重新锁定控制

    // 恢复之前的位置和旋转
    setTimeout(() => {
      controls.getObject().position.copy(currentPosition)
      controls.getObject().rotation.copy(currentRotation)
      // 重新初始化相机角度，确保我们的角度变量与实际相机状态同步
      initializeCameraAngles()
    }, 10)
  } else if (controls && isMobileDevice.value) {
    // 移动设备也需要重新初始化相机角度
    setTimeout(() => {
      controls.getObject().position.copy(currentPosition)
      controls.getObject().rotation.copy(currentRotation)
      // 重新初始化相机角度，确保我们的角度变量与实际相机状态同步
      initializeCameraAngles()
    }, 10)
  }
}

// 检测是否为移动设备
const isMobileDevice = ref(false)

// 检测设备类型
const checkMobileDevice = () => {
  isMobileDevice.value =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
}

// 处理移动摇杆触摸开始
const handleMoveJoystickStart = (event: TouchEvent | MouseEvent) => {
  console.log('移动摇杆触摸开始!', event.type)
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸
  moveActive.value = true

  let clientX: number, clientY: number

  if ('touches' in event) {
    const touch = event.touches[0]
    clientX = touch.clientX
    clientY = touch.clientY
    moveTouchId = touch.identifier // 记录触摸ID
  } else {
    clientX = (event as MouseEvent).clientX
    clientY = (event as MouseEvent).clientY
    moveTouchId = null // 鼠标事件不需要ID
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveStartPosition.x = clientX - joystickRect.left
  moveStartPosition.y = clientY - joystickRect.top
  moveCurrentPosition.x = moveStartPosition.x
  moveCurrentPosition.y = moveStartPosition.y

  // 更新摇杆位置
  updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)

  // 添加移动和结束事件监听
  document.addEventListener('mousemove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleMoveJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleMoveJoystickEnd, {
    passive: false,
  })
}

// 处理移动摇杆移动
const handleMoveJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!moveActive.value) return
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸

  let clientX: number, clientY: number

  if ('touches' in event && event.touches.length > 0) {
    // 查找匹配的触摸点
    let foundTouch = null
    for (let i = 0; i < event.touches.length; i++) {
      if (moveTouchId === null || event.touches[i].identifier === moveTouchId) {
        foundTouch = event.touches[i]
        break
      }
    }
    if (!foundTouch) return
    clientX = foundTouch.clientX
    clientY = foundTouch.clientY
  } else if ('changedTouches' in event && event.changedTouches.length > 0) {
    // 查找匹配的触摸点
    let foundTouch = null
    for (let i = 0; i < event.changedTouches.length; i++) {
      if (
        moveTouchId === null ||
        event.changedTouches[i].identifier === moveTouchId
      ) {
        foundTouch = event.changedTouches[i]
        break
      }
    }
    if (!foundTouch) return
    clientX = foundTouch.clientX
    clientY = foundTouch.clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveCurrentPosition.x = clientX - joystickRect.left
  moveCurrentPosition.y = clientY - joystickRect.top

  // 限制在基座范围内
  const maxDistance = 50
  const dx = moveCurrentPosition.x - moveStartPosition.x
  const dy = moveCurrentPosition.y - moveStartPosition.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance > maxDistance) {
    moveCurrentPosition.x = moveStartPosition.x + (dx * maxDistance) / distance
    moveCurrentPosition.y = moveStartPosition.y + (dy * maxDistance) / distance
  }

  // 更新摇杆位置
  updateMoveJoystickPosition(moveCurrentPosition.x, moveCurrentPosition.y)

  // 计算移动方向
  moveDirection.x = (moveCurrentPosition.x - moveStartPosition.x) / maxDistance // 左右移动，-1到1
  moveDirection.z = (moveStartPosition.y - moveCurrentPosition.y) / maxDistance // 前后移动，-1到1

  // 根据摇杆位置设置移动状态
  moveForward.value = moveDirection.z > 0.2
  moveBackward.value = moveDirection.z < -0.2
  moveLeft.value = moveDirection.x < -0.2
  moveRight.value = moveDirection.x > 0.2
}

// 处理移动摇杆结束
const handleMoveJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  moveActive.value = false

  // 停止所有移动
  moveForward.value = false
  moveBackward.value = false
  moveLeft.value = false
  moveRight.value = false

  // 复位方向向量
  moveDirection.x = 0
  moveDirection.z = 0

  // 复位摇杆位置（动画效果）
  const joystickBase = moveControl.value?.querySelector('.joystick-base')
  if (joystickBase) {
    const baseRect = joystickBase.getBoundingClientRect()
    const centerX = baseRect.width / 2
    const centerY = baseRect.height / 2
    updateMoveJoystickPosition(centerX, centerY)
  } else {
    updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)
  }

  // 移除事件监听
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)
}

// 更新移动摇杆位置
const updateMoveJoystickPosition = (x: number, y: number) => {
  moveJoystickStyle.value = {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  }
  console.log('更新移动摇杆位置:', x, y)
}

// 旧的摇杆开始函数已移除

// 旧的摇杆移动函数已移除

// 旧的摇杆结束和位置更新函数已移除

// 组件挂载时初始化场景
onMounted(() => {
  // 初始化区域信息
  initRegionInfo()

  // 初始化3D场景
  initScene()

  // 旧的摇杆事件绑定已移除，现在使用新的右侧视角摇杆系统
  // 新系统通过模板中的 @touchstart 和 @mousedown 直接绑定
})

// 移动设备屏幕滑动控制视角的相关变量（已禁用，改为使用右侧摇杆）
// let touchStartX = 0
// let touchStartY = 0
// let touchPreviousX = 0
// let touchPreviousY = 0
// let touchMoving = false
// let activeLookTouchId: number | null = null

// 标准FPS相机控制变量
let cameraYaw = 0 // 水平旋转角度（绕Y轴）
let cameraPitch = 0 // 垂直旋转角度（绕X轴）
const maxPitch = Math.PI / 2 - 0.1 // 最大俯仰角（89度），防止万向锁
const minPitch = -Math.PI / 2 + 0.1 // 最小俯仰角（-89度）
const mouseSensitivity = 0.002 // 鼠标灵敏度
const touchSensitivity = 0.006 // 触摸灵敏度（稍高一些）

// 标准FPS相机更新函数
const updateCameraRotation = (
  deltaX: number,
  deltaY: number,
  sensitivity: number
) => {
  if (!controls) return

  // 在移动设备上，我们不检查 isLocked 状态，因为我们总是希望能够控制视角
  // 在桌面设备上，只有锁定时才允许控制
  if (!isMobileDevice.value && !controls.isLocked) return

  // 更新欧拉角
  cameraYaw -= deltaX * sensitivity
  cameraPitch -= deltaY * sensitivity

  // 限制俯仰角，防止翻转和万向锁
  cameraPitch = Math.max(minPitch, Math.min(maxPitch, cameraPitch))

  // 应用旋转到相机
  const camera = controls.getObject()

  // 重置相机旋转
  camera.rotation.set(0, 0, 0)

  // 按正确顺序应用旋转：先Y轴（偏航），再X轴（俯仰）
  camera.rotateY(cameraYaw)
  camera.rotateX(cameraPitch)

  console.log('相机旋转更新:', { cameraYaw, cameraPitch, deltaX, deltaY })
}

// 初始化相机角度（从当前相机状态获取）
const initializeCameraAngles = () => {
  if (!controls) {
    console.log('初始化相机角度失败: controls 不存在')
    return
  }

  const camera = controls.getObject()
  console.log('相机当前状态:', {
    position: camera.position,
    rotation: camera.rotation,
    quaternion: camera.quaternion,
  })

  const euler = new THREE.Euler().setFromQuaternion(camera.quaternion, 'YXZ')
  console.log('从四元数转换的欧拉角:', { x: euler.x, y: euler.y, z: euler.z })

  // 如果相机没有旋转，直接设置为0
  if (Math.abs(euler.x) < 0.001 && Math.abs(euler.y) < 0.001) {
    cameraYaw = 0
    cameraPitch = 0
    console.log('相机无旋转，设置角度为0')
  } else {
    cameraYaw = euler.y
    cameraPitch = euler.x

    // 确保俯仰角在有效范围内
    cameraPitch = Math.max(minPitch, Math.min(maxPitch, cameraPitch))
    console.log('从相机状态获取角度')
  }

  console.log('最终初始化相机角度:', { cameraYaw, cameraPitch })
}

// 移动端手指触摸控制视角已禁用，改为使用右侧摇杆控制
// 以下函数已不再使用，保留注释作为参考
/*
const handleCanvasTouchStart = (event: TouchEvent) => { ... }
const handleCanvasTouchMove = (event: TouchEvent) => { ... }
const handleCanvasTouchEnd = (event: TouchEvent) => { ... }
*/

// 右侧视角摇杆事件处理
const handleLookJoystickStart = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  // 移除 stopPropagation，允许其他摇杆同时工作
  // event.stopPropagation()
  lookActive.value = true

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 重置摇杆位置
  lookStartPosition.x = joystickRect.width / 2
  lookStartPosition.y = joystickRect.height / 2
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0

  // 更新摇杆位置
  updateLookJoystickPosition(0, 0)

  // 使用不同的事件监听器名称，避免与移动摇杆冲突
  document.addEventListener('mousemove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleLookJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleLookJoystickEnd, {
    passive: false,
  })
}

const handleLookJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!lookActive.value) return
  event.preventDefault()

  let clientX: number, clientY: number

  if ('touches' in event && event.touches.length > 0) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 计算相对于摇杆中心的偏移
  const centerX = joystickRect.left + joystickRect.width / 2
  const centerY = joystickRect.top + joystickRect.height / 2
  const offsetX = clientX - centerX
  const offsetY = clientY - centerY

  // 限制摇杆移动范围
  const maxDistance = 40 // 最大移动距离
  const distance = Math.sqrt(offsetX * offsetX + offsetY * offsetY)

  if (distance <= maxDistance) {
    lookCurrentPosition.x = offsetX
    lookCurrentPosition.y = offsetY
  } else {
    // 限制在圆形范围内
    const angle = Math.atan2(offsetY, offsetX)
    lookCurrentPosition.x = Math.cos(angle) * maxDistance
    lookCurrentPosition.y = Math.sin(angle) * maxDistance
  }

  // 更新摇杆位置
  updateLookJoystickPosition(lookCurrentPosition.x, lookCurrentPosition.y)

  // 应用视角旋转 - 使用与手指触摸相同的灵敏度
  // 将摇杆位置转换为类似手指移动的像素值
  // 摇杆最大移动距离是40px，我们需要将其映射到合理的像素移动范围
  // 原来手指触摸的移动距离通常是几个到几十个像素
  const pixelMultiplier = 0.1 // 降低灵敏度，使其更接近手指触摸的感觉
  const deltaX = lookCurrentPosition.x * pixelMultiplier
  const deltaY = lookCurrentPosition.y * pixelMultiplier

  // 使用与手指触摸相同的灵敏度
  updateCameraRotation(deltaX, deltaY, touchSensitivity)
}

const handleLookJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  lookActive.value = false

  // 复位摇杆位置（动画效果）
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0
  updateLookJoystickPosition(0, 0)

  // 移除事件监听
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)
}

const updateLookJoystickPosition = (x: number, y: number) => {
  if (lookJoystickHandle.value) {
    lookJoystickStyle.value = {
      left: '50%',
      top: '50%',
      transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`,
    }
  }
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown)
  document.removeEventListener('keyup', onKeyUp)
  // 旧的摇杆事件监听器已移除
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)

  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener('click', onMouseClick)
    if (isMobileDevice.value) {
      renderer.domElement.removeEventListener('touchend', onTouchEnd)
      // 移动端已禁用手指触摸控制视角，不需要移除这些事件监听器
      // renderer.domElement.removeEventListener('touchstart', handleCanvasTouchStart)
      // renderer.domElement.removeEventListener('touchmove', handleCanvasTouchMove)
      // renderer.domElement.removeEventListener('touchend', handleCanvasTouchEnd)
    }
  }

  window.removeEventListener('resize', onWindowResize)

  cancelAnimationFrame(animationFrameId)

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }
})

// 小程序环境标记
const isFromMiniapp = ref(false)

// 添加区域信息状态
const regionName = ref('')
const provinceId = ref<number | null>(null)
const cityId = ref<number | null>(null)
const districtId = ref<number | null>(null)

// 尝试从route参数或localStorage中获取区域信息
const initRegionInfo = () => {
  // 检查是否从小程序进入
  const urlParams = new URLSearchParams(window.location.search)
  const fromParam = urlParams.get('from') === 'miniapp'
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true'

  isFromMiniapp.value = fromParam || fromStorage

  // 从路由参数中获取区域信息
  if (route.query.regionName)
    regionName.value = route.query.regionName as string
  if (route.query.provinceId)
    provinceId.value = parseInt(route.query.provinceId as string, 10) || null
  if (route.query.cityId)
    cityId.value = parseInt(route.query.cityId as string, 10) || null
  if (route.query.districtId)
    districtId.value = parseInt(route.query.districtId as string, 10) || null

  // 如果路由参数中没有，尝试从localStorage获取
  if (!regionName.value)
    regionName.value = localStorage.getItem('regionName') || '文化传承'
  if (!provinceId.value && localStorage.getItem('provinceId'))
    provinceId.value =
      parseInt(localStorage.getItem('provinceId') || '', 10) || null
  if (!cityId.value && localStorage.getItem('cityId'))
    cityId.value = parseInt(localStorage.getItem('cityId') || '', 10) || null
  if (!districtId.value && localStorage.getItem('districtId'))
    districtId.value =
      parseInt(localStorage.getItem('districtId') || '', 10) || null

  console.log('文化传承展厅接收到区域信息:', {
    regionName: regionName.value,
    provinceId: provinceId.value,
    cityId: cityId.value,
    districtId: districtId.value,
  })
}
</script>

<style scoped lang="scss">
.gallery-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.scene-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;

  & > * {
    pointer-events: auto;
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .icon {
    margin-right: 5px;
    font-size: 1.2rem;
  }
}

.gallery-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

  h1 {
    font-size: 2rem;
    margin: 0 0 5px 0;
    font-weight: bold;
  }

  p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }
}

.loading-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;

  .loading-text {
    margin-bottom: 10px;
    font-size: 1.2rem;
  }

  .progress-bar {
    width: 300px;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;

    .progress {
      height: 100%;
      background-color: #6b0000; /* 更深的红木色，更加醒目 */
      transition: width 0.3s ease;
    }
  }
}

.exhibit-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 900px;
  max-height: 85vh;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;

  &.active {
    opacity: 1;
    visibility: visible;
  }

  .panel-header {
    padding: 15px 20px;
    background-color: #6b0000; /* 更深的红木色，更加醒目 */
    color: #ffffff; /* 纯白色文字，提高对比度 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 4px solid rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      font-family: 'STKaiti', 'KaiTi', serif;
    }

    .heritage-type {
      font-size: 1rem;
      color: #808080;
      margin-bottom: 5px;
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 1.8rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
      }
    }
  }

  .panel-content {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .image-carousel {
      flex-shrink: 0;
      margin-bottom: 20px;
      height: 380px; /* 固定高度确保可见性 */
    }

    .exhibit-info-container {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;

      .exhibit-info {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .info-card {
          background: linear-gradient(
            135deg,
            rgba(255, 251, 240, 0.95),
            rgba(250, 245, 235, 0.9)
          );
          border-radius: 12px;
          border: 1px solid rgba(107, 0, 0, 0.15);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
          }

          .card-header {
            background: linear-gradient(135deg, #6b0000, #8b2500);
            color: white;
            padding: 12px 16px;

            h3 {
              margin: 0;
              font-size: 1.1rem;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;
              font-family: 'STKaiti', 'KaiTi', serif;

              i {
                font-size: 1rem;
                opacity: 0.9;
              }
            }
          }

          .card-content {
            padding: 16px;

            .info-row {
              display: flex;
              align-items: flex-start;
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                font-weight: 600;
                color: #6b0000;
                min-width: 80px;
                font-size: 0.95rem;
              }

              .value {
                flex: 1;
                color: #333;
                font-size: 0.95rem;
                line-height: 1.5;

                &.heritage-type-badge {
                  background: linear-gradient(135deg, #6b0000, #8b2500);
                  color: white;
                  padding: 4px 12px;
                  border-radius: 20px;
                  font-size: 0.85rem;
                  font-weight: 500;
                  display: inline-block;
                }

                &.protection-level {
                  background: linear-gradient(135deg, #228b22, #32cd32);
                  color: white;
                  padding: 3px 10px;
                  border-radius: 15px;
                  font-size: 0.8rem;
                  font-weight: 500;
                  display: inline-block;
                }
              }
            }

            .heritage-brief {
              font-size: 1rem;
              line-height: 1.7;
              color: #333;
              font-family: 'STKaiti', 'KaiTi', serif;
              text-align: justify;
              margin: 0;
            }

            .content-text {
              font-size: 0.95rem;
              line-height: 1.7;
              color: #444;
              text-align: justify;

              h4 {
                color: #6b0000;
                font-size: 1rem;
                margin: 16px 0 8px 0;
                font-weight: 600;
                border-left: 4px solid #6b0000;
                padding-left: 12px;
              }

              p {
                margin: 8px 0;
              }
            }

            .heritage-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              margin-bottom: 12px;

              .heritage-tag {
                background: linear-gradient(
                  135deg,
                  rgba(107, 0, 0, 0.1),
                  rgba(139, 37, 0, 0.1)
                );
                color: #6b0000;
                padding: 4px 10px;
                border-radius: 15px;
                font-size: 0.8rem;
                font-weight: 500;
                border: 1px solid rgba(107, 0, 0, 0.2);
              }
            }

            .cultural-value-text {
              font-size: 0.95rem;
              line-height: 1.6;
              color: #444;
              text-align: justify;
              margin: 0;
              font-style: italic;
            }
          }
        }

        .meta-info {
          margin-top: 20px;
          padding: 12px 16px;
          background: rgba(240, 240, 240, 0.5);
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.1);

          .meta-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.85rem;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              font-weight: 500;
              color: #666;
              min-width: 70px;
            }

            .meta-value {
              color: #888;
            }
          }
        }
      }
    }
  }

  .panel-actions {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    .detail-btn {
      padding: 10px 25px;
      background-color: #6b0000; /* 更深的红木色，更加醒目 */
      color: #ffffff; /* 纯白色文字，提高对比度 */
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.3s ease;
      border: none;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

      &:hover {
        background-color: darken(#6b0000, 10%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5); /* 增强阴影效果 */
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 768px) {
    width: 95%;
    max-height: 90vh;

    .panel-content {
      padding: 15px;

      .image-carousel {
        height: 300px;
      }
    }
  }

  @media screen and (max-width: 480px) {
    .panel-content {
      .image-carousel {
        height: 220px;
      }

      .exhibit-info-container .exhibit-info {
        .heritage-brief {
          font-size: 1rem;
        }
      }
    }

    .panel-header h2 {
      font-size: 1.3rem;
    }
  }
}

.controls-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.move-joystick {
  position: absolute;
  bottom: 80px;
  left: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      pointer-events: none;
      transition: transform 0.05s ease-out;
    }
  }
}

.look-joystick {
  position: absolute;
  bottom: 80px;
  right: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 100, 100, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 100, 100, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 120, 120, 0.9);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      pointer-events: none;
      transition: transform 0.05s ease-out;
    }
  }
}

.image-carousel {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  .exhibit-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: rgba(0, 0, 0, 0.05);
    transition: opacity 0.3s ease;
  }

  .thumbnail-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 0;

    .thumbnails {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 100%;
      padding: 0 15px;
      overflow-x: auto;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        height: 5px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 3px;
      }

      .thumbnail {
        flex: 0 0 auto;
        width: 60px;
        height: 50px;
        border: 2px solid transparent;
        border-radius: 3px;
        overflow: hidden;
        transition: all 0.2s ease;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        &:hover {
          transform: translateY(-2px);
        }

        &.active {
          border-color: #6b0000; /* 更深的红木色，更加醒目 */
          transform: translateY(-3px);
        }
      }
    }
  }

  .carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 10;
    padding: 0 15px;

    .control-btn {
      background-color: rgba(0, 0, 0, 0.6);
      border: none;
      color: white;
      font-size: 1.8rem;
      font-weight: bold;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(139, 37, 0, 0.8); /* 深红木色 */
        transform: scale(1.1);
      }
    }
  }
}

.fullscreen-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;

  .fullscreen-image-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;

    img {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
      border: 2px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    }

    .close-fullscreen-btn {
      position: absolute;
      top: -20px;
      right: -20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid white;
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #6b0000; /* 更深的红木色，更加醒目 */
        transform: rotate(90deg);
      }
    }
  }
}

/* 自定义滚动条样式 */
.exhibit-info-container::-webkit-scrollbar {
  width: 6px;
}

.exhibit-info-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.exhibit-info-container::-webkit-scrollbar-thumb {
  background: rgba(107, 0, 0, 0.7); /* 深红木色，提高不透明度 */
  border-radius: 3px;
}

.exhibit-info-container::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 0, 0, 0.9); /* 深红木色，更高不透明度 */
}

/* 图标样式 */
.icon-info::before {
  content: 'ℹ️';
}
.icon-book::before {
  content: '📖';
}
.icon-detail::before {
  content: '📋';
}
.icon-star::before {
  content: '⭐';
}
.icon-shield::before {
  content: '🛡️';
}
.icon-location::before {
  content: '📍';
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .exhibit-panel {
    .panel-content {
      .exhibit-info-container .exhibit-info {
        gap: 15px;

        .info-card {
          .card-header h3 {
            font-size: 1rem;
          }

          .card-content {
            padding: 12px;

            .info-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .label {
                min-width: auto;
                font-size: 0.9rem;
              }

              .value {
                font-size: 0.9rem;
              }
            }

            .heritage-brief {
              font-size: 0.95rem;
            }

            .content-text {
              font-size: 0.9rem;

              h4 {
                font-size: 0.95rem;
              }
            }
          }
        }

        .meta-info {
          padding: 10px 12px;

          .meta-row {
            font-size: 0.8rem;

            .meta-label {
              min-width: 60px;
            }
          }
        }
      }
    }
  }
}
</style>

