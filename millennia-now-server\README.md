# 中国行政区划查询服务 (CityServerApi)

这是一个使用 FastAPI 和 MySQL 构建的 RESTful API 服务，提供中国行政区划数据的查询功能。

## 功能特点

- 提供省份、城市、区县、街道等多级行政区划查询
- 支持按层级查询行政区划（省→市→区县→街道）
- 支持获取完整地址信息的扁平化结果
- 实现数据缓存以提高响应速度
- 提供规范的错误处理和API文档
- **微信小程序用户认证系统**
- **分级权限管理（超级管理员、区域管理员、游客）**
- **支持微信小程序一键登录和手机号绑定**
- **JWT令牌认证和会话管理**

## 数据库结构

服务使用以下数据表存储行政区划信息：

1. **Province**: 省份信息
   - ProvinceId, Name, Abbr, Alias, Acronym

2. **City**: 城市信息
   - ProvinceId, CityId, Name, Acronym

3. **District**: 区县信息
   - ProvinceId, CityId, DistrictId, Name, Acronym, PostalCode

4. **Street**: 街道信息
   - ProvinceId, CityId, DistrictId, StreetId, Name, Acronym, PostalCode

## API 端点

- **GET /api/v1/provinces**: 获取所有省份列表
- **GET /api/v1/cities?province_id=**: 获取指定省份下的城市列表
- **GET /api/v1/districts?province_id=&city_id=**: 获取指定城市下的区县列表
- **GET /api/v1/streets?province_id=&city_id=&district_id=**: 获取指定区县下的街道列表
- **GET /api/v1/address-info?province_id=&city_id=&district_id=&street_id=**: 获取完整地址信息

## 微信小程序用户认证系统

该系统提供完整的微信小程序用户认证和权限管理功能，支持分级权限管理。

### 用户角色

1. **游客 (guest)**: 默认角色，可查看公开内容
2. **区县管理员 (district_admin)**: 管理特定区县的内容
3. **市级管理员 (city_admin)**: 管理特定城市及其下属区县的内容
4. **省级管理员 (province_admin)**: 管理特定省份及其下属城市、区县的内容
5. **超级管理员 (super_admin)**: 管理所有内容和用户

### 认证API端点

#### 用户认证

- **POST** `/api/v1/auth/wechat/login`
  - 描述: 微信小程序登录
  - 参数: `code` (微信授权码), `encrypted_data` (可选，加密用户信息), `iv` (可选，初始向量)
  - 返回: 访问令牌、刷新令牌和用户信息

- **POST** `/api/v1/auth/phone/bind`
  - 描述: 绑定手机号（需要先登录）
  - 参数: `code` (获取手机号的授权码), `encrypted_data` (加密手机号), `iv` (初始向量)
  - 返回: 更新后的令牌和用户信息

- **POST** `/api/v1/auth/refresh`
  - 描述: 刷新访问令牌
  - 参数: `refresh_token` (刷新令牌)
  - 返回: 新的访问令牌和刷新令牌

- **POST** `/api/v1/auth/logout`
  - 描述: 用户登出
  - 参数: `refresh_token` (刷新令牌)

- **GET** `/api/v1/auth/me`
  - 描述: 获取当前用户信息
  - 需要认证

- **PUT** `/api/v1/auth/me`
  - 描述: 更新当前用户信息
  - 参数: `nickname`, `avatar_url`, `gender`
  - 需要认证

#### 用户管理（管理员功能）

- **GET** `/api/v1/users/`
  - 描述: 获取用户列表（分页）
  - 参数: `page`, `page_size`, `role`, `province_id`, `city_id`, `district_id`, `search`
  - 需要管理员权限

- **GET** `/api/v1/users/{user_id}`
  - 描述: 获取指定用户详情
  - 需要管理员权限

- **PUT** `/api/v1/users/{user_id}/role`
  - 描述: 更新用户角色
  - 参数: `role`, `province_id`, `city_id`, `district_id`
  - 需要管理员权限

- **PUT** `/api/v1/users/{user_id}/status`
  - 描述: 启用或禁用用户
  - 参数: `is_active`
  - 需要管理员权限

- **DELETE** `/api/v1/users/{user_id}`
  - 描述: 删除用户
  - 需要超级管理员权限

### 权限管理规则

1. **数据访问权限**：
   - 超级管理员：可访问所有数据
   - 省级管理员：仅可访问所属省份的数据
   - 市级管理员：仅可访问所属城市的数据
   - 区县管理员：仅可访问所属区县的数据
   - 游客：仅可访问公开数据

2. **用户管理权限**：
   - 超级管理员：可管理所有用户
   - 省级管理员：可管理所属省份内的低级别用户
   - 市级管理员：可管理所属城市内的区县管理员和游客
   - 区县管理员：可管理所属区县内的游客
   - 不能管理同级或更高级别的用户

### 数据表结构（用户系统）

1. **users** (用户表)
   - 存储用户基本信息、角色和管理区域
   - 支持微信openid和手机号登录

2. **user_sessions** (用户会话表)
   - 存储JWT令牌和会话信息
   - 支持令牌刷新和会话管理

3. **login_logs** (登录日志表)
   - 记录用户登录历史和安全信息

## 文化时间线API

该API用于支持文化时间线页面，提供地点历史文化数据。

### 主要API端点

#### 文化时间线页面数据

- **GET** `/api/v1/places/{place_id}/culture-timeline`
  - 描述: 获取指定地点的完整文化时间线页面数据
  - 参数: `place_id` - 地点ID
  - 返回: 包含地点基本信息、时间轴数据、文化传承项目、城市记忆等完整页面数据

#### 地点管理

- **POST** `/api/v1/places`
  - 创建新地点
- **GET** `/api/v1/places`
  - 获取地点列表
- **GET** `/api/v1/places/{place_id}`
  - 获取地点详情
- **PUT** `/api/v1/places/{place_id}`
  - 更新地点信息
- **DELETE** `/api/v1/places/{place_id}`
  - 删除地点

#### 历史时间轴管理

- **POST** `/api/v1/timelines`
  - 创建新的时间轴
- **GET** `/api/v1/places/{place_id}/timelines`
  - 获取特定地点的时间轴列表
- **GET** `/api/v1/timelines/{timeline_id}`
  - 获取时间轴详情
- **PUT** `/api/v1/timelines/{timeline_id}`
  - 更新时间轴信息
- **DELETE** `/api/v1/timelines/{timeline_id}`
  - 删除时间轴

#### 文化遗产标签

- **POST** `/api/v1/heritage-tags`
  - 创建新的文化遗产标签
- **GET** `/api/v1/heritage-tags`
  - 获取标签列表
- **POST** `/api/v1/timelines/{timeline_id}/tags/{tag_id}`
  - 为时间轴添加标签
- **DELETE** `/api/v1/timelines/{timeline_id}/tags/{tag_id}`
  - 从时间轴移除标签

#### 时间轴图片管理

- **POST** `/api/v1/timeline-images`
  - 为时间轴添加图片
- **GET** `/api/v1/timelines/{timeline_id}/images`
  - 获取时间轴的所有图片
- **DELETE** `/api/v1/timeline-images/{image_id}`
  - 删除时间轴图片

#### 文化传承项目

- **POST** `/api/v1/heritage-items`
  - 创建新的文化传承项目
- **GET** `/api/v1/places/{place_id}/heritage-items`
  - 获取特定地点的文化传承项目列表
- **GET** `/api/v1/heritage-items/{item_id}`
  - 获取文化传承项目详情
- **PUT** `/api/v1/heritage-items/{item_id}`
  - 更新文化传承项目
- **DELETE** `/api/v1/heritage-items/{item_id}`
  - 删除文化传承项目

#### 城市记忆

- **POST** `/api/v1/city-memories`
  - 创建新的城市记忆
- **GET** `/api/v1/places/{place_id}/city-memories`
  - 获取特定地点的城市记忆列表
- **GET** `/api/v1/city-memories/{memory_id}`
  - 获取城市记忆详情
- **PUT** `/api/v1/city-memories/{memory_id}`
  - 更新城市记忆
- **DELETE** `/api/v1/city-memories/{memory_id}`
  - 删除城市记忆

#### 样式配置

- **POST** `/api/v1/style-configs`
  - 创建新的样式配置
- **GET** `/api/v1/places/{place_id}/style-config`
  - 获取特定地点的样式配置
- **PUT** `/api/v1/style-configs/{config_id}`
  - 更新样式配置

#### 文件资源管理

- **POST** `/api/v1/upload`
  - 上传文件
- **GET** `/api/v1/resources/{resource_id}`
  - 获取资源信息
- **GET** `/api/v1/resources/{resource_id}/download`
  - 下载资源文件
- **DELETE** `/api/v1/resources/{resource_id}`
  - 删除资源

### 数据表结构

1. **places** (地点基本信息表)
   - 存储地点的基本信息
   - 作为整个系统的核心表，其他表关联到此表

2. **timelines** (历史时间轴表)
   - 存储地点的历史时间轴数据
   - 通过place_id关联到特定地点

3. **timeline_images** (时间轴详情图片表)
   - 存储时间轴详情展开后显示的图片资源
   - 通过timeline_id关联到特定时间轴记录

4. **heritage_tags** (文化遗产标签表)
   - 集中存储所有文化遗产标签
   - 避免标签重复，实现标签的规范化管理

5. **timeline_tags** (时间轴与标签关联表)
   - 建立时间轴与标签之间的多对多关系
   - 通过timeline_id和tag_id关联两个表

6. **heritage_items** (当代文化传承表)
   - 存储当代文化传承项目信息
   - 通过place_id关联到特定地点

7. **city_memories** (城市记忆表)
   - 存储城市发展的重要记忆点
   - 通过place_id关联到特定地点

8. **resources** (文件资源表)
   - 统一管理系统中使用的所有文件资源
   - 所有上传的图片都存储在此表中

9. **style_configs** (样式配置表)
   - 存储UI样式相关的配置信息
   - 支持为不同地点设置不同的主题风格

### 示例数据

可以通过运行`app/database/sample_places_data.sql`脚本来插入示例数据，以便测试API功能。

## 安装与运行

### 前置条件

- Python 3.8+
- MySQL 5.7+

### 安装步骤

1. 克隆代码库:
```bash
git clone [repository_url]
cd millennia-now-server
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. 创建并配置 `.env` 文件:
```bash
cp env_example.txt .env
# 编辑 .env 文件，设置数据库连接信息和微信小程序配置
```

4. 初始化数据库:
```bash
python init_db.py
```

5. 创建超级管理员（可选）:
```bash
python create_superuser.py --openid <your_wechat_openid> --nickname "超级管理员"
```

6. 启动服务:
```bash
python -m app.main
```

7. 访问 API 文档:
```
http://localhost:8000/docs
```

## 微信小程序配置

### 环境变量配置

在 `.env` 文件中配置以下微信小程序相关参数：

```bash
# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id       # 微信小程序AppID
WECHAT_APP_SECRET=your_wechat_app_secret   # 微信小程序AppSecret

# JWT配置
JWT_SECRET_KEY=your_super_secret_key_here_change_in_production
```

### 微信小程序端集成

#### 1. 微信登录

```javascript
// 微信小程序端代码示例
wx.login({
  success: res => {
    if (res.code) {
      // 发送 res.code 到后台服务器
      wx.request({
        url: 'https://your-server.com/api/v1/auth/wechat/login',
        method: 'POST',
        data: {
          code: res.code
        },
        success: response => {
          // 保存访问令牌
          const { access_token, refresh_token } = response.data;
          wx.setStorageSync('access_token', access_token);
          wx.setStorageSync('refresh_token', refresh_token);
        }
      });
    }
  }
});
```

#### 2. 获取用户信息登录

```javascript
// 获取用户信息并登录
wx.getUserProfile({
  desc: '用于完善用户信息',
  success: (res) => {
    wx.login({
      success: loginRes => {
        wx.request({
          url: 'https://your-server.com/api/v1/auth/wechat/login',
          method: 'POST',
          data: {
            code: loginRes.code,
            encrypted_data: res.encryptedData,
            iv: res.iv
          },
          success: response => {
            // 处理登录成功
          }
        });
      }
    });
  }
});
```

#### 3. 绑定手机号

```javascript
// 绑定手机号
wx.getPhoneNumber({
  success: res => {
    wx.request({
      url: 'https://your-server.com/api/v1/auth/phone/bind',
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('access_token')}`
      },
      data: {
        code: res.code,
        encrypted_data: res.encryptedData,
        iv: res.iv
      },
      success: response => {
        // 处理绑定成功
      }
    });
  }
});
```

#### 4. API请求拦截器

```javascript
// 请求拦截器，自动添加认证头
function request(options) {
  const token = wx.getStorageSync('access_token');
  if (token) {
    options.header = options.header || {};
    options.header['Authorization'] = `Bearer ${token}`;
  }
  
  return wx.request({
    ...options,
    fail: (err) => {
      // 处理认证失败，尝试刷新令牌
      if (err.statusCode === 401) {
        refreshToken();
      }
    }
  });
}

function refreshToken() {
  const refreshToken = wx.getStorageSync('refresh_token');
  wx.request({
    url: 'https://your-server.com/api/v1/auth/refresh',
    method: 'POST',
    data: { refresh_token: refreshToken },
    success: res => {
      wx.setStorageSync('access_token', res.data.access_token);
      wx.setStorageSync('refresh_token', res.data.refresh_token);
    }
  });
}
```

## 开发者指南

### 项目结构

```
millennia-now-server/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   └── admin_divisions.py  # API路由实现
│   │   └── api.py                  # API路由注册
│   ├── core/
│   │   ├── cache.py                # 缓存实现
│   │   └── exceptions.py           # 自定义异常
│   ├── database/
│   │   └── db.py                   # 数据库连接配置
│   ├── models/
│   │   └── admin_divisions.py      # SQLAlchemy ORM模型
│   ├── schemas/
│   │   └── admin_division_schemas.py  # Pydantic模型
│   └── main.py                     # 应用程序入口
├── .env.example                    # 环境变量示例
├── README.md                       # 项目说明
└── requirements.txt                # 项目依赖
```

### 缓存策略

API 使用内存缓存来提高查询性能，缓存策略如下:

- 省份、城市、区县、街道数据默认缓存24小时
- 缓存键基于查询参数自动生成
- 可通过修改 `app/core/cache.py` 中的TTL配置调整缓存时间

### 错误处理

API 实现了统一的错误处理模型:

- 资源未找到错误 (404)
- 参数验证错误 (400)
- 数据库错误 (500)

每个错误都返回统一格式的JSON响应，包含 `error_code` 和 `message` 字段。 