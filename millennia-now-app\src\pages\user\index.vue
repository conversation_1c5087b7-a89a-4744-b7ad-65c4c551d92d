<template>
  <view class="user-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image v-if="authManager.currentUser?.avatar_url"
               :src="authManager.currentUser.avatar_url"
               class="avatar-img" />
        <view v-else
              class="avatar-placeholder">
          <text class="avatar-text">{{ getAvatarText() }}</text>
        </view>
      </view>

      <view class="user-info">
        <view class="user-name">
          {{ authManager.currentUser?.nickname || '未设置昵称' }}
        </view>
        <view class="user-role">
          {{ authManager.userRoleDisplayName }}
        </view>
        <view v-if="authManager.currentUser?.phone"
              class="user-phone">
          {{ authManager.currentUser.phone }}
        </view>

        <!-- 管理员管辖权信息 -->
        <view v-if="authManager.isAdmin && authManager.currentUser"
              class="admin-authority">
          <!-- 管辖区域 -->
          <view class="authority-section">
            <text class="authority-title">管辖区域：</text>
            <text class="authority-value">{{ getManagementArea() }}</text>
          </view>

          <!-- 管理模块权限 -->
          <view v-if="authManager.currentUser.role !== 'SUPER_ADMIN'"
                class="authority-section">
            <text class="authority-title">管理权限：</text>
            <view class="module-permissions">
              <view v-for="(permission, key) in modulePermissionLabels"
                    :key="key"
                    :class="['permission-tag', { 'active': hasModulePermission(key) }]">
                {{ permission }}
              </view>
            </view>
          </view>

          <!-- 超级管理员显示全部权限 -->
          <view v-else
                class="authority-section">
            <text class="authority-title">管理权限：</text>
            <view class="super-admin-permissions">
              <text class="super-admin-text">全部权限</text>
            </view>
          </view>
        </view>
      </view>

      <view class="user-actions">
        <button v-if="!authManager.isAuthenticated"
                @click="handleLogin"
                class="login-btn">
          点击登录
        </button>
        <button v-else
                @click="handleLogout"
                class="logout-btn">
          退出登录
        </button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-title">个人中心</view>

      <view class="menu-item"
            @click="editProfile">
        <view class="menu-icon">👤</view>
        <view class="menu-text">编辑资料</view>
        <view class="menu-arrow">></view>
      </view>

    </view>

    <!-- 管理功能（仅管理员可见） -->
    <view v-if="authManager.isAdmin"
          class="menu-section">
      <view class="menu-title">管理功能</view>

      <view class="menu-item"
            @click="goToUserManagement">
        <view class="menu-icon">👥</view>
        <view class="menu-text">用户管理</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item"
            @click="goToCultureManagement">
        <view class="menu-icon">🎨</view>
        <view class="menu-text">文化管理</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 其他功能 -->
    <view class="menu-section">
      <view class="menu-title">其他</view>

      <view class="menu-item"
            @click="showAbout">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 登录/注册弹窗 -->
    <view v-if="showLoginPopup"
          class="popup-overlay"
          @click="closeLoginPopup">
      <view class="login-popup"
            @click.stop>
        <view class="popup-title">{{ isRegisterMode ? '用户注册' : '用户登录' }}</view>

        <!-- 登录表单 -->
        <view v-if="!isRegisterMode"
              class="login-form">
          <view class="form-item">
            <text class="form-label">手机号</text>
            <input v-model="loginForm.phone"
                   class="form-input"
                   placeholder="请输入手机号"
                   maxlength="11"
                   type="number"
                   confirm-type="next"
                   @blur="validateLoginPhone" />
            <text v-if="loginPhoneError"
                  class="error-text">{{ loginPhoneError }}</text>
          </view>

          <view class="form-item">
            <text class="form-label">密码</text>
            <input v-model="loginForm.password"
                   class="form-input"
                   placeholder="请输入密码"
                   type="password"
                   confirm-type="done"
                   @confirm="handlePasswordLogin" />
          </view>

          <view class="popup-actions">
            <button @click="closeLoginPopup"
                    class="cancel-btn">取消</button>
            <button @click="handlePasswordLogin"
                    class="confirm-btn"
                    :disabled="!canLogin">登录</button>
          </view>

          <view class="switch-mode">
            <text class="switch-text">还没有账号？</text>
            <text class="switch-link"
                  @click="switchToRegister">立即注册</text>
          </view>
        </view>

        <!-- 注册表单 -->
        <view v-else
              class="register-form">
          <view class="form-item">
            <text class="form-label">手机号</text>
            <input v-model="registerForm.phone"
                   class="form-input"
                   placeholder="请输入手机号"
                   maxlength="11"
                   type="number"
                   confirm-type="next"
                   @blur="validateRegisterPhone" />
            <text v-if="registerPhoneError"
                  class="error-text">{{ registerPhoneError }}</text>
          </view>

          <view class="form-item">
            <text class="form-label">密码</text>
            <input v-model="registerForm.password"
                   class="form-input"
                   placeholder="请输入密码（6-20位）"
                   type="password"
                   confirm-type="next"
                   @blur="validatePassword" />
            <text v-if="passwordError"
                  class="error-text">{{ passwordError }}</text>
          </view>

          <view class="form-item">
            <text class="form-label">确认密码</text>
            <input v-model="registerForm.confirmPassword"
                   class="form-input"
                   placeholder="请再次输入密码"
                   type="password"
                   confirm-type="done"
                   @blur="validateConfirmPassword"
                   @confirm="handleRegister" />
            <text v-if="confirmPasswordError"
                  class="error-text">{{ confirmPasswordError }}</text>
          </view>

          <view class="popup-actions">
            <button @click="closeLoginPopup"
                    class="cancel-btn">取消</button>
            <button @click="handleRegister"
                    class="confirm-btn"
                    :disabled="!canRegister">注册</button>
          </view>

          <view class="switch-mode">
            <text class="switch-text">已有账号？</text>
            <text class="switch-link"
                  @click="switchToLogin">立即登录</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑资料弹窗 -->
    <view v-if="showEditPopup"
          class="popup-overlay"
          @click="closeProfileEdit">
      <view class="profile-edit-popup"
            @click.stop>
        <view class="popup-title">编辑资料</view>

        <view class="form-item">
          <text class="form-label">昵称</text>
          <input v-model="editForm.nickname"
                 class="form-input"
                 placeholder="请输入昵称"
                 maxlength="20"
                 type="text"
                 confirm-type="done" />
        </view>

        <view class="form-item">
          <text class="form-label">性别</text>
          <picker :value="editForm.gender"
                  :range="genderOptions"
                  range-key="label"
                  @change="onGenderChange">
            <view class="form-input picker-input">
              {{ genderOptions[editForm.gender]?.label || '请选择' }}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">手机号</text>
          <view class="form-input phone-readonly">{{ editForm.phone || '未设置' }}</view>
          <text class="phone-tip">手机号为登录账号，不可修改</text>
        </view>

        <view class="popup-actions">
          <button @click="closeProfileEdit"
                  class="cancel-btn">取消</button>
          <button @click="saveProfile"
                  class="confirm-btn">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { authManager } from '@/store/modules/auth'
import { updateCurrentUser } from '@/api/auth'

// 弹窗显示状态
const showEditPopup = ref(false)
const showLoginPopup = ref(false)
const isRegisterMode = ref(false)

// 登录表单
const loginForm = reactive({
  phone: '',
  password: '',
})

// 注册表单
const registerForm = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
})

// 编辑表单
const editForm = reactive({
  nickname: '',
  gender: 0,
  phone: '',
})

// 验证错误信息
const loginPhoneError = ref('')
const registerPhoneError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 },
]

// 计算属性：是否可以登录
const canLogin = computed(() => {
  return (
    loginForm.phone.trim() !== '' &&
    loginForm.password.trim() !== '' &&
    !loginPhoneError.value
  )
})

// 计算属性：是否可以注册
const canRegister = computed(() => {
  return (
    registerForm.phone.trim() !== '' &&
    registerForm.password.trim() !== '' &&
    registerForm.confirmPassword.trim() !== '' &&
    !registerPhoneError.value &&
    !passwordError.value &&
    !confirmPasswordError.value
  )
})

// 模块权限标签
const modulePermissionLabels = {
  ancient_books: '古籍管理',
  paintings: '书画珍品',
  archives: '档案故事',
  videos: '影像文献',
}

// 获取头像文字
const getAvatarText = () => {
  const nickname = authManager.currentUser?.nickname
  if (nickname && nickname.length > 0) {
    return nickname.charAt(0).toUpperCase()
  }
  return '用'
}

// 获取管理区域信息
const getManagementArea = () => {
  const user = authManager.currentUser
  if (!user || !authManager.isAdmin) return ''

  const areas = []
  if (user.province_name) areas.push(user.province_name)
  if (user.city_name) areas.push(user.city_name)
  if (user.district_name) areas.push(user.district_name)

  return areas.length > 0 ? areas.join(' - ') : '全国'
}

// 检查是否有指定模块权限
const hasModulePermission = (moduleKey: string) => {
  const user = authManager.currentUser
  if (!user || user.role === 'GUEST') return false
  if (user.role === 'SUPER_ADMIN') return true

  const permissions = user.module_permissions
  if (!permissions) return false

  return permissions[moduleKey as keyof typeof permissions] === true
}

// 显示登录弹窗
const handleLogin = () => {
  showLoginPopup.value = true
  isRegisterMode.value = false
  // 清空表单
  loginForm.phone = ''
  loginForm.password = ''
  loginPhoneError.value = ''
}

// 关闭登录弹窗
const closeLoginPopup = () => {
  showLoginPopup.value = false
  isRegisterMode.value = false
  // 清空所有表单和错误信息
  loginForm.phone = ''
  loginForm.password = ''
  registerForm.phone = ''
  registerForm.password = ''
  registerForm.confirmPassword = ''
  loginPhoneError.value = ''
  registerPhoneError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
}

// 切换到注册模式
const switchToRegister = () => {
  isRegisterMode.value = true
  // 清空登录表单错误
  loginPhoneError.value = ''
  // 如果登录表单有手机号，复制到注册表单
  if (loginForm.phone) {
    registerForm.phone = loginForm.phone
  }
}

// 切换到登录模式
const switchToLogin = () => {
  isRegisterMode.value = false
  // 清空注册表单错误
  registerPhoneError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
  // 如果注册表单有手机号，复制到登录表单
  if (registerForm.phone) {
    loginForm.phone = registerForm.phone
  }
}

// 验证登录手机号
const validateLoginPhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!loginForm.phone) {
    loginPhoneError.value = '请输入手机号'
    return false
  }
  if (!phoneRegex.test(loginForm.phone)) {
    loginPhoneError.value = '请输入正确的手机号格式'
    return false
  }
  loginPhoneError.value = ''
  return true
}

// 验证注册手机号
const validateRegisterPhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!registerForm.phone) {
    registerPhoneError.value = '请输入手机号'
    return false
  }
  if (!phoneRegex.test(registerForm.phone)) {
    registerPhoneError.value = '请输入正确的手机号格式'
    return false
  }
  registerPhoneError.value = ''
  return true
}

// 验证密码
const validatePassword = () => {
  if (!registerForm.password) {
    passwordError.value = '请输入密码'
    return false
  }
  if (registerForm.password.length < 6 || registerForm.password.length > 20) {
    passwordError.value = '密码长度应为6-20位'
    return false
  }
  passwordError.value = ''
  return true
}

// 验证确认密码
const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword) {
    confirmPasswordError.value = '请再次输入密码'
    return false
  }
  if (registerForm.confirmPassword !== registerForm.password) {
    confirmPasswordError.value = '两次输入的密码不一致'
    return false
  }
  confirmPasswordError.value = ''
  return true
}

// 账号密码登录
const handlePasswordLogin = async () => {
  try {
    // 验证表单
    if (!validateLoginPhone()) return
    if (!loginForm.password.trim()) {
      uni.showToast({
        title: '请输入密码',
        icon: 'none',
      })
      return
    }

    uni.showLoading({ title: '登录中...' })

    // 调用账号密码登录API
    await authManager.loginWithPassword(loginForm.phone, loginForm.password)

    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    closeLoginPopup()
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请检查账号密码',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 用户注册
const handleRegister = async () => {
  try {
    // 验证所有表单字段
    if (!validateRegisterPhone()) return
    if (!validatePassword()) return
    if (!validateConfirmPassword()) return

    uni.showLoading({ title: '注册中...' })

    // 调用用户注册API
    await authManager.register(registerForm.phone, registerForm.password)

    uni.showToast({
      title: '注册成功，已自动登录',
      icon: 'success',
    })

    // 注册成功后关闭弹窗
    closeLoginPopup()
  } catch (error) {
    console.error('注册失败:', error)
    uni.showToast({
      title: '注册失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 退出登录
const handleLogout = async () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await authManager.logout()
          uni.showToast({
            title: '已退出登录',
            icon: 'success',
          })
        } catch (error) {
          console.error('退出登录失败:', error)
        }
      }
    },
  })
}

// 编辑资料
const editProfile = () => {
  if (!authManager.isAuthenticated) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
    })
    return
  }

  // 初始化表单数据
  editForm.nickname = authManager.currentUser?.nickname || ''
  editForm.gender = authManager.currentUser?.gender || 0
  editForm.phone = authManager.currentUser?.phone || ''

  showEditPopup.value = true
}

// 性别选择变化
const onGenderChange = (e: any) => {
  editForm.gender = parseInt(e.detail.value)
}

// 关闭编辑弹窗
const closeProfileEdit = () => {
  showEditPopup.value = false
}

// 保存资料
const saveProfile = async () => {
  try {
    if (!editForm.nickname.trim()) {
      uni.showToast({
        title: '请输入昵称',
        icon: 'none',
      })
      return
    }

    uni.showLoading({ title: '保存中...' })

    const updateData: any = {
      nickname: editForm.nickname.trim(),
      gender: editForm.gender,
    }

    // 不再允许修改手机号
    // if (editForm.phone) {
    //   updateData.phone = editForm.phone
    // }

    const updatedUser = await updateCurrentUser(updateData)

    authManager.updateUser(updatedUser)
    showEditPopup.value = false

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 跳转到用户管理
const goToUserManagement = () => {
  uni.navigateTo({
    url: '/pages/admin/users',
  })
}

// 跳转到文化管理
const goToCultureManagement = () => {
  if (!authManager.isAuthenticated || !authManager.isAdmin) {
    uni.showToast({
      title: '权限不足',
      icon: 'none',
    })
    return
  }

  const user = authManager.currentUser

  if (!user) {
    uni.showToast({
      title: '用户信息获取失败',
      icon: 'none',
    })
    return
  }

  // 获取模块权限，确保类型安全
  const modulePermissions = user.module_permissions || {
    ancient_books: false,
    paintings: false,
    archives: false,
    videos: false,
  }

  // 构建传递给文化管理页面的参数
  const params = {
    role: user.role,
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    user_id: user.id || '',
    // 添加模块权限参数，确保始终传递字符串值
    ancient_books_permission: modulePermissions.ancient_books
      ? 'true'
      : 'false',
    paintings_permission: modulePermissions.paintings ? 'true' : 'false',
    archives_permission: modulePermissions.archives ? 'true' : 'false',
    videos_permission: modulePermissions.videos ? 'true' : 'false',
  }
  // 将参数编码为URL查询字符串
  const queryString = Object.keys(params)
    .map(
      (key) =>
        `${key}=${encodeURIComponent(params[key as keyof typeof params])}`
    )
    .join('&')

  uni.navigateTo({
    url: `/pages/culture/heritage?${queryString}`,
  })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '千载·今知小程序\n版本:1.0.0\n参赛人:钟宇柠（重庆邮电大学）',
    showCancel: false,
    confirmText: '知道了',
  })
}
</script>

<style scoped>
.user-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 用户信息卡片 */
.user-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #c8161e;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: #ffffff;
  font-size: 48rpx;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-role {
  font-size: 28rpx;
  color: #c8161e;
  margin-bottom: 10rpx;
}

.user-phone {
  font-size: 26rpx;
  color: #666;
}

/* 管理员权限信息 */
.admin-authority {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.authority-section {
  margin-bottom: 15rpx;
}

.authority-section:last-child {
  margin-bottom: 0;
}

.authority-title {
  font-size: 24rpx;
  color: #888;
  margin-right: 10rpx;
}

.authority-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.module-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 8rpx;
}

.permission-tag {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  background-color: #f0f0f0;
  color: #999;
  border: 1rpx solid #e0e0e0;
}

.permission-tag.active {
  background-color: #e8f5e8;
  color: #4caf50;
  border-color: #4caf50;
}

.super-admin-permissions {
  margin-top: 8rpx;
}

.super-admin-text {
  font-size: 24rpx;
  color: #c8161e;
  font-weight: 500;
}

.user-actions {
  margin-left: 20rpx;
}

.login-btn,
.logout-btn {
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  border: none;
}

.login-btn {
  background-color: #c8161e;
  color: #ffffff;
}

.logout-btn {
  background-color: #f5f5f5;
  color: #666;
}

/* 菜单部分 */
.menu-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.menu-title {
  padding: 30rpx 40rpx 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 50rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 弹窗通用样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 登录弹窗 */
.login-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.profile-edit-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.popup-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #333;
  line-height: 1.4;
  min-height: 80rpx;
  display: block;
  outline: none;
}

.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.picker-input {
  display: flex;
  align-items: center;
  color: #333;
}

.error-text {
  color: #ff4444;
  font-size: 24rpx;
  margin-top: 10rpx;
  display: block;
}

.popup-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #c8161e;
  color: #ffffff;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}

/* 登录/注册模式切换 */
.switch-mode {
  text-align: center;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.switch-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.switch-link {
  font-size: 26rpx;
  color: #c8161e;
  text-decoration: underline;
}

/* 编辑资料弹窗中手机号只读样式 */
.phone-readonly {
  background-color: #f0f0f0; /* 浅灰色背景 */
  color: #666; /* 灰色文字 */
  cursor: not-allowed; /* 禁用光标 */
  user-select: none; /* 禁止选择 */
}

.phone-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}
</style> 