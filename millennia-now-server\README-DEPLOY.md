# 🚀 Millennia Now 云服务器部署指南

## 快速开始

### 1. 准备SSL证书
确保您的SSL证书文件位于正确位置：
```bash
ssl/luckyzyn.top.pem  # SSL证书文件
ssl/luckyzyn.top.key  # SSL私钥文件
```

### 2. 一键部署
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 启动所有服务
./deploy.sh

# 或者重新构建镜像
./deploy.sh --build
```

### 3. 验证部署
访问以下地址验证服务是否正常：
- https://luckyzyn.top (主站)
- https://luckyzyn.top/millennia-api/health (API健康检查)

## 服务架构

```
Internet
    ↓
Nginx (80/443) → 域名路由
    ├── / → Dify Web (3000)
    ├── /api → Dify API (5001)
    ├── /millennia-api → FastAPI (8001)
    ├── /minio → MinIO API (9000)
    └── /minio-console → MinIO Console (9001)
```

## 域名路由配置

| 路径 | 目标服务 | 说明 |
|------|----------|------|
| `/` | Dify Web | 主站首页 |
| `/api` | Dify API | Dify API服务 |
| `/console/api` | Dify API | Dify控制台API |
| `/v1` | Dify API | Dify v1 API |
| `/files` | Dify API | 文件服务 |
| `/explore` | Dify Web | 探索页面 |
| `/e/` | Plugin Daemon | 插件服务 |
| `/mcp` | Dify API | MCP协议 |
| `/millennia-api/` | FastAPI | Millennia Now API |
| `/millennia-docs` | FastAPI | API文档 |
| `/minio/` | MinIO API | 对象存储API |
| `/minio-console/` | MinIO Console | 存储管理界面 |
| `/static/` | Nginx | 静态文件 |

## 端口映射

| 服务 | 容器端口 | 宿主机端口 | 外部访问 |
|------|----------|------------|----------|
| Nginx | 80/443 | 80/443 | https://luckyzyn.top |
| FastAPI | 8000 | 8001 | 通过nginx代理 |
| MySQL | 3306 | 3307 | localhost:3307 |
| MinIO API | 9000 | 9000 | 通过nginx代理 |
| MinIO Console | 9001 | 9001 | 通过nginx代理 |

## 常用命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f nginx
docker-compose logs -f app
docker-compose logs -f minio
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart nginx
```

### 停止服务
```bash
docker-compose down
```

### 清理数据
```bash
./deploy.sh --clean
```

## 故障排除

### 1. 端口冲突
如果80/443端口被占用：
```bash
# 查看端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# 停止占用端口的服务
sudo systemctl stop nginx  # 如果是系统nginx
```

### 2. SSL证书问题
```bash
# 检查证书文件
ls -la ssl/
openssl x509 -in ssl/luckyzyn.top.pem -text -noout
```

### 3. 服务无法访问
```bash
# 检查nginx配置
docker-compose exec nginx nginx -t

# 重新加载nginx配置
docker-compose exec nginx nginx -s reload
```

### 4. 数据库连接问题
```bash
# 测试数据库连接
docker-compose exec mysql mysql -u root -p1qaz2wsx -e "SHOW DATABASES;"
```

## 备份与恢复

### 数据库备份
```bash
# docker-compose exec mysql mysqldump -u root -p1qaz2wsx millennia_now > backup_$(date +%Y%m%d).sql
```

### MinIO数据备份
```bash
# tar -czf minio_backup_$(date +%Y%m%d).tar.gz minio/data/
```

## 更新部署

```bash
# 拉取最新代码
git pull

# 重新部署
./deploy.sh --build
```

## 监控和维护

### 健康检查
```bash
# API健康检查
curl -f https://luckyzyn.top/millennia-api/health

# MinIO健康检查
curl -f https://luckyzyn.top/minio/minio/health/live
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h
du -sh minio/data/
```
