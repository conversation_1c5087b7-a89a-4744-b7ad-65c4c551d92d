<template>
  <view class="archive-container">
    <!-- 头部背景 -->
    <view class="header-section">
      <image :src="getImageSrc(pageData?.headerBgImage)"
             mode="aspectFill"
             class="header-bg"
             @error="onImageError" />
      <view class="header-overlay">
        <view class="header-content">
          <text class="main-title">声像文藏</text>
          <text class="sub-title">{{ pageData?.placeName || '千年文脉·声像传承' }}</text>
          <text class="description">{{ pageData?.introduction || '穿越千年时光，聆听文化回响，品味历史文脉的深厚底蕴' }}</text>
        </view>
      </view>

      <!-- 管理模式切换 -->
      <view class="manage-toggle"
            v-if="canManage"
            @click="toggleManageMode">
        <image src="/static/icons/edit.svg"
               class="manage-icon"
               mode="aspectFit" />
        <text class="manage-text">{{ isManageMode ? '退出管理' : '管理' }}</text>
      </view>
    </view>

    <!-- 导航分类 -->
    <view class="category-nav">
      <scroll-view scroll-x="true"
                   class="nav-scroll">
        <view class="nav-item"
              v-for="(category, index) in categories"
              :key="category.key"
              :class="{ active: activeCategory === category.key }"
              @click="switchCategory(category.key)">
          <text class="nav-icon">{{ category.icon }}</text>
          <text class="nav-text">{{ category.name }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 古籍典藏 -->
      <view class="section ancient-books"
            v-if="activeCategory === 'books'">
        <view class="section-header">
          <text class="section-title">📜 古籍典藏</text>
          <text class="section-desc">珍贵古籍的图像、版本、注释、解读</text>
          <view class="add-btn"
                v-if="isManageMode"
                @click="addArchiveItem('books')">
            <image src="/static/icons/add.svg"
                   class="add-icon"
                   mode="aspectFit" />
          </view>
        </view>

        <view class="items-grid">
          <view class="archive-item books-item"
                v-for="(item, index) in booksData"
                :key="item.id"
                @click="viewItemDetail(item)">
            <view class="item-image-container">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="item-image"
                     @error="(e) => onImageError(e, item)" />
              <view class="item-overlay">
                <view class="item-tags">
                  <text class="tag">{{ item.dynasty || '未知朝代' }}</text>
                  <text class="tag">{{ item.edition || '版本待考' }}</text>
                </view>
              </view>
            </view>
            <view class="item-content">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-author">{{ item.author || '佚名' }}</text>
              <text class="item-brief">{{ item.brief }}</text>
              <view class="item-features">
                <text class="feature-tag"
                      v-if="item.has_ocr">OCR识别</text>
                <text class="feature-tag"
                      v-if="item.has_annotation">AI标注</text>
                <text class="feature-tag"
                      v-if="item.has_scan">高清扫描</text>
              </view>
            </view>
            <!-- 管理操作 -->
            <view class="manage-actions"
                  v-if="isManageMode">
              <view class="action-btn edit-btn"
                    @click.stop="editArchiveItem(item)">
                <image src="/static/icons/edit.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
              <view class="action-btn delete-btn"
                    @click.stop="deleteArchiveItem(item)">
                <image src="/static/icons/delete.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 书画珍品 -->
      <view class="section calligraphy"
            v-if="activeCategory === 'calligraphy'">
        <view class="section-header">
          <text class="section-title">🖌️ 书画珍品</text>
          <text class="section-desc">名人字画、碑帖赏析，附作者简介、技法分析</text>
          <view class="add-btn"
                v-if="isManageMode"
                @click="addArchiveItem('calligraphy')">
            <image src="/static/icons/add.svg"
                   class="add-icon"
                   mode="aspectFit" />
          </view>
        </view>

        <view class="items-grid calligraphy-grid">
          <view class="archive-item calligraphy-item"
                v-for="(item, index) in calligraphyData"
                :key="item.id"
                @click="viewItemDetail(item)">
            <view class="item-image-container vertical">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="item-image"
                     @error="(e) => onImageError(e, item)" />
              <view class="item-overlay">
                <view class="artist-info">
                  <text class="artist-name">{{ item.artist || '佚名' }}</text>
                  <text class="style-tag">{{ item.style || '风格待考' }}</text>
                </view>
              </view>
            </view>
            <view class="item-content">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-period">{{ item.period || '年代不详' }}</text>
              <text class="item-brief">{{ item.brief }}</text>
              <view class="item-features">
                <text class="feature-tag"
                      v-if="item.has_style_analysis">风格分析</text>
                <text class="feature-tag"
                      v-if="item.has_technique">技法解读</text>
                <text class="feature-tag"
                      v-if="item.has_history">历史溯源</text>
              </view>
            </view>
            <!-- 管理操作 -->
            <view class="manage-actions"
                  v-if="isManageMode">
              <view class="action-btn edit-btn"
                    @click.stop="editArchiveItem(item)">
                <image src="/static/icons/edit.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
              <view class="action-btn delete-btn"
                    @click.stop="deleteArchiveItem(item)">
                <image src="/static/icons/delete.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 档案故事 -->
      <view class="section archives"
            v-if="activeCategory === 'archives'">
        <view class="section-header">
          <text class="section-title">📄 档案故事</text>
          <text class="section-desc">历史档案、契约、地方志，结合背景讲述文脉故事</text>
          <view class="add-btn"
                v-if="isManageMode"
                @click="addArchiveItem('archives')">
            <image src="/static/icons/add.svg"
                   class="add-icon"
                   mode="aspectFit" />
          </view>
        </view>

        <view class="archive-stories">
          <view class="story-item"
                v-for="(item, index) in archivesData"
                :key="item.id"
                @click="viewItemDetail(item)">
            <view class="story-image">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="story-img"
                     @error="(e) => onImageError(e, item)" />
            </view>
            <view class="story-content">
              <view class="story-header">
                <text class="story-title">{{ item.title }}</text>
                <text class="story-date">{{ item.date || '年代不详' }}</text>
              </view>
              <text class="story-type">{{ item.type || '档案类型' }}</text>
              <text class="story-brief">{{ item.brief }}</text>
              <view class="story-tags">
                <text class="story-tag"
                      v-if="item.has_digital">数字档案</text>
                <text class="story-tag"
                      v-if="item.has_ai_story">AI故事生成</text>
                <text class="story-tag"
                      v-if="item.has_context">历史背景</text>
              </view>
            </view>
            <!-- 管理操作 -->
            <view class="manage-actions"
                  v-if="isManageMode">
              <view class="action-btn edit-btn"
                    @click.stop="editArchiveItem(item)">
                <image src="/static/icons/edit.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
              <view class="action-btn delete-btn"
                    @click.stop="deleteArchiveItem(item)">
                <image src="/static/icons/delete.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 建筑遗构 -->
      <view class="section architecture"
            v-if="activeCategory === 'architecture'">
        <view class="section-header">
          <text class="section-title">🏛️ 建筑遗构</text>
          <text class="section-desc">古民居、庙宇、祠堂、书院等实体遗产影像记录</text>
          <view class="add-btn"
                v-if="isManageMode"
                @click="addArchiveItem('architecture')">
            <image src="/static/icons/add.svg"
                   class="add-icon"
                   mode="aspectFit" />
          </view>
        </view>

        <view class="architecture-grid">
          <view class="architecture-item"
                v-for="(item, index) in architectureData"
                :key="item.id"
                @click="viewItemDetail(item)">
            <view class="arch-image-container">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="arch-image"
                     @error="(e) => onImageError(e, item)" />
              <view class="arch-overlay">
                <view class="arch-type-tag">{{ item.type || '建筑类型' }}</view>
                <view class="arch-period">{{ item.period || '建造年代' }}</view>
              </view>
            </view>
            <view class="arch-content">
              <text class="arch-title">{{ item.title }}</text>
              <text class="arch-location">{{ item.location || '位置待定' }}</text>
              <text class="arch-brief">{{ item.brief }}</text>
              <view class="arch-features">
                <text class="feature-tag"
                      v-if="item.has_3d_scan">三维扫描</text>
                <text class="feature-tag"
                      v-if="item.has_aerial">航拍记录</text>
                <text class="feature-tag"
                      v-if="item.has_structure">结构分析</text>
                <text class="feature-tag"
                      v-if="item.has_history">历史考证</text>
              </view>
            </view>
            <!-- 管理操作 -->
            <view class="manage-actions"
                  v-if="isManageMode">
              <view class="action-btn edit-btn"
                    @click.stop="editArchiveItem(item)">
                <image src="/static/icons/edit.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
              <view class="action-btn delete-btn"
                    @click.stop="deleteArchiveItem(item)">
                <image src="/static/icons/delete.svg"
                       class="btn-icon"
                       mode="aspectFit" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空数据提示 -->
      <view class="empty-container"
            v-if="isCurrentCategoryEmpty">
        <image src="/static/images/no-image.svg"
               class="empty-icon"
               mode="aspectFit" />
        <text class="empty-text">{{ isManageMode ? '暂无数据，点击右上角"+"新建' : '暂无数据' }}</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
import { regionManager } from '../../store/modules/region'
import { getImageProxyUrl } from '../../utils/image'

// 分类定义
const categories = ref([
  { key: 'books', name: '古籍典藏', icon: '📜' },
  { key: 'calligraphy', name: '书画珍品', icon: '🖌️' },
  { key: 'archives', name: '档案故事', icon: '📄' },
  { key: 'architecture', name: '建筑遗构', icon: '🏛️' },
])

// 页面状态
const isLoading = ref(true)
const activeCategory = ref('books')
const isManageMode = ref(false)
const canManage = ref(false) // 根据用户权限设置

// 页面数据
const pageData = ref<any>(null)
const booksData = ref<any[]>([])
const calligraphyData = ref<any[]>([])
const archivesData = ref<any[]>([])
const architectureData = ref<any[]>([])

// 计算当前分类是否为空
const isCurrentCategoryEmpty = computed(() => {
  if (isLoading.value) return false

  switch (activeCategory.value) {
    case 'books':
      return booksData.value.length === 0
    case 'calligraphy':
      return calligraphyData.value.length === 0
    case 'archives':
      return archivesData.value.length === 0
    case 'architecture':
      return architectureData.value.length === 0
    default:
      return true
  }
})

// 初始化页面
onMounted(async () => {
  await loadPageData()

  // 设置导航栏
  uni.setNavigationBarTitle({
    title: '声像文藏',
  })

  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#8B4513',
  })
})

// 加载页面数据
const loadPageData = async () => {
  isLoading.value = true

  try {
    // 这里应该调用相应的API获取声像文藏数据
    // 目前使用模拟数据
    await loadMockData()
  } catch (error) {
    console.error('加载声像文藏数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 加载模拟数据
const loadMockData = async () => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 800))

  // 页面基本信息
  pageData.value = {
    placeName: '江南文化区',
    headerBgImage: '/static/images/archive-bg.jpg',
    introduction: '穿越千年时光，聆听文化回响，品味历史文脉的深厚底蕴',
  }

  // 古籍典藏数据
  booksData.value = [
    {
      id: 1,
      title: '四库全书总目提要',
      author: '纪昀等',
      dynasty: '清代',
      edition: '武英殿版',
      brief: '中国古代目录学的集大成之作，收录古籍约44000种',
      image: '/static/images/books/siku.jpg',
      has_ocr: true,
      has_annotation: true,
      has_scan: true,
    },
    {
      id: 2,
      title: '本草纲目',
      author: '李时珍',
      dynasty: '明代',
      edition: '金陵刻本',
      brief: '中国古代药物学巨著，记载药物1892种',
      image: '/static/images/books/bencao.jpg',
      has_ocr: true,
      has_annotation: false,
      has_scan: true,
    },
  ]

  // 书画珍品数据
  calligraphyData.value = [
    {
      id: 1,
      title: '兰亭序',
      artist: '王羲之',
      period: '东晋',
      style: '行书',
      brief: '天下第一行书，书法艺术的巅峰之作',
      image: '/static/images/calligraphy/lanting.jpg',
      has_style_analysis: true,
      has_technique: true,
      has_history: true,
    },
    {
      id: 2,
      title: '富春山居图',
      artist: '黄公望',
      period: '元代',
      style: '山水画',
      brief: '中国十大传世名画之一，描绘富春江两岸景色',
      image: '/static/images/calligraphy/fuchun.jpg',
      has_style_analysis: true,
      has_technique: false,
      has_history: true,
    },
  ]

  // 档案故事数据
  archivesData.value = [
    {
      id: 1,
      title: '康熙年间土地契约',
      type: '房产契约',
      date: '康熙三十二年',
      brief: '记录了江南地区土地买卖的详细过程，反映了当时的经济状况',
      image: '/static/images/archives/contract.jpg',
      has_digital: true,
      has_ai_story: true,
      has_context: true,
    },
    {
      id: 2,
      title: '民国时期县志稿',
      type: '地方志',
      date: '民国二十三年',
      brief: '详细记录了县内的地理、人文、经济等各方面情况',
      image: '/static/images/archives/gazetter.jpg',
      has_digital: true,
      has_ai_story: false,
      has_context: true,
    },
  ]

  // 建筑遗构数据
  architectureData.value = [
    {
      id: 1,
      title: '文庙大成殿',
      type: '祭祀建筑',
      period: '明代',
      location: '县城中心',
      brief: '保存完好的明代建筑，采用传统木构架结构',
      image: '/static/images/architecture/temple.jpg',
      has_3d_scan: true,
      has_aerial: true,
      has_structure: true,
      has_history: true,
    },
    {
      id: 2,
      title: '古民居群',
      type: '民居建筑',
      period: '清代',
      location: '老街区',
      brief: '典型的江南民居建筑群，展现了传统建筑智慧',
      image: '/static/images/architecture/houses.jpg',
      has_3d_scan: false,
      has_aerial: true,
      has_structure: false,
      has_history: true,
    },
  ]
}

// 切换分类
const switchCategory = (category: string) => {
  activeCategory.value = category
}

// 切换管理模式
const toggleManageMode = () => {
  isManageMode.value = !isManageMode.value
  uni.showToast({
    title: isManageMode.value ? '已进入管理模式' : '已退出管理模式',
    icon: 'none',
  })
}

// 查看详情
const viewItemDetail = (item: any) => {
  if (isManageMode.value) return

  // 根据不同类型跳转到对应的详情页面
  const categoryDetailMap = {
    books: 'archive-detail',
    calligraphy: 'archive-detail',
    archives: 'archive-detail',
    architecture: 'archive-detail',
  }

  const detailPage =
    categoryDetailMap[activeCategory.value as keyof typeof categoryDetailMap]

  uni.navigateTo({
    url: `/pages/culture/${detailPage}?type=${activeCategory.value}&id=${item.id}`,
  })
}

// 新增档案项目
const addArchiveItem = (type: string) => {
  uni.navigateTo({
    url: `/pages/culture/archive-edit?type=${type}&mode=create`,
  })
}

// 编辑档案项目
const editArchiveItem = (item: any) => {
  uni.navigateTo({
    url: `/pages/culture/archive-edit?type=${activeCategory.value}&mode=edit&id=${item.id}`,
  })
}

// 删除档案项目
const deleteArchiveItem = (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${item.title}"吗？删除后无法恢复。`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用删除API
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })

        // 刷新数据
        loadPageData()
      }
    },
  })
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  if (!imageSrc) {
    return '/static/images/no-image.svg'
  }
  return getImageProxyUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event?: any, item?: any) => {
  const target = event?.target || event?.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}
</script>

<style scoped>
.archive-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f4e6 0%, #ffffff 100%);
}

/* 头部区域 */
.header-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(139, 69, 19, 0.7) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  text-align: center;
  color: #ffffff;
  padding: 0 40rpx;
}

.main-title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  letter-spacing: 4rpx;
}

.sub-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.description {
  font-size: 28rpx;
  line-height: 1.6;
  opacity: 0.9;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.manage-toggle {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
}

.manage-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  filter: brightness(0) invert(1);
}

.manage-text {
  color: #ffffff;
  font-size: 24rpx;
}

/* 分类导航 */
.category-nav {
  background: #ffffff;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.nav-scroll {
  white-space: nowrap;
  padding: 0 20rpx;
}

.nav-item {
  display: inline-block;
  padding: 20rpx 32rpx;
  margin-right: 20rpx;
  border-radius: 50rpx;
  background: #f8f4e6;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.nav-item.active {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
  border-color: #8b4513;
  transform: translateY(-2rpx);
}

.nav-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.nav-text {
  font-size: 28rpx;
  color: #8b4513;
  font-weight: 500;
}

.nav-item.active .nav-text {
  color: #ffffff;
}

/* 内容区域 */
.content-area {
  padding: 40rpx 20rpx;
}

.section-header {
  position: relative;
  margin-bottom: 40rpx;
  text-align: center;
}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #8b4513;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
}

.section-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.add-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 48rpx;
  height: 48rpx;
  background: #8b4513;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 古籍典藏样式 */
.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320rpx, 1fr));
  gap: 30rpx;
}

.archive-item {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.archive-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.item-image-container {
  position: relative;
  height: 240rpx;
  overflow: hidden;
}

.item-image {
  width: 100%;
  height: 100%;
}

.item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.2) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20rpx;
}

.item-tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  background: rgba(255, 255, 255, 0.9);
  color: #8b4513;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.item-content {
  padding: 24rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-author {
  font-size: 24rpx;
  color: #8b4513;
  margin-bottom: 12rpx;
}

.item-brief {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.feature-tag {
  background: #f0f8ff;
  color: #0066cc;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  border: 1rpx solid #e0f0ff;
}

/* 书画珍品样式 */
.calligraphy-grid .archive-item {
  max-width: 280rpx;
}

.item-image-container.vertical {
  height: 320rpx;
}

.artist-info {
  align-self: flex-end;
  text-align: right;
}

.artist-name {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

.style-tag {
  background: rgba(255, 255, 255, 0.9);
  color: #8b4513;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
}

.item-period {
  font-size: 22rpx;
  color: #8b4513;
  margin-bottom: 12rpx;
  font-style: italic;
}

/* 档案故事样式 */
.archive-stories {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.story-item {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.story-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.story-image {
  width: 200rpx;
  height: 160rpx;
  flex-shrink: 0;
}

.story-img {
  width: 100%;
  height: 100%;
}

.story-content {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.story-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.story-date {
  font-size: 22rpx;
  color: #8b4513;
  background: #f8f4e6;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.story-type {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.story-brief {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.story-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.story-tag {
  background: #e8f5e8;
  color: #22c55e;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  border: 1rpx solid #d1fae5;
}

/* 建筑遗构样式 */
.architecture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 30rpx;
}

.architecture-item {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.architecture-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.arch-image-container {
  position: relative;
  height: 220rpx;
  overflow: hidden;
}

.arch-image {
  width: 100%;
  height: 100%;
}

.arch-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.4) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20rpx;
}

.arch-type-tag {
  background: rgba(255, 255, 255, 0.9);
  color: #8b4513;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  align-self: flex-start;
}

.arch-period {
  color: #ffffff;
  font-size: 24rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
  align-self: flex-end;
}

.arch-content {
  padding: 24rpx;
}

.arch-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.arch-location {
  font-size: 24rpx;
  color: #8b4513;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.arch-location::before {
  content: '📍';
  margin-right: 8rpx;
}

.arch-brief {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.arch-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.arch-features .feature-tag {
  background: #fff0f5;
  color: #e91e63;
  border: 1rpx solid #fce4ec;
}

/* 管理操作 */
.manage-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.edit-btn {
  background: rgba(0, 122, 255, 0.9);
}

.delete-btn {
  background: rgba(255, 59, 48, 0.9);
}

.btn-icon {
  width: 20rpx;
  height: 20rpx;
  filter: brightness(0) invert(1);
}

/* 空数据状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.4;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8b4513;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .items-grid {
    grid-template-columns: 1fr;
  }

  .architecture-grid {
    grid-template-columns: 1fr;
  }

  .calligraphy-grid {
    grid-template-columns: repeat(auto-fill, minmax(240rpx, 1fr));
  }
}
</style>
</rewritten_file>