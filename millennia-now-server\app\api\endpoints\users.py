from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Optional

from app.database.db import get_db
from app.schemas.user_schemas import (
    UserListRequest, UserListResponse, UserResponse, 
    UserRoleUpdateRequest, ModulePermissionUpdateRequest
)
from app.models.users import User, UserRole, ModulePermission
from app.models.admin_divisions import Province, City, District
from app.core.dependencies import require_admin, require_super_admin
from app.core.auth import permission_service

router = APIRouter()


def add_region_names_to_user(user: User, db: Session) -> UserResponse:
    """
    为用户添加省市区名称信息并返回UserResponse
    """
    # 创建基础的用户响应对象
    user_data = {
        'id': user.id,
        'openid': user.openid,
        'nickname': user.nickname,
        'avatar_url': user.avatar_url,
        'gender': user.gender,
        'phone': user.phone,
        'role': user.role.value if isinstance(user.role, UserRole) else user.role,
        'is_active': user.is_active,
        'province_id': user.province_id,
        'city_id': user.city_id,
        'district_id': user.district_id,
        'module_permissions': user.get_all_module_permissions(),
        'created_at': user.created_at,
        'last_login_at': user.last_login_at,
        'province_name': None,
        'city_name': None,
        'district_name': None
    }
    
    # 获取省份名称
    if user.province_id:
        province = db.query(Province).filter(Province.province_id == user.province_id).first()
        if province:
            user_data['province_name'] = province.name
    
    # 获取城市名称
    if user.city_id and user.province_id:
        city = db.query(City).filter(
            and_(City.city_id == user.city_id, City.province_id == user.province_id)
        ).first()
        if city:
            user_data['city_name'] = city.name
    
    # 获取区县名称
    if user.district_id and user.city_id and user.province_id:
        district = db.query(District).filter(
            and_(
                District.district_id == user.district_id,
                District.city_id == user.city_id,
                District.province_id == user.province_id
            )
        ).first()
        if district:
            user_data['district_name'] = district.name
    
    return UserResponse(**user_data)


@router.get("/", response_model=UserListResponse, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    role: Optional[UserRole] = Query(None, description="角色筛选"),
    province_id: Optional[int] = Query(None, description="省份ID筛选"),
    city_id: Optional[int] = Query(None, description="城市ID筛选"),
    district_id: Optional[int] = Query(None, description="区县ID筛选"),
    search: Optional[str] = Query(None, description="搜索昵称或手机号"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取用户列表（需要管理员权限）
    
    管理员只能查看其管理区域内的用户：
    - 超级管理员：可查看所有用户
    - 省级管理员：只能查看所属省份的用户
    - 市级管理员：只能查看所属城市的用户
    - 区县管理员：只能查看所属区县的用户
    """
    query = db.query(User)
    
    # 根据当前用户权限过滤
    if current_user.role != UserRole.SUPER_ADMIN:
        if current_user.role == UserRole.PROVINCE_ADMIN:
            # 省级管理员只能看到同省内的市级管理员、区县管理员（不显示游客）
            query = query.filter(
                and_(User.role.in_([UserRole.CITY_ADMIN, UserRole.DISTRICT_ADMIN]), 
                     User.province_id == current_user.province_id)
            )
        elif current_user.role == UserRole.CITY_ADMIN:
            # 市级管理员只能看到同市内的区县管理员（不显示游客）
            query = query.filter(
                and_(User.role == UserRole.DISTRICT_ADMIN,
                     User.province_id == current_user.province_id,
                     User.city_id == current_user.city_id)
            )
        elif current_user.role == UserRole.DISTRICT_ADMIN:
            # 区县管理员无法看到任何用户（包括游客和其他管理员）
            # 只能通过搜索功能查找游客
            query = query.filter(User.id == -1)  # 永远不会匹配任何用户
    
    # 应用额外筛选条件
    if role:
        query = query.filter(User.role == role)
    if province_id:
        query = query.filter(User.province_id == province_id)
    if city_id:
        query = query.filter(User.city_id == city_id)
    if district_id:
        query = query.filter(User.district_id == district_id)
    if search:
        query = query.filter(
            or_(
                User.nickname.like(f"%{search}%"),
                User.phone.like(f"%{search}%")
            )
        )
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    users = query.offset(offset).limit(page_size).all()
    
    # 转换响应，添加省市区名称
    user_responses = []
    for user in users:
        user_response = add_region_names_to_user(user, db)
        user_responses.append(user_response)
    
    return UserListResponse(
        users=user_responses,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """获取指定用户详情（需要管理员权限）"""
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查是否有权限查看该用户
    if not permission_service.can_manage_user(current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问该用户信息"
        )
    
    return add_region_names_to_user(user, db)


@router.put("/{user_id}/role", response_model=UserResponse, summary="更新用户角色")
async def update_user_role(
    user_id: int,
    request: UserRoleUpdateRequest,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    更新用户角色（需要管理员权限）
    
    权限规则：
    - 超级管理员可以设置任何角色
    - 省级管理员可以设置区县、市级管理员（限本省）
    - 市级管理员可以设置区县管理员（限本市）
    - 区县管理员不能设置其他管理员角色
    """
    # 添加调试日志
    print(f"更新用户角色请求: user_id={user_id}")
    print(f"  role: {request.role} (type: {type(request.role)})")
    print(f"  province_id: {request.province_id}")
    print(f"  city_id: {request.city_id}")
    print(f"  district_id: {request.district_id}")
    
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查是否有权限管理该用户
    if not permission_service.can_manage_user(current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权管理该用户"
        )
    
    # 验证角色和区域数据的完整性
    if request.role == UserRole.PROVINCE_ADMIN and not request.province_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="省级管理员必须指定省份"
        )
    elif request.role == UserRole.CITY_ADMIN and (not request.province_id or not request.city_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="市级管理员必须指定省份和城市"
        )
    elif request.role == UserRole.DISTRICT_ADMIN and (not request.province_id or not request.city_id or not request.district_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="区县管理员必须指定省份、城市和区县"
        )

    # 检查是否有权限设置目标角色
    if current_user.role != UserRole.SUPER_ADMIN:
        if request.role == UserRole.SUPER_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权设置超级管理员角色"
            )
        
        if current_user.role == UserRole.DISTRICT_ADMIN and request.role != UserRole.GUEST:
            # 区县管理员可以将游客设置为区县管理员，但不能设置其他角色
            if user.role != UserRole.GUEST or request.role != UserRole.DISTRICT_ADMIN:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="区县管理员只能将游客用户设置为区县管理员"
                )
        
        if current_user.role == UserRole.CITY_ADMIN and request.role in [UserRole.PROVINCE_ADMIN, UserRole.CITY_ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="市级管理员不能设置省级或同级管理员"
            )
        
        if current_user.role == UserRole.PROVINCE_ADMIN and request.role == UserRole.PROVINCE_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="省级管理员不能设置同级管理员"
            )
        
        # 检查区域权限：管理员只能在其管辖区域内设置下级管理员
        if request.role in [UserRole.PROVINCE_ADMIN, UserRole.CITY_ADMIN, UserRole.DISTRICT_ADMIN]:
            if current_user.role == UserRole.PROVINCE_ADMIN:
                # 省级管理员只能在自己的省份内设置管理员
                if request.province_id != current_user.province_id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="只能在自己管辖的省份内设置管理员"
                    )
            elif current_user.role == UserRole.CITY_ADMIN:
                # 市级管理员只能在自己的城市内设置管理员
                if request.province_id != current_user.province_id or request.city_id != current_user.city_id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="只能在自己管辖的城市内设置管理员"
                    )
    
    # 更新用户角色和管理区域
    print(f"开始更新用户数据:")
    print(f"  更新前: role={user.role}, province_id={user.province_id}, city_id={user.city_id}, district_id={user.district_id}")
    
    user.role = request.role
    
    # 更新模块权限（如果提供了）
    if request.module_permissions is not None:
        user.module_permissions = request.module_permissions
        print(f"  更新模块权限: {request.module_permissions}")
    
    # 根据角色设置管理区域
    if request.role == UserRole.GUEST or request.role == UserRole.SUPER_ADMIN:
        # 游客和超级管理员不需要管理区域
        user.province_id = None
        user.city_id = None
        user.district_id = None
        print(f"  {request.role.value}角色: 清除所有区域信息")
    elif request.role == UserRole.PROVINCE_ADMIN:
        # 省级管理员只需要省份ID
        user.province_id = request.province_id
        user.city_id = None
        user.district_id = None
        print(f"  省级管理员: 设置province_id={request.province_id}")
    elif request.role == UserRole.CITY_ADMIN:
        # 市级管理员需要省份ID和城市ID
        user.province_id = request.province_id
        user.city_id = request.city_id
        user.district_id = None
        print(f"  市级管理员: 设置province_id={request.province_id}, city_id={request.city_id}")
    elif request.role == UserRole.DISTRICT_ADMIN:
        # 区县管理员需要省份ID、城市ID和区县ID
        user.province_id = request.province_id
        user.city_id = request.city_id
        user.district_id = request.district_id
        print(f"  区县管理员: 设置province_id={request.province_id}, city_id={request.city_id}, district_id={request.district_id}")
    
    print(f"  更新后: role={user.role}, province_id={user.province_id}, city_id={user.city_id}, district_id={user.district_id}")
    
    db.commit()
    
    # 添加成功日志
    print(f"用户角色更新成功: user_id={user_id}, new_role={user.role.value}, province_id={user.province_id}, city_id={user.city_id}, district_id={user.district_id}")
    
    return add_region_names_to_user(user, db)


@router.put("/{user_id}/status", response_model=UserResponse, summary="更新用户状态")
async def update_user_status(
    user_id: int,
    is_active: bool,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """启用或禁用用户（需要管理员权限）"""
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查是否有权限管理该用户
    if not permission_service.can_manage_user(current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权管理该用户"
        )
    
    # 不能禁用自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能禁用自己"
        )
    
    user.is_active = is_active
    db.commit()
    
    return add_region_names_to_user(user, db)


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_super_admin()),
    db: Session = Depends(get_db)
):
    """删除用户（仅超级管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )
    
    db.delete(user)
    db.commit()
    
    return {"message": "用户删除成功"}


@router.get("/search/guests", response_model=UserListResponse, summary="搜索游客用户")
async def search_guest_users(
    phone: str = Query(..., description="手机号搜索（必须完整准确）"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    搜索游客用户（需要管理员权限）
    
    专门用于通过完整准确的手机号搜索游客用户
    注意：必须提供完整的手机号，不支持模糊搜索
    """
    # 构建查询，只搜索游客用户，使用精确匹配
    query = db.query(User).filter(
        User.role == UserRole.GUEST,
        User.phone == phone  # 精确匹配手机号
    )
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    users = query.offset(offset).limit(page_size).all()
    
    # 转换响应，添加省市区名称
    user_responses = []
    for user in users:
        user_response = add_region_names_to_user(user, db)
        user_responses.append(user_response)
    
    return UserListResponse(
        users=user_responses,
        total=total,
        page=page,
        page_size=page_size
    )


@router.put("/{user_id}/module-permissions", response_model=UserResponse, summary="更新用户模块权限")
async def update_user_module_permissions(
    user_id: int,
    request: ModulePermissionUpdateRequest,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    更新用户的模块管理权限（需要管理员权限）
    
    权限规则：
    - 超级管理员可以设置所有用户的模块权限
    - 其他管理员只能为其管辖区域内的用户设置权限
    - 游客不能拥有任何管理权限
    """
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查是否有权限管理该用户
    if not permission_service.can_manage_user(current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权管理该用户"
        )
    
    # 游客不能拥有任何管理权限
    if user.role == UserRole.GUEST:
        has_any_permission = any(request.module_permissions.values())
        if has_any_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="游客用户不能拥有管理权限"
            )
    
    # 更新模块权限
    user.module_permissions = request.module_permissions
    db.commit()
    
    return add_region_names_to_user(user, db)


@router.get("/{user_id}/module-permissions", summary="获取用户模块权限")
async def get_user_module_permissions(
    user_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """获取指定用户的模块权限（需要管理员权限）"""
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查是否有权限查看该用户
    if not permission_service.can_manage_user(current_user, user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问该用户信息"
        )
    
    return {
        "user_id": user.id,
        "module_permissions": user.get_all_module_permissions()
    }


@router.get("/module-permissions/available", summary="获取可用的模块权限列表")
async def get_available_module_permissions(
    current_user: User = Depends(require_admin())
):
    """获取所有可用的模块权限列表"""
    return {
        "modules": [
            {
                "key": module.value,
                "name": {
                    "ancient_books": "古籍管理",
                    "paintings": "书画珍品",
                    "archives": "档案故事",
                    "videos": "影像文献"
                }.get(module.value, module.value),
                "description": {
                    "ancient_books": "管理古籍典藏模块，包括古籍录入、编辑、删除等操作",
                    "paintings": "管理书画珍品模块，包括书画作品录入、编辑、删除等操作",
                    "archives": "管理档案故事模块，包括档案录入、编辑、删除等操作",
                    "videos": "管理影像文献模块，包括视频录入、编辑、删除等操作"
                }.get(module.value, f"{module.value}模块管理权限")
            }
            for module in ModulePermission
        ]
    } 