<template>
  <view class="container">
    <!-- 顶部操作区 -->
    <view class="action-bar">
      <view class="post-btn"
            @click="showPostModal">
        <text class="post-icon">✏️</text>
        <text class="post-text">发布动态</text>
      </view>
      <view class="filter-tabs">
        <view v-for="(tab, index) in tabs"
              :key="index"
              :class="['tab-item', currentTab === index ? 'active' : '']"
              @click="switchTab(index)">
          <text>{{ tab }}</text>
        </view>
      </view>
    </view>

    <!-- 动态列表 -->
    <scroll-view scroll-y
                 class="post-list"
                 @scrolltolower="loadMore"
                 refresher-enabled
                 :refresher-triggered="refreshing"
                 @refresherrefresh="onRefresh">
      <view class="post-item"
            v-for="(item, index) in postList"
            :key="index">
        <!-- 用户信息 -->
        <view class="post-header">
          <view class="user-info">
            <image class="avatar"
                   :src="item.avatar"
                   mode="aspectFill"></image>
            <view class="user-detail">
              <text class="username">{{ item.username }}</text>
              <text class="user-tag"
                    v-if="item.isOfficial">官方</text>
              <text class="post-time">{{ item.time }}</text>
            </view>
          </view>
          <text class="more-icon">⋮</text>
        </view>

        <!-- 动态内容 -->
        <view class="post-content">
          <text class="post-text">{{ item.content }}</text>
          <!-- 图片区域 -->
          <view :class="['post-images', `post-images-${item.images.length > 4 ? 'multi' : item.images.length}`]">
            <image v-for="(img, imgIndex) in item.images"
                   :key="imgIndex"
                   :src="img"
                   mode="aspectFill"
                   class="post-image"
                   @click="previewImage(item.images, imgIndex)"></image>
          </view>
          <!-- 位置信息 -->
          <view class="post-location"
                v-if="item.location">
            <text class="location-icon">📍</text>
            <text class="location-text">{{ item.location }}</text>
          </view>
        </view>

        <!-- 互动区域 -->
        <view class="post-actions">
          <view class="action-item"
                @click="likePost(index)">
            <text :class="['action-icon', item.isLiked ? 'liked' : '']">{{ item.isLiked ? '❤️' : '🤍' }}</text>
            <text class="action-count">{{ item.likes }}</text>
          </view>
          <view class="action-item"
                @click="navigateToComments(item.id)">
            <text class="action-icon">💬</text>
            <text class="action-count">{{ item.comments }}</text>
          </view>
          <view class="action-item"
                @click="sharePost(item)">
            <text class="action-icon">🔄</text>
            <text class="action-count">分享</text>
          </view>
        </view>

        <!-- 评论预览 -->
        <view class="post-comments"
              v-if="item.commentList && item.commentList.length > 0">
          <view class="comment-item"
                v-for="(comment, commentIndex) in item.commentList"
                :key="commentIndex">
            <text class="comment-username">{{ comment.username }}：</text>
            <text class="comment-content">{{ comment.content }}</text>
          </view>
          <view class="view-more"
                v-if="item.comments > item.commentList.length"
                @click="navigateToComments(item.id)">
            <text>查看全部{{ item.comments }}条评论</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-state"
            v-if="loading">
        <text>加载中...</text>
      </view>
      <view class="loading-state"
            v-if="!loading && noMore">
        <text>没有更多内容了</text>
      </view>
    </scroll-view>

    <!-- 发布动态弹窗 -->
    <view class="post-modal"
          v-if="showModal">
      <view class="modal-mask"
            @click="hidePostModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text>发布动态</text>
          <text class="close-btn"
                @click="hidePostModal">×</text>
        </view>
        <view class="modal-body">
          <textarea class="post-textarea"
                    v-model="newPost.content"
                    placeholder="分享你的旅行体验..."
                    maxlength="500"></textarea>
          <view class="image-picker">
            <view class="image-list">
              <view class="image-item"
                    v-for="(img, index) in newPost.images"
                    :key="index">
                <image :src="img"
                       mode="aspectFill"></image>
                <text class="delete-icon"
                      @click="removeImage(index)">×</text>
              </view>
              <view class="add-image"
                    @click="chooseImage"
                    v-if="newPost.images.length < 9">
                <text class="add-icon">+</text>
              </view>
            </view>
          </view>
          <view class="location-picker"
                @click="chooseLocation">
            <text class="location-icon">📍</text>
            <text v-if="newPost.location">{{ newPost.location }}</text>
            <text v-else>添加位置</text>
          </view>
        </view>
        <view class="modal-footer">
          <button class="publish-btn"
                  @click="publishPost"
                  :disabled="!canPublish">发布</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 标签页
const tabs = ref(['推荐', '关注', '附近'])
const currentTab = ref(0)

// 动态列表数据
const postList = ref([
  {
    id: '1',
    username: '武汉文旅',
    isOfficial: true,
    avatar: '/static/images/avatars/official.jpg',
    time: '2小时前',
    content:
      '【黄鹤楼秋色正好】秋高气爽，登楼远眺，长江滚滚，气势磅礴。现在正是黄鹤楼最美的季节，各位游客千万不要错过这个绝佳的游览时机！',
    images: [
      '/static/images/posts/post1-1.jpg',
      '/static/images/posts/post1-2.jpg',
      '/static/images/posts/post1-3.jpg',
    ],
    location: '黄鹤楼',
    likes: 287,
    comments: 42,
    isLiked: false,
    commentList: [
      { username: '旅行者小王', content: '美景美如画，下周一定去打卡！' },
      {
        username: '江城老李',
        content: '这个角度拍得真不错，请问是在哪个位置拍的？',
      },
    ],
  },
  {
    id: '2',
    username: '小明同学',
    isOfficial: false,
    avatar: '/static/images/avatars/user1.jpg',
    time: '昨天',
    content:
      '东湖绿道骑行太舒服了！一路上风景如画，空气清新，绝对是周末放松的好去处。分享一些沿途的美景，希望大家也能来体验！',
    images: [
      '/static/images/posts/post2-1.jpg',
      '/static/images/posts/post2-2.jpg',
      '/static/images/posts/post2-3.jpg',
      '/static/images/posts/post2-4.jpg',
    ],
    location: '东湖绿道',
    likes: 156,
    comments: 23,
    isLiked: true,
    commentList: [
      {
        username: '绿道骑士',
        content: '天气好的时候去真是享受，路线请问全程多长？',
      },
      { username: '摄影爱好者', content: '第三张照片的构图太棒了！' },
    ],
  },
  {
    id: '3',
    username: '美食猎人',
    isOfficial: false,
    avatar: '/static/images/avatars/user2.jpg',
    time: '3天前',
    content:
      '户部巷美食一日游！从早吃到晚，热干面、小龙虾、豆皮、鸭脖...样样都不能错过！武汉美食真的太丰富了，吃货们快来打卡~',
    images: [
      '/static/images/posts/post3-1.jpg',
      '/static/images/posts/post3-2.jpg',
    ],
    location: '户部巷',
    likes: 321,
    comments: 67,
    isLiked: false,
    commentList: [
      {
        username: '吃货小李',
        content: '看饿了！请问第一张图是哪家店的热干面？',
      },
      {
        username: '老武汉',
        content: '户部巷人气旺，建议错峰前往，不然要排很久队',
      },
      { username: '旅游达人', content: '收藏了，下次去武汉一定按图索骥！' },
    ],
  },
])

// 加载和刷新状态
const loading = ref(false)
const refreshing = ref(false)
const noMore = ref(false)

// 发布动态相关
const showModal = ref(false)
const newPost = ref({
  content: '',
  images: [],
  location: '',
})

// 计算属性：是否可以发布
const canPublish = computed(() => {
  return newPost.value.content.trim() !== '' || newPost.value.images.length > 0
})

// 切换标签
const switchTab = (index: number) => {
  if (currentTab.value === index) return
  currentTab.value = index
  refreshing.value = true
  setTimeout(() => {
    refreshing.value = false
  }, 1000)
}

// 加载更多
const loadMore = () => {
  if (loading.value || noMore.value) return
  loading.value = true

  // 模拟加载更多数据
  setTimeout(() => {
    if (postList.value.length >= 15) {
      noMore.value = true
    } else {
      // 复制一些现有数据作为新数据
      const newPosts = JSON.parse(JSON.stringify(postList.value.slice(0, 2)))
      newPosts.forEach((post: any, index: number) => {
        post.id = `new-${postList.value.length + index}`
        post.time = '刚刚'
      })
      postList.value = [...postList.value, ...newPosts]
    }
    loading.value = false
  }, 1000)
}

// 下拉刷新
const onRefresh = () => {
  setTimeout(() => {
    // 模拟刷新数据
    refreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'none',
    })
  }, 1000)
}

// 点赞
const likePost = (index: number) => {
  const post = postList.value[index]
  post.isLiked = !post.isLiked
  post.likes += post.isLiked ? 1 : -1
}

// 查看评论
const navigateToComments = (postId: string) => {
  uni.showToast({
    title: '评论功能开发中',
    icon: 'none',
  })
}

// 分享动态
const sharePost = (post: any) => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
}

// 显示发布弹窗
const showPostModal = () => {
  showModal.value = true
}

// 隐藏发布弹窗
const hidePostModal = () => {
  showModal.value = false
  // 重置表单
  newPost.value = {
    content: '',
    images: [],
    location: '',
  }
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 9 - newPost.value.images.length,
    success: (res) => {
      newPost.value.images = [...newPost.value.images, ...res.tempFilePaths]
    },
  })
}

// 移除图片
const removeImage = (index: number) => {
  newPost.value.images.splice(index, 1)
}

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      newPost.value.location = res.name
    },
    fail: () => {
      uni.showToast({
        title: '获取位置失败',
        icon: 'none',
      })
    },
  })
}

// 发布动态
const publishPost = () => {
  if (!canPublish.value) return

  // 模拟发布
  const newPostData = {
    id: `new-${Date.now()}`,
    username: '我',
    isOfficial: false,
    avatar: '/static/images/avatars/me.jpg',
    time: '刚刚',
    content: newPost.value.content,
    images: newPost.value.images,
    location: newPost.value.location,
    likes: 0,
    comments: 0,
    isLiked: false,
    commentList: [],
  }

  postList.value.unshift(newPostData)
  hidePostModal()

  uni.showToast({
    title: '发布成功',
    icon: 'success',
  })
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: images[current],
  })
}
</script>

<style>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部操作区 */
.action-bar {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.post-btn {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  margin-bottom: 20rpx;
}

.post-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.post-text {
  color: #666;
  font-size: 28rpx;
}

.filter-tabs {
  display: flex;
  justify-content: space-around;
  border-bottom: 1rpx solid #efefef;
}

.tab-item {
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #c8161e;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 6rpx;
  background-color: #c8161e;
  border-radius: 3rpx;
}

/* 动态列表 */
.post-list {
  height: calc(100vh - 190rpx);
}

.post-item {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.user-tag {
  font-size: 20rpx;
  color: #ffffff;
  background-color: #c8161e;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  margin-bottom: 4rpx;
}

.post-time {
  font-size: 22rpx;
  color: #999;
}

.more-icon {
  font-size: 36rpx;
  color: #999;
}

.post-content {
  margin-bottom: 20rpx;
}

.post-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.post-images-1 .post-image {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
}

.post-images-2 .post-image,
.post-images-4 .post-image {
  width: 335rpx;
  height: 335rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.post-images-3 .post-image,
.post-images-multi .post-image {
  width: 220rpx;
  height: 220rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
}

.post-images-2 .post-image:nth-child(2n),
.post-images-4 .post-image:nth-child(2n),
.post-images-3 .post-image:nth-child(3n),
.post-images-multi .post-image:nth-child(3n) {
  margin-right: 0;
}

.post-location {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.location-icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.location-text {
  font-size: 24rpx;
  color: #007aff;
}

.post-actions {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid #efefef;
  border-bottom: 1rpx solid #efefef;
  margin-bottom: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.action-icon.liked {
  color: #c8161e;
}

.action-count {
  font-size: 24rpx;
  color: #666;
}

.post-comments {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 12rpx;
}

.comment-item {
  margin-bottom: 10rpx;
}

.comment-username {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.comment-content {
  font-size: 24rpx;
  color: #333;
}

.view-more {
  text-align: center;
  margin-top: 10rpx;
}

.view-more text {
  font-size: 24rpx;
  color: #999;
}

.loading-state {
  text-align: center;
  padding: 30rpx 0;
}

.loading-state text {
  font-size: 24rpx;
  color: #999;
}

/* 发布动态弹窗 */
.post-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: #ffffff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #efefef;
}

.modal-header text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.post-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #efefef;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item,
.add-image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  color: #ffffff;
  text-align: center;
  line-height: 36rpx;
  font-size: 24rpx;
}

.add-image {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1rpx dashed #cdcdcd;
  border-radius: 8rpx;
}

.add-icon {
  font-size: 60rpx;
  color: #cdcdcd;
}

.location-picker {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #efefef;
}

.location-picker text {
  font-size: 28rpx;
  color: #666;
}

.modal-footer {
  padding: 20rpx 30rpx 50rpx;
}

.publish-btn {
  background-color: #c8161e;
  color: #ffffff;
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.publish-btn[disabled] {
  background-color: #f5f5f5;
  color: #999;
}
</style> 