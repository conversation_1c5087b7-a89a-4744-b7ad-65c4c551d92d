<template>
  <view class="location-selector">
    <!-- 触发器 -->
    <view class="trigger"
          @click="showSelector">
      <text class="location-icon">📍</text>
      <text class="location-name">{{ selectedLocationName }}</text>
      <text class="location-arrow">▼</text>
    </view>

    <!-- 弹窗选择器 -->
    <view class="selector-modal"
          v-if="visible"
          @click="hideSelector">
      <view class="modal-content"
            @click.stop>
        <!-- 标题 -->
        <view class="modal-header">
          <text class="modal-title">选择城市</text>
          <text class="modal-close"
                @click="hideSelector">✕</text>
        </view>

        <!-- 当前位置 -->
        <view class="current-location"
              @click="useCurrentLocation">
          <view class="location-info">
            <text class="location-icon">📍</text>
            <text class="location-text">当前位置</text>
          </view>
          <view class="location-detail">
            <text class="location-name"
                  v-if="currentLocationName">{{ currentLocationName }}</text>
            <text class="location-loading"
                  v-else>正在定位...</text>
          </view>
        </view>

        <!-- 选择器 -->
        <view class="selector-content">
          <!-- 省份选择 -->
          <view class="selector-column">
            <text class="column-title">省份</text>
            <scroll-view class="column-list"
                         scroll-y>
              <view class="list-item"
                    :class="{ active: selectedProvince?.province_id === province.province_id }"
                    v-for="province in provinces"
                    :key="province.province_id"
                    @click="selectProvince(province)">
                <text class="item-text">{{ province.name }}</text>
              </view>
            </scroll-view>
          </view>

          <!-- 城市选择 -->
          <view class="selector-column"
                v-if="selectedProvince">
            <text class="column-title">城市</text>
            <scroll-view class="column-list"
                         scroll-y>
              <view class="list-item"
                    :class="{ active: selectedCity?.city_id === city.city_id }"
                    v-for="city in cities"
                    :key="city.city_id"
                    @click="selectCity(city)">
                <text class="item-text">{{ city.name }}</text>
              </view>
            </scroll-view>
          </view>

          <!-- 区县选择 -->
          <view class="selector-column"
                v-if="selectedCity">
            <text class="column-title">区县</text>
            <scroll-view class="column-list"
                         scroll-y>
              <view class="list-item"
                    :class="{ active: selectedDistrict?.district_id === district.district_id }"
                    v-for="district in districts"
                    :key="district.district_id"
                    @click="selectDistrict(district)">
                <text class="item-text">{{ district.name }}</text>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 确认按钮 -->
        <view class="modal-footer">
          <button class="confirm-btn"
                  :disabled="!selectedCity"
                  @click="confirmSelection">
            确认选择
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getProvinces, getCities, getDistricts } from '@/api/admin_divisions'
import type { Province, City, District } from '@/api/admin_divisions'
import { useLocationStore } from '@/store/modules/location'
import { regionManager } from '@/store/modules/region'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请选择城市',
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  change: [
    value: {
      province: Province | null
      city: City | null
      district: District | null
      fullName: string
    }
  ]
}>()

// 状态
const visible = ref(false)
const loading = ref(false)
const currentLocationName = ref('')

// 选择状态
const selectedProvince = ref<Province | null>(null)
const selectedCity = ref<City | null>(null)
const selectedDistrict = ref<District | null>(null)

// 列表数据
const provinces = ref<Province[]>([])
const cities = ref<City[]>([])
const districts = ref<District[]>([])

// 计算属性
const selectedLocationName = computed(() => {
  return props.modelValue || props.placeholder
})

// 页面加载
onMounted(async () => {
  await loadProvinces()
  await getCurrentLocation()
})

// 显示选择器
const showSelector = () => {
  visible.value = true
}

// 隐藏选择器
const hideSelector = () => {
  visible.value = false
}

// 加载省份列表
const loadProvinces = async () => {
  try {
    loading.value = true
    provinces.value = await getProvinces()
  } catch (error) {
    console.error('加载省份失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择省份
const selectProvince = async (province: Province) => {
  selectedProvince.value = province
  selectedCity.value = null
  selectedDistrict.value = null
  cities.value = []
  districts.value = []

  try {
    loading.value = true
    cities.value = await getCities(province.province_id)
  } catch (error) {
    console.error('加载城市失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择城市
const selectCity = async (city: City) => {
  selectedCity.value = city
  selectedDistrict.value = null
  districts.value = []

  try {
    loading.value = true
    districts.value = await getDistricts(
      selectedProvince.value!.province_id,
      city.city_id
    )
  } catch (error) {
    console.error('加载区县失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择区县
const selectDistrict = (district: District) => {
  selectedDistrict.value = district
}

// 获取当前位置
const getCurrentLocation = async () => {
  try {
    const locationStore = useLocationStore()
    const locationInfo = await locationStore.getCurrentLocation()
    currentLocationName.value = locationInfo.fullAddress

    // 自动匹配并设置区域ID
    try {
      await regionManager.matchRegionIdsByLocation({
        province: locationInfo.province,
        city: locationInfo.city,
        district: locationInfo.district,
      })
      console.log('LocationSelector: 定位后自动匹配区域ID成功')
    } catch (regionError) {
      console.warn('LocationSelector: 定位后自动匹配区域ID失败:', regionError)
    }
  } catch (error) {
    currentLocationName.value = '定位失败'
  }
}

// 使用当前位置
const useCurrentLocation = async () => {
  if (currentLocationName.value && currentLocationName.value !== '定位失败') {
    const locationStore = useLocationStore()
    const locationInfo = locationStore.currentLocation

    emit('update:modelValue', locationInfo.fullAddress)
    emit('change', {
      province: null,
      city: null,
      district: null,
      fullName: locationInfo.fullAddress,
    })
    hideSelector()
  } else {
    // 重新尝试获取位置
    try {
      loading.value = true
      const locationStore = useLocationStore()
      const locationInfo = await locationStore.getCurrentLocation()
      currentLocationName.value = locationInfo.fullAddress

      emit('update:modelValue', locationInfo.fullAddress)
      emit('change', {
        province: null,
        city: null,
        district: null,
        fullName: locationInfo.fullAddress,
      })
      hideSelector()
    } catch (error) {
      uni.showToast({
        title: '定位失败，请手动选择',
        icon: 'none',
      })
    } finally {
      loading.value = false
    }
  }
}

// 确认选择
const confirmSelection = () => {
  if (!selectedCity.value) {
    uni.showToast({
      title: '请选择城市',
      icon: 'none',
    })
    return
  }

  // 构建完整地址
  let fullName = selectedProvince.value?.name || ''
  if (selectedCity.value) {
    fullName += selectedCity.value.name
  }
  if (selectedDistrict.value) {
    fullName += selectedDistrict.value.name
  }

  // 保存到状态管理
  const locationStore = useLocationStore()
  locationStore.setLocation({
    province: selectedProvince.value?.name || '',
    city: selectedCity.value?.name || '',
    district: selectedDistrict.value?.name || '',
    street: '',
    fullAddress: fullName,
    longitude: 0,
    latitude: 0,
  })

  // 保存区域ID到全局状态
  regionManager.setSelectedRegionIds(
    selectedProvince.value,
    selectedCity.value,
    selectedDistrict.value
  )

  emit('update:modelValue', fullName)
  emit('change', {
    province: selectedProvince.value,
    city: selectedCity.value,
    district: selectedDistrict.value,
    fullName,
  })

  hideSelector()
}
</script>

<style scoped>
.location-selector {
  position: relative;
}

/* 触发器样式 */
.trigger {
  display: flex;
  align-items: center;
}

.location-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.location-name {
  font-size: 28rpx;
  color: #333;
  max-width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-arrow {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

/* 弹窗样式 */
.selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #efefef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 32rpx;
  color: #999;
}

/* 当前位置 */
.current-location {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #efefef;
}

.location-info {
  display: flex;
  align-items: center;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.location-detail {
  flex: 1;
  text-align: center;
}

.location-loading {
  font-size: 26rpx;
  color: #999;
}

/* 选择器内容 */
.selector-content {
  flex: 1;
  display: flex;
  min-height: 400rpx;
  max-height: 400rpx;
}

.selector-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1rpx solid #efefef;
}

.selector-column:last-child {
  border-right: none;
}

.column-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #efefef;
  background-color: #f8f8f8;
}

.column-list {
  flex: 1;
  height: 0;
}

.list-item {
  padding: 20rpx 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.list-item.active {
  background-color: #fff3f3;
}

.item-text {
  font-size: 24rpx;
  color: #333;
}

.list-item.active .item-text {
  color: #c8161e;
}

/* 弹窗底部 */
.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #efefef;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  background-color: #c8161e;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn[disabled] {
  background-color: #cccccc;
}
</style> 