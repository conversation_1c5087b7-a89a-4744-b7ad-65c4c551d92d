-- 重置用户认证表 - 清理旧数据并重建
-- 解决枚举值不匹配问题

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 清理现有表（按依赖关系顺序）
-- ================================

-- 1. 删除会话表（依赖用户表）
DROP TABLE IF EXISTS `user_sessions`;

-- 2. 删除登录日志表（依赖用户表）
DROP TABLE IF EXISTS `login_logs`;

-- 3. 删除用户表
DROP TABLE IF EXISTS `users`;

-- ================================
-- 重新创建用户认证表
-- ================================

-- 1. 用户表 - 使用正确的枚举值
CREATE TABLE `users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号（登录账号）',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知,1-男,2-女',
    `role` ENUM('GUEST', 'DISTRICT_ADMIN', 'CITY_ADMIN', 'PROVINCE_ADMIN', 'SUPER_ADMIN') NOT NULL DEFAULT 'GUEST' COMMENT '用户角色',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    `province_id` SMALLINT DEFAULT NULL COMMENT '管理的省份ID，仅管理员有效',
    `city_id` SMALLINT DEFAULT NULL COMMENT '管理的城市ID，仅管理员有效',
    `district_id` SMALLINT DEFAULT NULL COMMENT '管理的区县ID，仅管理员有效',
    `module_permissions` JSON DEFAULT NULL COMMENT '模块管理权限JSON：{"ancient_books":true,"paintings":false,"archives":true,"videos":false}',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    
    INDEX `idx_phone` (`phone`),
    INDEX `idx_role` (`role`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_province_id` (`province_id`),
    INDEX `idx_city_id` (`city_id`),
    INDEX `idx_district_id` (`district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户会话表
CREATE TABLE `user_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(255) NOT NULL UNIQUE COMMENT '会话标识',
    `access_token` VARCHAR(500) NOT NULL COMMENT '访问令牌',
    `refresh_token` VARCHAR(500) NOT NULL UNIQUE COMMENT '刷新令牌',
    `device_info` JSON DEFAULT NULL COMMENT '设备信息',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_access_token` (`access_token`(100)),
    INDEX `idx_refresh_token` (`refresh_token`(100)),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 3. 登录日志表
CREATE TABLE `login_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID，可为空（登录失败时）',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '尝试登录的手机号',
    `login_type` VARCHAR(20) NOT NULL DEFAULT 'password' COMMENT '登录方式：password',
    `success` BOOLEAN NOT NULL COMMENT '是否成功',
    `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_login_type` (`login_type`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- ================================
-- 插入测试用户数据
-- ================================

-- 1. 超级管理员
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`) 
VALUES (
    '13800000000', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '系统管理员', 
    'SUPER_ADMIN',
    TRUE
);

-- 2. 省级管理员（示例：重庆市）
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`, `province_id`, `module_permissions`) 
VALUES (
    '13800000001', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '重庆省级管理员', 
    'PROVINCE_ADMIN',
    TRUE,
    50, -- 重庆省ID
    '{"ancient_books": true, "paintings": true, "archives": true, "videos": true}'
);

-- 3. 市级管理员（示例：重庆市主城区）
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`, `province_id`, `city_id`, `module_permissions`) 
VALUES (
    '13800000002', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '重庆市级管理员', 
    'CITY_ADMIN',
    TRUE,
    50, -- 重庆省ID
    5001, -- 重庆市ID
    '{"ancient_books": true, "paintings": false, "archives": true, "videos": false}'
);

-- 4. 区县管理员（示例：渝中区）
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`, `province_id`, `city_id`, `district_id`, `module_permissions`) 
VALUES (
    '13800000003', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '渝中区管理员', 
    'DISTRICT_ADMIN',
    TRUE,
    50, -- 重庆省ID
    5001, -- 重庆市ID
    500103, -- 渝中区ID
    '{"ancient_books": true, "paintings": false, "archives": false, "videos": false}'
);

-- 5. 普通用户
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`) 
VALUES (
    '13900000001', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '普通用户', 
    'GUEST',
    TRUE
);

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ================================
-- 验证创建结果
-- ================================

SELECT 'Auth tables reset successfully!' as message;

-- 显示用户统计
SELECT 
    '用户统计' as category,
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'SUPER_ADMIN' THEN 1 END) as super_admin_count,
    COUNT(CASE WHEN role = 'PROVINCE_ADMIN' THEN 1 END) as province_admin_count,
    COUNT(CASE WHEN role = 'CITY_ADMIN' THEN 1 END) as city_admin_count,
    COUNT(CASE WHEN role = 'DISTRICT_ADMIN' THEN 1 END) as district_admin_count,
    COUNT(CASE WHEN role = 'GUEST' THEN 1 END) as guest_count
FROM users;

-- 显示测试账号信息
SELECT 
    '测试账号信息' as info,
    phone as 手机号,
    nickname as 昵称,
    role as 角色,
    CASE 
        WHEN province_id IS NOT NULL AND city_id IS NOT NULL AND district_id IS NOT NULL 
        THEN CONCAT('省:', province_id, ' 市:', city_id, ' 区:', district_id)
        WHEN province_id IS NOT NULL AND city_id IS NOT NULL 
        THEN CONCAT('省:', province_id, ' 市:', city_id)
        WHEN province_id IS NOT NULL 
        THEN CONCAT('省:', province_id)
        ELSE '全国'
    END as 管辖范围
FROM users 
ORDER BY 
    CASE role 
        WHEN 'SUPER_ADMIN' THEN 1
        WHEN 'PROVINCE_ADMIN' THEN 2
        WHEN 'CITY_ADMIN' THEN 3
        WHEN 'DISTRICT_ADMIN' THEN 4
        WHEN 'GUEST' THEN 5
    END;

SELECT '所有测试账号密码均为: admin123' as password_info;
