from pydantic import BaseModel, field_validator
from typing import Optional, Dict, Any
from datetime import datetime
from app.models.users import UserRole, ModulePermission


class WechatLoginRequest(BaseModel):
    """微信登录请求"""
    code: str  # 微信小程序授权码
    encrypted_data: Optional[str] = None  # 加密的用户信息
    iv: Optional[str] = None  # 初始向量


class PhoneLoginRequest(BaseModel):
    """手机号登录请求"""
    code: str  # 微信小程序获取手机号的code
    encrypted_data: str  # 加密的手机号信息
    iv: str  # 初始向量


class UserUpdateRequest(BaseModel):
    """用户信息更新请求"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None
    phone: Optional[str] = None
    
    @field_validator('gender')
    @classmethod
    def validate_gender(cls, v):
        if v is not None and v not in [0, 1, 2]:
            raise ValueError('性别值必须是0、1或2')
        return v
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        if v is not None:
            # 简单的手机号格式验证
            import re
            if not re.match(r'^1[3-9]\d{9}$', v):
                raise ValueError('手机号格式不正确')
        return v


class UserRoleUpdateRequest(BaseModel):
    """用户角色更新请求（管理员专用）"""
    role: UserRole
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    district_id: Optional[int] = None
    module_permissions: Optional[Dict[str, bool]] = None
    
    @field_validator('role', mode='before')
    @classmethod
    def validate_role(cls, v):
        """确保角色是UserRole枚举类型"""
        if isinstance(v, str):
            # 将字符串转换为枚举
            for role in UserRole:
                if role.value == v:
                    return role
            raise ValueError(f"无效的角色值: {v}")
        return v
    
    @field_validator('module_permissions')
    @classmethod
    def validate_module_permissions(cls, v):
        """验证模块权限"""
        if v is not None:
            valid_modules = {module.value for module in ModulePermission}
            for module in v.keys():
                if module not in valid_modules:
                    raise ValueError(f"无效的模块权限: {module}")
        return v


class ModulePermissionUpdateRequest(BaseModel):
    """模块权限更新请求"""
    module_permissions: Dict[str, bool]
    
    @field_validator('module_permissions')
    @classmethod
    def validate_module_permissions(cls, v):
        """验证模块权限"""
        valid_modules = {module.value for module in ModulePermission}
        for module in v.keys():
            if module not in valid_modules:
                raise ValueError(f"无效的模块权限: {module}")
        return v


class UserResponse(BaseModel):
    """用户信息响应"""
    id: int
    openid: str
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: int
    phone: Optional[str] = None
    role: str
    is_active: bool
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    district_id: Optional[int] = None
    province_name: Optional[str] = None  # 省份名称
    city_name: Optional[str] = None  # 城市名称
    district_name: Optional[str] = None  # 区县名称
    module_permissions: Optional[Dict[str, bool]] = None
    created_at: datetime
    last_login_at: Optional[datetime] = None
    
    @field_validator('role', mode='before')
    @classmethod
    def serialize_role(cls, v):
        if isinstance(v, UserRole):
            return v.value
        return v
    
    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # 秒
    user: UserResponse


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求"""
    refresh_token: str


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class UserListResponse(BaseModel):
    """用户列表响应"""
    users: list[UserResponse]
    total: int
    page: int
    page_size: int


class UserListRequest(BaseModel):
    """用户列表查询请求"""
    page: int = 1
    page_size: int = 20
    role: Optional[UserRole] = None
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    district_id: Optional[int] = None
    search: Optional[str] = None  # 搜索昵称或手机号
    
    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @field_validator('page_size')
    @classmethod
    def validate_page_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v 