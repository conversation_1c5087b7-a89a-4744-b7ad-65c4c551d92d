<template>
  <view class="culture-management">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">文化管理</text>
      <text class="page-subtitle">{{ getFullRoleDisplayText() }}</text>
    </view>

    <!-- 九宫格功能菜单 -->
    <view class="grid-container">
      <view class="grid-item"
            @click="goToHomeManagement">
        <view class="grid-icon">🏠</view>
        <text class="grid-title">首页管理</text>
        <text class="grid-desc">管理首页内容</text>
      </view>

      <view class="grid-item"
            @click="goToHeritageManagement">
        <view class="grid-icon">📜</view>
        <text class="grid-title">文源纪管理</text>
        <text class="grid-desc">管理文源纪展示内容</text>
      </view>

      <view class="grid-item"
            @click="goToAncientBooksManagement">
        <view class="grid-icon">📚</view>
        <text class="grid-title">古籍管理</text>
        <text class="grid-desc">管理古籍典藏内容</text>
      </view>

      <view class="grid-item"
            @click="goToCultureMedia">
        <view class="grid-icon">🎬</view>
        <text class="grid-title">声像文藏</text>
        <text class="grid-desc">视听内容管理</text>
      </view>

      <view class="grid-item"
            @click="goToEventManagement">
        <view class="grid-icon">🎭</view>
        <text class="grid-title">活动管理</text>
        <text class="grid-desc">文化活动管理</text>
      </view>

      <view class="grid-item"
            @click="goToTourismManagement">
        <view class="grid-icon">🗺️</view>
        <text class="grid-title">旅游管理</text>
        <text class="grid-desc">文化旅游管理</text>
      </view>

      <view class="grid-item"
            @click="goToNewsManagement">
        <view class="grid-icon">📰</view>
        <text class="grid-title">资讯管理</text>
        <text class="grid-desc">文化资讯管理</text>
      </view>

      <view class="grid-item"
            @click="goToEducationManagement">
        <view class="grid-icon">📚</view>
        <text class="grid-title">教育管理</text>
        <text class="grid-desc">文化教育管理</text>
      </view>

      <view class="grid-item"
            @click="goToStatistics">
        <view class="grid-icon">📈</view>
        <text class="grid-title">统计报表</text>
        <text class="grid-desc">数据统计分析</text>
      </view>

      <view class="grid-item"
            @click="goToRailTransit">
        <view class="grid-icon">🚇</view>
        <text class="grid-title">轨道交通</text>
        <text class="grid-desc">重庆轨道2号线</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref, onMounted } from 'vue'

// 用户角色枚举（先定义，避免undefined错误）
const UserRole = {
  SUPER_ADMIN: 'super_admin',
  PROVINCE_ADMIN: 'province_admin',
  CITY_ADMIN: 'city_admin',
  DISTRICT_ADMIN: 'district_admin',
}

// 用户信息
const userInfo = ref({
  id: '',
  role: '',
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
  user_id: '',
  module_permissions: {
    ancient_books: false,
    paintings: false,
    archives: false,
    videos: false,
  },
})

// 角色显示名称
const roleDisplayName = ref('用户')

// 状态栏高度
const statusBarHeight = ref(0)

// 根据角色生成显示名称
const getRoleDisplayName = (role: string) => {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return '超级管理员'
    case UserRole.PROVINCE_ADMIN:
      return '省级管理员'
    case UserRole.CITY_ADMIN:
      return '市级管理员'
    case UserRole.DISTRICT_ADMIN:
      return '区县管理员'
    default:
      return '用户'
  }
}

// 模拟auth manager结构
const authManager = {
  get currentUser() {
    return userInfo.value
  },
  get userRoleDisplayName() {
    return roleDisplayName.value
  },
}

// 页面加载时获取参数
onLoad((options: any) => {
  if (options && options.role) {
    userInfo.value = {
      id: options.user_id || '',
      role: options.role || '',
      province_name: decodeURIComponent(options.province_name || ''),
      province_id: options.province_id || '',
      city_name: decodeURIComponent(options.city_name || ''),
      city_id: options.city_id || '',
      district_name: decodeURIComponent(options.district_name || ''),
      district_id: options.district_id || '',
      user_id: options.user_id || '',
      module_permissions: {
        ancient_books:
          options.role === 'super_admin' ||
          options.ancient_books_permission === 'true',
        paintings:
          options.role === 'super_admin' ||
          options.paintings_permission === 'true',
        archives:
          options.role === 'super_admin' ||
          options.archives_permission === 'true',
        videos:
          options.role === 'super_admin' ||
          options.videos_permission === 'true',
      },
    }

    roleDisplayName.value = getRoleDisplayName(userInfo.value.role)
  } else {
    // 如果没有参数，使用默认值（用于测试）
    userInfo.value = {
      id: 'test_user',
      role: UserRole.CITY_ADMIN,
      province_name: '河南省',
      province_id: '410000',
      city_name: '郑州市',
      city_id: '410100',
      district_name: '',
      district_id: '',
      user_id: 'test_user',
      module_permissions: {
        ancient_books: true,
        paintings: false,
        archives: false,
        videos: false,
      },
    }
    roleDisplayName.value = getRoleDisplayName(userInfo.value.role)
  }
})

// 页面挂载后设置状态栏高度
onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})

// 获取用户管辖区域文本
const getUserRegionText = () => {
  const user = authManager.currentUser
  if (!user) return ''
  if (user.role === UserRole.SUPER_ADMIN) {
    return '全国'
  } else if (user.role === UserRole.PROVINCE_ADMIN) {
    return user.province_name || '省级'
  } else if (user.role === UserRole.CITY_ADMIN) {
    return (
      `${user.province_name || ''} ${user.city_name || ''}`.trim() || '市级'
    )
  } else if (user.role === UserRole.DISTRICT_ADMIN) {
    return (
      `${user.province_name || ''} ${user.city_name || ''} ${
        user.district_name || ''
      }`.trim() || '区县级'
    )
  }
  return ''
}

// 获取完整的角色显示文本（角色名称+管辖区域）
const getFullRoleDisplayText = () => {
  const user = authManager.currentUser
  if (!user) return '用户'

  const roleName = authManager.userRoleDisplayName
  const regionText = getUserRegionText()

  if (user.role === UserRole.SUPER_ADMIN) {
    return `${roleName}（${regionText}）`
  } else if (regionText) {
    return `${roleName}（${regionText}）`
  } else {
    return roleName
  }
}

// 导航到各个功能模块
const goToHomeManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToHeritageManagement = () => {
  // 将当前用户权限信息传递给文源纪页面，启用管理模式
  const user = userInfo.value
  const params = {
    manage_mode: 'true', // 启用管理模式
    role: user.role,
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    user_id: user.id || '', // 修复：使用 user.id 而不是 user.user_id
  }

  // 将参数编码为URL查询字符串
  const queryString = Object.keys(params)
    .map(
      (key) =>
        `${key}=${encodeURIComponent(params[key as keyof typeof params])}`
    )
    .join('&')

  uni.navigateTo({
    url: `/pages/culture/heritage?${queryString}`,
  })
}

const goToAncientBooksManagement = () => {
  // 检查用户是否有古籍管理权限
  const user = userInfo.value
  if (!user) {
    uni.showToast({
      title: '请先登录',
      icon: 'error',
    })
    return
  }

  // 检查权限：超级管理员或具备古籍管理权限的管理员
  const hasPermission =
    user.role === 'super_admin' ||
    (user.module_permissions && user.module_permissions.ancient_books === true)

  if (!hasPermission) {
    uni.showToast({
      title: '您没有古籍管理权限',
      icon: 'error',
    })
    return
  }

  // 将当前用户权限信息传递给古籍管理页面，启用管理模式
  const params = {
    manage_mode: 'true', // 启用管理模式
    role: user.role,
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    user_id: user.user_id || user.id || '',
    // 添加模块权限参数
    ancient_books_permission: user.module_permissions?.ancient_books
      ? 'true'
      : 'false',
    paintings_permission: user.module_permissions?.paintings ? 'true' : 'false',
    archives_permission: user.module_permissions?.archives ? 'true' : 'false',
    videos_permission: user.module_permissions?.videos ? 'true' : 'false',
  }

  // 将参数编码为URL查询字符串
  const queryString = Object.keys(params)
    .map(
      (key) =>
        `${key}=${encodeURIComponent(params[key as keyof typeof params])}`
    )
    .join('&')

  uni.navigateTo({
    url: `/pages/culture/ancient-books-management?${queryString}`,
  })
}

const goToCultureMedia = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToEventManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToTourismManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToNewsManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToEducationManagement = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToStatistics = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToRailTransit = () => {
  uni.navigateTo({
    url: '/pages/rail/cq-line2',
  })
}
</script>

<style scoped>
.culture-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 页面内容区域 */

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 20rpx 0;
}

.grid-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.grid-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}

.grid-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  display: block;
}

.grid-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.grid-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 25rpx;
  }

  .grid-item {
    padding: 35rpx 15rpx;
  }

  .grid-icon {
    font-size: 50rpx;
  }

  .grid-title {
    font-size: 26rpx;
  }

  .grid-desc {
    font-size: 20rpx;
  }
}
</style> 