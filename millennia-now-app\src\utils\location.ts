// 位置相关工具函数

// 经纬度坐标接口
export interface Coordinate {
  longitude: number
  latitude: number
}

// 地址信息接口
export interface AddressInfo {
  province: string
  city: string
  district: string
  street?: string
  fullAddress: string
}

// 计算两点之间的距离（单位：米）
export const calculateDistance = (point1: Coordinate, point2: Coordinate): number => {
  const R = 6371000 // 地球半径，单位米
  const lat1Rad = (point1.latitude * Math.PI) / 180
  const lat2Rad = (point2.latitude * Math.PI) / 180
  const deltaLatRad = ((point2.latitude - point1.latitude) * Math.PI) / 180
  const deltaLngRad = ((point2.longitude - point1.longitude) * Math.PI) / 180

  const a =
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

// 格式化距离显示
export const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else if (distance < 10000) {
    return `${(distance / 1000).toFixed(1)}km`
  } else {
    return `${Math.round(distance / 1000)}km`
  }
}

// 验证经纬度是否有效
export const isValidCoordinate = (coordinate: Coordinate): boolean => {
  const { longitude, latitude } = coordinate
  return (
    typeof longitude === 'number' &&
    typeof latitude === 'number' &&
    longitude >= -180 &&
    longitude <= 180 &&
    latitude >= -90 &&
    latitude <= 90
  )
}

// 判断坐标是否在中国境内（粗略判断）
export const isInChina = (coordinate: Coordinate): boolean => {
  const { longitude, latitude } = coordinate
  // 中国大陆的大致范围
  return longitude >= 73 && longitude <= 135 && latitude >= 3 && latitude <= 54
}

// WGS84坐标系转GCJ02坐标系（高德地图使用的坐标系）
const PI = 3.1415926535897932384626
const EE = 0.00669342162296594323
const A = 6378245.0

const transformLat = (lng: number, lat: number): number => {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0
  ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0
  ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0
  return ret
}

const transformLng = (lng: number, lat: number): number => {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0
  ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0
  ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0
  return ret
}

export const wgs84ToGcj02 = (coordinate: Coordinate): Coordinate => {
  if (!isInChina(coordinate)) {
    return coordinate
  }

  const { longitude: lng, latitude: lat } = coordinate
  let dLat = transformLat(lng - 105.0, lat - 35.0)
  let dLng = transformLng(lng - 105.0, lat - 35.0)
  const radLat = (lat / 180.0) * PI
  let magic = Math.sin(radLat)
  magic = 1 - EE * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  dLat = (dLat * 180.0) / (((A * (1 - EE)) / (magic * sqrtMagic)) * PI)
  dLng = (dLng * 180.0) / ((A / sqrtMagic) * Math.cos(radLat) * PI)

  return {
    latitude: lat + dLat,
    longitude: lng + dLng
  }
}

// 获取定位权限状态
export const getLocationPermission = (): Promise<'granted' | 'denied' | 'prompt'> => {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN
    uni.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === true) {
          resolve('granted')
        } else if (res.authSetting['scope.userLocation'] === false) {
          resolve('denied')
        } else {
          resolve('prompt')
        }
      },
      fail: () => {
        resolve('prompt')
      }
    })
    // #endif

    // #ifdef H5
    if (navigator.geolocation) {
      resolve('granted')
    } else {
      resolve('denied')
    }
    // #endif

    // #ifdef APP-PLUS
    // App端权限检查
    resolve('prompt')
    // #endif
  })
}

// 请求定位权限
export const requestLocationPermission = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN
    uni.authorize({
      scope: 'scope.userLocation',
      success: () => {
        resolve(true)
      },
      fail: () => {
        // 权限被拒绝，引导用户到设置页面
        uni.showModal({
          title: '定位权限申请',
          content: '需要获取您的位置信息，请到设置页面打开定位权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting({
                success: (settingData) => {
                  if (settingData.authSetting['scope.userLocation']) {
                    resolve(true)
                  } else {
                    resolve(false)
                  }
                },
                fail: () => {
                  resolve(false)
                }
              })
            } else {
              resolve(false)
            }
          }
        })
      }
    })
    // #endif

    // #ifdef H5
    resolve(true)
    // #endif

    // #ifdef APP-PLUS
    resolve(true)
    // #endif
  })
}

// 格式化地址显示
export const formatAddress = (addressInfo: AddressInfo): string => {
  const { province, city, district, street } = addressInfo
  
  // 处理直辖市情况
  if (province === city) {
    return street ? `${city}${district}${street}` : `${city}${district}`
  }
  
  return street ? `${province}${city}${district}${street}` : `${province}${city}${district}`
}

// 解析完整地址，提取省市区信息
export const parseFullAddress = (fullAddress: string): Partial<AddressInfo> => {
  const provincePattern = /(.*?[省市自治区特别行政区])/
  const cityPattern = /(.*?[市州盟地区])/
  const districtPattern = /(.*?[区县市旗])/
  
  let province = ''
  let city = ''
  let district = ''
  let remaining = fullAddress
  
  // 提取省份
  const provinceMatch = remaining.match(provincePattern)
  if (provinceMatch) {
    province = provinceMatch[1]
    remaining = remaining.replace(province, '')
  }
  
  // 提取城市
  const cityMatch = remaining.match(cityPattern)
  if (cityMatch) {
    city = cityMatch[1]
    remaining = remaining.replace(city, '')
  }
  
  // 提取区县
  const districtMatch = remaining.match(districtPattern)
  if (districtMatch) {
    district = districtMatch[1]
    remaining = remaining.replace(district, '')
  }
  
  return {
    province: province.replace(/[省市自治区特别行政区]/g, ''),
    city: city.replace(/[市州盟地区]/g, ''),
    district: district.replace(/[区县市旗]/g, ''),
    street: remaining.trim() || undefined,
    fullAddress
  }
} 