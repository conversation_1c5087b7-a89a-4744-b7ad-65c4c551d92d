from functools import wraps
import time
from typing import Dict, Any, Callable, Tuple


class MemoryCache:
    """简单的内存缓存实现"""
    
    def __init__(self, ttl: int = 3600):
        """
        初始化缓存
        
        Args:
            ttl: 缓存生存时间（秒）
        """
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.ttl = ttl
    
    def get(self, key: str) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值或None
        """
        if key not in self.cache:
            return None
            
        value, timestamp = self.cache[key]
        if time.time() - timestamp > self.ttl:
            del self.cache[key]
            return None
            
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 要缓存的值
        """
        self.cache[key] = (value, time.time())
    
    def invalidate(self, key: str) -> None:
        """
        使缓存项无效
        
        Args:
            key: 要失效的缓存键
        """
        if key in self.cache:
            del self.cache[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()


# 创建全局缓存实例
cache = MemoryCache()


def cached(ttl: int = 3600):
    """
    用于缓存函数结果的装饰器
    
    Args:
        ttl: 缓存生存时间（秒）
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # 尝试从缓存获取
            cached_value = cache.get(key)
            if cached_value is not None:
                return cached_value
            
            # 调用原函数
            result = await func(*args, **kwargs)
            
            # 保存到缓存
            cache.set(key, result)
            return result
        return wrapper
    return decorator