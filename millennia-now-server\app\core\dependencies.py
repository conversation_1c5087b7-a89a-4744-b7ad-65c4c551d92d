from fastapi import Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
from app.database.db import get_db
from app.models.users import User, UserRole
from app.core.auth import token_service, permission_service

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # 验证令牌
    payload = token_service.verify_token(credentials.credentials)
    if payload is None:
        raise credentials_exception
    
    user_id = payload.get("user_id")
    if user_id is None:
        raise credentials_exception
    
    # 查询用户
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    if user is None:
        raise credentials_exception
    
    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    return current_user


async def get_optional_user(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """获取可选的当前用户（不强制要求认证）"""
    authorization = request.headers.get("authorization")
    if not authorization or not authorization.startswith("Bearer "):
        return None
    
    token = authorization.split(" ")[1]
    payload = token_service.verify_token(token)
    if payload is None:
        return None
    
    user_id = payload.get("user_id")
    if user_id is None:
        return None
    
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    return user


def require_role(required_role: UserRole):
    """需要特定角色的依赖"""
    def role_dependency(current_user: User = Depends(get_current_active_user)) -> User:
        if not permission_service.check_permission(current_user, required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        return current_user
    return role_dependency


def require_admin():
    """需要管理员权限的依赖"""
    return require_role(UserRole.DISTRICT_ADMIN)


def require_city_admin():
    """需要市级管理员权限的依赖"""
    return require_role(UserRole.CITY_ADMIN)


def require_province_admin():
    """需要省级管理员权限的依赖"""
    return require_role(UserRole.PROVINCE_ADMIN)


def require_super_admin():
    """需要超级管理员权限的依赖"""
    return require_role(UserRole.SUPER_ADMIN)


def check_area_permission(
    province_id: Optional[int] = None,
    city_id: Optional[int] = None,
    district_id: Optional[int] = None
):
    """检查区域权限的依赖"""
    def area_dependency(current_user: User = Depends(get_current_active_user)) -> User:
        if not permission_service.check_permission(
            current_user, 
            UserRole.DISTRICT_ADMIN,
            province_id=province_id,
            city_id=city_id,
            district_id=district_id
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该区域资源"
            )
        return current_user
    return area_dependency 