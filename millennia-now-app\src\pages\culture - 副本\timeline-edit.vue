<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="form-container"
          :class="{ 'content-hidden': showTemplateModal }">
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 时期名称 -->
        <view class="form-item">
          <text class="form-label">时期名称 <text class="required">*</text></text>
          <input class="form-input"
                 v-model="formData.period"
                 placeholder="请输入时期名称，如：现代、民国、清朝等"
                 maxlength="100" />
        </view>

        <!-- 年份范围 -->
        <view class="form-item">
          <text class="form-label">年份范围 <text class="required">*</text></text>
          <input class="form-input"
                 v-model="formData.year"
                 placeholder="请输入年份范围，如：1949至今、1912-1949等"
                 maxlength="100" />
        </view>

        <!-- 标题 -->
        <view class="form-item">
          <text class="form-label">标题 <text class="required">*</text> (当前值: {{ formData.title }})</text>
          <input class="form-input"
                 v-model="formData.title"
                 placeholder="请输入时间轴项目标题"
                 maxlength="200" />
        </view>

        <!-- 描述 -->
        <view class="form-item">
          <text class="form-label">描述 <text class="required">*</text></text>
          <textarea class="form-textarea"
                    v-model="formData.description"
                    placeholder="请输入项目描述，将显示在时间轴卡片上"
                    maxlength="1000"
                    :auto-height="true"
                    :min-height="120"
                    :show-count="true" />
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">图片和内容</view>

        <!-- 主图片 -->
        <view class="form-item">
          <text class="form-label">主图片</text>
          <view class="image-upload">
            <view class="image-preview"
                  v-if="formData.image">
              <image :src="formData.image"
                     mode="aspectFill"
                     class="preview-image" />
              <view class="image-actions">
                <text class="action-btn"
                      @click="changeMainImage">更换</text>
                <text class="action-btn delete"
                      @click="removeMainImage">删除</text>
              </view>
            </view>
            <view class="upload-btn"
                  v-else
                  @click="uploadMainImage"
                  :class="{ disabled: uploading }">
              <text class="upload-icon">{{ uploading ? '...' : '+' }}</text>
              <text class="upload-text">{{ uploading ? '上传中' : '上传主图片' }}</text>
            </view>
          </view>
        </view>

        <!-- 是否有详细内容 -->
        <view class="form-item">
          <text class="form-label">详细内容</text>
          <switch :checked="formData.has_detail"
                  @change="onHasDetailChange"
                  color="#007aff" />
          <text class="form-desc">开启后可添加详细内容和更多图片</text>
        </view>

        <!-- 详细内容（条件显示） -->
        <view class="form-item"
              v-if="formData.has_detail">
          <view class="form-label-with-button">
            <text class="form-label">详细内容（HTML格式）</text>
            <button class="template-btn"
                    @click="showTemplateSelector">选择模板</button>
          </view>
          <textarea class="form-textarea"
                    v-model="formData.detail"
                    placeholder="请输入详细内容，支持HTML格式，或点击上方选择模板按钮快速插入模板"
                    maxlength="10000"
                    :auto-height="true"
                    :min-height="150"
                    :show-count="true" />
        </view>

        <!-- 详细图片（条件显示） -->
        <view class="form-item"
              v-if="formData.has_detail">
          <text class="form-label">详细图片</text>
          <view class="detail-images">
            <!-- 已上传的图片 -->
            <view class="detail-image-item"
                  v-for="(img, index) in formData.detail_images"
                  :key="index">
              <image :src="img"
                     mode="aspectFill"
                     class="detail-image" />
              <view class="detail-image-actions">
                <text class="action-btn delete"
                      @click="removeDetailImage(index)">删除</text>
              </view>
            </view>
            <!-- 添加图片按钮 -->
            <view class="add-detail-image"
                  @click="addDetailImage"
                  v-if="formData.detail_images.length < 9"
                  :class="{ disabled: uploading }">
              <text class="add-icon">{{ uploading ? '...' : '+' }}</text>
              <text class="add-text">{{ uploading ? '上传中' : '添加图片' }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">文化遗产标签</view>

        <!-- 文化遗产标签 -->
        <view class="form-item">
          <text class="form-label">标签选择</text>
          <view class="tags-container">
            <!-- 预定义标签 -->
            <view class="tag-item"
                  v-for="tag in availableTags"
                  :key="tag"
                  :class="{ active: formData.heritage_tags.includes(tag) }"
                  @click="toggleTag(tag)">
              {{ tag }}
            </view>
          </view>
          <text class="form-desc">点击选择相关的文化遗产标签</text>
        </view>

        <!-- 已选择的标签 -->
        <view class="form-item"
              v-if="formData.heritage_tags.length > 0">
          <text class="form-label">已选择的标签</text>
          <view class="selected-tags-container">
            <view class="selected-tag-item"
                  v-for="(tag, index) in formData.heritage_tags"
                  :key="index">
              <text class="tag-text">{{ tag }}</text>
              <text class="remove-tag-btn"
                    @click="removeTag(index)">×</text>
            </view>
          </view>
          <text class="form-desc">点击标签右侧的 × 可以移除标签</text>
        </view>

        <!-- 自定义标签 -->
        <view class="form-item">
          <text class="form-label">自定义标签</text>
          <view class="custom-tag-input">
            <input class="form-input"
                   v-model="customTagInput"
                   placeholder="输入自定义标签后点击添加"
                   maxlength="50"
                   @confirm="addCustomTag" />
            <button class="add-tag-btn"
                    @click="addCustomTag"
                    :disabled="!customTagInput.trim()">添加</button>
          </view>
          <text class="form-desc">添加后的自定义标签会显示在上方"已选择的标签"区域</text>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">其他设置</view>

        <!-- 是否启用 -->
        <view class="form-item">
          <text class="form-label">启用状态</text>
          <switch :checked="formData.is_active"
                  @change="onActiveChange"
                  color="#007aff" />
          <text class="form-desc">关闭后，该时间轴项目将不会显示在前端</text>
        </view>

        <!-- 排序 -->
        <view class="form-item">
          <text class="form-label">排序</text>
          <input class="form-input"
                 v-model.number="formData.sort_order"
                 placeholder="数字越小排序越靠前"
                 type="number" />
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-section">
        <button class="save-button"
                @click="saveTimeline"
                :disabled="isLoading">
          {{ isLoading ? (isEdit ? '保存中...' : '创建中...') : (isEdit ? '保存' : '创建') }}
        </button>
      </view>
    </view>

    <!-- HTML模板选择弹窗 - 使用全屏覆盖方式 -->
    <view v-if="showTemplateModal"
          class="fullscreen-modal">
      <!-- 模板弹窗内容 -->
      <view class="template-modal">
        <view class="modal-header">
          <text class="modal-title">
            选择HTML模板
            <text class="template-count">({{ getDisplayTemplates().length }}个)</text>
          </text>
          <text @tap="closeTemplateSelector"
                class="close-btn">×</text>
        </view>

        <view class="search-section">
          <input v-model="templateSearchKeyword"
                 placeholder="搜索模板..."
                 class="search-input"
                 @input="onTemplateSearch" />
        </view>

        <!-- 模板列表 -->
        <!-- #ifdef H5 -->
        <view class="template-list-container native-scroll">
          <view class="template-item"
                v-for="(template, index) in getDisplayTemplates()"
                :key="index"
                @click="selectTemplate(template)">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.description }}</text>
            <view class="template-preview">{{ template.preview }}</view>
          </view>

          <!-- 无搜索结果提示 -->
          <view v-if="templateSearchKeyword.trim() && getDisplayTemplates().length === 0"
                class="no-results">
            <text class="no-results-text">未找到相关模板</text>
            <text class="no-results-desc">请尝试其他关键词</text>
          </view>
        </view>
        <!-- #endif -->

        <!-- #ifndef H5 -->
        <scroll-view class="template-list"
                     scroll-y
                     enable-back-to-top
                     enhanced
                     :show-scrollbar="true"
                     :scroll-with-animation="true"
                     :enable-passive="false">
          <view class="template-item"
                v-for="(template, index) in getDisplayTemplates()"
                :key="index"
                @tap="selectTemplate(template)">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.description }}</text>
            <view class="template-preview">{{ template.preview }}</view>
          </view>

          <!-- 无搜索结果提示 -->
          <view v-if="templateSearchKeyword.trim() && getDisplayTemplates().length === 0"
                class="no-results">
            <text class="no-results-text">未找到相关模板</text>
            <text class="no-results-desc">请尝试其他关键词</text>
          </view>
        </scroll-view>
        <!-- #endif -->

        <view class="modal-actions">
          <button @tap="closeTemplateSelector"
                  class="cancel-btn">取消</button>
        </view>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  createTimelineItem,
  updateTimelineItem,
  getTimelineItem,
  type TimelineItemCreateRequest,
  type TimelineItemUpdateRequest,
  type TimelineItem,
} from '../../api/heritage'
import {
  htmlTemplates,
  searchTemplates,
  type TemplateItem,
} from '../../config/htmlTemplates'
import { getImageProxyUrl } from '../../utils/image'
import { uploadImage } from '../../utils/upload'

// 页面状态
const isEdit = ref(false)
const isLoading = ref(false)
const loadingText = ref('加载中...')
const timelineId = ref<number | null>(null)
const placeId = ref<number | null>(null)
const uploading = ref(false)

// 自定义标签输入
const customTagInput = ref('')

// HTML模板选择器相关
const showTemplateModal = ref(false)
const templateSearchKeyword = ref('')

const filteredTemplates = ref<TemplateItem[]>([])

// 用户信息和地点信息
const userInfo = ref({
  role: '',
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
})

const placeInfo = ref({
  place_id: 0, // 修改为数值类型
  place_name: '',
})

// 表单数据
const formData = ref({
  period: '',
  year: '',
  title: '',
  description: '',
  image: '',
  has_detail: false,
  detail: '',
  detail_images: [] as string[],
  heritage_tags: [] as string[],
  is_active: true,
  sort_order: 0,
})

// 可选的文化遗产标签
const availableTags = ref([
  '国家级非物质文化遗产',
  '省级非物质文化遗产',
  '市级非物质文化遗产',
  '世界文化遗产',
  '国家重点文物保护单位',
  '省级文物保护单位',
  '历史文化名城',
  '传统手工艺',
  '民族文化',
  '古建筑',
  '文化景观',
  '民俗活动',
  '传统技艺',
  '口传文学',
  '传统音乐',
  '传统舞蹈',
  '传统戏剧',
  '传统体育',
  '传统美术',
  '传统医药',
])

// 初始化页面
const initPage = () => {
  const app = getApp()

  // 检查是否存在全局数据
  if (app.globalData && app.globalData.timelineEditData) {
    // 从全局数据获取参数
    const options = app.globalData.timelineEditData
    console.log('🔍 timeline-edit 接收到的全局数据:', options)

    // 获取用户信息
    userInfo.value = {
      role: options.role || '',
      province_name: options.province_name || '',
      province_id: options.province_id || '',
      city_name: options.city_name || '',
      city_id: options.city_id || '',
      district_name: options.district_name || '',
      district_id: options.district_id || '',
    }

    // 获取地点信息
    placeInfo.value = {
      place_id: parseInt(options.place_id) || 0,
      place_name: options.place_name || '',
    }
    placeId.value = placeInfo.value.place_id

    // 检查是否为编辑模式
    if (options.timeline_id) {
      isEdit.value = true
      timelineId.value = parseInt(options.timeline_id)

      // 直接使用全局数据中的时间轴信息，避免重新加载
      console.log('🔍 使用全局数据填充时间轴表单')

      // 逐个设置属性，确保响应式更新
      formData.value.period = options.period || ''
      formData.value.year = options.year || ''
      formData.value.title = options.title || ''
      formData.value.description = options.description || ''
      formData.value.image = options.image || ''
      formData.value.has_detail = options.has_detail || false
      formData.value.detail = options.detail || ''
      formData.value.detail_images = options.detail_images || []
      formData.value.heritage_tags = options.heritage_tags || []
      formData.value.is_active =
        options.is_active !== undefined ? options.is_active : true
      formData.value.sort_order = options.sort_order || 0

      console.log('🔍 时间轴表单数据已填充:', formData.value)
    }

    // 使用后清除全局数据
    // app.globalData.timelineEditData = null
  } else {
    // 尝试从旧的方式获取参数
    const pages = getCurrentPages() as any[]
    const currentPage = pages[pages.length - 1] as any
    const options = currentPage?.options || {}

    if (options.place_id) {
      // 旧的方式获取参数
      userInfo.value = {
        role: options.role || '',
        province_name: decodeURIComponent(options.province_name || ''),
        province_id: options.province_id || '',
        city_name: decodeURIComponent(options.city_name || ''),
        city_id: options.city_id || '',
        district_name: decodeURIComponent(options.district_name || ''),
        district_id: options.district_id || '',
      }

      placeInfo.value = {
        place_id: parseInt(options.place_id) || 0,
        place_name: decodeURIComponent(options.place_name || ''),
      }
      placeId.value = placeInfo.value.place_id

      if (options.timeline_id) {
        isEdit.value = true
        timelineId.value = parseInt(options.timeline_id)
        loadTimelineData()
      }
    } else {
      // 参数获取失败
      console.error('未找到参数数据')
      uni.showToast({
        title: '参数加载失败',
        icon: 'none',
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}

// 加载时间轴数据（编辑模式）
const loadTimelineData = async () => {
  if (!timelineId.value) return

  isLoading.value = true
  loadingText.value = '加载时间轴信息...'

  try {
    const timelineData = await getTimelineItem(timelineId.value)

    if (timelineData) {
      // 填充表单数据
      formData.value = {
        period: timelineData.period || '',
        year: timelineData.year || '',
        title: timelineData.title || '',
        description: timelineData.description || '',
        image: timelineData.image || '',
        has_detail: timelineData.has_detail || false,
        detail: timelineData.detail || '',
        detail_images: timelineData.detail_images || [],
        heritage_tags: timelineData.heritage_tags || [],
        is_active:
          timelineData.is_active !== undefined ? timelineData.is_active : true,
        sort_order: timelineData.sort_order || 0,
      }

      console.log('时间轴数据加载成功:', timelineData)
    } else {
      uni.showToast({
        title: '未找到时间轴数据',
        icon: 'none',
      })
      // 如果没有数据，延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  } catch (error: any) {
    console.error('加载时间轴信息失败:', error)

    let errorMessage = '加载失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })

    // 如果加载失败，延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  } finally {
    isLoading.value = false
  }
}

// 上传图片到服务器
const uploadImageToServer = async (
  filePath: string,
  type: 'main' | 'detail'
) => {
  uploading.value = true

  try {
    // 使用统一的上传工具，自动处理URL配置
    const result = await uploadImage(filePath, 'timeline')

    if (result.success && result.url) {
      // 使用工具函数处理图片URL
      const imageUrl = getImageProxyUrl(result.url)

      if (type === 'main') {
        formData.value.image = imageUrl
        uni.showToast({
          title: '主图片上传成功',
          icon: 'success',
          duration: 1500,
        })
      } else {
        // detail类型的图片会在addDetailImage函数中处理
        return imageUrl
      }
    } else {
      throw new Error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    })
    throw error
  } finally {
    uploading.value = false
  }
}

// 上传主图片
const uploadMainImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      await uploadImageToServer(res.tempFilePaths[0], 'main')
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 更换主图片
const changeMainImage = () => {
  uploadMainImage()
}

// 删除主图片
const removeMainImage = () => {
  formData.value.image = ''

  // 显示删除成功的提示
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 是否有详细内容变化
const onHasDetailChange = (e: any) => {
  formData.value.has_detail = e.detail.value
  if (!formData.value.has_detail) {
    formData.value.detail = ''
    formData.value.detail_images = []
  }
}

// 添加详细图片
const addDetailImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 9 - formData.value.detail_images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      // 显示上传进度
      uni.showLoading({
        title: '上传中...',
      })

      try {
        // 逐一上传图片
        for (const filePath of res.tempFilePaths) {
          const imageUrl = await uploadImageToServer(filePath, 'detail')
          if (imageUrl) {
            formData.value.detail_images.push(imageUrl)
          }
        }

        uni.hideLoading()
        uni.showToast({
          title: `成功上传${res.tempFilePaths.length}张图片`,
          icon: 'success',
          duration: 1500,
        })
      } catch (error) {
        uni.hideLoading()
        console.error('上传详细图片失败:', error)
      }
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 删除详细图片
const removeDetailImage = (index: number) => {
  formData.value.detail_images.splice(index, 1)

  // 显示删除成功的提示
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 切换标签选择
const toggleTag = (tag: string) => {
  const index = formData.value.heritage_tags.indexOf(tag)
  if (index > -1) {
    formData.value.heritage_tags.splice(index, 1)
  } else {
    formData.value.heritage_tags.push(tag)
  }
}

// 添加自定义标签
const addCustomTag = () => {
  const tag = customTagInput.value.trim()
  if (tag && !formData.value.heritage_tags.includes(tag)) {
    formData.value.heritage_tags.push(tag)
    customTagInput.value = ''
    uni.showToast({
      title: '标签已添加',
      icon: 'success',
      duration: 1500,
    })
  } else if (formData.value.heritage_tags.includes(tag)) {
    uni.showToast({
      title: '该标签已存在',
      icon: 'none',
      duration: 1500,
    })
  }
}

// 移除标签
const removeTag = (index: number) => {
  if (index >= 0 && index < formData.value.heritage_tags.length) {
    const removedTag = formData.value.heritage_tags[index]
    formData.value.heritage_tags.splice(index, 1)
    uni.showToast({
      title: `已移除标签: ${removedTag}`,
      icon: 'success',
      duration: 1500,
    })
  }
}

// HTML模板选择相关方法
const showTemplateSelector = () => {
  showTemplateModal.value = true
  templateSearchKeyword.value = ''
  filteredTemplates.value = htmlTemplates

  // 隐藏键盘和处理页面状态
  try {
    // 隐藏键盘
    uni.hideKeyboard()

    // 延迟执行，确保弹窗已经显示
    setTimeout(() => {
      // 在H5环境下才操作document
      // #ifdef H5
      if (typeof document !== 'undefined') {
        // 模糊所有输入框
        const inputs = document.querySelectorAll('input, textarea')
        inputs.forEach((input) => {
          if (input instanceof HTMLElement) {
            input.blur()
          }
        })
      }
      // #endif
    }, 100)
  } catch (error) {
    console.log('处理页面状态时出错:', error)
  }
}

const closeTemplateSelector = () => {
  showTemplateModal.value = false
  templateSearchKeyword.value = ''
}

// 模板搜索功能
const onTemplateSearch = () => {
  if (templateSearchKeyword.value.trim()) {
    filteredTemplates.value = searchTemplates(
      templateSearchKeyword.value.trim()
    )
  } else {
    filteredTemplates.value = htmlTemplates
  }
}

// 获取要显示的模板列表
const getDisplayTemplates = () => {
  return templateSearchKeyword.value.trim()
    ? filteredTemplates.value
    : htmlTemplates
}

const selectTemplate = (template: TemplateItem) => {
  // 如果有内容则换行添加，否则直接添加
  const currentContent = formData.value.detail.trim()
  if (currentContent) {
    formData.value.detail = currentContent + '\n\n' + template.content
  } else {
    formData.value.detail = template.content
  }

  uni.showToast({
    title: '模板已插入',
    icon: 'success',
    duration: 1000,
  })

  closeTemplateSelector()
}

// 启用状态变化
const onActiveChange = (e: any) => {
  formData.value.is_active = e.detail.value
}

// 保存时间轴项目
const saveTimeline = async () => {
  // 验证必填字段
  if (!formData.value.period.trim()) {
    uni.showToast({
      title: '请输入时期名称',
      icon: 'none',
    })
    return
  }

  if (!formData.value.year.trim()) {
    uni.showToast({
      title: '请输入年份范围',
      icon: 'none',
    })
    return
  }

  if (!formData.value.title.trim()) {
    uni.showToast({
      title: '请输入标题',
      icon: 'none',
    })
    return
  }

  if (!formData.value.description.trim()) {
    uni.showToast({
      title: '请输入描述',
      icon: 'none',
    })
    return
  }

  if (!placeId.value) {
    uni.showToast({
      title: '缺少地点信息',
      icon: 'none',
    })
    return
  }

  isLoading.value = true
  loadingText.value = isEdit.value ? '保存中...' : '创建中...'

  try {
    if (isEdit.value && timelineId.value) {
      // 编辑模式
      const updateData: TimelineItemUpdateRequest = {
        period: formData.value.period.trim(),
        year: formData.value.year.trim(),
        title: formData.value.title.trim(),
        description: formData.value.description.trim(),
        image: formData.value.image.trim() || null, // 明确传递 null 来清空图片
        has_detail: formData.value.has_detail,
        detail: formData.value.detail.trim() || undefined,
        detail_images:
          formData.value.detail_images.length > 0
            ? formData.value.detail_images
            : null, // 明确传递 null 来清空详细图片
        heritage_tags:
          formData.value.heritage_tags.length > 0
            ? formData.value.heritage_tags
            : null, // 明确传递 null 来清空标签
        is_active: formData.value.is_active,
        sort_order: formData.value.sort_order || 0,
      }

      await updateTimelineItem(timelineId.value, updateData)

      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    } else {
      // 创建模式
      const createData: TimelineItemCreateRequest = {
        period: formData.value.period.trim(),
        year: formData.value.year.trim(),
        title: formData.value.title.trim(),
        description: formData.value.description.trim(),
        has_detail: formData.value.has_detail,
        detail: formData.value.detail.trim() || undefined,
        detail_images:
          formData.value.detail_images.length > 0
            ? formData.value.detail_images
            : undefined,
        heritage_tags:
          formData.value.heritage_tags.length > 0
            ? formData.value.heritage_tags
            : undefined,
        is_active: formData.value.is_active,
        sort_order: formData.value.sort_order || 0,
      }

      // 设置图片字段（如果为空则不传递）
      if (formData.value.image && formData.value.image.trim()) {
        createData.image = formData.value.image.trim()
      }

      await createTimelineItem(placeId.value, createData)

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 保存成功后返回上一页
    setTimeout(() => {
      uni.navigateBack({
        delta: 1,
      })
    }, 1500)
  } catch (error: any) {
    console.error('保存时间轴项目失败:', error)

    let errorMessage = '保存失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 添加onLoad生命周期函数
const onLoad = (options: any) => {
  console.log('🔍 timeline-edit onLoad 接收到的参数:', options)

  // 设置导航栏标题（先设置一个默认标题）
  uni.setNavigationBarTitle({
    title: '历史时间轴编辑',
  })
}

// 注册onLoad到组件实例
defineExpose({
  onLoad,
})

onMounted(() => {
  // 在onMounted中处理数据初始化，确保页面完全加载
  console.log('🔍 timeline-edit onMounted 开始初始化')
  initPage()

  // 设置导航栏颜色，这里使用红色主题
  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#C8161E',
  })

  // 更新导航栏标题
  uni.setNavigationBarTitle({
    title: isEdit.value ? '编辑历史时间轴' : '添加历史时间轴',
  })
})
</script>

<style>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 表单容器 */
.form-container {
  padding-bottom: 40rpx;
}

.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

/* 必填项标记 */
.required {
  color: #ff4444;
  font-weight: bold;
}

/* 标签和按钮组合 */
.form-label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.template-btn {
  background-color: #007aff;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

.form-input,
.form-textarea {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
  line-height: 1.6;
}

.form-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  margin-left: 20rpx;
}

/* 图片上传 */
.image-upload {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 300rpx;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: 80rpx;
  text-align: center;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.8);
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  color: #999;
}

.upload-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
}

/* 详细图片 */
.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.detail-image-item {
  position: relative;
  width: 200rpx;
  height: 150rpx;
}

.detail-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.detail-image-actions {
  position: absolute;
  top: 5rpx;
  right: 5rpx;
}

.add-detail-image {
  width: 200rpx;
  height: 150rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-detail-image.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.add-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  padding: 10rpx 20rpx;
  background-color: #f8f9fa;
  color: #666;
  border-radius: 30rpx;
  font-size: 24rpx;
  border: 1px solid #e9ecef;
}

.tag-item.active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

/* 已选择标签样式 */
.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.selected-tag-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.tag-text {
  color: #1890ff;
  margin-right: 8rpx;
}

.remove-tag-btn {
  color: #ff4d4f;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
  padding: 0 4rpx;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-tag-btn:active {
  background-color: rgba(255, 77, 79, 0.1);
}

/* 自定义标签输入 */
.custom-tag-input {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.custom-tag-input .form-input {
  flex: 1;
}

.add-tag-btn {
  padding: 20rpx 30rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.add-tag-btn:disabled {
  background-color: #ccc;
  color: #999;
}

/* 保存按钮 */
.save-section {
  padding: 30rpx 20rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.save-button:disabled {
  background-color: #ccc;
  color: #999;
}

/* 加载中 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* HTML模板选择器样式 - 全屏模式 */
.fullscreen-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  z-index: 9999 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: hidden !important;
}

.template-modal {
  background-color: #fff !important;
  border-radius: 20rpx !important;
  width: 90% !important;
  max-width: 800rpx !important;
  max-height: 85vh !important;
  min-height: 70vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3) !important;
  position: relative !important;
  z-index: 10000 !important;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.template-count {
  font-size: 24rpx;
  font-weight: normal;
  opacity: 0.8;
  margin-left: 10rpx;
}

.close-btn {
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  padding: 10rpx;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.template-list-container {
  flex: 1 !important;
  height: calc(85vh - 200rpx) !important;
  overflow: hidden !important;
  background-color: #fff !important;
}

/* H5端原生滚动 */
.native-scroll {
  overflow-y: auto !important;
  padding: 20rpx !important;
  box-sizing: border-box !important;
}

/* 小程序端scroll-view */
.template-list {
  width: 100% !important;
  height: calc(85vh - 200rpx) !important;
  padding: 20rpx !important;
  background-color: #fff !important;
  box-sizing: border-box !important;
}

.template-item {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  position: relative;
}

.template-item:active {
  background-color: #f8f9fa;
  border-color: #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);
}

.template-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.template-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.template-preview {
  font-size: 22rpx;
  color: #888;
  background-color: #f8f9fa;
  padding: 15rpx;
  border-radius: 8rpx;
  border-left: 6rpx solid #007aff;
  overflow: hidden;
  max-height: 60rpx;
  line-height: 1.4;
}

/* 搜索区域样式 */
.search-section {
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 35rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.search-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.1);
}

/* 无搜索结果样式 */
.no-results {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-results-text {
  display: block;
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.no-results-desc {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

.modal-actions {
  display: flex;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.cancel-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  text-align: center;
  background-color: transparent;
  color: #666;
}

/* PC端滚动条样式 */
.native-scroll::-webkit-scrollbar,
.template-list::-webkit-scrollbar {
  width: 8rpx;
}

.native-scroll::-webkit-scrollbar-track,
.template-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4rpx;
}

.native-scroll::-webkit-scrollbar-thumb,
.template-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4rpx;
}

.native-scroll::-webkit-scrollbar-thumb:hover,
.template-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 当模板弹窗显示时，完全隐藏页面内容 */
.content-hidden {
  display: none !important;
}
</style>