from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import Session
from contextlib import contextmanager
import os
from dotenv import load_dotenv

load_dotenv()

# 数据库连接配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:password@localhost/china_divisions")

# 创建引擎
engine = create_engine(DATABASE_URL)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 获取数据库会话上下文（用于with语句）
@contextmanager
def get_db_context():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 用于FastAPI依赖注入的数据库会话函数
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()