<template>
  <view class="ancient-books-management">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="title-bar">
        <text class="page-title">古籍管理员 - 可管理自己上传的古籍（重庆市 市辖区 渝中区）</text>
        <button class="add-btn"
                @click="addNewBook">
          <image src="/static/icons/add.svg"
                 class="add-icon" />
          <text>新增古籍</text>
        </button>
      </view>

      <!-- 搜索区域 -->
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search"
                     size="20"
                     color="#999" />
          <input v-model="searchQuery"
                 placeholder="搜索古籍名称、作者或关键词..."
                 class="search-input"
                 @input="onSearchInput" />
        </view>
      </view>

      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view v-for="category in filterCategories"
              :key="category.code"
              :class="['filter-tab', { active: selectedCategory === category.code }]"
              @click="selectCategory(category.code)">
          {{ category.name }}
        </view>
        <view class="filter-tab-toggle"
              @click="toggleShowMyOnly">
          <text>仅显示我的古籍</text>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ stats.total }}</text>
        <text class="stat-label">古籍总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.myBooks }}</text>
        <text class="stat-label">我的古籍</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalVolumes }}</text>
        <text class="stat-label">卷册总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.rareBooks }}</text>
        <text class="stat-label">善本典藏</text>
      </view>
    </view>

    <!-- 古籍列表 -->
    <view class="books-container">
      <view v-for="book in filteredBooks"
            :key="book.id"
            class="book-card"
            @click="viewBookDetail(book)">
        <!-- 古籍基本信息 -->
        <view class="book-header">
          <view class="book-cover">
            <image :src="book.cover_image || '/static/images/no-image.svg'"
                   mode="aspectFit"
                   class="cover-image" />
            <view v-if="book.is_rare_book"
                  class="rare-badge">
              <uni-icons type="star-filled"
                         size="12"
                         color="#FFD700" />
              <text class="rare-text">善本</text>
            </view>
          </view>

          <view class="book-info">
            <view class="book-title">{{ book.title }}</view>

            <view class="book-meta-grid">
              <view class="meta-row">
                <text class="meta-label">作者：</text>
                <text class="meta-value">{{ book.author || '佚名' }}</text>
              </view>
              <view class="meta-row">
                <text class="meta-label">朝代：</text>
                <text class="meta-value">{{ book.dynasty || '未知' }}</text>
              </view>
              <view class="meta-row">
                <text class="meta-label">分类：</text>
                <text class="meta-value category-highlight">{{ book.sibu_category_name }}</text>
              </view>
              <view v-if="book.rare_book_number"
                    class="meta-row">
                <text class="meta-label">善本号：</text>
                <text class="meta-value rare-highlight">{{ book.rare_book_number }}</text>
              </view>
            </view>

            <view class="book-description">
              {{ book.description || '暂无简介' }}
            </view>
          </view>

          <!-- 管理操作按钮 -->
          <view class="action-buttons">
            <view class="action-btn volumes-btn"
                  @click.stop="manageVolumes(book)"
                  :class="{ disabled: !canEditBook(book) }">
              <uni-icons type="folder-add"
                         size="16"
                         color="#007aff" />
              <text class="action-text">卷册</text>
            </view>
            <view class="action-btn edit-btn"
                  @click.stop="editBook(book)"
                  :class="{ disabled: !canEditBook(book) }">
              <image src="/static/icons/edit.svg"
                     class="action-icon" />
            </view>
            <view class="action-btn delete-btn"
                  @click.stop="confirmDeleteBook(book)"
                  :class="{ disabled: !canDeleteBook(book) }">
              <image src="/static/icons/delete.svg"
                     class="action-icon" />
            </view>
          </view>
        </view>

        <!-- 古籍统计信息 -->
        <view class="book-stats">
          <view class="stat-item">
            <uni-icons type="calendar"
                       size="12"
                       color="#999" />
            <text class="stat-text">{{ formatDate(book.created_at) }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="eye"
                       size="12"
                       color="#999" />
            <text class="stat-text">浏览 {{ book.view_count || 0 }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="heart"
                       size="12"
                       color="#999" />
            <text class="stat-text">收藏 {{ book.favorite_count || 0 }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredBooks.length === 0 && !loading"
            class="empty-state">
        <image src="/static/images/no-image.svg"
               class="empty-image" />
        <text class="empty-text">暂无古籍数据</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading"
          class="loading-container">
      <uni-load-more status="loading" />
    </view>
  </view>
</template>

<script>
import { getAncientBookList, deleteAncientBook } from '../../api/ancient_books'

export default {
  name: 'AncientBooksManagement',
  data () {
    return {
      // 权限相关
      hasPermission: false,
      isSuperAdmin: false,
      currentUserId: null,

      // 数据相关
      books: [],
      loading: false,
      searchQuery: '',
      selectedCategory: 'all',
      showMyOnly: false,

      // 筛选选项
      filterCategories: [
        { code: 'all', name: '全部' },
        { code: 'A', name: '经部' },
        { code: 'B', name: '史部' },
        { code: 'C', name: '子部' },
        { code: 'D', name: '集部' }
      ]
    }
  },

  computed: {
    // 统计信息
    stats () {
      const total = this.books.length
      const myBooks = this.books.filter(book => book.created_by === this.currentUserId).length
      const totalVolumes = this.books.reduce((sum, book) => sum + (book.available_volumes_count || 0), 0)
      const rareBooks = this.books.filter(book => book.is_rare_book).length

      return {
        total,
        myBooks,
        totalVolumes,
        rareBooks
      }
    },

    // 过滤后的古籍列表
    filteredBooks () {
      let filtered = [...this.books]

      // 搜索过滤
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(book =>
          book.title.toLowerCase().includes(query) ||
          (book.author && book.author.toLowerCase().includes(query)) ||
          (book.description && book.description.toLowerCase().includes(query))
        )
      }

      // 分类过滤
      if (this.selectedCategory !== 'all') {
        filtered = filtered.filter(book => book.sibu_category === this.selectedCategory)
      }

      // 仅显示我的古籍
      if (this.showMyOnly) {
        filtered = filtered.filter(book => book.created_by === this.currentUserId)
      }

      return filtered
    }
  },

  onLoad (options) {
    console.log('古籍管理页面接收参数:', options)

    // 检查权限参数
    const ancientBooksPermission = options.ancient_books_permission
    const isSuperAdmin = options.is_super_admin === 'true'
    const userId = options.user_id ? parseInt(options.user_id) : null

    console.log('权限检查:', {
      ancientBooksPermission,
      isSuperAdmin,
      userId
    })

    if (!ancientBooksPermission && !isSuperAdmin) {
      console.error('没有古籍管理权限')
      uni.showToast({
        title: '没有古籍管理权限',
        icon: 'error',
        duration: 2000
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
      return
    }

    this.hasPermission = ancientBooksPermission === 'true' || isSuperAdmin
    this.isSuperAdmin = isSuperAdmin
    this.currentUserId = userId

    // 加载数据
    this.loadBooks()

    // 监听刷新事件
    uni.$on('refreshAncientBooksList', this.loadBooks)
  },

  onUnload () {
    // 移除事件监听
    uni.$off('refreshAncientBooksList', this.loadBooks)
  },

  methods: {
    // 加载古籍列表
    async loadBooks () {
      this.loading = true
      try {
        const response = await getAncientBookList({
          limit: 100,
          skip: 0
        })

        if (response && response.items) {
          this.books = response.items
          console.log('加载古籍列表成功:', this.books.length)
        } else {
          console.error('获取古籍列表失败')
          uni.showToast({
            title: '获取古籍列表失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('加载古籍列表出错:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 搜索输入处理
    onSearchInput () {
      // 实时搜索，这里可以添加防抖逻辑
    },

    // 选择分类
    selectCategory (code) {
      this.selectedCategory = code
    },

    // 切换仅显示我的古籍
    toggleShowMyOnly () {
      this.showMyOnly = !this.showMyOnly
    },

    // 查看古籍详情
    viewBookDetail (book) {
      uni.navigateTo({
        url: `/pages/culture/ancient-book-detail?id=${book.id}`
      })
    },

    // 新增古籍
    addNewBook () {
      uni.navigateTo({
        url: '/pages/culture/ancient-book-edit?mode=create'
      })
    },

    // 编辑古籍
    editBook (book) {
      if (!this.canEditBook(book)) {
        uni.showToast({
          title: '无权限编辑此古籍',
          icon: 'error'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/ancient-book-edit?mode=edit&book_id=${book.id}`
      })
    },

    // 管理卷册
    manageVolumes (book) {
      if (!this.canEditBook(book)) {
        uni.showToast({
          title: '无权限管理此古籍的卷册',
          icon: 'error'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/volume-list?bookId=${book.id}&book_title=${encodeURIComponent(book.title)}`
      })
    },

    // 确认删除古籍
    confirmDeleteBook (book) {
      if (!this.canDeleteBook(book)) {
        uni.showToast({
          title: '无权限删除此古籍',
          icon: 'error'
        })
        return
      }

      uni.showModal({
        title: '确认删除',
        content: `确定要删除古籍《${book.title}》吗？此操作不可恢复。`,
        confirmText: '删除',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.deleteBook(book)
          }
        }
      })
    },

    // 删除古籍
    async deleteBook (book) {
      try {
        uni.showLoading({
          title: '删除中...'
        })

        const success = await deleteAncientBook(book.id)

        if (success) {
          // 从列表中移除
          const index = this.books.findIndex(b => b.id === book.id)
          if (index > -1) {
            this.books.splice(index, 1)
          }

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('删除古籍失败:', error)
        uni.showToast({
          title: '删除失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 检查是否可以编辑古籍
    canEditBook (book) {
      return this.isSuperAdmin || book.created_by === this.currentUserId
    },

    // 检查是否可以删除古籍
    canDeleteBook (book) {
      return this.isSuperAdmin || book.created_by === this.currentUserId
    },

    // 格式化日期
    formatDate (dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.ancient-books-management {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部区域 */
.header-section {
  background: #fff;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.5);
}

.add-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 搜索区域 */
.search-container {
  margin-bottom: 24rpx;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  border: 1rpx solid #e0e0e0;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #007aff;
  color: #fff;
  border-color: #007aff;
}

.filter-tab-toggle {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 统计信息 */
.stats-section {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 24rpx;
  background: #fff;
  margin: 16rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 古籍列表 */
.books-container {
  padding: 0 24rpx 24rpx;
}

.book-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

.book-card:active {
  transform: translateY(1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 古籍头部信息 */
.book-header {
  display: flex;
  padding: 24rpx;
  position: relative;
}

.book-cover {
  position: relative;
  width: 120rpx;
  height: 160rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.rare-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
}

.rare-text {
  font-size: 18rpx;
  color: #ffd700;
}

.book-info {
  flex: 1;
  margin-right: 24rpx;
}

.book-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.book-meta-grid {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.meta-row {
  display: flex;
  align-items: center;
}

.meta-label {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
}

.meta-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.category-highlight {
  color: #1976d2;
  font-weight: 500;
}

.rare-highlight {
  color: #f57c00;
  font-weight: 500;
  font-family: monospace;
}

.book-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 管理操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  align-items: center;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.9);
}

.volumes-btn {
  background: #fff;
  border: 2rpx solid #007aff;
  width: 80rpx;
  height: 48rpx;
  border-radius: 24rpx;
  flex-direction: column;
  gap: 2rpx;
}

.volumes-btn.disabled {
  border-color: #ccc;
  opacity: 0.5;
}

.volumes-btn .action-text {
  font-size: 18rpx;
  color: #007aff;
}

.volumes-btn.disabled .action-text {
  color: #ccc;
}

.edit-btn {
  background: #007aff;
}

.edit-btn.disabled {
  background: #ccc;
  opacity: 0.5;
}

.delete-btn {
  background: #ff4757;
}

.delete-btn.disabled {
  background: #ccc;
  opacity: 0.5;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.action-btn.disabled .action-icon {
  filter: brightness(0) invert(0.6); /* 禁用状态下的图标颜色 */
}

/* 古籍统计信息 */
.book-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #f0f0f0;
}

.book-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 空状态和加载状态 */
.empty-state {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-container {
  padding: 40rpx;
  text-align: center;
}
</style> 