version: '3.8'

services:
  # FastAPI应用
  app:
    build: .
    ports:
      - "8001:8000"  # 
    environment:
      - DATABASE_URL=mysql+pymysql://root:1qaz2wsx@mysql:3306/millennia_now
      - MINIO_ENDPOINT=minio:9000  # 内部连接地址
      - MINIO_PUBLIC_ENDPOINT=47.109.155.100:9000  # 外部访问地址
      - MINIO_ACCESS_KEY=admin
      - MINIO_SECRET_KEY=admin123456
      - MINIO_SECURE=false
      - MINIO_BUCKET=millennia-now
      - WECHAT_APP_ID=wx7827d9f8bd827e3f
      - WECHAT_APP_SECRET=c10bc52d75428a192b5ee5289c155bf7
      - JWT_SECRET_KEY=millennia-now-production-jwt-secret-key-2024-luckyzyn-top
      - ENV=production
    depends_on:
      - mysql
    volumes:
      - ./static:/app/static
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=1qaz2wsx
      - MYSQL_DATABASE=millennia_now
      - MYSQL_USER=millennia_user
      - MYSQL_PASSWORD=1qaz2wsx
    ports:
      - "3307:3306"  # 使用3307端口避免冲突
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database_schema:/docker-entrypoint-initdb.d
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0


  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=admin123456
    volumes:
      - ./minio/data:/data
      - ./minio/config:/root/.minio
    command: server /data --console-address ":9001"
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"  # HTTPS端口
      - "80:80"    # HTTP端口
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./proxy.conf:/etc/nginx/proxy.conf  # 代理配置文件
      - ./static:/var/www/static
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    depends_on:
      - app
      - minio
    extra_hosts:
      - "host.docker.internal:**********"  # Linux下的Docker网关
    networks:
      - default
      - docker_default  # 连接到Dify网络
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  docker_default:
    external: true  # 使用外部的Dify网络
