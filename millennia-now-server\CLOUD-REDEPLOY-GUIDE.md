# 云服务器重新部署指南

## 🚀 **快速部署（推荐）**

### **1. 连接云服务器**
```bash
ssh root@**************
```

### **2. 进入项目目录**
```bash
cd /path/to/millennia-now-server
```

### **3. 拉取最新代码**
```bash
git pull origin master
```

### **4. 一键重新部署**
```bash
chmod +x clean-deploy.sh
./clean-deploy.sh
```

---

## 🔧 **手动部署步骤**

如果自动脚本有问题，可以手动执行以下步骤：

### **1. 停止现有服务**
```bash
docker-compose down --remove-orphans
```

### **2. 清理Docker资源**
```bash
docker system prune -f
docker rmi millennia-now-server_app 2>/dev/null || true
```

### **3. 检查环境配置**
```bash
# 确保环境文件存在
ls -la .env .env.production

# 如果.env不存在，复制生产配置
cp .env.production .env
```

### **4. 检查SSL证书**
```bash
ls -la ssl/luckyzyn.top.pem ssl/luckyzyn.top.key
```

### **5. 创建必要目录**
```bash
mkdir -p static minio/data minio/config
```

### **6. 重新构建并启动**
```bash
docker-compose up -d --build
```

### **7. 等待服务启动**
```bash
sleep 30
docker-compose ps
```

### **8. 检查服务状态**
```bash
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

---

## ✅ **验证部署**

### **1. 检查服务健康状态**
```bash
# 本地测试
curl -f http://localhost:8001/health
curl -f http://localhost:9000/minio/health/live

# 外部访问测试
curl -f https://luckyzyn.top/millennia-api/health
```

### **2. 测试MinIO直接访问**
```bash
# 测试MinIO直接访问（新功能）
curl -I http://**************:9000/millennia-now/
```

### **3. 检查nginx配置**
```bash
# 检查nginx配置语法
docker-compose exec nginx nginx -t

# 重新加载nginx配置
docker-compose exec nginx nginx -s reload
```

---

## 🔍 **故障排除**

### **1. 如果容器启动失败**
```bash
# 查看详细日志
docker-compose logs app
docker-compose logs nginx
docker-compose logs mysql
docker-compose logs minio

# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443
netstat -tlnp | grep :9000
```

### **2. 如果数据库连接失败**
```bash
# 检查MySQL容器
docker-compose exec mysql mysql -u root -p1qaz2wsx -e "SHOW DATABASES;"

# 重启MySQL
docker-compose restart mysql
```

### **3. 如果MinIO无法访问**
```bash
# 检查MinIO容器
docker-compose logs minio

# 测试MinIO连接
curl http://localhost:9000/minio/health/live
```

### **4. 如果nginx代理失败**
```bash
# 检查nginx配置
docker-compose exec nginx nginx -t

# 查看nginx日志
docker-compose logs nginx

# 重启nginx
docker-compose restart nginx
```

---

## 📋 **重要变更说明**

### **本次更新的主要变更：**

1. **MinIO直接访问**: 图片上传现在返回MinIO直接URL而非nginx代理URL
2. **配置简化**: 移除了不必要的nginx MinIO代理配置
3. **性能优化**: 减少了nginx负载，图片访问更快

### **新的图片URL格式：**
```
旧格式: https://luckyzyn.top/minio/millennia-now/images/xxx.jpg
新格式: http://**************:9000/millennia-now/images/xxx.jpg
```

### **需要注意的事项：**
- 前端应用会自动适配新的URL格式
- 旧的图片URL仍然可以通过nginx访问（向后兼容）
- MinIO现在使用HTTP协议直接访问

---

## 🎯 **部署完成后的访问地址**

- **主站 (Dify)**: https://luckyzyn.top
- **Millennia API**: https://luckyzyn.top/millennia-api
- **API文档**: https://luckyzyn.top/millennia-docs
- **MinIO控制台**: https://luckyzyn.top/minio-console
- **MinIO直接访问**: http://**************:9000

---

## 📞 **如需帮助**

如果部署过程中遇到问题，请提供以下信息：
1. 错误日志: `docker-compose logs`
2. 容器状态: `docker-compose ps`
3. 系统资源: `df -h && free -h`
4. 网络状态: `netstat -tlnp`
