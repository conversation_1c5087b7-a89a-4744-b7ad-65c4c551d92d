// 位置管理工具
// 使用简单的类实现，避免微信小程序环境中的循环引用问题

import { reactive, ref } from 'vue'
import { regionManager } from './region'

// 位置信息接口
export interface LocationInfo {
  province: string
  city: string
  district: string
  street: string
  fullAddress: string
  longitude: number
  latitude: number
}

// 默认位置信息
const defaultLocation: LocationInfo = {
  province: '',
  city: '请选择城市',
  district: '',
  street: '',
  fullAddress: '请选择城市',
  longitude: 0,
  latitude: 0
}

// 存储键名
const STORAGE_KEY = 'location_info'

// 响应式状态
const state = reactive({
  location: { ...defaultLocation },
  isLocating: false,
  hasPermission: false
})

// 保存到本地存储
function saveToStorage(location: LocationInfo) {
  try {
    uni.setStorageSync(STORAGE_KEY, JSON.stringify(location))
  } catch (error) {
    console.warn('保存位置信息失败:', error)
  }
}

// 从本地存储读取
function loadFromStorage(): LocationInfo {
  try {
    const stored = uni.getStorageSync(STORAGE_KEY)
    if (stored) {
      const parsed = JSON.parse(stored)
      return { ...defaultLocation, ...parsed }
    }
  } catch (error) {
    console.warn('读取位置信息失败:', error)
  }
  return { ...defaultLocation }
}

// 初始化加载
state.location = loadFromStorage()

// 高德地图API配置
const AMAP_CONFIG = {
  // 请在高德开放平台申请Web服务类型的API Key
  // https://lbs.amap.com/
  key: '041adb9cf3c9a5f127439604674ea4bd', // 请替换为你的Web服务API Key
  baseUrl: 'https://restapi.amap.com'
}

// 从坐标获取地址信息
async function getAddressFromCoords(longitude: number, latitude: number): Promise<{
  province: string
  city: string
  district: string
  street: string
  fullAddress: string
}> {
  return new Promise((resolve, reject) => {
    console.log('开始逆地理编码:', `${longitude},${latitude}`)

    // 注意：如果GPS使用wgs84坐标系，高德API会自动处理坐标转换
    uni.request({
      url: `${AMAP_CONFIG.baseUrl}/v3/geocode/regeo`,
      method: 'GET',
      data: {
        key: AMAP_CONFIG.key,
        location: `${longitude},${latitude}`,
        extensions: 'base',
        coordsys: 'wgs84'  // 指定输入坐标系为wgs84
      },
      success: (res) => {
        console.log('逆地理编码响应:', res)
        const data = res.data as any
        
        if (data && data.status === '1' && data.regeocode) {
          const addressComponent = data.regeocode.addressComponent
          const province = addressComponent.province || ''
          let city = addressComponent.city || ''
          
          // 处理直辖市情况
          if (Array.isArray(city) && city.length === 0) {
            city = province
          }
          
          resolve({
            province,
            city,
            district: addressComponent.district || '',
            street: addressComponent.township || '',
            fullAddress: data.regeocode.formatted_address || ''
          })
        } else {
          console.error('逆地理编码API返回错误:', data)
          reject(new Error(`逆地理编码失败: ${data?.info || '未知错误'}`))
        }
      },
      fail: (err) => {
        console.error('逆地理编码请求失败:', err)
        reject(new Error('逆地理编码请求失败'))
      }
    })
  })
}

// IP定位
async function getLocationByIP(): Promise<{ province: string; city: string }> {
  return new Promise((resolve, reject) => {
    console.log('开始IP定位')
    
    uni.request({
      url: `${AMAP_CONFIG.baseUrl}/v3/ip`,
      method: 'GET',
      data: {
        key: AMAP_CONFIG.key
      },
      success: (res) => {
        console.log('IP定位响应:', res)
        const data = res.data as any
        
        if (data && data.status === '1') {
          resolve({
            province: data.province || '',
            city: data.city || ''
          })
        } else {
          console.error('IP定位API返回错误:', data)
          reject(new Error(`IP定位失败: ${data?.info || '未知错误'}`))
        }
      },
      fail: (err) => {
        console.error('IP定位请求失败:', err)
        reject(new Error('IP定位请求失败'))
      }
    })
  })
}

// 位置管理操作
export const locationManager = {
  // 获取当前位置
  get currentLocation(): LocationInfo {
    return { ...state.location }
  },

  // 获取完整地址
  get fullAddress(): string {
    return state.location.fullAddress
  },

  // 是否正在定位
  get isLocating(): boolean {
    return state.isLocating
  },

  // 是否有定位权限
  get hasLocationPermission(): boolean {
    return state.hasPermission
  },

  // 设置位置信息
  setLocation(location: Partial<LocationInfo>): void {
    state.location = { ...defaultLocation, ...location }
    saveToStorage(state.location)
  },

  // 清空位置信息
  clearLocation(): void {
    state.location = { ...defaultLocation }
    try {
      uni.removeStorageSync(STORAGE_KEY)
    } catch (error) {
      console.warn('清除位置信息失败:', error)
    }
  },

  // 获取当前定位
  async getCurrentLocation(): Promise<LocationInfo> {
    state.isLocating = true
    
    try {
      return new Promise<LocationInfo>((resolve, reject) => {
        // 安卓APP定位配置 - 使用wgs84避免坐标系转换失败
        const locationOptions = {
          type: 'wgs84',           // 使用wgs84坐标系，避免转换失败
          altitude: false,         // 不获取海拔信息
          geocode: false,          // 不进行系统逆地理编码，使用高德API
          timeout: 15000,          // 15秒超时，给GPS更多时间
          enableHighAccuracy: true // 启用高精度定位
        }

        console.log('开始GPS定位，参数:', locationOptions)

        uni.getLocation({
          ...locationOptions,
          success: async (res: any) => {
            console.log('GPS定位成功:', res)
            state.hasPermission = true
            const { longitude, latitude } = res
            
            try {
              const addressInfo = await getAddressFromCoords(longitude, latitude)
              
              const locationInfo: LocationInfo = {
                province: addressInfo.province || '',
                city: addressInfo.city || '',
                district: addressInfo.district || '',
                street: addressInfo.street || '',
                fullAddress: addressInfo.fullAddress || `${addressInfo.province}${addressInfo.city}${addressInfo.district}`,
                longitude,
                latitude
              }
              
              this.setLocation(locationInfo)
              resolve(locationInfo)
            } catch (geoError) {
              console.warn('逆地理编码失败:', geoError)
              
              const locationInfo: LocationInfo = {
                ...state.location,
                longitude,
                latitude
              }
              
              this.setLocation(locationInfo)
              resolve(locationInfo)
            }
          },
          fail: async (err) => {
            console.warn('GPS定位失败，尝试IP定位:', err)
            state.hasPermission = false
            
            try {
              const ipLocation = await getLocationByIP()
              const locationInfo: LocationInfo = {
                province: ipLocation.province || '',
                city: ipLocation.city || '',
                district: '',
                street: '',
                fullAddress: `${ipLocation.province}${ipLocation.city}`,
                longitude: 0,
                latitude: 0
              }
              
              this.setLocation(locationInfo)
              resolve(locationInfo)
            } catch (ipError) {
              console.error('IP定位也失败:', ipError)
              reject(new Error('定位失败，请手动选择城市'))
            }
          },
          complete: () => {
            state.isLocating = false
          }
        })
      })
    } catch (error) {
      state.isLocating = false
      throw error
    }
  },

  // 更新权限状态
  updatePermissionStatus(granted: boolean): void {
    state.hasPermission = granted
  }
}

// 兼容之前的使用方式
export const useLocationStore = () => locationManager 