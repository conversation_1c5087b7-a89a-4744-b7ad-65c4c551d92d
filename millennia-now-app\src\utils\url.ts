// URL工具函数，兼容小程序环境

/**
 * 构建查询字符串
 * @param params 查询参数对象
 * @returns 查询字符串（不包含?）
 */
export function buildQueryString(params: Record<string, any>): string {
  const queryParams: string[] = []
  
  for (const [key, value] of Object.entries(params)) {
    if (value !== null && value !== undefined && value !== '') {
      const encodedValue = encodeURIComponent(String(value))
      queryParams.push(`${key}=${encodedValue}`)
    }
  }
  
  return queryParams.join('&')
}

/**
 * 构建完整的URL（包含查询字符串）
 * @param baseUrl 基础URL
 * @param params 查询参数对象
 * @returns 完整的URL
 */
export function buildUrl(baseUrl: string, params: Record<string, any> = {}): string {
  const queryString = buildQueryString(params)
  
  if (!queryString) {
    return baseUrl
  }
  
  const separator = baseUrl.includes('?') ? '&' : '?'
  return `${baseUrl}${separator}${queryString}`
} 