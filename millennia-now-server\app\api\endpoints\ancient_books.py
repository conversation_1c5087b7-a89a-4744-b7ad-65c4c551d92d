from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func, text

from app.database.db import get_db
from app.models.ancient_books import AncientBook, UserBookVolumeFavorite, BookVolumeViewHistory
from app.models.ancient_book_volumes import AncientBookVolume
from app.models.users import User, UserRole, ModulePermission
from app.core.module_permissions import require_ancient_books_permission
from app.core.dependencies import get_current_user
from app.schemas.ancient_book_schemas import (
    AncientBookCreate,
    AncientBookUpdate,
    AncientBookResponse,
    AncientBookList,
    AncientBookFilter,
    AncientBookVolumeCreate,
    AncientBookVolumeUpdate,
    AncientBookVolumeResponse,
    VolumeReadRequest,
    VolumeFavoriteRequest,
    VolumeViewHistoryResponse
)

router = APIRouter()

@router.get("/", response_model=AncientBookList)
def get_ancient_books(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sibu_category: Optional[str] = Query(None, pattern="^[ABCD]$", description="四部分类"),
    dynasty: Optional[str] = Query(None, description="朝代"),
    is_rare: Optional[bool] = Query(None, description="是否为善本"),
    is_digitized: Optional[bool] = Query(None, description="是否已数字化"),
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取古籍列表"""
    query = db.query(AncientBook).filter(AncientBook.status == "active")
    
    # 可见性过滤
    if current_user and current_user.role == UserRole.SUPER_ADMIN:
        # 超级管理员可以查看所有古籍
        pass
    elif current_user and current_user.has_module_permission(ModulePermission.ANCIENT_BOOKS):
        # 古籍管理员可以查看公众和研究员可见的古籍
        query = query.filter(AncientBook.visibility.in_(["public", "researcher"]))
    else:
        # 普通用户只能查看公众可见的古籍
        query = query.filter(AncientBook.visibility == "public")
    
    # 搜索过滤
    if search:
        search_filter = or_(
            AncientBook.title.contains(search),
            AncientBook.author.contains(search),
            AncientBook.keywords.contains(search),
            AncientBook.description.contains(search)
        )
        query = query.filter(search_filter)
    
    # 分类过滤
    if sibu_category:
        query = query.filter(AncientBook.sibu_category == sibu_category)
    
    # 朝代过滤
    if dynasty:
        query = query.filter(AncientBook.dynasty == dynasty)
    
    # 善本过滤
    if is_rare is not None:
        if is_rare:
            query = query.filter(AncientBook.rare_book_number.isnot(None))
        else:
            query = query.filter(AncientBook.rare_book_number.is_(None))
    
    # 数字化过滤（暂时禁用，等待新的卷册模型完善）
    # if is_digitized is not None:
    #     if is_digitized:
    #         query = query.join(AncientBookVolume).filter(AncientBookVolume.status == 'published')
    #     else:
    #         query = query.outerjoin(AncientBookVolume).filter(
    #             or_(
    #                 AncientBookVolume.id.is_(None),
    #                 AncientBookVolume.status == 'draft'
    #             )
    #         )
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    books = query.offset(skip).limit(limit).all()
    
    return AncientBookList(
        items=books,
        total=total,
        skip=skip,
        limit=limit
    )

@router.get("/{book_id}", response_model=AncientBookResponse)
def get_ancient_book(
    book_id: int = Path(..., description="古籍ID"),
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取古籍详情"""
    book = db.query(AncientBook).filter(
        AncientBook.id == book_id,
        AncientBook.status == "active"
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="古籍不存在")
    
    # 可见性检查
    if current_user and current_user.role == UserRole.SUPER_ADMIN:
        # 超级管理员可以查看所有古籍
        pass
    elif current_user and current_user.has_module_permission(ModulePermission.ANCIENT_BOOKS):
        # 古籍管理员可以查看公众和研究员可见的古籍
        if book.visibility not in ["public", "researcher"]:
            raise HTTPException(status_code=403, detail="无权访问此古籍")
    else:
        # 普通用户只能查看公众可见的古籍
        if book.visibility != "public":
            raise HTTPException(status_code=403, detail="无权访问此古籍")
    
    # 增加浏览次数
    book.view_count += 1
    db.commit()
    
    return book

@router.post("/", response_model=AncientBookResponse)
def create_ancient_book(
    book_data: AncientBookCreate,
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """创建古籍"""
    # 检查善本书号唯一性
    if book_data.rare_book_number:
        existing = db.query(AncientBook).filter(
            AncientBook.rare_book_number == book_data.rare_book_number
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="善本书号已存在")
    
    # 创建古籍主体
    book_dict = book_data.dict(exclude={'volumes'})
    book_dict['created_by'] = current_user.id  # 设置创建者
    book_dict['updated_by'] = current_user.id
    # 设置默认可见性
    if not book_dict.get('visibility'):
        book_dict['visibility'] = 'public'
    book = AncientBook(**book_dict)
    db.add(book)
    db.flush()  # 获取ID
    
    # 创建卷册
    if book_data.volumes:
        for volume_data in book_data.volumes:
            volume_dict = volume_data.dict()
            # 新的卷册模型只保留基本字段
            volume = AncientBookVolume(
                book_id=book.id,
                volume_number=volume_dict.get('volume_number'),
                volume_title=volume_dict.get('volume_title'),
                content_description=volume_dict.get('content_description'),
                status='draft'
            )
            db.add(volume)
    
    db.commit()
    db.refresh(book)
    
    return book

@router.put("/{book_id}", response_model=AncientBookResponse)
def update_ancient_book(
    book_data: AncientBookUpdate,
    book_id: int = Path(..., description="古籍ID"),
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """更新古籍"""
    book = db.query(AncientBook).filter(
        AncientBook.id == book_id,
        AncientBook.status == "active"
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="古籍不存在")
    
    # 权限检查：只有超级管理员或创建者可以编辑
    if current_user.role != UserRole.SUPER_ADMIN and book.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="无权编辑此古籍")
    
    # 检查善本书号唯一性
    if book_data.rare_book_number and book_data.rare_book_number != book.rare_book_number:
        existing = db.query(AncientBook).filter(
            AncientBook.rare_book_number == book_data.rare_book_number,
            AncientBook.id != book_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="善本书号已存在")
    
    # 更新字段
    update_data = book_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(book, field, value)
    
    db.commit()
    db.refresh(book)
    
    return book

@router.delete("/{book_id}")
def delete_ancient_book(
    book_id: int = Path(..., description="古籍ID"),
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """删除古籍（软删除）"""
    book = db.query(AncientBook).filter(AncientBook.id == book_id).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="古籍不存在")
    
    # 权限检查：只有超级管理员或创建者可以删除
    if current_user.role != UserRole.SUPER_ADMIN and book.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此古籍")
    
    book.status = "archived"
    db.commit()
    
    return {"message": "古籍已删除"}

@router.get("/{book_id}/volumes", response_model=List[AncientBookVolumeResponse])
def get_book_volumes(
    book_id: int = Path(..., description="古籍ID"),
    db: Session = Depends(get_db)
):
    """获取古籍卷册列表"""
    volumes = db.query(AncientBookVolume).filter(
        AncientBookVolume.book_id == book_id,
        AncientBookVolume.status.in_(["draft", "published"])
    ).order_by(AncientBookVolume.volume_number).all()
    
    return volumes

@router.get("/volumes/{volume_id}", response_model=AncientBookVolumeResponse)
def get_volume(
    volume_id: int = Path(..., description="卷册ID"),
    db: Session = Depends(get_db)
):
    """获取卷册详情"""
    volume = db.query(AncientBookVolume).filter(
        AncientBookVolume.id == volume_id,
        AncientBookVolume.status.in_(["draft", "published"])
    ).first()
    
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    # 增加浏览次数
    volume.view_count += 1
    db.commit()
    
    return volume

@router.post("/{book_id}/volumes", response_model=AncientBookVolumeResponse)
def create_volume(
    volume_data: AncientBookVolumeCreate,
    book_id: int = Path(..., description="古籍ID"),
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """创建卷册"""
    # 检查古籍是否存在
    book = db.query(AncientBook).filter(
        AncientBook.id == book_id,
        AncientBook.status == "active"
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="古籍不存在")
    
    # 检查卷册序号唯一性
    existing = db.query(AncientBookVolume).filter(
        AncientBookVolume.book_id == book_id,
        AncientBookVolume.volume_number == volume_data.volume_number,
        AncientBookVolume.status.in_(["draft", "published"])
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="卷册序号已存在")
    
    # 创建卷册（使用新模型字段）
    volume = AncientBookVolume(
        book_id=book_id,
        volume_number=volume_data.volume_number,
        volume_title=volume_data.volume_title,
        content_description=volume_data.content_description,
        status='draft'
    )
    db.add(volume)
    db.commit()
    db.refresh(volume)
    
    return volume

@router.put("/volumes/{volume_id}", response_model=AncientBookVolumeResponse)
def update_volume(
    volume_data: AncientBookVolumeUpdate,
    volume_id: int = Path(..., description="卷册ID"),
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """更新卷册"""
    volume = db.query(AncientBookVolume).filter(
        AncientBookVolume.id == volume_id,
        AncientBookVolume.status.in_(["draft", "published"])
    ).first()
    
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    # 更新字段（只更新新模型支持的字段）
    update_data = volume_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field in ['volume_title', 'content_description'] and value is not None:
            setattr(volume, field, value)
    
    db.commit()
    db.refresh(volume)
    
    return volume

@router.delete("/volumes/{volume_id}")
def delete_volume(
    volume_id: int = Path(..., description="卷册ID"),
    current_user: User = Depends(require_ancient_books_permission),
    db: Session = Depends(get_db)
):
    """删除卷册（软删除）"""
    volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    volume.status = "deleted"
    db.commit()
    
    return {"message": "卷册已删除"}

@router.post("/volumes/{volume_id}/read")
def read_volume(
    volume_id: int = Path(..., description="卷册ID"),
    user_id: int = Query(..., description="用户ID"),
    start_page: int = Query(1, ge=1, description="开始页码"),
    db: Session = Depends(get_db)
):
    """开始阅读卷册"""
    volume = db.query(AncientBookVolume).filter(
        AncientBookVolume.id == volume_id,
        AncientBookVolume.status.in_(["draft", "published"])
    ).first()
    
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    if volume.status != "published":
        raise HTTPException(status_code=400, detail="卷册尚未发布")
    
    # 记录浏览历史
    view_history = BookVolumeViewHistory(
        user_id=user_id,
        volume_id=volume_id,
        last_page=start_page
    )
    db.add(view_history)
    db.commit()
    
    return {
        "message": "开始阅读",
        "volume": volume,
        "start_page": start_page
    }

@router.post("/volumes/{volume_id}/favorite")
def favorite_volume(
    volume_id: int = Path(..., description="卷册ID"),
    user_id: int = Query(..., description="用户ID"),
    db: Session = Depends(get_db)
):
    """收藏卷册"""
    volume = db.query(AncientBookVolume).filter(
        AncientBookVolume.id == volume_id,
        AncientBookVolume.status.in_(["draft", "published"])
    ).first()
    
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    # 检查是否已收藏
    existing = db.query(UserBookVolumeFavorite).filter(
        UserBookVolumeFavorite.user_id == user_id,
        UserBookVolumeFavorite.volume_id == volume_id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="已收藏此卷册")
    
    # 创建收藏
    favorite = UserBookVolumeFavorite(
        user_id=user_id,
        volume_id=volume_id
    )
    db.add(favorite)
    
    # 更新收藏数（暂时注释掉，新模型中没有这个字段）
    # volume.favorite_count += 1
    
    db.commit()
    
    return {"message": "收藏成功"}

@router.delete("/volumes/{volume_id}/favorite")
def unfavorite_volume(
    volume_id: int = Path(..., description="卷册ID"),
    user_id: int = Query(..., description="用户ID"),
    db: Session = Depends(get_db)
):
    """取消收藏卷册"""
    favorite = db.query(UserBookVolumeFavorite).filter(
        UserBookVolumeFavorite.user_id == user_id,
        UserBookVolumeFavorite.volume_id == volume_id
    ).first()
    
    if not favorite:
        raise HTTPException(status_code=404, detail="未收藏此卷册")
    
    # 删除收藏
    db.delete(favorite)
    
    # 更新收藏数（暂时注释掉，新模型中没有这个字段）
    # volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    # if volume and volume.favorite_count > 0:
    #     volume.favorite_count -= 1
    
    db.commit()
    
    return {"message": "取消收藏成功"}

@router.get("/stats/summary")
def get_stats_summary(db: Session = Depends(get_db)):
    """获取统计摘要"""
    stats = {
        "total_books": db.query(AncientBook).filter(AncientBook.status == "active").count(),
        "total_volumes": db.query(AncientBookVolume).filter(AncientBookVolume.status.in_(["draft", "published"])).count(),
        "published_volumes": db.query(AncientBookVolume).filter(
            AncientBookVolume.status == "published"
        ).count(),
        "rare_books": db.query(AncientBook).filter(
            AncientBook.status == "active",
            AncientBook.rare_book_number.isnot(None)
        ).count(),
        "sibu_distribution": {}
    }
    
    # 四部分类统计
    sibu_stats = db.query(
        AncientBook.sibu_category,
        func.count(AncientBook.id).label('count')
    ).filter(
        AncientBook.status == "active"
    ).group_by(AncientBook.sibu_category).all()
    
    category_names = {'A': '经部', 'B': '史部', 'C': '子部', 'D': '集部'}
    for category, count in sibu_stats:
        if category:
            stats["sibu_distribution"][category_names.get(category, category)] = count
    
    return stats

@router.get("/search/suggestions")
def get_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="建议数量"),
    db: Session = Depends(get_db)
):
    """获取搜索建议"""
    suggestions = []
    
    # 古籍标题建议
    title_suggestions = db.query(AncientBook.title).filter(
        AncientBook.status == "active",
        AncientBook.title.contains(q)
    ).limit(limit // 2).all()
    
    suggestions.extend([title for (title,) in title_suggestions])
    
    # 作者建议
    author_suggestions = db.query(AncientBook.author).filter(
        AncientBook.status == "active",
        AncientBook.author.contains(q),
        AncientBook.author.isnot(None)
    ).distinct().limit(limit // 2).all()
    
    suggestions.extend([author for (author,) in author_suggestions])
    
    return {"suggestions": suggestions[:limit]} 