import httpx
import json
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class WechatMiniProgramService:
    """微信小程序服务"""
    
    def __init__(self):
        self.app_id = os.getenv("WECHAT_APP_ID")
        self.app_secret = os.getenv("WECHAT_APP_SECRET")
        self.base_url = "https://api.weixin.qq.com"
        
        if not self.app_id or not self.app_secret:
            raise ValueError("请在环境变量中配置 WECHAT_APP_ID 和 WECHAT_APP_SECRET")
    
    async def code2session(self, code: str) -> Dict[str, Any]:
        """
        通过授权码获取session_key和openid
        """
        url = f"{self.base_url}/sns/jscode2session"
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            result = response.json()
            
            if "errcode" in result:
                raise ValueError(f"微信授权失败: {result.get('errmsg', '未知错误')}")
            
            return result
    
    async def get_phone_number(self, code: str) -> Dict[str, Any]:
        """
        通过code获取手机号
        """
        # 首先需要获取access_token
        access_token = await self.get_access_token()
        
        url = f"{self.base_url}/wxa/business/getuserphonenumber"
        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "code": code
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{url}?access_token={access_token}",
                json=data,
                headers=headers
            )
            result = response.json()
            
            if result.get("errcode") != 0:
                raise ValueError(f"获取手机号失败: {result.get('errmsg', '未知错误')}")
            
            return result.get("phone_info", {})
    
    async def get_access_token(self) -> str:
        """
        获取access_token
        """
        url = f"{self.base_url}/cgi-bin/token"
        params = {
            "grant_type": "client_credential",
            "appid": self.app_id,
            "secret": self.app_secret
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            result = response.json()
            
            if "errcode" in result:
                raise ValueError(f"获取access_token失败: {result.get('errmsg', '未知错误')}")
            
            return result["access_token"]
    
    def decrypt_data(self, encrypted_data: str, session_key: str, iv: str) -> Dict[str, Any]:
        """
        解密微信加密数据
        """
        try:
            # Base64解码
            encrypted_data = base64.b64decode(encrypted_data)
            session_key = base64.b64decode(session_key)
            iv = base64.b64decode(iv)
            
            # AES解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            decrypted = unpad(decrypted, AES.block_size)
            
            # 解析JSON
            result = json.loads(decrypted.decode('utf-8'))
            
            # 验证appId
            if result.get('watermark', {}).get('appid') != self.app_id:
                raise ValueError("appId验证失败")
            
            return result
            
        except Exception as e:
            raise ValueError(f"数据解密失败: {str(e)}")


# 全局实例
wechat_service = WechatMiniProgramService() 