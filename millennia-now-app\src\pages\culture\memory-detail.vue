<template>
  <view class="memory-detail-container">
    <!-- 加载状态 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>

    <!-- 详细内容 -->
    <view class="detail-content"
          v-else-if="memoryData">
      <!-- 头部图片 -->
      <view class="header-image-container"
            v-if="memoryData.image">
        <image :src="memoryData.image"
               mode="aspectFill"
               class="header-image" />
        <view class="image-overlay">
          <text class="memory-title">{{ memoryData.title }}</text>
          <text class="memory-year">{{ memoryData.year }}</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title"
              v-if="!memoryData.image">
          <text class="title-text">{{ memoryData.title }}</text>
          <text class="year-text">{{ memoryData.year }}</text>
        </view>

        <view class="description-content"
              v-if="memoryData.description">
          <text class="description-text">{{ memoryData.description }}</text>
        </view>
      </view>

      <!-- 详细内容 -->
      <view class="detail-content-section"
            v-if="memoryData.detail_content">
        <view class="section-header">
          <text class="section-title-text">详细内容</text>
        </view>
        <view class="detail-content-container">
          <rich-text :nodes="memoryData.detail_content"></rich-text>
        </view>
      </view>

      <!-- 详细图片 -->
      <view class="images-section"
            v-if="memoryData.detail_images && memoryData.detail_images.length > 0">
        <view class="section-header">
          <text class="section-title-text">相关图片</text>
        </view>
        <view class="images-grid">
          <view class="image-item"
                v-for="(image, index) in memoryData.detail_images"
                :key="index"
                @click="previewImage(memoryData.detail_images, index)">
            <image :src="image"
                   mode="aspectFill"
                   class="grid-image" />
          </view>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <text class="update-time">最后更新：{{ formatDate(memoryData.updated_at) }}</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container"
          v-else>
      <text class="error-text">内容加载失败</text>
      <button class="retry-btn"
              @click="loadMemoryDetail">重试</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getMemoryItemDetail, type MemoryItem } from '@/api/heritage'

// 页面状态
const isLoading = ref(true)
const loadingText = ref('加载中...')
const memoryData = ref<MemoryItem | null>(null)
const memoryId = ref<number | null>(null)

// 初始化页面
onMounted(async () => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}

  if (options.memory_id) {
    memoryId.value = parseInt(options.memory_id)
    await loadMemoryDetail()
  } else {
    isLoading.value = false
    uni.showToast({
      title: '缺少记忆ID',
      icon: 'none',
    })
  }

  // 设置导航栏
  uni.setNavigationBarTitle({
    title: memoryData.value?.title || '城市记忆详情',
  })
})

// 加载城市记忆详情
const loadMemoryDetail = async () => {
  if (!memoryId.value) {
    uni.showToast({
      title: '缺少记忆ID',
      icon: 'none',
    })
    return
  }

  try {
    isLoading.value = true
    loadingText.value = '加载中...'

    const data = await getMemoryItemDetail(memoryId.value)
    if (data) {
      memoryData.value = data

      // 更新导航栏标题
      uni.setNavigationBarTitle({
        title: data.title,
      })
    } else {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    }
  } catch (error: any) {
    console.error('加载城市记忆详情失败:', error)

    let errorMessage = '加载失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: current,
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}
</script>

<style scoped>
.memory-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 详细内容 */
.detail-content {
  background-color: #fff;
}

.detail-content-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.detail-content-container {
  padding: 30rpx;
  line-height: 1.8;
  font-size: 28rpx;
  color: #333;
}

/* 头部图片 */
.header-image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.header-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
}

.memory-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.memory-year {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 基本信息 */
.info-section {
  padding: 30rpx;
}

.section-title {
  margin-bottom: 30rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.year-text {
  display: block;
  font-size: 28rpx;
  color: #c8161e;
  background-color: #fff0f0;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  display: inline-block;
}

.description-content {
  margin-top: 30rpx;
}

.description-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
}

/* 图片展示 */
.images-section {
  padding: 30rpx;
  background-color: #f8f9fa;
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.image-item {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.grid-image {
  width: 100%;
  height: 300rpx;
}

/* 底部信息 */
.footer-info {
  padding: 30rpx;
  text-align: center;
  background-color: #f8f9fa;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
</style> 