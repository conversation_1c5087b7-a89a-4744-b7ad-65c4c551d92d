// 导入统一的API配置
import { getBaseURL } from '@/config/api'

// 基础配置 - 现在从统一配置获取
const BASE_URL = getBaseURL()

// 统一请求方法
function request<T = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  includeBinaryUrls?: boolean  // 是否包含二进制图片URL
}): Promise<T> {
  return new Promise((resolve, reject) => {
    // 构建完整URL
    const url = config.url.startsWith('http') 
      ? config.url 
      : `${BASE_URL}${config.url}`

    // 合并请求头
    const header: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.header
    }

    // 获取存储的token（如果有）
    try {
      const token = uni.getStorageSync('access_token')
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }
    } catch (error) {
      console.warn('获取token失败:', error)
    }

    // 如果需要包含二进制图片URL，添加查询参数
    let requestData = config.data
    if (config.includeBinaryUrls && config.method === 'GET') {
      requestData = {
        ...requestData,
        include_binary_urls: true
      }
    }

    // 发起请求
    uni.request({
      url,
      method: config.method || 'GET',
      data: requestData,
      header,
      timeout: 30000, // 增加超时时间
      success: (response: any) => {
        console.log(`[${config.method || 'GET'}] ${url}:`, response)
        
        // 检查HTTP状态码
        if (response.statusCode >= 200 && response.statusCode < 300) {
          resolve(response.data)
        } else {
          console.error('请求失败:', response)
          reject(new Error(`HTTP ${response.statusCode}: ${response.data?.message || '请求失败'}`))
        }
      },
      fail: (error: any) => {
        console.error(`[${config.method || 'GET'}] ${url} 请求失败:`, {
          errMsg: error.errMsg,
          errno: error.errno,
          errCode: error.errCode,
          statusCode: error.statusCode,
          error: error
        })

        // 显示用户友好的错误提示
        if (error.errMsg?.includes('ERR_CONNECTION_RESET') || error.errno === -101) {
          uni.showToast({
            title: '服务器连接中断，请稍后重试',
            icon: 'none',
            duration: 3000
          })
        } else {
          uni.showToast({
            title: '网络连接失败，请检查网络设置',
            icon: 'none',
            duration: 3000
          })
        }

        reject(new Error(error.errMsg || '网络请求失败'))
      }
    })
  })
}

// 文化遗产地点信息接口
export interface HeritagePlace {
  id: number
  place_name: string
  place_desc?: string
  header_bg_image?: string
  introduction?: string
  footer_text?: string
  province_id?: number
  city_id?: number
  district_id?: number
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 时间轴项目接口
export interface TimelineItem {
  id: number
  place_id: number
  period: string
  year: string
  title: string
  description: string
  image?: string
  has_detail: boolean
  detail?: string
  detail_images: string[]
  heritage_tags: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 文化遗产项目接口
export interface HeritageItem {
  id: number
  place_id: number
  title: string
  type: string
  brief: string
  image?: string
  detail_content?: string
  detail_images: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 城市记忆项目接口
export interface MemoryItem {
  id: number
  place_id: number
  title: string
  year: string
  image?: string
  description?: string
  detail_content?: string
  detail_images: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 完整页面数据接口
export interface HeritagePageData {
  place_info: HeritagePlace
  timeline_data: TimelineItem[]
  heritage_data: HeritageItem[]
  memory_data: MemoryItem[]
}

// API响应接口
export interface ApiResponse<T> {
  statusCode?: number
  message?: string
  data?: T
}

/**
 * 根据省市区ID获取文化遗产地点信息
 * @param params 查询参数
 * @param includeBinaryUrls 是否包含二进制图片URL
 */
export async function getHeritageePlaceByRegion(params: {
  province_id?: number
  city_id?: number
  district_id?: number
}, includeBinaryUrls: boolean = true): Promise<HeritagePlace | null> {
  try {
    // 构建查询参数对象
    const queryParams: any = {}
    if (params.province_id) queryParams.province_id = params.province_id
    if (params.city_id) queryParams.city_id = params.city_id
    if (params.district_id) queryParams.district_id = params.district_id
    
    const response = await request<HeritagePlace | ApiResponse<null>>({
      url: '/heritage/places/by-region',
      method: 'GET',
      data: queryParams,
      includeBinaryUrls
    })

    // 检查是否是错误响应
    if (response && typeof response === 'object' && 'statusCode' in response) {
      if (response.statusCode === 404) {
        console.log('当前区域未构建数据')
        return null
      }
    }

    return response as HeritagePlace
  } catch (error) {
    console.error('获取文化遗产地点信息失败:', error)
    return null
  }
}

/**
 * 根据省市区ID获取完整的文化遗产页面数据
 * @param params 查询参数
 * @param includeBinaryData 是否包含图片二进制数据
 */
export async function getHeritagePageDataByRegion(params: {
  province_id?: number
  city_id?: number
  district_id?: number
}, includeBinaryData: boolean = true): Promise<HeritagePageData | null> {
  try {
    // 构建查询参数对象
    const queryParams: any = {}
    if (params.province_id) queryParams.province_id = params.province_id
    if (params.city_id) queryParams.city_id = params.city_id
    if (params.district_id) queryParams.district_id = params.district_id
    
    // 添加二进制数据选项
    queryParams.include_binary_data = includeBinaryData
    
    const response = await request<HeritagePageData | ApiResponse<null>>({
      url: '/heritage/places/by-region/full',
      method: 'GET',
      data: queryParams
    })

    // 检查是否是错误响应
    if (response && typeof response === 'object' && 'statusCode' in response) {
      if (response.statusCode === 404) {
        console.log('当前区域无数据')
        return null
      }
    }

    return response as HeritagePageData
  } catch (error) {
    console.error('获取文化遗产页面数据失败:', error)
    return null
  }
}

/**
 * 根据地点ID获取文化遗产地点信息
 * @param placeId 地点ID
 * @param includeBinaryData 是否包含图片二进制数据
 */
export async function getHeritagePlace(placeId: number | string, includeBinaryData: boolean = true): Promise<HeritagePlace | null> {
  try {
    // 确保placeId是整数
    const id = typeof placeId === 'string' ? parseInt(placeId, 10) : placeId
    if (isNaN(id)) {
      console.error('无效的place ID:', placeId)
      return null
    }

    const response = await request<HeritagePlace | ApiResponse<null>>({
      url: `/heritage/places/${id}`,
      method: 'GET',
      data: { include_binary_data: includeBinaryData }
    })

    // 检查是否是错误响应
    if (response && typeof response === 'object' && 'statusCode' in response) {
      if (response.statusCode === 404) {
        console.log('当前区域未构建数据')
        return null
      }
    }

    return response as HeritagePlace
  } catch (error) {
    console.error('获取文化遗产地点信息失败:', error)
    return null
  }
}

/**
 * 根据地点ID获取完整的文化遗产页面数据
 * @param placeId 地点ID
 * @param includeBinaryData 是否包含图片二进制数据
 */
export async function getHeritagePageData(placeId: number | string, includeBinaryData: boolean = true): Promise<HeritagePageData | null> {
  try {
    // 确保placeId是整数
    const id = typeof placeId === 'string' ? parseInt(placeId, 10) : placeId
    if (isNaN(id)) {
      console.error('无效的place ID:', placeId)
      return null
    }

    const response = await request<HeritagePageData | ApiResponse<null>>({
      url: `/heritage/places/${id}/full`,
      method: 'GET',
      data: { include_binary_data: includeBinaryData }
    })

    // 检查是否是错误响应
    if (response && typeof response === 'object' && 'statusCode' in response) {
      if (response.statusCode === 404) {
        console.log('当前区域无数据')
        return null
      }
    }

    return response as HeritagePageData
  } catch (error) {
    console.error('获取文化遗产页面数据失败:', error)
    return null
  }
}

// ==================== 地点管理API ====================

// 地点创建请求接口
export interface HeritagePlaceCreateRequest {
  place_name: string
  place_desc?: string
  header_bg_image?: string
  introduction?: string
  footer_text?: string
  province_id: number
  city_id?: number
  district_id?: number
  is_active?: boolean
  sort_order?: number
}

// 地点更新请求接口
export interface HeritagePlaceUpdateRequest {
  place_name?: string
  place_desc?: string
  header_bg_image?: string | null
  introduction?: string
  footer_text?: string
  is_active?: boolean
  sort_order?: number
}

/**
 * 创建文化遗产地点
 */
export async function createHeritagePlace(data: HeritagePlaceCreateRequest): Promise<HeritagePlace | null> {
  try {
    const response = await request<HeritagePlace>({
      url: '/heritage/places',
      method: 'POST',
      data: data
    })

    return response
  } catch (error) {
    console.error('创建文化遗产地点失败:', error)
    throw error
  }
}

/**
 * 更新文化遗产地点
 */
export async function updateHeritagePlace(placeId: number, data: HeritagePlaceUpdateRequest): Promise<HeritagePlace | null> {
  try {
    const response = await request<HeritagePlace>({
      url: `/heritage/places/${placeId}`,
      method: 'PUT',
      data: data
    })

    return response
  } catch (error) {
    console.error('更新文化遗产地点失败:', error)
    throw error
  }
}

/**
 * 删除文化遗产地点
 */
export async function deleteHeritagePlace(placeId: number): Promise<boolean> {
  try {
    await request({
      url: `/heritage/places/${placeId}`,
      method: 'DELETE'
    })

    return true
  } catch (error) {
    console.error('删除文化遗产地点失败:', error)
    throw error
  }
}

// ==================== 时间轴管理API ====================

// 时间轴项目创建请求接口
export interface TimelineItemCreateRequest {
  period: string
  year: string
  title: string
  description: string
  image?: string
  has_detail?: boolean
  detail?: string
  detail_images?: string[]
  heritage_tags?: string[]
  is_active?: boolean
  sort_order?: number
}

// 时间轴项目更新请求接口
export interface TimelineItemUpdateRequest {
  period?: string
  year?: string
  title?: string
  description?: string
  image?: string | null
  has_detail?: boolean
  detail?: string
  detail_images?: string[] | null
  heritage_tags?: string[] | null
  is_active?: boolean
  sort_order?: number
}

/**
 * 创建时间轴项目
 */
export async function createTimelineItem(placeId: number, data: TimelineItemCreateRequest): Promise<TimelineItem | null> {
  try {
    const response = await request<TimelineItem>({
      url: `/heritage/places/${placeId}/timeline`,
      method: 'POST',
      data: data
    })

    return response
  } catch (error) {
    console.error('创建时间轴项目失败:', error)
    throw error
  }
}

/**
 * 更新时间轴项目
 */
export async function updateTimelineItem(itemId: number, data: TimelineItemUpdateRequest): Promise<TimelineItem | null> {
  try {
    const response = await request<TimelineItem>({
      url: `/heritage/timeline/${itemId}`,
      method: 'PUT',
      data: data
    })

    return response
  } catch (error) {
    console.error('更新时间轴项目失败:', error)
    throw error
  }
}

/**
 * 获取时间轴项目详情
 */
export async function getTimelineItem(itemId: number): Promise<TimelineItem | null> {
  try {
    const response = await request<TimelineItem>({
      url: `/heritage/timeline/${itemId}`,
      method: 'GET'
    })

    return response
  } catch (error) {
    console.error('获取时间轴项目失败:', error)
    throw error
  }
}

/**
 * 删除时间轴项目
 */
export async function deleteTimelineItem(itemId: number): Promise<boolean> {
  try {
    await request({
      url: `/heritage/timeline/${itemId}`,
      method: 'DELETE'
    })

    return true
  } catch (error) {
    console.error('删除时间轴项目失败:', error)
    throw error
  }
}

// ==================== 文化遗产项目管理API ====================

// 文化遗产项目创建请求接口
export interface HeritageItemCreateRequest {
  title: string
  type: string
  brief: string
  image?: string
  detail_content?: string
  detail_images?: string[]
  is_active?: boolean
  sort_order?: number
}

// 文化遗产项目更新请求接口
export interface HeritageItemUpdateRequest {
  title?: string
  type?: string
  brief?: string
  image?: string | null
  detail_content?: string
  detail_images?: string[] | null
  is_active?: boolean
  sort_order?: number
}

/**
 * 创建文化遗产项目
 */
export async function createHeritageItem(placeId: number, data: HeritageItemCreateRequest): Promise<HeritageItem | null> {
  try {
    const response = await request<HeritageItem>({
      url: `/heritage/places/${placeId}/heritage`,
      method: 'POST',
      data: data
    })

    return response
  } catch (error) {
    console.error('创建文化遗产项目失败:', error)
    throw error
  }
}

/**
 * 更新文化遗产项目
 */
export async function updateHeritageItem(itemId: number, data: HeritageItemUpdateRequest): Promise<HeritageItem | null> {
  try {
    const response = await request<HeritageItem>({
      url: `/heritage/heritage/${itemId}`,
      method: 'PUT',
      data: data
    })

    return response
  } catch (error) {
    console.error('更新文化遗产项目失败:', error)
    throw error
  }
}

/**
 * 获取文化遗产项目详情
 */
export async function getHeritageItemDetail(itemId: number): Promise<HeritageItem | null> {
  try {
    const response = await request<HeritageItem>({
      url: `/heritage/heritage/${itemId}`,
      method: 'GET'
    })

    return response
  } catch (error) {
    console.error('获取文化遗产项目失败:', error)
    throw error
  }
}

/**
 * 删除文化遗产项目
 */
export async function deleteHeritageItem(itemId: number): Promise<boolean> {
  try {
    await request({
      url: `/heritage/heritage/${itemId}`,
      method: 'DELETE'
    })

    return true
  } catch (error) {
    console.error('删除文化遗产项目失败:', error)
    throw error
  }
}

// ==================== 城市记忆管理API ====================

// 城市记忆创建请求接口
export interface MemoryItemCreateRequest {
  title: string
  year: string
  image?: string
  description?: string
  detail_content?: string
  detail_images?: string[]
  is_active?: boolean
  sort_order?: number
}

// 城市记忆更新请求接口
export interface MemoryItemUpdateRequest {
  title?: string
  year?: string
  image?: string | null
  description?: string
  detail_content?: string | null
  detail_images?: string[] | null
  is_active?: boolean
  sort_order?: number
}

// 城市记忆列表响应接口
export interface MemoryItemListResponse {
  items: MemoryItem[]
  total: number
  page: number
  page_size: number
}

/**
 * 获取城市记忆列表
 */
export async function getMemoryItemList(
  placeId: number,
  page: number = 1,
  pageSize: number = 20
): Promise<MemoryItemListResponse | null> {
  try {
    const response = await request<MemoryItemListResponse>({
      url: `/heritage/places/${placeId}/memory`,
      method: 'GET',
      data: {
        page,
        page_size: pageSize
      }
    })
    return response
  } catch (error) {
    console.error('获取城市记忆列表失败:', error)
    return null
  }
}

/**
 * 创建城市记忆项目
 */
export async function createMemoryItem(placeId: number, data: MemoryItemCreateRequest): Promise<MemoryItem | null> {
  try {
    const response = await request<MemoryItem>({
      url: `/heritage/places/${placeId}/memory`,
      method: 'POST',
      data
    })
    return response
  } catch (error) {
    console.error('创建城市记忆项目失败:', error)
    return null
  }
}

/**
 * 更新城市记忆项目
 */
export async function updateMemoryItem(itemId: number, data: MemoryItemUpdateRequest): Promise<MemoryItem | null> {
  try {
    const response = await request<MemoryItem>({
      url: `/heritage/memory/${itemId}`,
      method: 'PUT',
      data
    })
    return response
  } catch (error) {
    console.error('更新城市记忆项目失败:', error)
    return null
  }
}

/**
 * 获取城市记忆项目详情
 */
export async function getMemoryItemDetail(itemId: number): Promise<MemoryItem | null> {
  try {
    const response = await request<MemoryItem>({
      url: `/heritage/memory/${itemId}`,
      method: 'GET'
    })
    return response
  } catch (error) {
    console.error('获取城市记忆项目详情失败:', error)
    return null
  }
}

/**
 * 删除城市记忆项目
 */
export async function deleteMemoryItem(itemId: number): Promise<boolean> {
  try {
    await request({
      url: `/heritage/memory/${itemId}`,
      method: 'DELETE'
    })
    return true
  } catch (error) {
    console.error('删除城市记忆项目失败:', error)
    return false
  }
}

/**
 * 获取相关文化遗产项目
 * @param heritageId 遗产ID
 */
export async function getRelatedHeritages(heritageId: number): Promise<HeritageItem[]> {
  try {
    const response = await request<HeritageItem[]>({
      url: `/heritage/heritage/${heritageId}/related`,
      method: 'GET'
    })
    return response || []
  } catch (error) {
    console.error('获取相关文化遗产失败:', error)
    return []
  }
}