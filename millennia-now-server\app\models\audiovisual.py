from sqlalchemy import Column, Integer, String, Text, ForeignKey, Boolean, DateTime, func, TIMESTAMP, SmallInteger, Enum, Float, JSON
from sqlalchemy.orm import relationship
from app.database.db import Base
import enum


class ArchiveType(enum.Enum):
    """档案类型枚举"""
    ANCIENT_BOOKS = "古籍典藏"
    PAINTINGS = "书画珍品" 
    ARCHIVES = "档案故事"
    VIDEOS = "影像文献"


class AudioVisualArchive(Base):
    """声像文藏主表"""
    __tablename__ = "audiovisual_archives"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False, comment="标题")
    type = Column(Enum(ArchiveType), nullable=False, comment="档案类型")
    description = Column(Text, nullable=True, comment="描述")
    
    # 基本信息
    year = Column(String(50), nullable=True, comment="年代")
    dynasty = Column(String(50), nullable=True, comment="朝代/时期")
    author = Column(String(100), nullable=True, comment="作者/创作者")
    
    # 图像信息
    cover_image = Column(String(500), nullable=True, comment="封面图片URL")
    thumbnail = Column(String(500), nullable=True, comment="缩略图URL")
    images = Column(JSON, nullable=True, comment="相关图片URLs的JSON数组")
    
    # 数字化信息
    is_digitized = Column(Boolean, default=False, comment="是否已数字化")
    is_high_res = Column(Boolean, default=False, comment="是否高清")
    digital_files = Column(JSON, nullable=True, comment="数字化文件信息JSON")
    
    # 内容分析
    has_ocr = Column(Boolean, default=False, comment="是否有OCR识别")
    ocr_content = Column(Text, nullable=True, comment="OCR识别内容")
    ocr_accuracy = Column(Float, nullable=True, comment="OCR识别准确率")
    
    has_ai_analysis = Column(Boolean, default=False, comment="是否有AI分析")
    ai_tags = Column(JSON, nullable=True, comment="AI标签JSON数组")
    ai_analysis = Column(JSON, nullable=True, comment="AI分析结果JSON")
    
    # 标签和分类
    tags = Column(JSON, nullable=True, comment="标签JSON数组")
    category = Column(String(100), nullable=True, comment="分类")
    sub_category = Column(String(100), nullable=True, comment="子分类")
    
    # 历史背景
    historical_context = Column(Text, nullable=True, comment="历史背景介绍")
    cultural_significance = Column(Text, nullable=True, comment="文化意义")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览次数")
    favorite_count = Column(Integer, default=0, comment="收藏次数")
    download_count = Column(Integer, default=0, comment="下载次数")
    
    # 关联的行政区域
    province_id = Column(SmallInteger, nullable=True, comment="省份ID")
    city_id = Column(SmallInteger, nullable=True, comment="城市ID")
    district_id = Column(SmallInteger, nullable=True, comment="区县ID")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_featured = Column(Boolean, default=False, comment="是否推荐")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    ancient_book = relationship("AncientBook", back_populates="archive", uselist=False, cascade="all, delete-orphan")
    painting = relationship("Painting", back_populates="archive", uselist=False, cascade="all, delete-orphan")
    archive_document = relationship("ArchiveDocument", back_populates="archive", uselist=False, cascade="all, delete-orphan")
    video_document = relationship("VideoDocument", back_populates="archive", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<AudioVisualArchive(id={self.id}, title='{self.title}', type='{self.type.value}')>"


class AncientBook(Base):
    """古籍典藏详细信息表"""
    __tablename__ = "ancient_books"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="关联档案ID")
    
    # 古籍专属信息
    original_title = Column(String(200), nullable=True, comment="原始书名")
    alternative_titles = Column(JSON, nullable=True, comment="别名JSON数组")
    volume_count = Column(Integer, nullable=True, comment="卷数")
    page_count = Column(Integer, nullable=True, comment="页数")
    
    # 版本信息
    edition = Column(String(100), nullable=True, comment="版本")
    publisher = Column(String(100), nullable=True, comment="出版者")
    printing_method = Column(String(50), nullable=True, comment="印刷方式")
    
    # 分类信息（四部分类法）
    classical_category = Column(String(20), nullable=True, comment="四部分类：经史子集")
    subject_classification = Column(String(100), nullable=True, comment="学科分类")
    
    # 保存状况
    preservation_status = Column(String(50), nullable=True, comment="保存状况")
    completeness = Column(Float, nullable=True, comment="完整性百分比")
    condition_notes = Column(Text, nullable=True, comment="状况说明")
    
    # OCR和AI特有信息
    text_recognition_status = Column(String(20), default="未处理", comment="文字识别状态")
    punctuation_status = Column(String(20), default="未处理", comment="标点处理状态")
    annotation_count = Column(Integer, default=0, comment="标注数量")
    
    # 相关文献
    related_books = Column(JSON, nullable=True, comment="相关古籍ID数组")
    references = Column(JSON, nullable=True, comment="参考文献JSON数组")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive", back_populates="ancient_book")
    
    def __repr__(self):
        return f"<AncientBook(id={self.id}, archive_id={self.archive_id})>"


class Painting(Base):
    """书画珍品详细信息表"""
    __tablename__ = "paintings"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="关联档案ID")
    
    # 书画专属信息
    art_type = Column(String(50), nullable=True, comment="艺术类型：书法/绘画")
    painting_type = Column(String(50), nullable=True, comment="绘画类型：山水/花鸟/人物等")
    medium = Column(String(100), nullable=True, comment="创作媒介")
    dimensions = Column(String(100), nullable=True, comment="尺寸")
    
    # 艺术风格
    art_school = Column(String(100), nullable=True, comment="艺术流派")
    art_style = Column(String(100), nullable=True, comment="艺术风格")
    techniques = Column(JSON, nullable=True, comment="技法JSON数组")
    
    # 风格分析
    has_style_analysis = Column(Boolean, default=False, comment="是否有风格分析")
    style_similarity = Column(Float, nullable=True, comment="风格相似度")
    style_analysis_result = Column(JSON, nullable=True, comment="风格分析结果JSON")
    
    # 技法分析
    has_technique_analysis = Column(Boolean, default=False, comment="是否有技法分析")
    technique_analysis = Column(JSON, nullable=True, comment="技法分析结果JSON")
    
    # 色彩分析
    has_color_analysis = Column(Boolean, default=False, comment="是否有色彩分析")
    color_palette = Column(JSON, nullable=True, comment="色彩调色板JSON")
    dominant_colors = Column(JSON, nullable=True, comment="主色调JSON数组")
    
    # 作品信息
    subject_matter = Column(String(200), nullable=True, comment="题材内容")
    artistic_value = Column(Text, nullable=True, comment="艺术价值")
    historical_importance = Column(Text, nullable=True, comment="历史重要性")
    
    # 收藏信息
    collection_history = Column(JSON, nullable=True, comment="收藏历史JSON数组")
    exhibition_history = Column(JSON, nullable=True, comment="展览历史JSON数组")
    
    # 相关作品
    related_works = Column(JSON, nullable=True, comment="相关作品ID数组")
    influenced_works = Column(JSON, nullable=True, comment="影响的作品ID数组")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive", back_populates="painting")
    
    def __repr__(self):
        return f"<Painting(id={self.id}, archive_id={self.archive_id})>"


class ArchiveDocument(Base):
    """档案故事详细信息表"""
    __tablename__ = "archive_documents"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="关联档案ID")
    
    # 档案专属信息
    document_type = Column(String(50), nullable=True, comment="档案类型：契约/地方志/官府档案等")
    archive_level = Column(String(20), nullable=True, comment="档案级别")
    file_number = Column(String(100), nullable=True, comment="档案编号")
    
    # 内容信息
    main_content = Column(Text, nullable=True, comment="主要内容")
    key_information = Column(JSON, nullable=True, comment="关键信息JSON")
    persons_involved = Column(JSON, nullable=True, comment="涉及人物JSON数组")
    places_involved = Column(JSON, nullable=True, comment="涉及地点JSON数组")
    
    # 历史时间线
    timeline_events = Column(JSON, nullable=True, comment="历史时间线事件JSON数组")
    related_events = Column(JSON, nullable=True, comment="相关历史事件JSON数组")
    
    # 保存和来源信息
    original_location = Column(String(200), nullable=True, comment="原始保存地点")
    acquisition_method = Column(String(100), nullable=True, comment="获得方式")
    source_institution = Column(String(200), nullable=True, comment="来源机构")
    
    # 文献价值
    historical_value = Column(Text, nullable=True, comment="史学价值")
    research_significance = Column(Text, nullable=True, comment="研究意义")
    citation_count = Column(Integer, default=0, comment="被引用次数")
    
    # 相关档案
    related_documents = Column(JSON, nullable=True, comment="相关档案ID数组")
    follow_up_documents = Column(JSON, nullable=True, comment="后续档案ID数组")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive", back_populates="archive_document")
    
    def __repr__(self):
        return f"<ArchiveDocument(id={self.id}, archive_id={self.archive_id})>"


class VideoDocument(Base):
    """影像文献详细信息表"""
    __tablename__ = "video_documents"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="关联档案ID")
    
    # 影像专属信息
    video_type = Column(String(50), nullable=True, comment="影像类型：历史影像/口述历史/纪录片等")
    duration = Column(String(20), nullable=True, comment="时长（格式：HH:MM:SS）")
    resolution = Column(String(20), nullable=True, comment="分辨率")
    frame_rate = Column(Float, nullable=True, comment="帧率")
    
    # 文件信息
    video_url = Column(String(500), nullable=True, comment="视频文件URL")
    video_formats = Column(JSON, nullable=True, comment="可用格式JSON数组")
    file_size = Column(String(50), nullable=True, comment="文件大小")
    
    # 音频信息
    audio_format = Column(String(20), nullable=True, comment="音频格式")
    has_audio = Column(Boolean, default=True, comment="是否有音频")
    audio_quality = Column(String(20), nullable=True, comment="音频质量")
    
    # 字幕信息
    has_subtitles = Column(Boolean, default=False, comment="是否有字幕")
    subtitle_languages = Column(JSON, nullable=True, comment="字幕语言JSON数组")
    subtitles = Column(JSON, nullable=True, comment="字幕内容JSON数组")
    auto_generated_subtitles = Column(Boolean, default=False, comment="是否自动生成字幕")
    
    # 拍摄信息
    shooting_date = Column(String(50), nullable=True, comment="拍摄日期")
    shooting_location = Column(String(200), nullable=True, comment="拍摄地点")
    cameraman = Column(String(100), nullable=True, comment="摄影师")
    production_company = Column(String(200), nullable=True, comment="制作公司")
    
    # 内容分析
    key_scenes = Column(JSON, nullable=True, comment="关键场景JSON数组")
    featured_persons = Column(JSON, nullable=True, comment="出现人物JSON数组")
    topics_covered = Column(JSON, nullable=True, comment="涉及主题JSON数组")
    
    # 播放统计
    play_count = Column(Integer, default=0, comment="播放次数")
    total_play_time = Column(Integer, default=0, comment="总播放时长（秒）")
    average_play_duration = Column(Float, nullable=True, comment="平均观看时长（秒）")
    
    # 相关视频
    related_videos = Column(JSON, nullable=True, comment="相关视频ID数组")
    series_info = Column(JSON, nullable=True, comment="系列信息JSON")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive", back_populates="video_document")
    
    def __repr__(self):
        return f"<VideoDocument(id={self.id}, archive_id={self.archive_id})>"


class UserFavorite(Base):
    """用户收藏表"""
    __tablename__ = "user_favorites"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment="用户ID")
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="档案ID")
    
    # 收藏信息
    favorite_type = Column(Enum(ArchiveType), nullable=False, comment="收藏类型")
    notes = Column(Text, nullable=True, comment="收藏备注")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive")
    
    def __repr__(self):
        return f"<UserFavorite(id={self.id}, user_id={self.user_id}, archive_id={self.archive_id})>"


class ArchiveViewHistory(Base):
    """档案浏览历史表"""
    __tablename__ = "archive_view_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True, comment="用户ID（可为空，支持匿名浏览）")
    archive_id = Column(Integer, ForeignKey("audiovisual_archives.id"), nullable=False, comment="档案ID")
    
    # 浏览信息
    view_duration = Column(Integer, nullable=True, comment="浏览时长（秒）")
    device_info = Column(JSON, nullable=True, comment="设备信息JSON")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    
    # 时间字段
    viewed_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    
    # 关系
    archive = relationship("AudioVisualArchive")
    
    def __repr__(self):
        return f"<ArchiveViewHistory(id={self.id}, user_id={self.user_id}, archive_id={self.archive_id})>" 