/**
 * uni-app多平台构建脚本
 * 通过读取manifest.json配置，构建所有配置的平台
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 读取manifest.json配置
const manifestPath = path.resolve(__dirname, '../src/manifest.json');
const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

// 支持的平台列表
const platforms = [];

// 检查各平台的配置
if (manifest['mp-weixin']) {
  platforms.push('mp-weixin');
}
if (manifest['mp-alipay']) {
  platforms.push('mp-alipay');
}
if (manifest['mp-baidu']) {
  platforms.push('mp-baidu');
}
if (manifest['mp-toutiao']) {
  platforms.push('mp-toutiao');
}
if (manifest['mp-qq']) {
  platforms.push('mp-qq');
}
if (manifest['h5']) {
  platforms.push('h5');
}
if (manifest['app-plus']) {
  platforms.push('app-plus');
}
if (manifest['quickapp']) {
  platforms.push('quickapp-webview');
}

console.log('准备构建的平台:', platforms);

// 为每个平台执行构建
platforms.forEach(platform => {
  const command = `npm run build:${platform}`;
  console.log(`开始构建 ${platform} 平台: ${command}`);
  
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(`[${platform}] 构建成功: \n${output}`);
  } catch (error) {
    console.error(`[${platform}] 构建失败: ${error.message}`);
    console.error(error.stdout);
  }
});

console.log('所有平台构建完成，请检查dist目录下的输出结果'); 