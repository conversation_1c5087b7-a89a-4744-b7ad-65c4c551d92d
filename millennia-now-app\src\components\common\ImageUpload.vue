<template>
  <view class="image-upload">
    <!-- 提示信息 -->
    <view v-if="showTips && tipsText"
          class="upload-tips">
      <text class="tips-text">{{ tipsText }}</text>
    </view>

    <!-- 上传区域 -->
    <view class="upload-area"
          @click="selectImages">
      <view class="upload-content">
        <image v-if="!multiple && imageList.length > 0"
               :src="imageList[0]"
               class="preview-image"
               mode="aspectFill" />
        <view v-else
              class="upload-placeholder">
          <image src="/static/icons/add.svg"
                 class="add-icon" />
          <text class="upload-text">
            {{ multiple ? `${uploadText} (${imageList.length}/${maxCount})` : uploadText }}
          </text>
        </view>
      </view>
    </view>

    <!-- 多图预览 -->
    <view v-if="multiple && imageList.length > 0"
          class="image-grid">
      <view v-for="(image, index) in imageList"
            :key="index"
            class="image-item">
        <image :src="image"
               class="grid-image"
               mode="aspectFill" />
        <view class="delete-btn"
              @click="removeImage(index)">
          <image src="/static/icons/delete.svg"
                 class="delete-icon" />
        </view>
      </view>
    </view>

    <!-- 上传进度 -->
    <view v-if="uploading"
          class="upload-progress">
      <progress :percent="uploadProgress"
                :show-info="true"
                stroke-width="4"
                activeColor="#007aff" />
      <text class="progress-text">上传中... {{ uploadProgress }}%</text>
    </view>

    <!-- 错误信息 -->
    <view v-if="errorMessage"
          class="error-message">
      <text class="error-text">{{ errorMessage }}</text>
    </view>
  </view>
</template>

<script>
import { uploadImage, uploadImages } from '../../utils/upload'
import { getDisplayImageUrl } from '../../utils/image'

export default {
  name: 'ImageUpload',
  props: {
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 9
    },
    // 上传类型前缀
    uploadType: {
      type: String,
      default: 'images'
    },
    // 上传分类 (heritage, memory, timeline, place)
    category: {
      type: String,
      default: ''
    },
    // 初始图片列表
    value: {
      type: [Array, String],
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 上传按钮文本
    uploadText: {
      type: String,
      default: '选择图片'
    },
    // 是否显示提示
    showTips: {
      type: Boolean,
      default: false
    },
    // 提示文本
    tipsText: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      imageList: [],
      uploading: false,
      uploadProgress: 0,
      errorMessage: ''
    }
  },
  watch: {
    value: {
      handler (newVal) {
        if (Array.isArray(newVal)) {
          // 如果是数组，直接使用
          this.imageList = [...newVal]
        } else if (typeof newVal === 'string' && newVal.trim()) {
          // 如果是字符串且不为空，转换为数组
          this.imageList = [newVal.trim()]
        } else {
          // 其他情况（null, undefined, 空字符串）设为空数组
          this.imageList = []
        }
      },
      immediate: true
    }
  },
  methods: {
    // 选择图片
    async selectImages () {
      if (this.disabled || this.uploading) return

      try {
        this.errorMessage = ''

        const count = this.multiple ?
          Math.min(this.maxCount - this.imageList.length, 9) : 1

        if (count <= 0) {
          this.showError('已达到最大上传数量')
          return
        }

        const res = await uni.chooseImage({
          count,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })

        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          await this.uploadSelectedImages(res.tempFilePaths)
        }
      } catch (error) {
        console.error('选择图片失败:', error)
        this.showError('选择图片失败')
      }
    },

    // 上传选中的图片
    async uploadSelectedImages (tempFilePaths) {
      this.uploading = true
      this.uploadProgress = 0

      try {
        const uploadCategory = this.uploadType || this.category || 'images'

        if (this.multiple) {
          // 批量上传
          const results = await uploadImages(tempFilePaths, uploadCategory, (progress) => {
            this.uploadProgress = progress
          })

          const successUrls = results
            .filter(result => result.success)
            .map(result => getDisplayImageUrl(result.url))

          if (successUrls.length > 0) {
            this.imageList.push(...successUrls)
            this.emitChange()
          }

          const failedCount = results.length - successUrls.length
          if (failedCount > 0) {
            this.showError(`${failedCount} 张图片上传失败`)
          }
        } else {
          // 单图上传
          const result = await uploadImage(tempFilePaths[0], uploadCategory, (progress) => {
            this.uploadProgress = progress
          })

          if (result.success) {
            this.imageList = [getDisplayImageUrl(result.url)]
            this.emitChange()
          } else {
            this.showError(result.message || '上传失败')
          }
        }
      } catch (error) {
        console.error('上传失败:', error)
        this.showError('上传失败，请重试')
      } finally {
        this.uploading = false
        this.uploadProgress = 0
      }
    },

    // 删除图片
    removeImage (index) {
      if (this.disabled) return

      this.imageList.splice(index, 1)
      this.emitChange()
    },

    // 发送变化事件
    emitChange () {
      // 根据multiple属性决定返回的数据类型
      const emitValue = this.multiple ? this.imageList : (this.imageList[0] || '')

      this.$emit('input', emitValue)
      this.$emit('change', emitValue)
      this.$emit('update:modelValue', emitValue) // Vue 3 兼容

      // 兼容旧版本的事件
      if (this.imageList.length > 0) {
        this.$emit('upload-success', {
          urls: this.imageList,
          url: this.imageList[this.imageList.length - 1] // 最新上传的图片
        })
      }
    },

    // 显示错误信息
    showError (message) {
      this.errorMessage = message
      this.$emit('upload-error', message) // 发送错误事件
      setTimeout(() => {
        this.errorMessage = ''
      }, 3000)
    },

    // 清空图片
    clear () {
      this.imageList = []
      this.emitChange()
    },

    // 获取图片列表
    getImageList () {
      return this.imageList
    }
  }
}
</script>

<style scoped>
.image-upload {
  width: 100%;
}

.upload-tips {
  margin-bottom: 20rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.upload-area {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-area:active {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.upload-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.grid-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

.upload-progress {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
  text-align: center;
}

.error-message {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff5f5;
  border: 1rpx solid #fed7d7;
  border-radius: 8rpx;
}

.error-text {
  font-size: 28rpx;
  color: #e53e3e;
  text-align: center;
}

/* 禁用状态 */
.image-upload.disabled .upload-area {
  opacity: 0.5;
  pointer-events: none;
}

.image-upload.disabled .delete-btn {
  display: none;
}
</style> 