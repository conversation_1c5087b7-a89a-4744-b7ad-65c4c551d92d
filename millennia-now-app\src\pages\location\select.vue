<template>
  <view class="location-select-container">
    <!-- 标题栏 -->
    <view class="header">
      <view class="header-left"
            @click="goBack">
        <view class="back-btn">
          <text class="back-icon">←</text>
        </view>
      </view>
      <text class="header-title">选择城市</text>
      <view class="header-right"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 当前位置 -->
      <view class="current-location"
            @click="getCurrentLocation">
        <view class="location-info">
          <text class="location-icon">📍</text>
          <text class="location-text">当前位置</text>
        </view>
        <view class="location-detail">
          <text class="location-name"
                v-if="currentLocationName">{{ currentLocationName }}</text>
          <text class="location-loading"
                v-else>正在定位...</text>
        </view>
        <text class="location-arrow">></text>
      </view>

      <!-- 选择器容器 -->
      <view class="selector-container">
        <!-- 省份选择 -->
        <view class="selector-section">
          <text class="selector-title">省份</text>
          <scroll-view class="selector-list"
                       scroll-y>
            <view class="selector-item"
                  :class="{ active: selectedProvince?.province_id === province.province_id }"
                  v-for="province in provinces"
                  :key="province.province_id"
                  @click="selectProvince(province)">
              <text class="item-text">{{ province.name }}</text>
              <text class="item-check"
                    v-if="selectedProvince?.province_id === province.province_id">✓</text>
            </view>
          </scroll-view>
        </view>

        <!-- 城市选择 -->
        <view class="selector-section"
              v-if="selectedProvince">
          <text class="selector-title">城市</text>
          <scroll-view class="selector-list"
                       scroll-y>
            <view class="selector-item"
                  :class="{ active: selectedCity?.city_id === city.city_id }"
                  v-for="city in cities"
                  :key="city.city_id"
                  @click="selectCity(city)">
              <text class="item-text">{{ city.name }}</text>
              <text class="item-check"
                    v-if="selectedCity?.city_id === city.city_id">✓</text>
            </view>
          </scroll-view>
        </view>

        <!-- 区县选择 -->
        <view class="selector-section"
              v-if="selectedCity">
          <text class="selector-title">区县</text>
          <scroll-view class="selector-list"
                       scroll-y>
            <view class="selector-item"
                  :class="{ active: selectedDistrict?.district_id === district.district_id }"
                  v-for="district in districts"
                  :key="district.district_id"
                  @click="selectDistrict(district)">
              <text class="item-text">{{ district.name }}</text>
              <text class="item-check"
                    v-if="selectedDistrict?.district_id === district.district_id">✓</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 确认按钮 - 固定在底部 -->
    <view class="confirm-container">
      <button class="confirm-btn"
              :disabled="!selectedCity"
              @click="confirmSelection">
        确认选择
      </button>
    </view>

    <!-- 加载提示 -->
    <view class="loading-mask"
          v-if="loading">
      <view class="loading-content">
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getProvinces, getCities, getDistricts } from '@/api/admin_divisions'
import type { Province, City, District } from '@/api/admin_divisions'
import { locationManager } from '@/store/modules/location'
import { regionManager } from '@/store/modules/region'

// 数据状态
const loading = ref(false)
const currentLocationName = ref('')

// 选择状态
const selectedProvince = ref<Province | null>(null)
const selectedCity = ref<City | null>(null)
const selectedDistrict = ref<District | null>(null)

// 列表数据
const provinces = ref<Province[]>([])
const cities = ref<City[]>([])
const districts = ref<District[]>([])

// 初始化
onMounted(async () => {
  await loadProvinces()
  await getCurrentLocation()
})

// 加载省份列表
const loadProvinces = async () => {
  try {
    loading.value = true
    provinces.value = await getProvinces()
  } catch (error) {
    uni.showToast({
      title: '加载省份失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 选择省份
const selectProvince = async (province: Province) => {
  selectedProvince.value = province
  selectedCity.value = null
  selectedDistrict.value = null
  cities.value = []
  districts.value = []

  try {
    loading.value = true
    cities.value = await getCities(province.province_id)
  } catch (error) {
    uni.showToast({
      title: '加载城市失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 选择城市
const selectCity = async (city: City) => {
  selectedCity.value = city
  selectedDistrict.value = null
  districts.value = []

  try {
    loading.value = true
    districts.value = await getDistricts(
      selectedProvince.value!.province_id,
      city.city_id
    )
  } catch (error) {
    uni.showToast({
      title: '加载区县失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 选择区县
const selectDistrict = (district: District) => {
  selectedDistrict.value = district
}

// 获取当前位置
const getCurrentLocation = async () => {
  try {
    loading.value = true
    const locationInfo = await locationManager.getCurrentLocation()
    currentLocationName.value = locationInfo.fullAddress

    // 自动匹配区域ID并回显到选择器
    try {
      const matchedRegionIds = await regionManager.matchRegionIdsByLocation({
        province: locationInfo.province,
        city: locationInfo.city,
        district: locationInfo.district,
      })

      if (matchedRegionIds) {
        console.log('选择页面: 定位后自动匹配区域ID成功，开始回显')

        // 回显省份
        if (matchedRegionIds.provinceId > 0) {
          const matchedProvince = provinces.value.find(
            (p) => p.province_id === matchedRegionIds.provinceId
          )
          if (matchedProvince) {
            console.log('回显省份:', matchedProvince.name)
            await selectProvince(matchedProvince)

            // 回显城市
            if (matchedRegionIds.cityId > 0) {
              const matchedCity = cities.value.find(
                (c) => c.city_id === matchedRegionIds.cityId
              )
              if (matchedCity) {
                console.log('回显城市:', matchedCity.name)
                await selectCity(matchedCity)

                // 回显区县
                if (matchedRegionIds.districtId > 0) {
                  const matchedDistrict = districts.value.find(
                    (d) => d.district_id === matchedRegionIds.districtId
                  )
                  if (matchedDistrict) {
                    console.log('回显区县:', matchedDistrict.name)
                    selectDistrict(matchedDistrict)
                  }
                }
              } else {
                console.warn(
                  '未找到匹配的城市, cityId:',
                  matchedRegionIds.cityId
                )
              }
            }
          } else {
            console.warn(
              '未找到匹配的省份, provinceId:',
              matchedRegionIds.provinceId
            )
          }
        }
        console.log('选择页面: 自动回显完成')
      }
    } catch (regionError) {
      console.warn('选择页面: 定位后自动匹配区域ID失败:', regionError)
    }
  } catch (error) {
    currentLocationName.value = '定位失败，请手动选择'
  } finally {
    loading.value = false
  }
}

// 确认选择
const confirmSelection = () => {
  if (!selectedCity.value) {
    uni.showToast({
      title: '请选择城市',
      icon: 'none',
    })
    return
  }

  const result = {
    province: selectedProvince.value,
    city: selectedCity.value,
    district: selectedDistrict.value,
    fullName: getFullLocationName(),
  }

  // 保存到状态管理
  locationManager.setLocation({
    province: selectedProvince.value?.name || '',
    city: selectedCity.value?.name || '',
    district: selectedDistrict.value?.name || '',
    fullAddress: getFullLocationName(),
    street: '',
    longitude: 0,
    latitude: 0,
  })

  // 保存区域ID到全局状态
  regionManager.setSelectedRegionIds(
    selectedProvince.value,
    selectedCity.value,
    selectedDistrict.value
  )

  uni.showToast({
    title: '已选择' + getFullLocationName(),
    icon: 'success',
  })

  // 返回上一页
  setTimeout(() => {
    uni.navigateBack()
  }, 500)
}

// 获取完整地址名称
const getFullLocationName = () => {
  let name = selectedProvince.value?.name || ''
  if (selectedCity.value) {
    name += selectedCity.value.name
  }
  if (selectedDistrict.value) {
    name += selectedDistrict.value.name
  }
  return name
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.location-select-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #efefef;
  position: relative;
  z-index: 100;
}

.header-left,
.header-right {
  width: 80rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.back-btn:active {
  background-color: #e0e0e0;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-bottom: 150rpx; /* 为底部按钮留出空间 */
}

/* 当前位置 */
.current-location {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  flex-shrink: 0;
}

.location-info {
  display: flex;
  align-items: center;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.location-detail {
  flex: 1;
  text-align: center;
}

.location-name {
  font-size: 26rpx;
  color: #666;
}

.location-loading {
  font-size: 26rpx;
  color: #999;
}

.location-arrow {
  font-size: 24rpx;
  color: #999;
}

.use-location-btn {
  background-color: #c8161e;
  border-radius: 32rpx;
  padding: 8rpx 20rpx;
  margin-left: 20rpx;
}

.use-btn-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 选择器容器 */
.selector-container {
  flex: 1;
  display: flex;
  background-color: #ffffff;
  overflow: hidden;
}

.selector-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1rpx solid #efefef;
}

.selector-section:last-child {
  border-right: none;
}

.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #efefef;
  background-color: #f8f8f8;
}

.selector-list {
  flex: 1;
  height: 0;
}

.selector-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.selector-item.active {
  background-color: #fff3f3;
}

.item-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.selector-item.active .item-text {
  color: #c8161e;
}

.item-check {
  font-size: 28rpx;
  color: #c8161e;
  font-weight: bold;
}

/* 确认按钮 - 固定在底部 */
.confirm-container {
  padding: 30rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #efefef;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #c8161e;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.confirm-btn:active {
  background-color: #a01419;
}

.confirm-btn[disabled] {
  background-color: #cccccc;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background-color: #ffffff;
  padding: 40rpx 60rpx;
  border-radius: 12rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}
</style> 