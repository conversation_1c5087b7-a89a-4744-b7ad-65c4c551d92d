<template>
  <view class="image-upload-simple">
    <!-- 单图模式 -->
    <view v-if="!multiple"
          class="single-upload">
      <view v-if="imageUrl"
            class="image-preview">
        <image :src="imageUrl"
               class="preview-img"
               mode="aspectFill" />
        <view class="image-actions">
          <text class="action-btn"
                @click="removeImage">删除</text>
        </view>
      </view>
      <view v-else
            class="upload-btn"
            @click="chooseImage">
        <text class="upload-text">+ 选择图片</text>
      </view>
    </view>

    <!-- 多图模式 -->
    <view v-else
          class="multiple-upload">
      <view class="image-list">
        <view v-for="(img, index) in imageList"
              :key="index"
              class="image-item">
          <image :src="img"
                 class="grid-img"
                 mode="aspectFill" />
          <view class="delete-btn"
                @click="removeImageAt(index)">×</view>
        </view>
        <view v-if="imageList.length < maxCount"
              class="add-btn"
              @click="chooseImage">
          <text class="add-text">+</text>
        </view>
      </view>
    </view>

    <!-- 上传进度 -->
    <view v-if="uploading"
          class="upload-progress">
      <text>上传中...</text>
    </view>
  </view>
</template>

<script>
import { getBaseURL, getToken } from '@/utils/request'

export default {
  name: 'ImageUploadSimple',
  props: {
    value: {
      type: [String, Array],
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    maxCount: {
      type: Number,
      default: 9
    },
    uploadType: {
      type: String,
      default: 'images'
    }
  },
  data () {
    return {
      uploading: false
    }
  },
  computed: {
    imageUrl () {
      return this.multiple ? '' : this.value
    },
    imageList () {
      return this.multiple ? (Array.isArray(this.value) ? this.value : []) : []
    }
  },
  methods: {
    chooseImage () {
      const count = this.multiple ?
        Math.min(this.maxCount - this.imageList.length, 9) : 1

      uni.chooseImage({
        count,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.handleImageSelect(res.tempFilePaths)
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    async handleImageSelect (tempFilePaths) {
      this.uploading = true

      try {
        if (this.multiple) {
          // 多图上传
          const uploadPromises = tempFilePaths.map(async (filePath) => {
            const result = await this.uploadSingleImage(filePath)
            return result
          })

          const results = await Promise.all(uploadPromises)
          const successUrls = results.filter(r => r.success).map(r => r.url)

          if (successUrls.length > 0) {
            const newList = [...this.imageList, ...successUrls]
            this.emitChange(newList)
            this.$emit('upload-success', {
              urls: successUrls,
              url: successUrls
            })
          }

          if (results.some(r => !r.success)) {
            this.$emit('upload-error', '部分图片上传失败')
          }
        } else {
          // 单图上传
          const result = await this.uploadSingleImage(tempFilePaths[0])
          if (result.success) {
            this.emitChange(result.url)
            this.$emit('upload-success', result.url)
          } else {
            this.$emit('upload-error', result.message || '上传失败')
          }
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        this.$emit('upload-error', '图片上传失败')
      } finally {
        this.uploading = false
      }
    },

    async uploadSingleImage (filePath) {
      return new Promise((resolve) => {
        uni.uploadFile({
          url: `${getBaseURL()}/api/upload/${this.uploadType}/image`,
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${getToken()}`
          },
          success: (uploadRes) => {
            try {
              const data = JSON.parse(uploadRes.data)
              if (data.success) {
                resolve({
                  success: true,
                  url: data.data.url
                })
              } else {
                resolve({
                  success: false,
                  message: data.message || '上传失败'
                })
              }
            } catch (error) {
              resolve({
                success: false,
                message: '响应解析失败'
              })
            }
          },
          fail: (error) => {
            console.error('上传请求失败:', error)
            resolve({
              success: false,
              message: '网络请求失败'
            })
          }
        })
      })
    },

    removeImage () {
      this.emitChange('')
    },

    removeImageAt (index) {
      const newList = [...this.imageList]
      newList.splice(index, 1)
      this.emitChange(newList)
    },

    emitChange (value) {
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>

<style scoped>
.image-upload-simple {
  width: 100%;
}

.single-upload {
  width: 200rpx;
  height: 200rpx;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-img {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 10rpx;
  border-radius: 0 0 8rpx 8rpx;
}

.action-btn {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

.upload-btn {
  width: 100%;
  height: 100%;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-text {
  color: #999;
  font-size: 28rpx;
}

.multiple-upload {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.grid-img {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.add-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.add-text {
  color: #999;
  font-size: 48rpx;
}

.upload-progress {
  margin-top: 20rpx;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}
</style> 