from sqlalchemy import Column, Integer, String, Text, ForeignKey, Boolean, DateTime, func, TIMESTAMP, SmallInteger, UniqueConstraint, Enum, JSON
from sqlalchemy.orm import relationship
from app.database.db import Base
import enum


class UserRole(enum.Enum):
    """用户角色枚举"""
    GUEST = "guest"  # 游客
    DISTRICT_ADMIN = "district_admin"  # 区县管理员
    CITY_ADMIN = "city_admin"  # 市级管理员
    PROVINCE_ADMIN = "province_admin"  # 省级管理员
    SUPER_ADMIN = "super_admin"  # 超级管理员


class ModulePermission(enum.Enum):
    """模块权限枚举"""
    ANCIENT_BOOKS = "ancient_books"  # 古籍管理
    PAINTINGS = "paintings"  # 书画珍品
    ARCHIVES = "archives"  # 档案故事
    VIDEOS = "videos"  # 影像文献


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    openid = Column(String(100), nullable=False, unique=True, comment="微信小程序openid")
    unionid = Column(String(100), nullable=True, comment="微信unionid")
    session_key = Column(String(100), nullable=True, comment="微信session_key")
    nickname = Column(String(100), nullable=True, comment="用户昵称")
    avatar_url = Column(String(255), nullable=True, comment="用户头像URL")
    gender = Column(SmallInteger, default=0, comment="性别：0-未知,1-男,2-女")
    phone = Column(String(20), nullable=True, unique=True, comment="手机号")
    role = Column(Enum(UserRole), default=UserRole.GUEST, nullable=False, comment="用户角色")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    province_id = Column(SmallInteger, nullable=True, comment="管理的省份ID，仅管理员有效")
    city_id = Column(SmallInteger, nullable=True, comment="管理的城市ID，仅管理员有效")
    district_id = Column(SmallInteger, nullable=True, comment="管理的区县ID，仅管理员有效")
    module_permissions = Column(JSON, nullable=True, comment="模块管理权限JSON：{\"ancient_books\":true,\"paintings\":false,\"archives\":true,\"videos\":false}")
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    last_login_at = Column(TIMESTAMP, nullable=True, comment="最后登录时间")
    
    # 关系
    user_sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, openid='{self.openid}', role='{self.role.value}')>"
    
    def has_module_permission(self, module: ModulePermission) -> bool:
        """检查用户是否有指定模块的管理权限"""
        # 超级管理员拥有所有权限
        if self.role == UserRole.SUPER_ADMIN:
            return True
        
        # 游客没有任何管理权限
        if self.role == UserRole.GUEST:
            return False
        
        # 检查模块权限配置
        if self.module_permissions is None:
            return False
        
        return self.module_permissions.get(module.value, False)
    
    def set_module_permission(self, module: ModulePermission, enabled: bool) -> None:
        """设置用户的模块管理权限"""
        if self.module_permissions is None:
            self.module_permissions = {}
        
        # 创建新的字典，避免SQLAlchemy的可变性问题
        new_permissions = dict(self.module_permissions)
        new_permissions[module.value] = enabled
        self.module_permissions = new_permissions
    
    def get_all_module_permissions(self) -> dict:
        """获取用户的所有模块权限"""
        # 超级管理员拥有所有权限
        if self.role == UserRole.SUPER_ADMIN:
            return {module.value: True for module in ModulePermission}
        
        # 游客没有任何权限
        if self.role == UserRole.GUEST:
            return {module.value: False for module in ModulePermission}
        
        # 返回配置的权限，默认为False
        permissions = {}
        for module in ModulePermission:
            permissions[module.value] = self.module_permissions.get(module.value, False) if self.module_permissions else False
        
        return permissions
    
    def has_any_management_permission(self) -> bool:
        """检查用户是否有任何管理权限"""
        if self.role == UserRole.SUPER_ADMIN:
            return True
        
        if self.role == UserRole.GUEST or self.module_permissions is None:
            return False
        
        return any(self.module_permissions.values())


class UserSession(Base):
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    access_token = Column(String(255), nullable=False, unique=True, comment="访问令牌")
    refresh_token = Column(String(255), nullable=False, unique=True, comment="刷新令牌")
    expires_at = Column(TIMESTAMP, nullable=False, comment="令牌过期时间")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    device_info = Column(Text, nullable=True, comment="设备信息JSON")
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    user = relationship("User", back_populates="user_sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id})>"


class LoginLog(Base):
    __tablename__ = "login_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="用户ID，可为空（登录失败时）")
    openid = Column(String(100), nullable=True, comment="尝试登录的openid")
    phone = Column(String(20), nullable=True, comment="尝试登录的手机号")
    login_type = Column(String(20), nullable=False, comment="登录方式：wechat, phone")
    success = Column(Boolean, nullable=False, comment="是否成功")
    failure_reason = Column(String(255), nullable=True, comment="失败原因")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    
    def __repr__(self):
        return f"<LoginLog(id={self.id}, user_id={self.user_id}, success={self.success})>" 