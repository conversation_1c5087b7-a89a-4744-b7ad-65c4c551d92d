<template>
  <view class="archive-detail-container">
    <!-- 加载状态 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>

    <!-- 详细内容 -->
    <view class="detail-content"
          v-else-if="archiveData">
      <!-- 头部图片 -->
      <view class="header-image-container"
            v-if="archiveData.image">
        <image :src="archiveData.image"
               mode="aspectFill"
               class="header-image" />
        <view class="image-overlay">
          <view class="type-indicator">
            <text class="type-icon">{{ getTypeIcon(archiveType) }}</text>
            <text class="type-name">{{ getTypeName(archiveType) }}</text>
          </view>
          <text class="archive-title">{{ archiveData.title }}</text>
          <view class="archive-meta">
            <text class="meta-info"
                  v-if="archiveData.author">{{ archiveData.author }}</text>
            <text class="meta-info"
                  v-if="archiveData.artist">{{ archiveData.artist }}</text>
            <text class="meta-info"
                  v-if="archiveData.period">{{ archiveData.period }}</text>
            <text class="meta-info"
                  v-if="archiveData.dynasty">{{ archiveData.dynasty }}</text>
            <text class="meta-info"
                  v-if="archiveData.date">{{ archiveData.date }}</text>
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title"
              v-if="!archiveData.image">
          <text class="title-text">{{ archiveData.title }}</text>
          <view class="type-badge">
            <text class="badge-icon">{{ getTypeIcon(archiveType) }}</text>
            <text class="badge-text">{{ getTypeName(archiveType) }}</text>
          </view>
        </view>

        <!-- 古籍特有信息 -->
        <view class="book-info"
              v-if="archiveType === 'books'">
          <view class="info-row">
            <text class="info-label">作者：</text>
            <text class="info-value">{{ archiveData.author || '佚名' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">朝代：</text>
            <text class="info-value">{{ archiveData.dynasty || '未知' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">版本：</text>
            <text class="info-value">{{ archiveData.edition || '版本待考' }}</text>
          </view>
          <view class="features-row">
            <text class="feature-tag"
                  v-if="archiveData.has_ocr">OCR识别</text>
            <text class="feature-tag"
                  v-if="archiveData.has_annotation">AI标注</text>
            <text class="feature-tag"
                  v-if="archiveData.has_scan">高清扫描</text>
          </view>
        </view>

        <!-- 书画特有信息 -->
        <view class="calligraphy-info"
              v-if="archiveType === 'calligraphy'">
          <view class="info-row">
            <text class="info-label">艺术家：</text>
            <text class="info-value">{{ archiveData.artist || '佚名' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">年代：</text>
            <text class="info-value">{{ archiveData.period || '年代不详' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">风格：</text>
            <text class="info-value">{{ archiveData.style || '风格待考' }}</text>
          </view>
          <view class="features-row">
            <text class="feature-tag"
                  v-if="archiveData.has_style_analysis">风格分析</text>
            <text class="feature-tag"
                  v-if="archiveData.has_technique">技法解读</text>
            <text class="feature-tag"
                  v-if="archiveData.has_history">历史溯源</text>
          </view>
        </view>

        <!-- 档案特有信息 -->
        <view class="archive-info"
              v-if="archiveType === 'archives'">
          <view class="info-row">
            <text class="info-label">档案类型：</text>
            <text class="info-value">{{ archiveData.type || '档案类型' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">时间：</text>
            <text class="info-value">{{ archiveData.date || '年代不详' }}</text>
          </view>
          <view class="features-row">
            <text class="feature-tag"
                  v-if="archiveData.has_digital">数字档案</text>
            <text class="feature-tag"
                  v-if="archiveData.has_ai_story">AI故事生成</text>
            <text class="feature-tag"
                  v-if="archiveData.has_context">历史背景</text>
          </view>
        </view>

        <!-- 建筑特有信息 -->
        <view class="architecture-info"
              v-if="archiveType === 'architecture'">
          <view class="info-row">
            <text class="info-label">建筑类型：</text>
            <text class="info-value">{{ archiveData.type || '建筑类型' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">建造年代：</text>
            <text class="info-value">{{ archiveData.period || '年代不详' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">地理位置：</text>
            <text class="info-value">{{ archiveData.location || '位置待定' }}</text>
          </view>
          <view class="features-row">
            <text class="feature-tag"
                  v-if="archiveData.has_3d_scan">三维扫描</text>
            <text class="feature-tag"
                  v-if="archiveData.has_aerial">航拍记录</text>
            <text class="feature-tag"
                  v-if="archiveData.has_structure">结构分析</text>
            <text class="feature-tag"
                  v-if="archiveData.has_history">历史考证</text>
          </view>
        </view>

        <!-- 简介 -->
        <view class="brief-content">
          <text class="brief-text">{{ archiveData.brief }}</text>
        </view>
      </view>

      <!-- 详细内容 -->
      <view class="detail-section"
            v-if="archiveData.detail_content">
        <view class="section-header">
          <text class="section-title-text">详细介绍</text>
        </view>
        <view class="detail-text-content">
          <rich-text :nodes="archiveData.detail_content"></rich-text>
        </view>
      </view>

      <!-- AI分析内容 -->
      <view class="ai-analysis-section"
            v-if="archiveData.ai_analysis">
        <view class="section-header">
          <text class="section-title-text">AI智能分析</text>
          <view class="ai-badge">
            <text class="ai-text">AI</text>
          </view>
        </view>
        <view class="ai-content">
          <rich-text :nodes="archiveData.ai_analysis"></rich-text>
        </view>
      </view>

      <!-- 相关图片 -->
      <view class="images-section"
            v-if="archiveData.detail_images && archiveData.detail_images.length > 0">
        <view class="section-header">
          <text class="section-title-text">相关图片</text>
        </view>
        <view class="images-grid">
          <view class="image-item"
                v-for="(image, index) in archiveData.detail_images"
                :key="index"
                @click="previewImage(archiveData.detail_images, index)">
            <image :src="image"
                   mode="aspectFill"
                   class="grid-image" />
            <view class="image-mask">
              <text class="preview-text">预览</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 三维模型展示 (仅建筑类型) -->
      <view class="model-section"
            v-if="archiveType === 'architecture' && archiveData.model_url">
        <view class="section-header">
          <text class="section-title-text">三维模型</text>
        </view>
        <view class="model-container">
          <text class="model-placeholder">三维模型展示区域</text>
          <text class="model-tip">点击查看交互式三维模型</text>
        </view>
      </view>

      <!-- 相关推荐 -->
      <view class="related-section"
            v-if="relatedItems && relatedItems.length > 0">
        <view class="section-header">
          <text class="section-title-text">相关推荐</text>
        </view>
        <scroll-view scroll-x="true"
                     class="related-scroll">
          <view class="related-item"
                v-for="item in relatedItems"
                :key="item.id"
                @click="viewRelatedItem(item)">
            <image :src="item.image"
                   mode="aspectFill"
                   class="related-image" />
            <text class="related-title">{{ item.title }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <text class="update-time">最后更新：{{ formatDate(archiveData.updated_at) }}</text>
        <view class="share-actions">
          <button class="share-btn"
                  @click="shareItem">
            <text class="share-text">分享</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container"
          v-else>
      <text class="error-text">内容加载失败</text>
      <button class="retry-btn"
              @click="loadArchiveDetail">重试</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getImageProxyUrl } from '../../utils/image'

// 页面状态
const isLoading = ref(true)
const loadingText = ref('加载中...')
const archiveData = ref<any>(null)
const relatedItems = ref<any[]>([])
const archiveType = ref('')
const archiveId = ref<number | null>(null)

// 类型映射
const typeMap = {
  books: { name: '古籍典藏', icon: '📜' },
  calligraphy: { name: '书画珍品', icon: '🖌️' },
  archives: { name: '档案故事', icon: '📄' },
  architecture: { name: '建筑遗构', icon: '🏛️' },
}

// 初始化页面
onMounted(async () => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}

  if (options.type && options.id) {
    archiveType.value = options.type
    archiveId.value = parseInt(options.id)
    await loadArchiveDetail()
  } else {
    isLoading.value = false
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
  }

  // 设置导航栏
  uni.setNavigationBarTitle({
    title: archiveData.value?.title || '详情',
  })
})

// 加载详情数据
const loadArchiveDetail = async () => {
  if (!archiveId.value || !archiveType.value) {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    return
  }

  try {
    isLoading.value = true
    loadingText.value = '加载中...'

    // 这里应该调用相应的API获取详情数据
    // 目前使用模拟数据
    await loadMockDetail()
  } catch (error: any) {
    console.error('加载声像文藏详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 加载模拟详情数据
const loadMockDetail = async () => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 800))

  // 根据类型生成模拟数据
  switch (archiveType.value) {
    case 'books':
      archiveData.value = {
        id: archiveId.value,
        title: '四库全书总目提要',
        author: '纪昀等',
        dynasty: '清代',
        edition: '武英殿版',
        brief:
          '中国古代目录学的集大成之作，收录古籍约44000种。该书是清朝乾隆年间编纂的重要文献，记录了中华文明数千年的典籍精华。',
        image: '/static/images/books/siku.jpg',
        has_ocr: true,
        has_annotation: true,
        has_scan: true,
        detail_content:
          '<p>《四库全书总目提要》是清朝乾隆年间编纂的中国古代最大的文献目录，由纪昀等学者主持编纂。</p><p>全书分为经、史、子、集四部，共收录古籍44000余种，是研究中国古代文献的重要工具书。</p>',
        ai_analysis:
          '<p><strong>AI文本识别结果：</strong></p><p>通过OCR技术，我们已经对该古籍进行了全文数字化处理，识别准确率达到99.2%。</p><p><strong>内容摘要：</strong></p><p>该书记录了从先秦到清初的重要典籍，为研究中华文明提供了宝贵资料。</p>',
        detail_images: [
          '/static/images/books/siku-1.jpg',
          '/static/images/books/siku-2.jpg',
          '/static/images/books/siku-3.jpg',
        ],
        updated_at: '2024-01-15T10:30:00Z',
      }
      break

    case 'calligraphy':
      archiveData.value = {
        id: archiveId.value,
        title: '兰亭序',
        artist: '王羲之',
        period: '东晋',
        style: '行书',
        brief:
          '天下第一行书，书法艺术的巅峰之作。此作品笔法精妙，结构严谨，被后世誉为"书圣"之作。',
        image: '/static/images/calligraphy/lanting.jpg',
        has_style_analysis: true,
        has_technique: true,
        has_history: true,
        detail_content:
          '<p>《兰亭序》是东晋书法家王羲之于永和九年（353年）三月三日在兰亭雅集时创作的书法作品。</p><p>全文共324字，记录了当时文人雅士聚会的盛况和作者对人生的感悟。</p>',
        ai_analysis:
          '<p><strong>书法风格分析：</strong></p><p>通过AI算法分析，该作品具有以下特点：</p><ul><li>笔画流畅自然，结构匀称</li><li>墨色浓淡相宜，层次丰富</li><li>章法布局合理，疏密有度</li></ul>',
        detail_images: [
          '/static/images/calligraphy/lanting-1.jpg',
          '/static/images/calligraphy/lanting-2.jpg',
        ],
        updated_at: '2024-01-10T15:20:00Z',
      }
      break

    case 'archives':
      archiveData.value = {
        id: archiveId.value,
        title: '康熙年间土地契约',
        type: '房产契约',
        date: '康熙三十二年',
        brief:
          '记录了江南地区土地买卖的详细过程，反映了当时的经济状况和社会制度。',
        image: '/static/images/archives/contract.jpg',
        has_digital: true,
        has_ai_story: true,
        has_context: true,
        detail_content:
          '<p>这份契约详细记录了康熙三十二年江南地区的一次土地交易。</p><p>从中可以看出当时的土地制度、价格水平以及法律程序。</p>',
        ai_analysis:
          '<p><strong>历史背景分析：</strong></p><p>基于AI语义分析，这份档案反映了：</p><ul><li>康熙年间江南地区经济繁荣</li><li>土地私有制度逐步完善</li><li>契约法律制度相对健全</li></ul>',
        detail_images: [
          '/static/images/archives/contract-1.jpg',
          '/static/images/archives/contract-2.jpg',
        ],
        updated_at: '2024-01-08T09:45:00Z',
      }
      break

    case 'architecture':
      archiveData.value = {
        id: archiveId.value,
        title: '文庙大成殿',
        type: '祭祀建筑',
        period: '明代',
        location: '县城中心',
        brief:
          '保存完好的明代建筑，采用传统木构架结构，是研究古代建筑技艺的重要实例。',
        image: '/static/images/architecture/temple.jpg',
        has_3d_scan: true,
        has_aerial: true,
        has_structure: true,
        has_history: true,
        detail_content:
          '<p>文庙大成殿建于明代，是当地保存最完好的古建筑之一。</p><p>建筑采用传统的抬梁式木构架，体现了明代建筑的典型特征。</p>',
        ai_analysis:
          '<p><strong>建筑结构分析：</strong></p><p>通过三维扫描和AI分析：</p><ul><li>主体结构采用榫卯技术</li><li>屋面为传统歇山顶</li><li>柱础石雕工艺精湛</li></ul>',
        detail_images: [
          '/static/images/architecture/temple-1.jpg',
          '/static/images/architecture/temple-2.jpg',
          '/static/images/architecture/temple-3.jpg',
        ],
        model_url: '/static/models/temple.obj',
        updated_at: '2024-01-12T14:15:00Z',
      }
      break
  }

  // 加载相关推荐
  relatedItems.value = [
    {
      id: 2,
      title: '相关文物',
      image: '/static/images/related-1.jpg',
    },
    {
      id: 3,
      title: '同期作品',
      image: '/static/images/related-2.jpg',
    },
  ]

  // 更新导航栏标题
  uni.setNavigationBarTitle({
    title: archiveData.value.title,
  })
}

// 获取类型图标
const getTypeIcon = (type: string) => {
  return typeMap[type as keyof typeof typeMap]?.icon || '📄'
}

// 获取类型名称
const getTypeName = (type: string) => {
  return typeMap[type as keyof typeof typeMap]?.name || '档案'
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: current,
  })
}

// 查看相关项目
const viewRelatedItem = (item: any) => {
  uni.navigateTo({
    url: `/pages/culture/archive-detail?type=${archiveType.value}&id=${item.id}`,
  })
}

// 分享项目
const shareItem = () => {
  uni.share({
    title: archiveData.value?.title,
    path: `/pages/culture/archive-detail?type=${archiveType.value}&id=${archiveId.value}`,
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}
</script>

<style scoped>
.archive-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8b4513;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 头部图片 */
.header-image-container {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}

.header-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.type-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.type-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.type-name {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

.archive-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 1.3;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.archive-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.meta-info {
  background: rgba(255, 255, 255, 0.15);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

/* 信息区域 */
.info-section {
  background: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.title-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.type-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.badge-icon {
  font-size: 32rpx;
}

.badge-text {
  background: #8b4513;
  color: #ffffff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.features-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 20rpx;
}

.feature-tag {
  background: #f0f8ff;
  color: #0066cc;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid #e0f0ff;
}

.brief-content {
  margin-top: 40rpx;
}

.brief-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

/* 详细内容区域 */
.detail-section,
.ai-analysis-section {
  background: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #8b4513;
}

.section-title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #8b4513;
}

.ai-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.ai-text {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.detail-text-content,
.ai-content {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
}

/* 图片展示 */
.images-section {
  background: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.image-item {
  position: relative;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.grid-image {
  width: 100%;
  height: 100%;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:active .image-mask {
  opacity: 1;
}

.preview-text {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
}

/* 三维模型 */
.model-section {
  background: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.model-container {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ccc;
}

.model-placeholder {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.model-tip {
  font-size: 24rpx;
  color: #999;
}

/* 相关推荐 */
.related-section {
  background: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.related-scroll {
  white-space: nowrap;
}

.related-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  text-align: center;
}

.related-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.related-title {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 底部信息 */
.footer-info {
  background: #ffffff;
  padding: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}

.share-actions {
  display: flex;
  gap: 20rpx;
}

.share-btn {
  background: #8b4513;
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 25rpx;
  border: none;
  font-size: 26rpx;
}

.share-text {
  color: #ffffff;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: #8b4513;
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  border: none;
  font-size: 28rpx;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .images-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .archive-title {
    font-size: 40rpx;
  }

  .title-text {
    font-size: 40rpx;
  }
}
</style> 