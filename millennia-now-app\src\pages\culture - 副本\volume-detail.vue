<template>
  <view class="volume-detail">
    <!-- 卷册基本信息 -->
    <view class="volume-info">
      <view class="header">
        <text class="volume-title">{{ volumeData.volume_title || `第${volumeData.volume_number}册` }}</text>
        <text class="book-title">{{ bookInfo.title }}</text>
      </view>

      <view class="info-grid">
        <view class="info-item">
          <text class="label">卷册序号</text>
          <text class="value">{{ volumeData.volume_number }}</text>
        </view>
        <view class="info-item">
          <text class="label">总页数</text>
          <text class="value">{{ volumeData.total_pages }}</text>
        </view>
        <view class="info-item">
          <text class="label">状态</text>
          <text class="value status"
                :class="volumeData.status">{{ getStatusLabel(volumeData.status) }}</text>
        </view>
        <view class="info-item">
          <text class="label">页码范围</text>
          <text class="value">{{ getPageRange() }}</text>
        </view>
      </view>

      <view class="description"
            v-if="volumeData.content_description">
        <text class="label">内容描述</text>
        <text class="content">{{ volumeData.content_description }}</text>
      </view>
    </view>

    <!-- 阅读模式切换 -->
    <view class="view-mode">
      <view class="mode-tabs">
        <view class="tab-item"
              :class="{ active: viewMode === 'gallery' }"
              @click="setViewMode('gallery')">
          <text>图片浏览</text>
        </view>
        <view class="tab-item"
              :class="{ active: viewMode === 'reader' }"
              @click="setViewMode('reader')">
          <text>阅读模式</text>
        </view>
        <view class="tab-item"
              :class="{ active: viewMode === 'text' }"
              @click="setViewMode('text')">
          <text>文字版</text>
        </view>
      </view>
    </view>

    <!-- 图片浏览模式 -->
    <view class="gallery-mode"
          v-if="viewMode === 'gallery'">
      <view class="page-grid">
        <view class="page-item"
              v-for="(page, index) in pages"
              :key="page.id"
              @click="previewPage(index)">
          <image :src="page.image_url"
                 mode="aspectFit"
                 class="page-image"
                 :lazy-load="true" />
          <view class="page-info">
            <text class="page-number">{{ page.page_number }}</text>
            <text class="page-label">{{ page.page_label || '无标签' }}</text>
            <text class="page-type">{{ getPageTypeLabel(page.page_type) }}</text>
            <view class="ocr-status"
                  :class="page.ocr_status">
              <text>{{ getOCRStatusLabel(page.ocr_status) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 阅读模式 -->
    <view class="reader-mode"
          v-if="viewMode === 'reader'">
      <view class="reader-controls">
        <button @click="prevPage"
                :disabled="currentPageIndex === 0"
                class="nav-btn">
          上一页
        </button>
        <text class="page-indicator">
          {{ currentPageIndex + 1 }} / {{ pages.length }}
        </text>
        <button @click="nextPage"
                :disabled="currentPageIndex === pages.length - 1"
                class="nav-btn">
          下一页
        </button>
      </view>

      <view class="reader-content"
            v-if="currentPage">
        <image :src="currentPage.image_url"
               mode="widthFix"
               class="reader-image"
               @click="toggleFullscreen" />

        <view class="page-details">
          <view class="page-meta">
            <text class="page-title">第{{ currentPage.page_number }}页</text>
            <text class="page-label">{{ currentPage.page_label || '无标签' }}</text>
            <text class="page-type">{{ getPageTypeLabel(currentPage.page_type) }}</text>
          </view>

          <view class="ocr-section"
                v-if="currentPage.ocr_text || currentPage.corrected_text">
            <text class="section-title">识别文字</text>
            <view class="text-content">
              <text class="ocr-text"
                    v-if="currentPage.corrected_text">
                {{ currentPage.corrected_text }}
              </text>
              <text class="ocr-text raw"
                    v-else-if="currentPage.ocr_text">
                {{ currentPage.ocr_text }}
              </text>
              <text class="no-text"
                    v-else>暂无识别文字</text>
            </view>

            <view class="ocr-info"
                  v-if="currentPage.ocr_text">
              <text class="confidence">
                识别置信度: {{ (currentPage.ocr_confidence * 100).toFixed(1) }}%
              </text>
              <text class="corrected"
                    v-if="currentPage.is_corrected">
                ✓ 已人工校对
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 文字版模式 -->
    <view class="text-mode"
          v-if="viewMode === 'text'">
      <view class="text-controls">
        <button @click="toggleFontSize"
                class="control-btn">
          {{ fontSize === 'small' ? '小' : fontSize === 'medium' ? '中' : '大' }}
        </button>
        <button @click="toggleTheme"
                class="control-btn">
          {{ theme === 'light' ? '🌙' : '☀️' }}
        </button>
      </view>

      <view class="text-content-list"
            :class="{ 'dark-theme': theme === 'dark' }">
        <view class="text-page"
              v-for="page in pagesWithText"
              :key="page.id"
              :class="fontSize">
          <view class="text-page-header">
            <text class="page-title">第{{ page.page_number }}页</text>
            <text class="page-label">{{ page.page_label }}</text>
          </view>

          <view class="text-page-content">
            <text class="page-text"
                  v-if="page.corrected_text">
              {{ page.corrected_text }}
            </text>
            <text class="page-text raw"
                  v-else-if="page.ocr_text">
              {{ page.ocr_text }}
            </text>
            <text class="no-text"
                  v-else>此页暂无文字内容</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动操作按钮 -->
    <view class="fab-container"
          v-if="isOwner">
      <button @click="editVolume"
              class="fab">
        <text class="fab-icon">✏️</text>
      </button>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request'

export default {
  name: 'VolumeDetail',
  data () {
    return {
      volumeId: null,
      volumeData: {},
      bookInfo: {},
      pages: [],
      currentPageIndex: 0,
      viewMode: 'gallery', // gallery, reader, text
      fontSize: 'medium', // small, medium, large
      theme: 'light', // light, dark
      isOwner: false,
      loading: false
    }
  },

  computed: {
    currentPage () {
      return this.pages[this.currentPageIndex]
    },

    pagesWithText () {
      return this.pages.filter(page => page.ocr_text || page.corrected_text)
    }
  },

  onLoad (options) {
    this.volumeId = parseInt(options.volumeId)
    this.loadVolumeData()
  },

  methods: {
    async loadVolumeData () {
      this.loading = true
      try {
        const response = await request({
          url: `/api/v1/ancient-book-volumes/volumes/${this.volumeId}`,
          method: 'GET'
        })

        this.volumeData = response.data
        this.pages = response.data.pages || []
        this.bookInfo = response.data.book || {}

        // 按页码排序
        this.pages.sort((a, b) => a.page_number - b.page_number)

        // 检查是否为所有者
        this.checkOwnership()

      } catch (error) {
        console.error('获取卷册数据失败:', error)
        uni.showToast({
          title: '获取卷册数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    async checkOwnership () {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo && userInfo.id) {
          this.isOwner = userInfo.id === this.volumeData.created_by
        }
      } catch (error) {
        console.error('检查所有权失败:', error)
      }
    },

    // 视图模式切换
    setViewMode (mode) {
      this.viewMode = mode
      if (mode === 'reader' && this.pages.length > 0) {
        this.currentPageIndex = 0
      }
    },

    // 阅读模式导航
    prevPage () {
      if (this.currentPageIndex > 0) {
        this.currentPageIndex--
      }
    },

    nextPage () {
      if (this.currentPageIndex < this.pages.length - 1) {
        this.currentPageIndex++
      }
    },

    // 图片预览
    previewPage (index) {
      const urls = this.pages.map(page => page.image_url)
      uni.previewImage({
        urls: urls,
        current: urls[index],
        longPressActions: {
          itemList: ['保存图片'],
          success: (data) => {
            if (data.tapIndex === 0) {
              this.saveImage(urls[index])
            }
          }
        }
      })
    },

    // 保存图片
    saveImage (imageUrl) {
      uni.downloadFile({
        url: imageUrl,
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        }
      })
    },

    // 全屏切换
    toggleFullscreen () {
      // 这里可以实现全屏显示逻辑
      uni.previewImage({
        urls: [this.currentPage.image_url],
        current: this.currentPage.image_url
      })
    },

    // 文字模式控制
    toggleFontSize () {
      const sizes = ['small', 'medium', 'large']
      const currentIndex = sizes.indexOf(this.fontSize)
      this.fontSize = sizes[(currentIndex + 1) % sizes.length]
    },

    toggleTheme () {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
    },

    // 编辑卷册
    editVolume () {
      uni.navigateTo({
        url: `/pages/culture/volume-edit?volumeId=${this.volumeId}&bookId=${this.bookInfo.id}`
      })
    },

    // 工具方法
    getStatusLabel (status) {
      const statusMap = {
        'draft': '草稿',
        'published': '已发布',
        'archived': '已归档'
      }
      return statusMap[status] || status
    },

    getPageTypeLabel (type) {
      const typeMap = {
        'cover': '封面',
        'contents': '目录',
        'preface': '序言',
        'content': '正文',
        'appendix': '附录',
        'colophon': '版权页'
      }
      return typeMap[type] || type
    },

    getOCRStatusLabel (status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '处理失败'
      }
      return statusMap[status] || status
    },

    getPageRange () {
      if (!this.volumeData.start_page && !this.volumeData.end_page) {
        return '未设置'
      }
      return `${this.volumeData.start_page || ''} - ${this.volumeData.end_page || ''}`
    }
  }
}
</script>

<style scoped>
.volume-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.volume-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.header {
  margin-bottom: 30rpx;
}

.volume-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.book-title {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.label {
  font-size: 24rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status {
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  color: white;
  max-width: 120rpx;
}

.status.draft {
  background-color: #ffeaa7;
  color: #d63031;
}

.status.published {
  background-color: #00b894;
}

.status.archived {
  background-color: #636e72;
}

.description {
  border-top: 2rpx solid #eee;
  padding-top: 20rpx;
}

.description .label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
  display: block;
}

.description .content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.view-mode {
  background: white;
  margin-bottom: 20rpx;
}

.mode-tabs {
  display: flex;
  border-bottom: 2rpx solid #eee;
}

.tab-item {
  flex: 1;
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: #007aff;
  border-bottom-color: #007aff;
}

/* 图片浏览模式 */
.gallery-mode {
  padding: 20rpx;
}

.page-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.page-item {
  background: white;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.page-image {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
}

.page-info {
  padding: 20rpx;
}

.page-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-label,
.page-type {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.ocr-status {
  display: inline-block;
  padding: 5rpx 10rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  margin-top: 10rpx;
}

.ocr-status.pending {
  background-color: #ffeaa7;
  color: #d63031;
}

.ocr-status.processing {
  background-color: #74b9ff;
  color: white;
}

.ocr-status.completed {
  background-color: #00b894;
  color: white;
}

.ocr-status.failed {
  background-color: #e17055;
  color: white;
}

/* 阅读模式 */
.reader-mode {
  background: white;
  margin-bottom: 20rpx;
}

.reader-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #eee;
}

.nav-btn {
  padding: 15rpx 30rpx;
  border: 2rpx solid #007aff;
  border-radius: 25rpx;
  background: white;
  color: #007aff;
  font-size: 26rpx;
}

.nav-btn:disabled {
  opacity: 0.3;
  border-color: #ccc;
  color: #ccc;
}

.page-indicator {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.reader-content {
  padding: 30rpx;
}

.reader-image {
  width: 100%;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.page-details {
  border-top: 2rpx solid #eee;
  padding-top: 30rpx;
}

.page-meta {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.ocr-section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.text-content {
  background: #f8f9fa;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.ocr-text {
  font-size: 26rpx;
  line-height: 1.8;
  color: #333;
}

.ocr-text.raw {
  color: #666;
}

.no-text {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  padding: 40rpx;
}

.ocr-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.corrected {
  color: #00b894;
  font-weight: 500;
}

/* 文字版模式 */
.text-mode {
  background: white;
  margin-bottom: 20rpx;
}

.text-controls {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #eee;
}

.control-btn {
  padding: 15rpx 25rpx;
  border: 2rpx solid #ddd;
  border-radius: 20rpx;
  background: white;
  color: #333;
  font-size: 24rpx;
}

.text-content-list {
  padding: 30rpx;
}

.text-content-list.dark-theme {
  background: #1a1a1a;
  color: #e0e0e0;
}

.text-page {
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #eee;
}

.text-page:last-child {
  border-bottom: none;
}

.text-page.small .page-text {
  font-size: 24rpx;
}

.text-page.medium .page-text {
  font-size: 28rpx;
}

.text-page.large .page-text {
  font-size: 32rpx;
}

.text-page-header {
  margin-bottom: 20rpx;
}

.text-page-content {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.page-text {
  line-height: 1.8;
  color: #333;
}

.page-text.raw {
  color: #666;
}

.dark-theme .text-page-content {
  background: #2a2a2a;
}

.dark-theme .page-text {
  color: #e0e0e0;
}

.dark-theme .page-text.raw {
  color: #999;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 100rpx;
  right: 30rpx;
  z-index: 1000;
}

.fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: #007aff;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
}

.fab-icon {
  font-size: 36rpx;
}
</style> 