# 环境变量配置示例文件
# 请将此文件复制为 .env 并修改相应的配置值

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost/millennia_now

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# JWT配置 - 生产环境请使用更复杂的密钥
JWT_SECRET_KEY=your_super_secret_key_here_change_in_production

# Minio对象存储配置
# 注意：MINIO_ENDPOINT只需要主机名和端口，不要包含http://前缀
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=12345678
MINIO_SECURE=false
MINIO_BUCKET=millennia-now

# Minio服务启动命令（参考）:
# docker run -d --name minio -p 9000:9000 -p 9001:9001 \
#   -e MINIO_ROOT_USER=admin -e MINIO_ROOT_PASSWORD=12345678 \
#   -v minio_data:/data minio/minio server /data --console-address ':9001'

# 服务端口
PORT=8000

# 环境
ENV=development 