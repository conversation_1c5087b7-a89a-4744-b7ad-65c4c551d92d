<template>
  <view class="container"
        v-if="pageData || isLoading">
    <!-- 页面顶部信息 -->
    <view class="header-section"
          v-if="pageData">
      <image class="header-bg"
             :src="pageData.headerBgImage"
             mode="aspectFill"></image>
      <view class="header-content">
        <text class="place-name">{{pageData.placeName}}</text>
        <text class="place-desc">{{pageData.placeDesc}}</text>
      </view>
    </view>

    <!-- 简介 -->
    <view class="intro-section"
          v-if="pageData">
      <text class="intro-text">{{pageData?.introduction || ''}}</text>
    </view>

    <!-- 时间轴 -->
    <view class="timeline-section"
          v-if="pageData || isLoading">
      <view class="timeline-title">
        <text>历史文脉溯源</text>
      </view>

      <view class="timeline">
        <!-- 无数据提示 -->
        <view class="empty-tip"
              v-if="!pageData || !pageData.timelineData || pageData.timelineData.length === 0">
          <text class="empty-text">暂无历史文脉数据</text>
        </view>

        <view class="timeline-item"
              v-for="(item, index) in pageData?.timelineData || []"
              :key="index"
              v-show="visibleTimelineItems.includes(index)">
          <!-- 时间标记 -->
          <view class="time-marker">
            <view class="time-dot"></view>
            <view class="time-line"
                  v-if="index !== (pageData?.timelineData?.length || 0) - 1"></view>
          </view>

          <!-- 内容卡片 -->
          <view class="time-card"
                :class="{ 'clickable': item.hasDetail }"
                @click="item.hasDetail ? expandDetail(index) : null">
            <view class="time-period">
              <text class="period-name">{{ item.period }}</text>
              <text class="period-year">{{ item.year }}</text>
            </view>
            <view class="card-content">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="card-image"
                     @error="(e) => onImageError(e, item)"></image>
              <view class="card-text">
                <text class="card-title">{{ item.title }}</text>
                <text class="card-desc">{{ item.description }}</text>
              </view>
            </view>

            <!-- 文化遗产标签 -->
            <view class="heritage-tags"
                  v-if="item.heritages && item.heritages.length > 0">
              <view class="tag"
                    v-for="(tag, tagIndex) in item.heritages"
                    :key="tagIndex">
                {{ tag }}
              </view>
            </view>

            <!-- 展开更多 -->
            <view class="expand-section"
                  v-if="item.hasDetail">
              <text class="expand-text">{{ item.isExpanded ? '收起' : '展开更多' }}</text>
              <text class="expand-icon"
                    :class="{ 'expanded': item.isExpanded }">▼</text>
            </view>

            <!-- 详细内容 -->
            <view class="detail-content"
                  v-if="item.isExpanded && item.detail">
              <rich-text :nodes="item.detail"></rich-text>

              <!-- 相关图片展示 -->
              <view class="detail-images"
                    v-if="item.detailImages && item.detailImages.length > 0">
                <image v-for="(img, imgIndex) in item.detailImages"
                       :key="imgIndex"
                       :src="getImageSrc(img)"
                       mode="aspectFill"
                       class="detail-image"
                       @error="(e) => onImageError(e, {image: img})"
                       @click="previewDetailImages(item.detailImages, imgIndex)"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer"
          v-if="pageData">
      <text class="footer-text">{{pageData?.footerText || ''}}</text>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>
  
  <script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { regionManager } from '../../store/modules/region'
import {
  getHeritagePageData,
  getHeritagePageDataByRegion,
  type HeritagePageData,
} from '../../api/heritage'
import { getImageProxyUrl } from '../../utils/image'

// 定义页面数据类型接口
interface TimelineItem {
  id?: number
  period: string
  year: string
  title: string
  description: string
  image: string
  heritages?: string[]
  hasDetail: boolean
  isExpanded: boolean
  detail?: string
  detailImages?: string[]
  sortOrder?: number
}

interface PageData {
  placeId?: number
  placeName: string
  placeDesc: string
  headerBgImage: string
  introduction: string
  footerText: string
  timelineData: TimelineItem[]
}

// 页面数据
const pageData = ref<PageData | null>(null)
const isLoading = ref(true)

// 添加时间轴动画控制变量
const timelineAnimationStarted = ref(false)
const visibleTimelineItems = ref<number[]>([])

// 展开详情
const expandDetail = (index: number) => {
  if (pageData.value && pageData.value.timelineData) {
    // 确保该索引已经被添加到可见项目中
    if (!visibleTimelineItems.value.includes(index)) {
      visibleTimelineItems.value.push(index)
    }

    // 切换展开状态
    pageData.value.timelineData[index].isExpanded =
      !pageData.value.timelineData[index].isExpanded

    // 强制更新视图
    nextTick(() => {
      console.log(
        '展开状态已更新:',
        index,
        pageData.value?.timelineData[index].isExpanded
      )
    })
  }
}

// 启动时间轴动画，从古至今依次显示时间轴项目
const startTimelineAnimation = () => {
  if (timelineAnimationStarted.value || !pageData.value?.timelineData?.length)
    return

  timelineAnimationStarted.value = true
  visibleTimelineItems.value = []

  // 按照时间顺序显示每个时间轴项目
  pageData.value.timelineData.forEach((_, index) => {
    setTimeout(() => {
      visibleTimelineItems.value.push(index)

      // 如果是最后一个项目，标记动画完成
      if (index === pageData.value!.timelineData.length - 1) {
        console.log('时间轴动画完成，所有项目可见')
      }
    }, 800 * (index + 1)) // 每800毫秒显示一个
  })

  // 确保即使动画未完成，5秒后也显示所有项目（防止交互问题）
  setTimeout(() => {
    if (pageData.value && pageData.value.timelineData) {
      const allIndices = pageData.value.timelineData.map((_, i) => i)
      // 添加尚未显示的项目
      allIndices.forEach((i) => {
        if (!visibleTimelineItems.value.includes(i)) {
          visibleTimelineItems.value.push(i)
        }
      })
      console.log('安全检查：确保所有时间轴项目可见')
    }
  }, 8000) // 8秒后确保所有项目可见
}

// 图片预览
const previewImage = (images: string[], current: number) => {
  if (!images || images.length === 0) {
    console.error('预览图片列表为空')
    return
  }

  // 确保所有图片都是有效的URL
  const validImages = images.filter((img) => !!img)

  if (validImages.length === 0) {
    uni.showToast({
      title: '没有可预览的图片',
      icon: 'none',
    })
    return
  }

  // 使用uni.previewImage API预览图片
  uni.previewImage({
    urls: validImages,
    current: validImages[current] || validImages[0],
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败:', err)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none',
      })
    },
  })
}

// 详情图片预览专用函数
const previewDetailImages = (images: string[], current: number) => {
  if (!images || images.length === 0) return

  // 将所有图片URL通过getImageProxyUrl处理
  const processedUrls = images.map((img) => getImageProxyUrl(img))
  previewImage(processedUrls, current)
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  if (!imageSrc) {
    return '/static/images/no-image.svg'
  }
  // 使用图片代理URL处理函数
  return getImageProxyUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event: any, item: any) => {
  console.log('图片加载失败:', event)
  // 将图片源替换为错误占位图
  const target = event.target || event.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}

// 获取页面数据
const fetchPageData = async () => {
  isLoading.value = true
  try {
    let data: HeritagePageData | null = null

    // 从URL参数获取placeId
    const pages = getCurrentPages() as any[]
    const currentPage = pages[pages.length - 1] as any
    const urlPlaceId = currentPage?.options?.placeId

    if (urlPlaceId) {
      // 如果URL中有placeId，直接使用该ID获取数据
      console.log('使用URL中的placeId获取数据:', urlPlaceId)
      data = await getHeritagePageData(urlPlaceId)
    } else {
      // 普通模式：优先使用当前选择的区域ID获取数据
      const regionIds = regionManager.currentRegionIds

      if (regionIds.provinceId) {
        console.log('根据当前选择的区域ID获取历史文脉数据:', regionIds)
        data = await getHeritagePageDataByRegion({
          province_id: regionIds.provinceId || undefined,
          city_id: regionIds.cityId || undefined,
          district_id: regionIds.districtId || undefined,
        })
      } else {
        // 如果没有区域ID，尝试使用缓存的heritage place
        const currentHeritagePlace = regionManager.currentHeritagePlace

        if (currentHeritagePlace) {
          console.log(
            '备用方案：使用缓存的heritage place获取数据:',
            currentHeritagePlace.id
          )
          data = await getHeritagePageData(currentHeritagePlace.id)
        }
      }
    }

    if (!data) {
      // 如果没有获取到数据，显示提示并返回首页
      uni.showToast({
        title: '当前地区未构建该功能',
        icon: 'none',
        duration: 2000,
      })

      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }, 2000)
      return
    }

    // 转换API数据为页面数据格式
    pageData.value = {
      placeId: data.place_info.id,
      placeName: data.place_info.place_name,
      placeDesc: data.place_info.place_desc || '',
      headerBgImage:
        data.place_info.header_bg_image || '/static/images/no-image.svg',
      introduction: data.place_info.introduction || '',
      footerText: data.place_info.footer_text || '',
      timelineData: (data.timeline_data || [])
        .map((item: any) => ({
          id: item.id,
          period: item.period,
          year: item.year,
          title: item.title,
          description: item.description,
          image: item.image || '/static/images/no-image.svg',
          heritages: item.heritage_tags || [],
          hasDetail: item.has_detail,
          isExpanded: false,
          detail: item.detail,
          detailImages: item.detail_images || [],
          sortOrder: item.sort_order || 0,
        }))
        .sort((a, b) => a.sortOrder - b.sortOrder), // 按 sort_order 从小到大排序
    }

    console.log('历史文脉页面数据加载成功:', pageData.value)
    isLoading.value = false

    // 重置时间轴动画状态并延迟启动动画
    timelineAnimationStarted.value = false
    // 延迟启动时间轴动画，等待页面渲染完成
    setTimeout(() => {
      startTimelineAnimation()
    }, 500)
  } catch (error: any) {
    console.error('数据加载失败:', error)

    uni.showToast({
      title: '当前地区未构建该功能',
      icon: 'none',
      duration: 2000,
    })

    // 延迟返回首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 2000)

    // 其他错误，直接返回
    isLoading.value = false
  }
}

onMounted(() => {
  fetchPageData()
})
</script>
  
  <style>
.container {
  background-color: #f8f8f8;
  position: relative;
  min-height: 100vh;
}

/* 顶部样式 */
.header-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
  animation: fadeIn 1.5s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.header-bg {
  width: 100%;
  height: 100%;
  transform: scale(1.1);
  animation: zoomOut 2s ease-out forwards;
}

@keyframes zoomOut {
  0% {
    transform: scale(1.1);
    filter: brightness(0.8) saturate(1.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
  }
}

.header-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 1.5s ease-out forwards;
  animation-delay: 0.5s;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.place-name {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 10rpx;
}

.place-desc {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 简介样式 */
.intro-section {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 1.5s ease-out forwards;
  animation-delay: 0.8s;
  position: relative;
  overflow: hidden;
}

.intro-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  animation: inkFade 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.intro-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  position: relative;
  z-index: 1;
}

/* 时间轴样式 */
.timeline-section {
  padding: 30rpx 20rpx;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 1s;
}

.timeline-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.timeline-title:after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  width: 0;
  height: 6rpx;
  background-color: #c8161e;
  border-radius: 3rpx;
  transform: translateX(-50%);
  animation: lineExpand 1.5s ease-out forwards;
  animation-delay: 1.5s;
}

@keyframes lineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 80rpx;
  }
}

/* 墨滴效果 */
.timeline-title::before {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(200, 22, 30, 0.7) 0%,
    rgba(200, 22, 30, 0) 70%
  );
  border-radius: 50%;
  transform: translateX(-50%);
  opacity: 0;
  z-index: 0;
  animation: inkDrop 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkDrop {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    width: 100rpx;
    height: 100rpx;
    opacity: 0;
  }
}

.timeline {
  padding: 20rpx 0 20rpx 20rpx;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
  opacity: 0;
  transform: translateX(-50px);
  animation: slideIn 0.8s forwards;
  animation-fill-mode: both;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.time-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  width: 30rpx;
}

.time-dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #c8161e;
  margin-top: 30rpx;
  z-index: 2;
  position: relative;
  box-shadow: 0 0 0 rgba(200, 22, 30, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(200, 22, 30, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(200, 22, 30, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(200, 22, 30, 0);
  }
}

.time-line {
  width: 4rpx;
  background-color: #e0e0e0;
  flex: 1;
  margin-top: 10rpx;
  position: relative;
  overflow: hidden;
}

.time-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background-color: #c8161e;
  animation: lineGrow 2s ease-out forwards;
}

@keyframes lineGrow {
  0% {
    height: 0%;
  }
  100% {
    height: 100%;
  }
}

.time-card {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 可点击卡片的样式 */
.time-card.clickable {
  cursor: pointer;
}

.time-card.clickable:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

.time-card.clickable:active {
  transform: translateY(0);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 水墨扩散效果 */
.time-card::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: 0;
  animation: inkSpread 1.5s ease-out forwards;
}

@keyframes inkSpread {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    width: 300%;
    height: 300%;
    opacity: 0;
  }
}

.time-period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.period-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.period-year {
  font-size: 28rpx;
  color: #999;
}

.card-content {
  display: flex;
  margin-bottom: 20rpx;
}

.card-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.card-text {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.card-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.heritage-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.tag {
  background-color: #f0f5ff;
  color: #3370ff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.expand-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
  padding: 10rpx 0;
  border-top: 1px dashed #efefef;
  cursor: pointer;
}

.expand-section:active {
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.05);
}

.expand-text {
  font-size: 26rpx;
  color: #3370ff;
  margin-right: 10rpx;
}

.expand-icon {
  font-size: 20rpx;
  color: #3370ff;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.detail-content {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px dashed #efefef;
  max-height: 2000rpx; /* 设置最大高度用于动画 */
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.detail-content.collapsed {
  max-height: 0;
  padding-top: 0;
  border-top: none;
  transition: max-height 0.3s ease-in-out, padding-top 0.3s ease-in-out,
    border-top 0.3s ease-in-out;
}

.detail-content rich-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

.detail-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 0;
  margin-bottom: 0;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.detail-image:active {
  transform: scale(0.95);
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
}

.detail-image::after {
  content: '🔍';
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  opacity: 0.8;
  pointer-events: none;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  gap: 15rpx;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 底部样式 */
.footer {
  text-align: center;
  padding: 60rpx 0;
}

.footer-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载中样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #c8161e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>