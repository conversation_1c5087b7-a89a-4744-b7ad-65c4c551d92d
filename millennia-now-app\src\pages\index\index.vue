<template>
  <view class="container"
        :style="{ backgroundImage: `url(${backgroundURL})` }">
    <!-- 位置搜索栏 -->
    <view class="location-search-bar">
      <view class="location-section"
            @tap="chooseCity">
        <text class="location-icon">📍</text>
        <text class="location-name">{{ currentLocationText }}</text>
        <text class="location-arrow">▼</text>
      </view>

      <!-- 调试按钮：查看区域ID -->
      <view class="debug-button"
            @tap="showRegionIds"
            v-if="regionManager.hasCompleteRegion">
        <text class="debug-text">ID</text>
      </view>
      <view class="search-section"
            @tap="goToSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索景点、活动、美食...</text>
      </view>
      <!-- 删除日历按钮 -->
    </view>

    <!-- 欢迎弹窗动画 -->
    <view v-if="showWelcome"
          class="welcome-popup"
          @tap="closeWelcome">
      <view class="welcome-content">
        <!-- 按照正确顺序排列 -->
        <view class="welcome-title"
              :class="{ 'step1': welcomeStep >= 1 }">欢迎来到</view>
        <!-- 修改欢迎弹窗中的区域名称显示方式 -->
        <view class="welcome-district"
              :class="{ 'step2': welcomeStep >= 2 }">
          <span class="district-text">{{ welcomeDistrict }}</span>
          <view class="particle-container">
            <view v-for="n in 5"
                  :key="n"
                  class="particle"
                  :style="{ '--particle-delay': `${0.2 + n * 0.1}s` }"></view>
          </view>
        </view>
        <view class="welcome-desc"
              :class="{ 'step3': welcomeStep >= 3 }">让我们一起探索当地的文化吧</view>
        <view class="welcome-seal"
              :class="{ 'show': welcomeStep >= 4 }"></view>
      </view>
    </view>

    <!-- 水墨动画区域 -->
    <view class="ink-animation-area">
      <!-- 滚动指示器 - 仅在小屏幕显示 -->
      <view class="scroll-indicator"
            v-if="showScrollIndicator">
        <view class="scroll-arrow"></view>
        <text class="scroll-text">向下滑动查看更多</text>
      </view>

      <view class="ink-sections-container">
        <!-- 历史文脉区域 - 卷轴型 -->
        <view class="ink-section history-section"
              :class="{ 'animate-in': welcomeAnimationCompleted }"
              @tap="navigateToSection('history')">
          <view class="scroll-container">
            <view class="scroll-handle top-handle"></view>

            <view class="scroll-paper"
                  :style="{ backgroundImage: `url('${wenmaiURL}')` }">
              <!-- 边缘阴影效果 -->
              <view class="edge-shadow-left"></view>
              <view class="edge-shadow-right"></view>

              <view class="section-content">
                <text class="ink-title">历史文脉</text>
                <view class="ink-decoration">
                  <view class="decoration-line"></view>
                  <text class="decoration-symbol">探索地区发展历史</text>
                  <view class="decoration-line"></view>
                </view>
              </view>

              <!-- 水墨渲染动画 -->
              <view class="ink-animation">
                <view class="ink-drop"
                      v-for="n in 5"
                      :key="n"></view>
              </view>
            </view>

            <view class="scroll-handle bottom-handle"></view>
          </view>
        </view>

        <!-- 文化传承区域 - 中间位置 -->
        <view class="ink-section culture-section"
              :class="{ 'animate-in': welcomeAnimationCompleted }"
              @tap="navigateToSection('culture')">
          <view class="culture-container">
            <view class="culture-paper"
                  :style="{ backgroundImage: `url('${chuanchengURL}')` }">
              <!-- 边缘阴影效果 -->
              <view class="edge-shadow-top"></view>
              <view class="edge-shadow-bottom"></view>

              <view class="section-content">
                <text class="ink-title">文化传承</text>
                <view class="ink-decoration">
                  <view class="decoration-line"></view>
                  <text class="decoration-symbol">传统文化数字保护</text>
                  <view class="decoration-line"></view>
                </view>
              </view>

              <!-- 水墨渲染动画 -->
              <view class="ink-animation culture-ink">
                <view class="ink-splash"
                      v-for="n in 5"
                      :key="n"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 当代记忆区域 - 相册样式 -->
        <view class="ink-section memory-section"
              :class="{ 'animate-in': welcomeAnimationCompleted }"
              @tap="navigateToSection('memory')">
          <view class="memory-container">
            <view class="memory-paper">
              <view class="section-content"
                    :style="{ backgroundImage: `url('${getBaseURL().replace('/api', '')}/static/image/memory.jpg')` }">
                <text class="ink-title">当代记忆</text>
                <view class="ink-decoration">
                  <view class="decoration-line"></view>
                  <text class="decoration-symbol">城市影像数字档案</text>
                  <view class="decoration-line"></view>
                </view>
              </view>

              <!-- 水墨渲染动画 -->
              <view class="ink-animation">
                <view class="ink-memory"
                      v-for="n in 5"
                      :key="n"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 重新调整菜单结构和层级 -->
      <view class="menu-container">
        <!-- 先渲染背景遮罩 -->
        <view v-if="isMenuOpen"
              class="menu-backdrop"
              @click="toggleMenu"></view>

        <!-- 悬浮菜单按钮和菜单面板 - 移除拖动事件 -->
        <view class="floating-menu">
          <view class="menu-button"
                @click.stop="toggleMenu">
            <text class="menu-icon">☰</text>
          </view>

          <!-- 菜单面板 -->
          <view class="menu-panel"
                :class="{'show': isMenuOpen}"
                v-if="isMenuOpen">
            <view class="menu-item"
                  @click="goToCalendarDirect">
              <text class="menu-item-icon">🌍</text>
              <text class="menu-item-text">云游文化</text>
            </view>
            <view class="menu-item"
                  @click="goToAiChatDirect">
              <text class="menu-item-icon">🤖</text>
              <text class="menu-item-text">千载·今知AI问答</text>
            </view>
            <view class="menu-item"
                  @click="goToChongqingMetro">
              <text class="menu-item-icon">🚇</text>
              <text class="menu-item-text">乘着轨道游重庆</text>
            </view>
          </view>
        </view>
      </view>
    </view> <!-- 关闭 ink-sections-container -->
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { locationManager } from '../../store/modules/location'
import { regionManager } from '../../store/modules/region'
import { getBaseURL } from '../../config/api'

// 悬浮菜单状态
const isMenuOpen = ref(false)

// 切换菜单显示状态
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value

  // 菜单关闭后，重置任何可能的状态
  if (!isMenuOpen.value) {
    // 确保关闭菜单后，整个页面可以正常交互
    setTimeout(() => {
      // 延迟一下以确保状态更新后再执行其他操作
    }, 50)
  }
}

// 计算当前位置显示文本
const currentLocationText = computed(() => {
  const location = locationManager.currentLocation

  // 优先显示区县
  if (location.district) {
    return location.district
  }

  // 其次显示城市
  if (location.city && location.city !== '请选择城市') {
    return location.city
  }

  // 最后显示省份或默认文本
  return location.province || '选择城市'
})

// 跳转到城市选择页面
const chooseCity = () => {
  uni.navigateTo({
    url: '/pages/location/select',
  })
}

// 显示当前区域ID信息（调试用）
const showRegionIds = () => {
  const regionIds = regionManager.currentRegionIds
  const message = `当前区域ID信息：
省份：${regionIds.provinceName}(${regionIds.provinceId || '未设置'})
城市：${regionIds.cityName}(${regionIds.cityId || '未设置'})
区县：${regionIds.districtName}(${regionIds.districtId || '未设置'})

完整名称：${regionManager.fullRegionName}
是否完整：${regionManager.hasCompleteRegion ? '是' : '否'}
有区县信息：${regionManager.hasDistrict ? '是' : '否'}`

  uni.showModal({
    title: '区域ID信息',
    content: message,
    showCancel: false,
    confirmText: '知道了',
  })
}

// 跳转到搜索页面
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 直接跳转到日历页面（不使用间接调用）
const goToCalendarDirect = () => {
  console.log('点击了云游文化按钮')
  // 关闭菜单
  isMenuOpen.value = false

  // 获取当前区域信息
  const regionIds = regionManager.currentRegionIds
  const regionName = regionManager.fullRegionName || '默认地区'

  // 手动构建查询字符串，避免使用URLSearchParams（小程序不支持）
  const queryParams = [
    `from=miniapp`,
    `regionName=${encodeURIComponent(regionName)}`,
    `provinceId=${encodeURIComponent(regionIds.provinceId?.toString() || '')}`,
    `cityId=${encodeURIComponent(regionIds.cityId?.toString() || '')}`,
    `districtId=${encodeURIComponent(regionIds.districtId?.toString() || '')}`,
  ].join('&')

  // 立即执行跳转
  // 使用统一配置的前端地址
  const baseUrl = getBaseURL().replace(':8443/api', ':5173/')
  const externalUrl = `${baseUrl}?${queryParams}`

  console.log('跳转URL:', externalUrl)

  // #ifdef APP-PLUS
  console.log('App环境，使用plus打开外部浏览器')
  plus.runtime.openURL(externalUrl)
  return
  // #endif

  // #ifdef H5
  console.log('H5环境，使用window.open')
  window.open(externalUrl, '_blank')
  return
  // #endif

  // 其他环境（小程序等），使用webview页面
  console.log('小程序环境，使用webview跳转')
  uni.showToast({
    title: '正在跳转...',
    icon: 'none',
  })

  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/webview/index?url=' + encodeURIComponent(externalUrl),
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none',
        })
      },
    })
  }, 300)
}

// 直接跳转到AI问答页面
const goToAiChatDirect = () => {
  console.log('点击了千载·今知AI问答按钮')
  // 关闭菜单
  isMenuOpen.value = false

  // 显示提示
  uni.showToast({
    title: '功能开发中...',
    icon: 'none',
    duration: 2000,
  })
}

// 跳转到重庆轨道2号线页面（应用内页面）
const goToChongqingMetro = () => {
  console.log('点击了乘着轨道游重庆按钮')
  // 关闭菜单
  isMenuOpen.value = false

  // 显示加载提示
  uni.showToast({
    title: '正在加载轨道旅游图...',
    icon: 'loading',
    duration: 1000,
  })

  // 延迟跳转，让用户看到加载提示
  setTimeout(() => {
    console.log('跳转到重庆轨道2号线页面（应用内）')

    // 直接跳转到应用内的轨道交通页面
    uni.navigateTo({
      url: '/pages/rail/cq-line2',
      success: () => {
        console.log('成功跳转到重庆轨道2号线页面')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败，请重试',
          icon: 'none',
          duration: 2000,
        })
      },
    })
  }, 800)
}

// 区域导航功能
const navigateToSection = (type: 'history' | 'culture' | 'memory') => {
  let url = '/pages/culture/history'

  switch (type) {
    case 'history':
      url = '/pages/culture/history'
      break
    case 'culture':
      // 修改这里，从heritage改为heritage-content页面
      url = '/pages/culture/heritage-content'
      break
    case 'memory':
      // 修改这里，从memory-detail改为memory-content页面
      url = '/pages/culture/memory-content'
      break
  }

  uni.navigateTo({ url })
}

// 欢迎弹窗逻辑
const showWelcome = ref(false)
const welcomeDistrict = ref('')
const welcomeStep = ref(0)
let welcomeTimer: number | null = null
let stepTimer: number | null = null

// 添加新的响应式变量来控制其他动画
const welcomeAnimationCompleted = ref(false)

// 滚动指示器状态 - 在小屏幕上显示
const showScrollIndicator = ref(false)

// 在script部分修改，使用完整区域名称
function triggerWelcome(district: string) {
  if (!district) return

  // 清除所有之前的定时器，确保不会有冲突
  if (stepTimer) clearTimeout(stepTimer)
  if (welcomeTimer) clearTimeout(welcomeTimer)

  // 获取完整区域名称
  const fullName = getFullRegionName(district)

  // 使用完整区域名称
  console.log('显示区域名称:', fullName) // 调试用
  welcomeDistrict.value = fullName || district

  showWelcome.value = true
  welcomeStep.value = 0
  welcomeAnimationCompleted.value = false

  // 分步动画，调整时间
  stepTimer = setTimeout(() => {
    welcomeStep.value = 1
  }, 600) // 欢迎语

  stepTimer = setTimeout(() => {
    welcomeStep.value = 2
  }, 1200) // 区县名 - 提前显示

  stepTimer = setTimeout(() => {
    welcomeStep.value = 3
  }, 1800) // 描述

  stepTimer = setTimeout(() => {
    welcomeStep.value = 4
  }, 3000) // 印章 - 在描述显示后延迟显示

  // 延长欢迎时间
  welcomeTimer = setTimeout(() => {
    showWelcome.value = false
    welcomeStep.value = 0

    setTimeout(() => {
      welcomeAnimationCompleted.value = true
    }, 300)
  }, 6000) // 6秒后自动关闭
}

function closeWelcome() {
  // 清除所有定时器
  if (welcomeTimer) clearTimeout(welcomeTimer)
  if (stepTimer) clearTimeout(stepTimer)

  // 关闭动画
  showWelcome.value = false
  welcomeStep.value = 0
  welcomeAnimationCompleted.value = true // 手动关闭时也标记为已完成
}

// 动态背景URL - 使用统一配置
const baseURL = getBaseURL().replace('/api', '')
const backgroundURL = `${baseURL}/static/image/bak.png`
const wenmaiURL = `${baseURL}/static/image/wenmai.png`
const chuanchengURL = `${baseURL}/static/image/chuancheng.png`
// 监听定位变化 - 监听完整地址变化
watch(
  () => locationManager.currentLocation.fullAddress,
  (newAddress, oldAddress) => {
    if (newAddress && newAddress !== oldAddress) {
      // 获取区县名称用于显示
      const district = locationManager.currentLocation.district
      triggerWelcome(district)
    }
  }
)

// 获取完整区域名称的辅助函数
function getFullRegionName(district: string): string {
  let fullName = regionManager.fullRegionName || ''
  if (!fullName) {
    const location = locationManager.currentLocation
    fullName = [
      location.province || '',
      location.city || '',
      location.district || district,
    ]
      .filter(Boolean)
      .join('')
  }
  return fullName
}

// 同时监听区县变化，处理动画中途城市变化的情况
watch(
  () => locationManager.currentLocation.district,
  (newDistrict, oldDistrict) => {
    if (newDistrict && newDistrict !== oldDistrict) {
      if (showWelcome.value) {
        // 如果欢迎动画正在显示，重新触发动画以显示新城市
        console.log('动画进行中检测到城市变化，重新触发动画:', newDistrict)
        triggerWelcome(newDistrict)
      }
    }
  }
)

// 检测屏幕尺寸并控制滚动指示器
const checkScreenSize = () => {
  const systemInfo = uni.getSystemInfoSync()
  const screenHeight = systemInfo.screenHeight
  const screenWidth = systemInfo.screenWidth

  // 在小屏幕或低分辨率设备上显示滚动指示器
  showScrollIndicator.value = screenWidth <= 480 || screenHeight <= 600
}

// 首次进入页面时，如果没有显示欢迎动画，直接标记为已完成
onMounted(() => {
  // 检测屏幕尺寸
  checkScreenSize()

  const district = locationManager.currentLocation.district
  if (district) {
    triggerWelcome(district)
  } else {
    // 延迟一小段时间再显示其他动画，让页面有时间渲染
    setTimeout(() => {
      welcomeAnimationCompleted.value = true
    }, 500)
  }

  // 在动画完成后延迟隐藏滚动指示器
  setTimeout(() => {
    if (showScrollIndicator.value) {
      setTimeout(() => {
        showScrollIndicator.value = false
      }, 3000) // 3秒后隐藏
    }
  }, 2000)
})

// 事件处理代码修改
// 处理菜单项点击
const handleMenuItemClick = (type: 'calendar' | 'ai') => {
  // 先关闭菜单
  isMenuOpen.value = false

  // 延迟执行跳转，确保菜单关闭动画完成
  setTimeout(() => {
    if (type === 'calendar') {
      goToCalendarDirect() // 更新为新函数名
    } else if (type === 'ai') {
      goToAiChatDirect() // 更新为新函数名
    }
  }, 100)
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: transparent;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  animation: backgroundFade 2s ease-in forwards,
    backgroundPan 40s linear infinite alternate;
  background-size: 120% 120%;
}

@keyframes backgroundPan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes backgroundFade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(255, 255, 255, 0.6); */
  z-index: 0;
}

.location-search-bar,
.ink-animation-area,
.welcome-popup {
  position: relative;
  z-index: 1;
}

/* 位置搜索栏 */
.location-search-bar {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.8);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #333333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(5px);
}

.location-section {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.location-icon {
  font-size: 18px;
  margin-right: 4px;
}

.location-name {
  font-size: 14px;
  font-weight: bold;
  max-width: 80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-arrow {
  font-size: 12px;
  margin-left: 2px;
}

.debug-button {
  background-color: #333333;
  border-radius: 12px;
  padding: 4px 8px;
  margin-left: 8px;
}

.debug-text {
  font-size: 10px;
  color: #ffffff;
  font-weight: bold;
}

.search-section {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 16px;
  padding: 6px 12px;
}

.search-icon {
  font-size: 14px;
  margin-right: 6px;
  color: #666;
}

.search-placeholder {
  font-size: 13px;
  color: #666;
}

.calendar-btn {
  margin-left: 10px;
}

.calendar-icon {
  font-size: 18px;
}

/* 悬浮菜单按钮 - 固定位置 */
.floating-menu {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000; /* 高于背景遮罩 */
  pointer-events: auto; /* 允许接收点击事件 */
}

.menu-button {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  /* 改用渐变背景，增加中国传统元素 */
  background: linear-gradient(135deg, #ba0001 0%, #8c0001 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(139, 0, 0, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  /* 内部边框，增加精致感 */
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.menu-icon {
  font-size: 28px;
  color: #fff1d0; /* 暖金色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.menu-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
}

.menu-panel {
  position: absolute;
  bottom: 75px; /* 调整位置，在按钮上方 */
  right: 0;
  /* 更改面板样式为中国传统风格 */
  background: linear-gradient(135deg, #fbf8ee 0%, #f5f1e4 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 180px;
  /* 添加装饰边框 */
  border: 1px solid #e8d4a9;
  z-index: 1001; /* 确保在按钮和背景遮罩之上 */
  pointer-events: auto; /* 确保可点击 */
}

.menu-panel.show {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.menu-item {
  padding: 15px;
  border-bottom: 1px solid rgba(160, 120, 95, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-icon {
  font-size: 18px;
  margin-right: 10px;
}

.menu-item-text {
  font-size: 16px;
  color: #7a5230; /* 传统木色 */
  font-weight: 500;
  font-family: 'KaiTi', 'STKaiti', serif; /* 使用楷体增强传统风格 */
}

.menu-item:hover,
.menu-item:active {
  background-color: rgba(232, 212, 169, 0.3);
}

/* 背景遮罩 - 点击关闭菜单 */
.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1); /* 轻微可见的背景 */
  z-index: 998; /* 确保在菜单按钮下方，但在其他内容上方 */
  pointer-events: auto; /* 确保可接收点击事件 */
}

/* 水墨动画区域 */
.ink-animation-area {
  position: relative;
  height: calc(100vh - 60px);
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
}

/* 三等分容器 - 统一布局 */
.ink-sections-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 每个区域自适应宽度 - 统一基础样式 */
.ink-section {
  flex: 1 1 0;
  min-width: 0;
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 10px;
  box-sizing: border-box;
  cursor: pointer;
  transition: transform 0.8s cubic-bezier(0.2, 0.8, 0.2, 1), opacity 0.8s,
    box-shadow 0.4s;
  will-change: transform, opacity;
  opacity: 0;
}

/* 各区域初始状态和动画 */
.history-section {
  transform: translateX(-150px) rotate(-10deg);
}
.culture-section {
  transform: translateY(150px) rotate(5deg);
}
.memory-section {
  transform: translateY(150px);
}

/* 各区域悬停效果 */
.history-section:hover {
  transform: translateY(-10px) rotate(-1deg) scale(1.05);
}
.culture-section:hover {
  transform: translateY(-10px) rotate(2deg) scale(1.05);
}
.memory-section:hover {
  transform: translateY(-15px) scale(1.05);
}

/* 各区域显示动画 */
.history-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
}
.culture-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
  transition-delay: 0.2s;
}
.memory-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
  transition-delay: 0.4s;
}

/* 内容容器统一样式 */
.scroll-container,
.culture-container,
.memory-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.5s ease;
  padding: 0;
}

.history-section.animate-in .scroll-container,
.culture-section.animate-in .culture-container,
.memory-section.animate-in .memory-container {
  opacity: 1;
}

/* 响应式布局 - 增加更多断点和细节控制 */
/* 超小屏幕（低分辨率设备）*/
@media screen and (max-width: 480px), screen and (max-height: 600px) {
  .ink-animation-area {
    height: calc(100vh - 80px); /* 减小高度为搜索栏留出空间 */
  }

  .ink-sections-container {
    flex-direction: column;
    height: auto;
    min-height: calc(100vh - 120px); /* 减去搜索栏和安全区域 */
    gap: 8px; /* 减小间距 */
    padding: 8px;
    overflow-y: visible; /* 让父容器处理滚动 */
  }

  .ink-section {
    width: 100%;
    height: 22vh; /* 进一步减小高度 */
    min-height: 140px; /* 减小最小高度 */
    max-height: 180px; /* 限制最大高度 */
    padding: 6px;
    flex-shrink: 0; /* 防止被压缩 */
  }

  .ink-title {
    font-size: 18px; /* 进一步减小字体 */
  }

  .decoration-symbol {
    font-size: 11px; /* 进一步减小字体 */
  }

  /* 确保当代记忆区域可见 */
  .memory-section {
    margin-bottom: 20px; /* 底部留出额外空间 */
  }

  /* 滚动指示器在小屏幕上的特殊样式 */
  .scroll-indicator {
    bottom: 10px;
    font-size: 10px;
  }

  .scroll-arrow {
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 10px solid #333;
  }
}

/* 小屏幕 */
@media screen and (min-width: 481px) and (max-width: 768px) {
  .ink-sections-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
    gap: 15px;
    padding: 15px;
    overflow-y: auto; /* 允许滚动 */
  }

  .ink-section {
    width: 100%;
    height: 28vh; /* 稍微减小 */
    min-height: 180px;
    max-height: 250px; /* 限制最大高度 */
    padding: 10px;
    flex-shrink: 0; /* 防止被压缩 */
  }

  .ink-title {
    font-size: 24px;
  }

  .decoration-symbol {
    font-size: 14px;
  }
}

/* 中等屏幕尺寸 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .ink-sections-container {
    gap: 20px;
    padding: 0 20px;
  }

  .ink-section {
    min-height: 250px;
  }
}

/* 大屏幕尺寸 */
@media screen and (min-width: 1025px) {
  .ink-sections-container {
    gap: 30px;
    padding: 0 30px;
    max-width: 1600px;
    margin: 0 auto;
  }

  .ink-section {
    min-height: 300px;
  }
}

/* 历史文脉区域展开动画 */
.history-section.animate-in .scroll-paper {
  animation: unfold 3.5s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

@keyframes unfold {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

/* 文化传承区域特殊样式 */
.culture-paper {
  /* background-image 现在通过动态样式绑定设置 */
  background-size: cover;
  background-position: center;
  transform-origin: center center;
  clip-path: ellipse(0% 60% at 50% 50%);
  transform: scaleX(0);
}

/* 扇子展开动画 */
.culture-section.animate-in .culture-paper {
  animation: fanUnfold 2.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  animation-delay: 0.5s;
}

@keyframes fanUnfold {
  0% {
    clip-path: ellipse(0% 60% at 50% 50%);
    transform: scaleX(0);
  }
  60% {
    clip-path: ellipse(100% 60% at 50% 50%);
    transform: scaleX(1.05);
  }
  100% {
    clip-path: ellipse(100% 60% at 50% 50%);
    transform: scaleX(1);
  }
}

/* 当代记忆区域特殊样式 - 更逼真的相框效果 */
.memory-paper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0 auto;
  z-index: 1;

  /* 动画初始状态 */
  transform: scale(0);
  opacity: 0;
}

/* 内容区域样式 */
.memory-paper .section-content {
  /* background-image 现在通过动态样式绑定设置 */
  background-size: cover !important;
  background-position: center !important;
  background-blend-mode: overlay !important; /* 使用混合模式 */
}

/* 相册展开动画 */
.memory-section.animate-in .memory-paper {
  animation: albumReveal 2.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  animation-delay: 0.8s;
}

@keyframes albumReveal {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-5deg);
  }
  40% {
    opacity: 0.8;
    transform: scale(0.8) rotate(2deg);
  }
  70% {
    opacity: 1;
    transform: scale(1.05) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 内容淡入动画 */
.history-section.animate-in .scroll-paper .section-content {
  animation: fadeIn 2.5s forwards;
  animation-delay: 3s;
  opacity: 0;
}

.culture-section.animate-in .culture-paper .section-content {
  animation: fadeIn 2.5s forwards;
  animation-delay: 3s;
  opacity: 0;
}

.memory-section.animate-in .memory-paper .section-content {
  animation: fadeIn 2.5s forwards;
  animation-delay: 3s;
  opacity: 0;
}

/* 水墨动画效果 - 保留各区域特色 */
.ink-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 历史文脉水墨效果 */
.ink-drop {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transform: scale(0);
  opacity: 0;
}

@keyframes inkExpand {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    transform: scale(1);
    opacity: 0.8;
  }
  60% {
    transform: scale(10);
    opacity: 0.1;
  }
  100% {
    transform: scale(20);
    opacity: 0;
  }
}

.history-section.animate-in .ink-drop:nth-child(1) {
  top: 20%;
  left: 30%;
  animation: inkExpand 8s ease-out 1s infinite;
}

.history-section.animate-in .ink-drop:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: inkExpand 8s ease-out 4s infinite;
}

.history-section.animate-in .ink-drop:nth-child(3) {
  top: 40%;
  left: 50%;
  animation: inkExpand 8s ease-out 7s infinite;
}

.history-section.animate-in .ink-drop:nth-child(4) {
  top: 70%;
  left: 20%;
  animation: inkExpand 8s ease-out 10s infinite;
}

.history-section.animate-in .ink-drop:nth-child(5) {
  top: 30%;
  left: 80%;
  animation: inkExpand 8s ease-out 13s infinite;
}

/* 文化传承水墨效果 */
.ink-splash {
  position: absolute;
  width: 60px;
  height: 60px;
  background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M50,10 C60,30 80,40 90,50 C80,60 60,70 50,90 C40,70 20,60 10,50 C20,40 40,30 50,10 Z" fill="black" fill-opacity="0.3"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transform: scale(0.5) rotate(0deg);
}

@keyframes inkSplash {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  20% {
    opacity: 0.7;
    transform: scale(1) rotate(45deg);
  }
  60% {
    opacity: 0.5;
    transform: scale(1.2) rotate(90deg);
  }
  100% {
    opacity: 0;
    transform: scale(1.5) rotate(180deg);
  }
}

.culture-section.animate-in .ink-splash:nth-child(1) {
  top: 20%;
  left: 30%;
  animation: inkSplash 8s ease-out 2s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: inkSplash 8s ease-out 5s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(3) {
  top: 40%;
  left: 50%;
  animation: inkSplash 8s ease-out 8s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(4) {
  top: 70%;
  left: 20%;
  animation: inkSplash 8s ease-out 11s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(5) {
  top: 30%;
  left: 80%;
  animation: inkSplash 8s ease-out 14s infinite;
}

/* 当代记忆水墨效果 - 增强版 */
.ink-memory {
  position: absolute;
  width: 150px;
  height: 150px;
  background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M30,20 Q50,5 70,20 T90,50 Q70,80 50,90 Q30,80 10,50 Q30,20 30,20 Z" fill="black" fill-opacity="0.7"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transform: scale(0.5) rotate(0deg);
}

@keyframes memoryFade {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  30% {
    opacity: 0.6;
    transform: scale(1) rotate(45deg);
  }
  70% {
    opacity: 0.3;
    transform: scale(1.3) rotate(90deg);
  }
  100% {
    opacity: 0;
    transform: scale(1.8) rotate(180deg);
  }
}

.memory-section.animate-in .ink-memory:nth-child(1) {
  top: 20%;
  left: 30%;
  animation: memoryFade 8s ease-out 2s infinite;
}

.memory-section.animate-in .ink-memory:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: memoryFade 8s ease-out 5s infinite;
}

.memory-section.animate-in .ink-memory:nth-child(3) {
  top: 40%;
  left: 50%;
  animation: memoryFade 8s ease-out 8s infinite;
}

.memory-section.animate-in .ink-memory:nth-child(4) {
  top: 70%;
  left: 20%;
  animation: memoryFade 8s ease-out 11s infinite;
}

.memory-section.animate-in .ink-memory:nth-child(5) {
  top: 30%;
  left: 80%;
  animation: memoryFade 8s ease-out 14s infinite;
}

/* 标题和装饰统一样式 */
.ink-title {
  font-size: 32px;
  font-weight: bold;
  color: #000;
  font-family: 'KaiTi', 'STKaiti', serif;
  letter-spacing: 6px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  margin-bottom: 15px;
}

.ink-decoration {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.decoration-line {
  flex: 1;
  height: 1px;
}

.decoration-symbol {
  margin: 0 15px;
  color: #000;
  font-size: calc(14px + 0.5vw);
  font-family: 'KaiTi', 'STKaiti', serif;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* 欢迎弹窗样式 - 水墨风格 */
.welcome-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.8s;
  overflow: hidden;
  backdrop-filter: blur(2px);
}

.welcome-bg-svg {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 400px;
  height: 200px;
  transform: translate(-50%, -50%);
  z-index: 1;
  pointer-events: none;
  animation: cloudDrift 6s ease-in-out infinite;
}

@keyframes cloudDrift {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.85;
  }
  50% {
    transform: translate(-52%, -48%) scale(1.05);
    opacity: 0.95;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.85;
  }
}

.welcome-top-svg,
.welcome-bottom-svg {
  position: absolute;
  left: 50%;
  width: 200px;
  height: 30px;
  transform: translateX(-50%);
  z-index: 3;
  pointer-events: none;
  animation: subtleSway 4s ease-in-out infinite;
}

.welcome-top-svg {
  top: 18%;
}
.welcome-bottom-svg {
  bottom: 18%;
}

@keyframes subtleSway {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-50%) rotate(2deg);
  }
  100% {
    transform: translateX(-50%) rotate(0deg);
  }
}

.welcome-content {
  background: linear-gradient(135deg, #ffffff 80%, #f0f0f0 100%);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 38px 38px 30px 38px;
  text-align: center;
  min-width: 240px;
  min-height: 160px;
  position: relative;
  z-index: 5;
  animation: popupScale 0.7s cubic-bezier(0.23, 1, 0.32, 1),
    glowPulse 6s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #333;
}

@keyframes glowPulse {
  0% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }
}

.welcome-title,
.welcome-district,
.welcome-desc {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.7s, transform 0.7s;
}

.welcome-title.step1 {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.1s;
}

.welcome-district.step2 {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.5s;
}

.welcome-desc.step3 {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.9s;
}

/* 调整区域名称样式，一次性显示 */
.welcome-district {
  width: 100%;
  margin: 10px 0;
  padding: 0 10px;
}

.district-text {
  display: block;
  font-size: 28px;
  color: #000;
  font-weight: bold;
  font-family: 'KaiTi', 'STKaiti', serif;
  letter-spacing: 2px;
  text-shadow: 0 2px 8px #fff, 0 1px 0 #333, 0 0 8px #ccc;
  opacity: 0;
  transform: scale(0.8);
  animation: districtAppear 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  animation-delay: 0.3s;
}

@keyframes districtAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 对于较长的地名，自动换行 */
@media screen and (max-width: 768px) {
  .district-text {
    font-size: 24px;
    letter-spacing: 1px;
    word-break: break-all;
    white-space: normal;
  }
}

.particle-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #333;
  border-radius: 50%;
  opacity: 0;
  animation: particleScatter 1.5s ease-out var(--particle-delay) forwards;
}

@keyframes particleScatter {
  0% {
    opacity: 0.8;
    transform: translate(0, 0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(
        calc(20px * (var(--n) - 3)),
        calc(15px * (var(--n) - 3))
      )
      scale(0.2);
  }
}

.welcome-seal {
  width: 80px;
  height: 30px;
  margin: 18px auto 0 auto;
  background: url('data:image/svg+xml;utf8,<svg width="60" height="20" viewBox="0 0 48 18" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="2" y="2" width="44" height="14" rx="7" fill="%23ffffff" stroke="%23333333" stroke-width="2"/><text x="24" y="13" text-anchor="middle" fill="%23333333" font-size="8" font-family="KaiTi,STKaiti,serif">千载·今知</text></svg>')
    no-repeat center/contain;
  opacity: 0; /* 初始状态为不可见 */
  transform: translateY(10px) scale(0.8); /* 初始位置略微下移并缩小 */
}

/* 使用 Vue 的 class 绑定来控制显示时机 */
.welcome-seal.show {
  animation: sealStamp 0.6s ease-in forwards;
}

@keyframes sealStamp {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  100% {
    opacity: 0.85;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes popupScale {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }
  80% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.history-section.animate-in .scroll-container {
  opacity: 1;
}

/* 去掉卷轴本体的特殊样式 */
.scroll-paper {
  /* background-image 现在通过动态样式绑定设置 */
  border-radius: 8px !important;
  transform-origin: center;
  background-size: cover;
  background-position: center;
}

/* 去掉卷轴本体的装饰效果 */
.scroll-paper::after,
.scroll-paper::before {
  display: none;
}

/* 去掉文化传承区域的边缘阴影 */
.culture-paper .edge-shadow-top,
.culture-paper .edge-shadow-bottom {
  display: none;
}

/* 关键帧动画 - 从中间向两侧展开，使用vw单位 */
@keyframes unfold {
  0% {
    width: 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.25);
  }
  100% {
    width: 100%; /* 使用视窗宽度单位 */
    max-width: 1200px; /* 设置最大宽度 */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }
}

/* 水墨动画效果 */
.ink-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 7;
}

.ink-drop {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  transform: scale(0);
  opacity: 0;
}

@keyframes inkExpand {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    transform: scale(1);
    opacity: 0.8;
  }
  60% {
    transform: scale(10);
    opacity: 0.1;
  }
  100% {
    transform: scale(20);
    opacity: 0;
  }
}

/* 修改为无限循环动画 */
.history-section.animate-in .ink-drop:nth-child(1) {
  top: 20%;
  left: 30%;
  animation: inkExpand 8s ease-out 1s infinite;
}

.history-section.animate-in .ink-drop:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: inkExpand 8s ease-out 4s infinite;
}

.history-section.animate-in .ink-drop:nth-child(3) {
  top: 40%;
  left: 50%;
  animation: inkExpand 8s ease-out 7s infinite;
}

.history-section.animate-in .ink-drop:nth-child(4) {
  top: 70%;
  left: 20%;
  animation: inkExpand 8s ease-out 10s infinite;
}

.history-section.animate-in .ink-drop:nth-child(5) {
  top: 30%;
  left: 80%;
  animation: inkExpand 8s ease-out 13s infinite;
}

/* 美化标题和装饰 - 使用响应式字体大小 */
.ink-title {
  font-size: 32px; /* 增大字体 */
  font-weight: bold;
  color: #000;
  font-family: 'KaiTi', 'STKaiti', serif;
  letter-spacing: 6px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8); /* 加强文字阴影 */
  margin-bottom: 15px;
}

.decoration-symbol {
  margin: 0 15px;
  color: #000;
  font-size: calc(14px + 0.5vw); /* 响应式字体大小 */
  font-family: 'KaiTi', 'STKaiti', serif;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* 在小屏幕上调整卷轴高度 */
@media screen and (max-width: 768px) {
  .scroll-paper {
    height: 100%; /* 小屏幕上减小高度 */
  }

  .scroll-paper .section-content {
    font-size: 6vw; /* 小屏幕上增大字体 */
  }
}

/* 在大屏幕上限制最大尺寸 */
@media screen and (min-width: 1200px) {
  .scroll-paper .section-content {
    font-size: 32px; /* 大屏幕上固定字体大小 */
  }
}

/* 文化传承区域样式 - 中间位置 */
.culture-section {
  opacity: 0;
  transform: translateY(150px) rotate(5deg);
}

.culture-section:hover {
  transform: translateY(-10px) rotate(2deg) scale(1.05);
}

.culture-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
  transition-delay: 0.2s;
}

/* 文化传承区域的纸张样式 */
.culture-paper {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 150px;
  /* background-image 现在通过动态样式绑定设置 */
  background-size: cover;
  background-position: center;
  overflow: hidden;
  margin: 0 auto;
  transform-origin: center center;
  z-index: 1;
  /* 扇形clip-path，初始状态 */
  clip-path: ellipse(0% 60% at 50% 50%);
  transform: scaleX(0);
  transition: none;
}

/* 扇子展开动画 */
.culture-section.animate-in .culture-paper {
  animation: fanUnfold 2.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  animation-delay: 0.5s;
}

@keyframes fanUnfold {
  0% {
    clip-path: ellipse(0% 60% at 50% 50%);
    transform: scaleX(0);
  }
  60% {
    clip-path: ellipse(100% 60% at 50% 50%);
    transform: scaleX(1.05);
  }
  100% {
    clip-path: ellipse(100% 60% at 50% 50%);
    transform: scaleX(1);
  }
}

/* 文化传承区域内容淡入动画 */
.culture-section.animate-in .culture-paper .section-content {
  animation: fadeIn 1.2s forwards;
  animation-delay: 2.3s;
  opacity: 0;
}

/* 文化传承区域水墨效果 */
.culture-ink .ink-splash {
  position: absolute;
  width: 60px;
  height: 60px;
  background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M50,10 C60,30 80,40 90,50 C80,60 60,70 50,90 C40,70 20,60 10,50 C20,40 40,30 50,10 Z" fill="black" fill-opacity="0.3"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transform: scale(0.5) rotate(0deg);
}

@keyframes inkSplash {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  20% {
    opacity: 0.7;
    transform: scale(1) rotate(45deg);
  }
  60% {
    opacity: 0.5;
    transform: scale(1.2) rotate(90deg);
  }
  100% {
    opacity: 0;
    transform: scale(1.5) rotate(180deg);
  }
}

.culture-section.animate-in .ink-splash:nth-child(1) {
  top: 20%;
  left: 30%;
  animation: inkSplash 8s ease-out 2s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: inkSplash 8s ease-out 5s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(3) {
  top: 40%;
  left: 50%;
  animation: inkSplash 8s ease-out 8s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(4) {
  top: 70%;
  left: 20%;
  animation: inkSplash 8s ease-out 11s infinite;
}

.culture-section.animate-in .ink-splash:nth-child(5) {
  top: 30%;
  left: 80%;
  animation: inkSplash 8s ease-out 14s infinite;
}

/* 当代记忆区域样式 - 左下角 */
.memory-section {
  opacity: 0;
  transform: translateY(150px);
}

.memory-section:hover {
  transform: translateY(-15px) scale(1.05);
}

.memory-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
  transition-delay: 0.4s;
}

.ink-wash-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.ink-wash {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
}

@keyframes inkWash {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  30% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.3;
  }
  70% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.ink-wash:nth-child(1) {
  animation: inkWash 6s infinite 0s;
}

.ink-wash:nth-child(2) {
  top: 70%;
  left: 60%;
  animation: inkWash 15s infinite 5s;
}

.ink-wash:nth-child(3) {
  top: 50%;
  left: 50%;
  animation: inkWash 15s infinite 10s;
}

/* 当代记忆区域样式 - 相册样式 */
.memory-section {
  opacity: 0;
  transform: translateY(150px);
}

.memory-section:hover {
  transform: translateY(-15px) scale(1.05);
}

.memory-section.animate-in {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
  transition-delay: 0.4s;
}

.ink-wash-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.ink-wash {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
}

@keyframes inkWash {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  30% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.3;
  }
  70% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.ink-wash:nth-child(1) {
  animation: inkWash 6s infinite 0s;
}

.ink-wash:nth-child(2) {
  top: 70%;
  left: 60%;
  animation: inkWash 15s infinite 5s;
}

.ink-wash:nth-child(3) {
  top: 50%;
  left: 50%;
  animation: inkWash 15s infinite 10s;
}

/* 统一标题和装饰样式 - 白色文字带炫光 */
.ink-title {
  font-size: 36px !important;
  font-weight: bold !important;
  color: #ffffff !important;
  font-family: 'KaiTi', 'STKaiti', serif !important;
  letter-spacing: 8px !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 255, 255, 0.5), 0 0 30px rgba(255, 255, 255, 0.3) !important;
  margin-bottom: 15px !important;
  animation: textGlow 3s ease-in-out infinite alternate !important;
}

.decoration-symbol {
  margin: 0 15px !important;
  color: #ffffff !important;
  font-size: calc(16px + 0.5vw) !important;
  font-family: 'KaiTi', 'STKaiti', serif !important;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8),
    0 0 15px rgba(255, 255, 255, 0.5) !important;
  animation: textGlow 4s ease-in-out infinite alternate !important;
}

.decoration-line {
  flex: 1 !important;
  height: 1px !important;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  ) !important;
  animation: lineGlow 4s ease-in-out infinite alternate !important;
}

/* 文字炫光动画 */
@keyframes textGlow {
  0% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(255, 255, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.9),
      0 0 25px rgba(255, 255, 255, 0.7), 0 0 35px rgba(255, 255, 255, 0.5);
  }
}

/* 装饰线炫光动画 */
@keyframes lineGlow {
  0% {
    background: linear-gradient(
      to right,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
  }
  100% {
    background: linear-gradient(
      to right,
      transparent,
      rgba(255, 255, 255, 0.9),
      transparent
    );
  }
}

/* 内容区域统一样式 - 半透明黑色背景，让文字更清晰 */
.section-content {
  position: relative !important;
  z-index: 5 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
  border-radius: 8px !important;
}

/* 确保通用样式在最后定义且优先级足够高 */
.ink-section .section-content {
  position: relative !important;
  z-index: 5 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
  border-radius: 8px !important;
}

/* 滚动指示器样式 */
.scroll-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.8;
  animation: fadeInOut 2s ease-in-out infinite;
}

.scroll-arrow {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 12px solid #333;
  margin-bottom: 5px;
  animation: bounce 1.5s ease-in-out infinite;
}

.scroll-text {
  font-size: 12px;
  color: #333;
  text-align: center;
  white-space: nowrap;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
}

/* 菜单容器样式 */
.menu-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 默认不拦截事件 */
  z-index: 999; /* 确保在最上层 */
}

/* 悬浮菜单按钮 - 固定位置 */
.floating-menu {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000; /* 高于背景遮罩 */
  pointer-events: auto; /* 允许接收点击事件 */
}

.menu-button {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  /* 改用渐变背景，增加中国传统元素 */
  background: linear-gradient(135deg, #ba0001 0%, #8c0001 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(139, 0, 0, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  /* 内部边框，增加精致感 */
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.menu-icon {
  font-size: 28px;
  color: #fff1d0; /* 暖金色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.menu-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
}

.menu-panel {
  position: absolute;
  bottom: 75px; /* 调整位置，在按钮上方 */
  right: 0;
  /* 更改面板样式为中国传统风格 */
  background: linear-gradient(135deg, #fbf8ee 0%, #f5f1e4 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 180px;
  /* 添加装饰边框 */
  border: 1px solid #e8d4a9;
  z-index: 1001; /* 确保在按钮和背景遮罩之上 */
  pointer-events: auto; /* 确保可点击 */
}

.menu-panel.show {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.menu-item {
  padding: 15px;
  border-bottom: 1px solid rgba(160, 120, 95, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-icon {
  font-size: 18px;
  margin-right: 10px;
}

.menu-item-text {
  font-size: 16px;
  color: #7a5230; /* 传统木色 */
  font-weight: 500;
  font-family: 'KaiTi', 'STKaiti', serif; /* 使用楷体增强传统风格 */
}

.menu-item:hover,
.menu-item:active {
  background-color: rgba(232, 212, 169, 0.3);
}

/* 背景遮罩 - 点击关闭菜单 */
.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1); /* 轻微可见的背景 */
  z-index: 998; /* 确保在菜单按钮下方，但在其他内容上方 */
  pointer-events: auto; /* 确保可接收点击事件 */
}
</style>