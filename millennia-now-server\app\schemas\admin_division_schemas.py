from pydantic import BaseModel, Field
from typing import List, Optional


class ProvinceBase(BaseModel):
    province_id: int
    name: str
    abbr: Optional[str] = None
    alias: Optional[str] = None
    acronym: Optional[str] = None
    
    class Config:
        from_attributes = True


class CityBase(BaseModel):
    province_id: int
    city_id: int
    name: str
    acronym: Optional[str] = None
    
    class Config:
        from_attributes = True


class DistrictBase(BaseModel):
    province_id: int
    city_id: int
    district_id: int
    name: str
    acronym: Optional[str] = None
    postal_code: Optional[str] = None
    
    class Config:
        from_attributes = True


class StreetBase(BaseModel):
    province_id: int
    city_id: int
    district_id: int
    street_id: int
    name: str
    acronym: Optional[str] = None
    postal_code: Optional[str] = None
    
    class Config:
        from_attributes = True


# 响应模型
class ProvinceResponse(ProvinceBase):
    pass


class CityResponse(CityBase):
    pass


class DistrictResponse(DistrictBase):
    pass


class StreetResponse(StreetBase):
    pass


# 列表响应模型
class ProvinceListResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[ProvinceResponse]


class CityListResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[CityResponse]


class DistrictListResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[DistrictResponse]


class StreetListResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[StreetResponse]


# 扁平化地址信息模型
class FlattenedAddressInfo(BaseModel):
    province_id: int
    province_name: str
    city_id: Optional[int] = None
    city_name: Optional[str] = None
    district_id: Optional[int] = None
    district_name: Optional[str] = None
    street_id: Optional[int] = None
    street_name: Optional[str] = None
    postal_code: Optional[str] = None
    
    class Config:
        from_attributes = True


class ErrorResponse(BaseModel):
    error_code: str
    message: str