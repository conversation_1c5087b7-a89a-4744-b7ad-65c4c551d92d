import { request } from './request'
import { getBaseURL } from '../config/api'

/**
 * 上传结果接口
 */
export interface UploadResult {
  success: boolean
  url?: string
  message?: string
}

/**
 * 上传进度回调函数
 */
export type ProgressCallback = (progress: number) => void

/**
 * 获取上传 URL
 */
function getUploadUrl(uploadType: string): string {
  const baseUrl = getBaseURL()
  
  // 映射上传类型到正确的端点
  const getUploadEndpoint = (type: string) => {
    switch (type) {
      case 'ancient-books':
        return 'ancient-books'
      case 'heritage':
        return 'heritage'
      case 'memory':
        return 'memory'
      case 'timeline':
        return 'timeline'
      case 'place':
        return 'place'
      case 'archive':
        return 'archive'
      default:
        return 'images' // 默认通用图片上传
    }
  }
  
  const endpoint = getUploadEndpoint(uploadType)
  return `${baseUrl}/upload/${endpoint}/image`
}

/**
 * 检查上传大小限制（微信小程序限制为10MB）
 */
async function checkFileSize(filePath: string): Promise<boolean> {
  // #ifdef MP-WEIXIN
  try {
    // 检查是否是本地文件路径
    if (!filePath.startsWith('http://tmp_') && !filePath.startsWith('wxfile://')) {
      console.warn('文件路径格式不正确，跳过大小检查:', filePath)
      return true
    }

    const MAX_SIZE = 10 * 1024 * 1024 // 10MB

    // 使用新的API替代即将废弃的 wx.getFileInfo
    const fileSystemManager = uni.getFileSystemManager()
    const fileInfo = await new Promise<{ size: number }>((resolve, reject) => {
      fileSystemManager.getFileInfo({
        filePath: filePath,
        success: (res: any) => resolve(res),
        fail: (error: any) => reject(error)
      })
    })

    if (fileInfo.size > MAX_SIZE) {
      uni.showToast({
        title: '文件大小超过10MB限制',
        icon: 'none'
      })
      return false
    }
    return true
  } catch (error) {
    console.error('获取文件大小失败:', error)
    return true // 获取失败时默认允许上传
  }
  // #endif

  // 其他平台默认允许
  return true
}

/**
 * 上传单张图片
 * @param filePath 文件路径
 * @param uploadType 上传类型 (heritage, memory, timeline, place)
 * @param onProgress 进度回调
 * @returns 上传结果
 */
export async function uploadImage(
  filePath: string, 
  uploadType: string = 'images',
  onProgress?: ProgressCallback
): Promise<UploadResult> {
  return new Promise(async (resolve) => {
    // 获取用户token
    const token = uni.getStorageSync('access_token')
    if (!token) {
      resolve({
        success: false,
        message: '请先登录'
      })
      return
    }

    // 检查文件大小
    const isValidSize = await checkFileSize(filePath)
    if (!isValidSize) {
      resolve({
        success: false,
        message: '文件大小超过限制'
      })
      return
    }

    // 构建上传URL
    const uploadUrl = getUploadUrl(uploadType)

    // 上传前显示加载提示
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    const uploadTask = uni.uploadFile({
      url: uploadUrl,
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}`
      },
      // 微信小程序特有：设置请求超时时间
      timeout: 60000, // 1分钟超时
      success: (res) => {
        try {
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve({
                success: true,
                url: data.data.url
              })
            } else {
              resolve({
                success: false,
                message: data.message || '上传失败'
              })
            }
          } else if (res.statusCode === 401) {
            // Token 过期
            uni.removeStorageSync('access_token')
            uni.showModal({
              title: '登录已过期',
              content: '请重新登录后再上传',
              showCancel: false,
              success: () => {
                uni.navigateTo({
                  url: '/pages/user/index'
                })
              }
            })
            resolve({
              success: false,
              message: '登录已过期'
            })
          } else {
            resolve({
              success: false,
              message: `上传失败，状态码: ${res.statusCode}`
            })
          }
        } catch (error) {
          console.error('解析上传结果失败:', error)
          resolve({
            success: false,
            message: '上传结果解析失败'
          })
        }
      },
      fail: (error) => {
        console.error('上传失败:', error)
        
        // 微信小程序网络错误处理
        let errorMessage = '上传失败'
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '上传超时，请检查网络'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '网络连接失败'
          }
        }
        
        resolve({
          success: false,
          message: errorMessage
        })
      },
      complete: () => {
        // 隐藏加载提示
        uni.hideLoading()
      }
    })

    // 监听上传进度
    if (onProgress) {
      uploadTask.onProgressUpdate((res) => {
        onProgress(res.progress)
      })
    }
  })
}

/**
 * 批量上传图片
 * @param filePaths 文件路径数组
 * @param uploadType 上传类型
 * @param onProgress 总体进度回调
 * @returns 上传结果数组
 */
export async function uploadImages(
  filePaths: string[], 
  uploadType: string = 'images',
  onProgress?: ProgressCallback
): Promise<UploadResult[]> {
  const results: UploadResult[] = []
  let completedCount = 0

  const updateProgress = () => {
    const progress = Math.round((completedCount / filePaths.length) * 100)
    onProgress && onProgress(progress)
  }

  // 并发上传（最多3个同时进行）
  const concurrency = 3
  const chunks = []
  
  for (let i = 0; i < filePaths.length; i += concurrency) {
    chunks.push(filePaths.slice(i, i + concurrency))
  }

  for (const chunk of chunks) {
    const promises = chunk.map(async (filePath, index) => {
      const result = await uploadImage(filePath, uploadType)
      completedCount++
      updateProgress()
      return result
    })

    const chunkResults = await Promise.all(promises)
    results.push(...chunkResults)
  }

  return results
}

/**
 * 选择并上传单张图片
 * @param uploadType 上传类型
 * @param onProgress 进度回调
 * @returns 上传结果
 */
export async function selectAndUploadImage(
  uploadType: string = 'images',
  onProgress?: ProgressCallback
): Promise<UploadResult> {
  try {
    // 检查是否有相机、相册权限
    // #ifdef MP-WEIXIN
    try {
      await new Promise<void>((resolve, reject) => {
        uni.authorize({
          scope: 'scope.camera',
          success: () => resolve(),
          fail: () => {
            // 如果没有权限，引导用户打开设置页面
            uni.showModal({
              title: '提示',
              content: '需要您授权使用相机',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting()
                }
              }
            })
            reject(new Error('没有相机权限'))
          }
        })
      })
    } catch (error: any) {
      console.warn('相机权限获取失败:', error)
      // 继续执行，因为用户可能只从相册选择
    }
    // #endif

    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'] as ('original' | 'compressed')[],
      sourceType: ['album', 'camera'] as ('album' | 'camera')[]
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      // 微信小程序下先压缩图片
      // #ifdef MP-WEIXIN
      const compressedPath = await compressImage(res.tempFilePaths[0])
      return await uploadImage(compressedPath, uploadType, onProgress)
      // #endif
      
      // 其他平台直接上传
      // #ifndef MP-WEIXIN
      return await uploadImage(res.tempFilePaths[0], uploadType, onProgress)
      // #endif
    } else {
      return {
        success: false,
        message: '未选择图片'
      }
    }
  } catch (error: any) {
    console.error('选择图片失败:', error)
    return {
      success: false,
      message: error.errMsg || '选择图片失败'
    }
  }
}

/**
 * 选择并上传多张图片
 * @param maxCount 最大选择数量
 * @param uploadType 上传类型
 * @param onProgress 进度回调
 * @returns 上传结果数组
 */
export async function selectAndUploadImages(
  maxCount: number = 9,
  uploadType: string = 'images',
  onProgress?: ProgressCallback
): Promise<UploadResult[]> {
  try {
    const res = await uni.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      return await uploadImages(res.tempFilePaths, uploadType, onProgress)
    } else {
      return [{
        success: false,
        message: '未选择图片'
      }]
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    return [{
      success: false,
      message: '选择图片失败'
    }]
  }
}

/**
 * 删除图片
 * @param imageUrl 图片URL
 * @returns 是否删除成功
 */
export async function deleteImage(imageUrl: string): Promise<boolean> {
  try {
    const response = await request({
      url: '/api/upload/delete',
      method: 'DELETE',
      data: { url: imageUrl }
    })

    return response.success
  } catch (error) {
    console.error('删除图片失败:', error)
    return false
  }
}

/**
 * 压缩图片
 * @param filePath 文件路径
 * @param quality 压缩质量 (0-100)
 * @returns 压缩后的文件路径
 */
export async function compressImage(
  filePath: string, 
  quality: number = 80
): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: quality,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: (error) => {
        console.error('图片压缩失败:', error)
        // 压缩失败时返回原图片
        resolve(filePath)
      }
    })
  })
}

/**
 * 获取图片信息
 * @param filePath 文件路径
 * @returns 图片信息
 */
export async function getImageInfo(filePath: string): Promise<any> {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: filePath,
      success: (res) => {
        resolve(res)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 预览图片
 * @param urls 图片URL数组
 * @param current 当前图片索引
 */
export function previewImages(urls: string[], current: number = 0): void {
  uni.previewImage({
    urls: urls,
    current: current
  })
}

/**
 * 保存图片到相册
 * @param filePath 图片路径
 * @returns 是否保存成功
 */
export async function saveImageToPhotosAlbum(filePath: string): Promise<boolean> {
  try {
    await uni.saveImageToPhotosAlbum({
      filePath: filePath
    })
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    return true
  } catch (error) {
    console.error('保存图片失败:', error)
    
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
    
    return false
  }
}

/**
 * 验证图片格式
 * @param filePath 文件路径
 * @returns 是否为有效图片格式
 */
export function validateImageFormat(filePath: string): boolean {
  const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'))
  return validExtensions.includes(extension)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
} 