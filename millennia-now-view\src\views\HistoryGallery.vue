<template>
  <div class="gallery-container">
    <!-- 3D场景容器 -->
    <div class="scene-container">
      <canvas ref="sceneCanvas"></canvas>
    </div>

    <!-- 用户界面元素 -->
    <div class="ui-container">
      <!-- 使用小程序返回按钮组件 -->
      <MiniappBackButton />

      <!-- 普通返回按钮（当不是从小程序进入时显示） -->
      <div class="back-button"
           @click="goBack"
           v-if="!isFromMiniapp">
        <span class="icon">←</span>
        <span class="text">返回</span>
      </div>

      <!-- 展厅标题 -->
      <div class="gallery-title">
        <h1>{{ regionName }}历史文脉</h1>
        <p>探索地区发展历史</p>
      </div>

      <!-- 加载进度指示器 -->
      <div class="loading-progress"
           v-if="loading">
        <div class="loading-text">加载中 {{ Math.floor(loadingProgress) }}%</div>
        <div class="progress-bar">
          <div class="progress"
               :style="{ width: loadingProgress + '%' }"></div>
        </div>
      </div>

      <!-- 展品信息面板 -->
      <div class="exhibit-panel"
           v-if="selectedExhibit"
           :class="{ 'active': selectedExhibit }">
        <div class="panel-header">
          <h2>{{ selectedExhibit.title }}</h2>
          <button class="close-btn"
                  @click="closeExhibitPanel">×</button>
        </div>
        <div class="panel-content">
          <!-- 图片轮播 - 固定在上方 -->
          <div class="image-carousel">
            <!-- 主图片 -->
            <img :src="currentImage"
                 alt="展品图片"
                 class="exhibit-image"
                 @click="showFullImage(currentImage)">

            <!-- 缩略图预览 -->
            <div class="thumbnail-container"
                 v-if="hasMultipleImages">
              <div class="thumbnails">
                <div v-for="(image, index) in allImages"
                     :key="index"
                     class="thumbnail"
                     :class="{ 'active': currentImageIndex === index }"
                     @click="setCurrentImage(index)">
                  <img :src="image"
                       :alt="`缩略图 ${index+1}`">
                </div>
              </div>
            </div>

            <!-- 轮播控制按钮 -->
            <div class="carousel-controls"
                 v-if="hasMultipleImages">
              <button class="control-btn prev"
                      @click="prevImage">&lt;</button>
              <button class="control-btn next"
                      @click="nextImage">&gt;</button>
            </div>
          </div>

          <!-- 展品信息区域 - 可独立滚动 -->
          <div class="exhibit-info-container">
            <div class="exhibit-info">
              <!-- 基本信息卡片 -->
              <div class="info-card basic-info">
                <div class="card-header">
                  <h3><i class="icon-clock"></i>历史信息</h3>
                </div>
                <div class="card-content">
                  <div class="info-row">
                    <span class="label">年代：</span>
                    <span class="value year-badge">{{ selectedExhibit.year }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">时期：</span>
                    <span class="value period-badge">{{ selectedExhibit.period }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.dynasty">
                    <span class="label">朝代：</span>
                    <span class="value">{{ selectedExhibit.dynasty }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.location">
                    <span class="label">地点：</span>
                    <span class="value">{{ selectedExhibit.location }}</span>
                  </div>
                </div>
              </div>

              <!-- 历史描述卡片 -->
              <div class="info-card description-info">
                <div class="card-header">
                  <h3><i class="icon-scroll"></i>历史描述</h3>
                </div>
                <div class="card-content">
                  <p class="history-description">{{ selectedExhibit.description }}</p>
                </div>
              </div>

              <!-- 详细介绍卡片 -->
              <div class="info-card detail-info"
                   v-if="selectedExhibit.content">
                <div class="card-header">
                  <h3><i class="icon-book"></i>详细介绍</h3>
                </div>
                <div class="card-content">
                  <div class="content-text"
                       v-html="formattedContent"></div>
                </div>
              </div>

              <!-- 历史意义卡片 -->
              <div class="info-card significance-info"
                   v-if="selectedExhibit.significance || selectedExhibit.historical_impact">
                <div class="card-header">
                  <h3><i class="icon-star"></i>历史意义</h3>
                </div>
                <div class="card-content">
                  <div v-if="selectedExhibit.historical_tags && selectedExhibit.historical_tags.length > 0"
                       class="historical-tags">
                    <span v-for="tag in selectedExhibit.historical_tags"
                          :key="tag"
                          class="historical-tag">{{ tag }}</span>
                  </div>
                  <p v-if="selectedExhibit.significance"
                     class="significance-text">{{ selectedExhibit.significance }}</p>
                  <p v-if="selectedExhibit.historical_impact"
                     class="impact-text">{{ selectedExhibit.historical_impact }}</p>
                </div>
              </div>

              <!-- 相关人物卡片 -->
              <div class="info-card people-info"
                   v-if="selectedExhibit.related_people || selectedExhibit.key_figures">
                <div class="card-header">
                  <h3><i class="icon-people"></i>相关人物</h3>
                </div>
                <div class="card-content">
                  <div class="info-row"
                       v-if="selectedExhibit.related_people">
                    <span class="label">相关人物：</span>
                    <span class="value">{{ selectedExhibit.related_people }}</span>
                  </div>
                  <div class="info-row"
                       v-if="selectedExhibit.key_figures">
                    <span class="label">关键人物：</span>
                    <span class="value">{{ selectedExhibit.key_figures }}</span>
                  </div>
                </div>
              </div>

              <!-- 历史背景卡片 -->
              <div class="info-card context-info"
                   v-if="selectedExhibit.historical_context || selectedExhibit.background">
                <div class="card-header">
                  <h3><i class="icon-context"></i>历史背景</h3>
                </div>
                <div class="card-content">
                  <p v-if="selectedExhibit.historical_context"
                     class="context-text">{{ selectedExhibit.historical_context }}</p>
                  <p v-if="selectedExhibit.background"
                     class="background-text">{{ selectedExhibit.background }}</p>
                </div>
              </div>

              <!-- 元数据信息 -->
              <div class="meta-info">
                <div class="meta-row"
                     v-if="selectedExhibit.created_at">
                  <span class="meta-label">创建时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.created_at) }}</span>
                </div>
                <div class="meta-row"
                     v-if="selectedExhibit.updated_at">
                  <span class="meta-label">更新时间：</span>
                  <span class="meta-value">{{ formatDate(selectedExhibit.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作提示 -->
      <div class="controls-hint"
           v-if="!loading && !selectedExhibit">
        <p>使用 WASD 或方向键移动，鼠标左键点击展品查看详情</p>
      </div>

      <!-- 移动端移动摇杆控制 -->
      <div class="move-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="moveControl"
           @touchstart.prevent="handleMoveJoystickStart"
           @mousedown.prevent="handleMoveJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="moveJoystickHandle"
               :style="moveJoystickStyle"></div>
        </div>
      </div>

      <!-- 移动端视角摇杆控制 -->
      <div class="look-joystick"
           v-if="!selectedExhibit && isMobileDevice"
           ref="lookControl"
           @touchstart.prevent="handleLookJoystickStart"
           @mousedown.prevent="handleLookJoystickStart">
        <div class="joystick-base">
          <div class="joystick-handle"
               ref="lookJoystickHandle"
               :style="lookJoystickStyle"></div>
        </div>
      </div>
    </div>

    <!-- 全屏图片查看器 -->
    <div class="fullscreen-image-viewer"
         v-if="fullScreenImage"
         @click="closeFullImage">
      <div class="fullscreen-image-container"
           @click.stop>
        <img :src="fullScreenImage"
             alt="全屏图片">
        <button class="close-fullscreen-btn"
                @click="closeFullImage">×</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as THREE from 'three'
import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls'
import { getHistoryData } from '@/api/history'
import MiniappBackButton from '../components/MiniappBackButton.vue'

const router = useRouter()
const route = useRoute()

// 小程序环境标记
const isFromMiniapp = ref(false)

// 添加区域信息状态
const regionName = ref('')
const provinceId = ref<number | null>(null)
const cityId = ref<number | null>(null)
const districtId = ref<number | null>(null)

// 尝试从route参数或localStorage中获取区域信息
const initRegionInfo = () => {
  // 检查是否从小程序进入
  const urlParams = new URLSearchParams(window.location.search)
  const fromParam = urlParams.get('from') === 'miniapp'
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true'

  isFromMiniapp.value = fromParam || fromStorage

  // 从路由参数中获取
  if (route.query.regionName)
    regionName.value = route.query.regionName as string
  if (route.query.provinceId)
    provinceId.value = parseInt(route.query.provinceId as string, 10) || null
  if (route.query.cityId)
    cityId.value = parseInt(route.query.cityId as string, 10) || null
  if (route.query.districtId)
    districtId.value = parseInt(route.query.districtId as string, 10) || null

  // 如果路由参数中没有，尝试从localStorage获取
  if (!regionName.value)
    regionName.value = localStorage.getItem('regionName') || '默认地区'
  if (!provinceId.value && localStorage.getItem('provinceId'))
    provinceId.value =
      parseInt(localStorage.getItem('provinceId') || '', 10) || null
  if (!cityId.value && localStorage.getItem('cityId'))
    cityId.value = parseInt(localStorage.getItem('cityId') || '', 10) || null
  if (!districtId.value && localStorage.getItem('districtId'))
    districtId.value =
      parseInt(localStorage.getItem('districtId') || '', 10) || null

  console.log('历史文脉展厅接收到区域信息:', {
    regionName: regionName.value,
    provinceId: provinceId.value,
    cityId: cityId.value,
    districtId: districtId.value,
  })
}

// 场景相关变量
const sceneCanvas = ref<HTMLCanvasElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: PointerLockControls
let raycaster: THREE.Raycaster
let clock = new THREE.Clock()
let animationFrameId: number

// 视角控制相关引用
const lookControl = ref<HTMLElement | null>(null)
const joystickHandle = ref<HTMLElement | null>(null)
const joystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 视角控制状态
const lookActive = ref(false)
const lookStartPosition = { x: 0, y: 0 }
const lookCurrentPosition = { x: 0, y: 0 }
const cameraRotation = { x: 0, y: 0 }

// 移动摇杆控制相关引用
const moveControl = ref<HTMLElement | null>(null)
const moveJoystickHandle = ref<HTMLElement | null>(null)
const moveJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 移动摇杆控制状态
const moveActive = ref(false)
const moveStartPosition = { x: 0, y: 0 }
const moveCurrentPosition = { x: 0, y: 0 }
const moveDirection = { x: 0, z: 0 } // 移动方向向量

// 右侧视角摇杆相关变量（新增）
const lookJoystickHandle = ref<HTMLElement | null>(null)
const lookJoystickStyle = ref({
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
})

// 加载状态
const loading = ref(true)
const loadingProgress = ref(0)

// 移动控制变量
const moveForward = ref(false)
const moveBackward = ref(false)
const moveLeft = ref(false)
const moveRight = ref(false)
const canJump = ref(false)

let velocity = new THREE.Vector3()
let direction = new THREE.Vector3()
let prevTime = performance.now()

// 展品数据
const exhibits = ref<any[]>([])
const exhibitObjects: THREE.Object3D[] = []
const selectedExhibit = ref<any>(null)

// 图片轮播相关
const currentImageIndex = ref(0)
const currentImage = computed(() => {
  if (!selectedExhibit.value) return ''
  return allImages.value[currentImageIndex.value]
})

const allImages = computed(() => {
  if (!selectedExhibit.value) return []

  // 主图片加上所有详情图片
  const images = [selectedExhibit.value.image]
  if (
    selectedExhibit.value.detail_images &&
    Array.isArray(selectedExhibit.value.detail_images)
  ) {
    images.push(...selectedExhibit.value.detail_images)
  }
  return images
})

const hasMultipleImages = computed(() => {
  return allImages.value.length > 1
})

const formattedContent = computed(() => {
  if (!selectedExhibit.value || !selectedExhibit.value.content) return ''
  // 将内容中的换行符转换为HTML换行
  return selectedExhibit.value.content.replace(/\n/g, '<br>')
})

// 格式化日期的方法
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return dateString
  }
}

// 全屏图片查看器相关
const fullScreenImage = ref<string | null>(null)

const showFullImage = (imageSrc: string) => {
  fullScreenImage.value = imageSrc
}

const closeFullImage = () => {
  fullScreenImage.value = null
}

// 图片轮播相关方法
const nextImage = () => {
  if (currentImageIndex.value < allImages.value.length - 1) {
    currentImageIndex.value++
  } else {
    currentImageIndex.value = 0 // 循环到第一张
  }
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  } else {
    currentImageIndex.value = allImages.value.length - 1 // 循环到最后一张
  }
}

const setCurrentImage = (index: number) => {
  currentImageIndex.value = index
}

// 初始化3D场景
const initScene = async () => {
  if (!sceneCanvas.value) return

  // 检测设备类型
  checkMobileDevice()
  console.log('是否为移动设备:', isMobileDevice.value)

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x1b1209) // 深棕色背景，更符合历史氛围
  scene.fog = new THREE.Fog(0x1b1209, 10, 50)

  // 添加环境光和方向光 - 调整为更温暖的色调
  const ambientLight = new THREE.AmbientLight(0xf5e1c0, 0.6) // 温暖的米黄色环境光
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xf9e2b9, 1.0) // 更强的温暖光源
  directionalLight.position.set(5, 10, 7.5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 添加点光源 - 暖色调提亮展厅
  const pointLight1 = new THREE.PointLight(0xf9c27d, 1.2, 20) // 暖橙色光源
  pointLight1.position.set(0, 3, 0)
  scene.add(pointLight1)

  // 设置相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  )
  camera.position.set(0, 1.6, 0) // 人眼高度约1.6米

  // 设置渲染器
  renderer = new THREE.WebGLRenderer({
    canvas: sceneCanvas.value,
    antialias: true,
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true

  // 使用指针锁定控制
  controls = new PointerLockControls(camera, renderer.domElement)
  scene.add(controls.getObject())

  // 移动设备上禁用指针锁定（因为不支持），但保留控制功能
  if (isMobileDevice.value) {
    console.log('移动设备：禁用指针锁定')
    // 修改lock方法以适应移动设备
    const originalLock = controls.lock
    controls.lock = function () {
      // 在移动设备上，只需将isLocked设为true而不实际尝试锁定指针
      this.isLocked = true
      return this
    }

    // 自动"锁定"控制，以便移动键可以正常工作
    controls.lock()
  }

  // 覆盖PointerLockControls的鼠标移动处理
  const originalOnMouseMove = (controls as any).onMouseMove
  if (originalOnMouseMove) {
    ;(controls as any).onMouseMove = function (event: MouseEvent) {
      if (!controls.isLocked) return

      const movementX = event.movementX || 0
      const movementY = event.movementY || 0

      // 使用我们的标准FPS相机控制
      updateCameraRotation(movementX, movementY, mouseSensitivity)
    }
  }

  // 设置射线投射器用于交互
  raycaster = new THREE.Raycaster()

  // 添加键盘事件监听
  document.addEventListener('keydown', onKeyDown)
  document.addEventListener('keyup', onKeyUp)

  // 添加点击事件监听
  renderer.domElement.addEventListener('click', onMouseClick)

  // 移动设备上添加触摸事件
  if (isMobileDevice.value) {
    // 用于选择展品
    renderer.domElement.addEventListener('touchend', onTouchEnd)
    // 移动端禁用手指触摸控制视角，只通过右侧摇杆控制
    // renderer.domElement.addEventListener('touchstart', handleCanvasTouchStart)
  }

  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize)

  // 加载数据并创建展厅
  await loadHistoryData()
  createHistoryGallery()

  // 开始动画循环
  animate()

  // 桌面设备才需要延迟锁定（移动设备已在初始化时处理）
  if (!isMobileDevice.value) {
    setTimeout(() => {
      controls.lock()
    }, 1000)
  }
}

// 加载历史文脉数据
const loadHistoryData = async () => {
  try {
    loading.value = true
    console.log('正在加载历史文脉数据...')

    // 获取真实历史文脉数据，使用区域ID参数
    const historyData = await getHistoryData({
      province_id: provinceId.value || undefined,
      city_id: cityId.value || undefined,
      district_id: districtId.value || undefined,
      page: 1,
      page_size: 50,
    })

    // 转换为展品格式
    exhibits.value = historyData.items.map((item: any) => ({
      id: item.id,
      title: item.title,
      description: item.description,
      image:
        item.image || `https://picsum.photos/seed/${item.id + 100}/800/600`, // 如果没有图片，使用占位图
      year: item.year,
      period: item.period,
      dynasty: item.dynasty || '',
      location: item.location || '',
      content: item.detail || item.description,
      detail_images: item.detail_images || [],
      significance: item.significance || '',
      historical_impact: item.historical_impact || '',
      historical_tags: item.historical_tags || [],
      related_people: item.related_people || '',
      key_figures: item.key_figures || '',
      historical_context: item.historical_context || '',
      background: item.background || '',
      created_at: item.created_at || '',
      updated_at: item.updated_at || '',
    }))

    // 按照年代排序（从今至古）
    exhibits.value.sort((a, b) => {
      // 尝试将年份转换为可比较的数字
      const yearA = parseYearToNumber(a.year)
      const yearB = parseYearToNumber(b.year)
      return yearB - yearA // 倒序排列
    })

    console.log('历史文脉数据加载成功:', exhibits.value.length)
    loading.value = false

    return exhibits.value
  } catch (error) {
    console.error('加载历史文脉数据失败:', error)
    loading.value = false
    return exhibits.value
  }
}

// 将年份字符串转换为可比较的数字
const parseYearToNumber = (yearStr: string): number => {
  if (!yearStr) return 0

  // 处理"公元前"年份
  if (yearStr.includes('公元前')) {
    const match = yearStr.match(/公元前(\d+)/)
    if (match && match[1]) {
      return -parseInt(match[1], 10)
    }
  }

  // 处理包含范围的年份，如"1911-1949年"，取前面的年份
  if (yearStr.includes('-')) {
    const match = yearStr.match(/(\d+)-\d+/)
    if (match && match[1]) {
      return parseInt(match[1], 10)
    }
  }

  // 处理普通年份
  const match = yearStr.match(/(\d+)/)
  if (match && match[1]) {
    return parseInt(match[1], 10)
  }

  return 0
}

// 创建历史文脉展厅
const createHistoryGallery = () => {
  if (!exhibits.value || exhibits.value.length === 0) return

  // 计算走廊长度 - 根据展品数量动态调整
  const wallSpacing = 10 // 每两根柱子间距10个单位

  // 计算每面墙可以容纳的展品数量
  const exhibitsPerWall = 4 // 每面墙最多放5个展品

  // 计算走廊长度 - 确保足够长以容纳所有展品
  const totalExhibits = exhibits.value.length
  const bothSidesExhibits = Math.ceil(totalExhibits / 2) // 两侧墙各自至少需要展示的展品数量
  const wallCount = Math.max(exhibitsPerWall, bothSidesExhibits) // 取较大值确保空间足够

  // 走廊总长度
  const corridorLength = wallCount * wallSpacing
  // 起始位置
  const startX = -corridorLength / 1.58

  // 创建古代青石板地面
  const floorGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const floorMaterial = new THREE.MeshStandardMaterial({
    color: 0x3d3c3a, // 古代青石色
    roughness: 0.9, // 增加粗糙度，模拟石材质感
    metalness: 0.1,
    envMapIntensity: 0.5, // 降低环境反射，模拟更古朴的材质
  })
  const floor = new THREE.Mesh(floorGeometry, floorMaterial)
  floor.rotation.x = -Math.PI / 2
  floor.position.set(0, 0, 0)
  floor.receiveShadow = false
  scene.add(floor)

  // 创建古代木梁天花板
  const ceilingGeometry = new THREE.PlaneGeometry(corridorLength + 20, 10)
  const ceilingMaterial = new THREE.MeshStandardMaterial({
    color: 0x3a2917, // 深褐色古代木梁
    roughness: 0.85,
    metalness: 0.05, // 降低金属感，更自然
    bumpScale: 0.05, // 增加微小凹凸，模拟木纹
  })
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial)
  ceiling.rotation.x = Math.PI / 2
  ceiling.position.set(0, 4, 0)
  ceiling.receiveShadow = true
  scene.add(ceiling)

  // 添加天花板木梁装饰
  for (let i = 0; i < wallCount + 1; i++) {
    const beamGeometry = new THREE.BoxGeometry(0.4, 0.3, 10)
    const beamMaterial = new THREE.MeshStandardMaterial({
      color: 0x2a1506, // 更深的木梁颜色
      roughness: 0.9,
      metalness: 0.05,
    })
    const beam = new THREE.Mesh(beamGeometry, beamMaterial)
    beam.position.set(startX + i * wallSpacing, 3.85, 0)
    beam.castShadow = true
    scene.add(beam)
  }

  // 创建左右墙壁 - 仿古砖墙面
  const sideWallGeometry = new THREE.PlaneGeometry(corridorLength + 20, 4)
  const wallMaterial = new THREE.MeshStandardMaterial({
    color: 0xe8ddc9, // 米黄色古砖墙
    roughness: 0.85,
    metalness: 0.05,
    bumpScale: 0.02, // 添加凹凸感，模拟砖墙纹理
  })

  // 左墙 - 添加花纹材质
  const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  leftWall.position.set(0, 2, 5)
  leftWall.rotation.y = Math.PI
  leftWall.receiveShadow = true
  scene.add(leftWall)

  // 右墙
  const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial)
  rightWall.position.set(0, 2, -5)
  rightWall.receiveShadow = true
  scene.add(rightWall)

  // 创建前后端实体墙 - 使用传统木门框设计
  // 前端墙 - 入口
  const frontWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const frontWallMaterial = new THREE.MeshStandardMaterial({
    color: 0xdbc8a0, // 浅米色古墙
    roughness: 0.9,
    metalness: 0.05,
    bumpScale: 0.03, // 增加凹凸纹理
  })
  const frontWall = new THREE.Mesh(frontWallGeometry, frontWallMaterial)
  frontWall.position.set(-corridorLength / 2 - 5, 2, 0)
  frontWall.castShadow = true
  frontWall.receiveShadow = true
  scene.add(frontWall)

  // 后端墙 - 尽头
  const endWallGeometry = new THREE.BoxGeometry(0.5, 4, 10)
  const endWallMaterial = new THREE.MeshStandardMaterial({
    color: 0xdbc8a0, // 浅米色古墙，与前墙保持一致
    roughness: 0.9,
    metalness: 0.05,
    bumpScale: 0.03,
  })
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial)
  endWall.position.set(corridorLength / 2.8, 2, 0) // 后墙位置 2.8是根据走廊长度计算的
  endWall.castShadow = true
  endWall.receiveShadow = true
  scene.add(endWall)

  // 创建柱子
  // 每个柱子的位置
  const pillarPositions: number[] = []
  for (let i = 0; i <= wallCount; i++) {
    pillarPositions.push(startX + i * wallSpacing)
  }

  // 添加柱子
  pillarPositions.forEach((x) => {
    createPillar(x, 0, 4.8) // 左侧柱子
    createPillar(x, 0, -4.8) // 右侧柱子
  })

  // 按顺序展示展品 - 先左侧墙再右侧墙
  exhibits.value.forEach((exhibit, index) => {
    // 计算展品位置
    const wallSide = index < exhibitsPerWall ? 'left' : 'right' // 左侧墙展示完再到右侧墙
    const wallIndex = wallSide === 'left' ? index : index - exhibitsPerWall // 在当前墙上的索引

    // 确保不超过墙的数量
    if (wallIndex >= wallCount) return

    // 计算展品位置 - 放在两个柱子之间的中点
    const leftPillarX = pillarPositions[wallIndex]
    const rightPillarX = pillarPositions[wallIndex + 1]
    const exhibitX = (leftPillarX + rightPillarX) / 2

    // 计算z位置 (左侧墙或右侧墙)
    const zPosition = wallSide === 'left' ? 4.9 : -4.9

    // 展示展品
    createExhibitOnWall(exhibitX, 0, zPosition, exhibit, wallSide)
  })

  // 添加中间一排天花板灯光
  for (let i = 0; i < pillarPositions.length - 1; i++) {
    const leftX = pillarPositions[i]
    const rightX = pillarPositions[i + 1]
    const centerX = (leftX + rightX) / 2

    // 在中央位置创建灯光
    createCeilingLight(centerX, 3.8, 0)
  }

  // 设置物理碰撞边界
  addPhysicalBoundaries(corridorLength)

  // 设置初始相机位置在走廊的最早时间点（最前端）
  controls.getObject().position.set(startX + 2, 1.6, 0)
}

// 添加物理碰撞边界
const addPhysicalBoundaries = (corridorLength: number) => {
  // 前墙碰撞检测边界
  const frontBoundary = new THREE.Vector3(-corridorLength / 2 - 4.5, 0, 0)
  // 后墙碰撞检测边界
  const endBoundary = new THREE.Vector3(corridorLength / 2.9, 0, 0)

  // 将边界保存到全局变量中，以便在animate函数中使用
  boundaries = {
    front: frontBoundary.x,
    end: endBoundary.x,
  }
}

// 全局边界变量
let boundaries: { front: number; end: number } = { front: 0, end: 0 }

// 墙面参数常量
const WALL_SPACING = 10 // 每两根柱子间距10个单位
const WALL_HEIGHT = 4 // 墙壁高度

// 在墙上创建展品
const createExhibitOnWall = (
  x: number,
  y: number,
  z: number,
  exhibit: any,
  side: 'left' | 'right'
) => {
  // 创建纹理
  const textureLoader = new THREE.TextureLoader()
  textureLoader.load(
    exhibit.image,
    (texture) => {
      const aspectRatio = texture.image.width / texture.image.height
      const imageWidth = 2
      const imageHeight = imageWidth / aspectRatio

      // 确定朝向 - 平行于墙面
      let wallRotationY = side === 'left' ? Math.PI : 0 // 左侧墙朝向走廊内部，右侧墙朝向走廊内部

      // 创建主展板(超薄，几乎就是一个平面)
      const boardDepth = 0.02 // 非常薄的展板
      // 计算展板尺寸 - 占两柱子间距的80%，墙高的80%
      const boardWidth = WALL_SPACING * 0.8 // 两柱子间距是10个单位，取80%
      const boardHeight = WALL_HEIGHT * 0.8 // 墙高4个单位，取80%
      const boardGeometry = new THREE.BoxGeometry(
        boardWidth,
        boardHeight,
        boardDepth
      )

      // 创建画布元素（将在展板前方直接显示）
      const canvasGeometry = new THREE.PlaneGeometry(imageWidth, imageHeight)

      // 获取原始图片宽高比
      const originalAspectRatio = texture.image.width / texture.image.height
      const canvasMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.FrontSide,
      })

      // 创建展板材质（古代石碑风格）
      const boardMaterial = new THREE.MeshStandardMaterial({
        color: 0x505050, // 更深的灰色石材，增强对比度
        roughness: 0.9,
        metalness: 0.1,
        bumpScale: 0.05, // 增加石材凹凸感
      })

      // 使用不同材质的数组创建展板
      const materials = [
        boardMaterial, // right
        boardMaterial, // left
        boardMaterial, // top
        boardMaterial, // bottom
        boardMaterial, // back (面向墙壁)
        boardMaterial, // front (面向走廊)
      ]

      // 创建展板
      const board = new THREE.Mesh(boardGeometry, materials)

      // 将展板放在墙上(稍微突出一点)
      const wallOffset = 0.1 // 从墙面稍微突出的距离
      board.position.set(
        x,
        y + 2,
        z - (side === 'left' ? -wallOffset : wallOffset)
      )
      board.rotation.y = wallRotationY
      // 为整个展板添加展品数据，使其可交互
      board.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(board)
      exhibitObjects.push(board) // 添加展板到可交互对象

      // 调整图片尺寸，使其适合展板左侧40%区域
      const imageAreaWidth = boardWidth * 0.4 // 图片区域占展板宽度的40%
      const adjustedImageWidth = imageAreaWidth * 0.9 // 图片宽度为图片区域的90%
      // 图片高度设为展板高度的80%，不再基于宽高比自适应
      const adjustedImageHeight = boardHeight * 0.8
      const adjustedCanvasGeometry = new THREE.PlaneGeometry(
        adjustedImageWidth,
        adjustedImageHeight
      )

      // 根据图片原始比例和目标比例调整纹理
      texture.center.set(0.5, 0.5) // 纹理中心点
      const targetAspectRatio = adjustedImageWidth / adjustedImageHeight

      if (originalAspectRatio > targetAspectRatio) {
        // 图片更宽，调整横向重复值
        texture.repeat.set(targetAspectRatio / originalAspectRatio, 1)
      } else {
        // 图片更高，调整纵向重复值
        texture.repeat.set(1, originalAspectRatio / targetAspectRatio)
      }

      // 计算图片水平偏移量 - 将其放置在左侧40%区域
      const horizontalOffset = -boardWidth / 2 + imageAreaWidth / 2 // 向左偏移到左侧40%区域的中心

      // 在展板前方放置图片
      const canvas = new THREE.Mesh(adjustedCanvasGeometry, canvasMaterial)
      const faceOffset = boardDepth / 2 + 0.001 // 稍微在展板前方
      canvas.position.set(
        x + horizontalOffset,
        y + 2,
        z - (side === 'left' ? -faceOffset : faceOffset)
      )
      canvas.rotation.y = wallRotationY
      canvas.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit }
      scene.add(canvas)
      exhibitObjects.push(canvas)

      // 创建详细信息面板 - 放置在右侧60%区域
      // 创建详细信息画布
      const infoCanvas = document.createElement('canvas')
      const infoContext = infoCanvas.getContext('2d')
      let infoTexture

      if (infoContext) {
        // 创建高分辨率画布以获得更清晰的文本
        infoCanvas.width = 1024
        infoCanvas.height = 1024

        // 设置古代卷轴风格背景 - 增强对比度
        const gradient = infoContext.createLinearGradient(
          0,
          0,
          0,
          infoCanvas.height
        )
        gradient.addColorStop(0, 'rgba(235, 225, 205, 1.0)') // 更浅更亮的背景色
        gradient.addColorStop(1, 'rgba(222, 210, 190, 1.0)') // 下部较深但依然明亮
        infoContext.fillStyle = gradient
        infoContext.fillRect(0, 0, infoCanvas.width, infoCanvas.height)

        // 添加羊皮纸纹理效果
        infoContext.strokeStyle = 'rgba(139, 69, 19, 0.05)' // 非常淡的棕色线条
        infoContext.lineWidth = 1
        const lineSpacing = 20

        // 水平纹理线
        for (let y = 0; y < infoCanvas.height; y += lineSpacing) {
          infoContext.beginPath()
          infoContext.moveTo(0, y)
          infoContext.lineTo(infoCanvas.width, y)
          infoContext.stroke()
        }

        // 创建装饰边框
        const borderWidth = 8
        infoContext.strokeStyle = 'rgba(100, 60, 20, 0.3)' // 古铜色边框
        infoContext.lineWidth = borderWidth
        infoContext.strokeRect(
          borderWidth / 2,
          borderWidth / 2,
          infoCanvas.width - borderWidth,
          infoCanvas.height - borderWidth
        )

        // 绘制标题 - 古书风格字体，增强对比度
        infoContext.font =
          'bold 65px "FangSong", "STFangsong", KaiTi, STKaiti, serif'
        infoContext.fillStyle = '#2B1600' // 更深的墨色，提高对比度
        infoContext.textAlign = 'center'
        infoContext.fillText(exhibit.title, infoCanvas.width / 2, 120)

        // 绘制古朴分隔线 - 带有装饰图案
        infoContext.strokeStyle = '#3A1F00' // 更深的棕色，提高可见度
        infoContext.lineWidth = 6 // 加粗分隔线
        infoContext.beginPath()
        infoContext.moveTo(200, 170) // 下移分隔线位置，避免与标题重叠
        infoContext.lineTo(infoCanvas.width - 200, 170)
        infoContext.stroke()

        // 添加装饰图案
        const patternSize = 30 // 稍微增大装饰
        infoContext.fillStyle = '#3A1F00' // 更深的棕色，与分隔线一致

        // 左侧装饰
        infoContext.beginPath()
        infoContext.arc(200 - patternSize, 170, patternSize / 2, 0, Math.PI * 2)
        infoContext.fill()

        // 右侧装饰
        infoContext.beginPath()
        infoContext.arc(
          infoCanvas.width - 200 + patternSize,
          170,
          patternSize / 2,
          0,
          Math.PI * 2
        )
        infoContext.fill()

        // 绘制年代与时期 - 书法风格字体
        infoContext.font =
          'bold 52px "FangSong", "STFangsong", KaiTi, STKaiti, serif'
        infoContext.fillStyle = '#2B1600' // 更深的墨色，与标题一致
        infoContext.textAlign = 'left'
        infoContext.fillText(`年代: ${exhibit.year}`, 100, 250) // 下移位置，避免与分隔线太近
        infoContext.fillText(`时期: ${exhibit.period}`, 100, 330) // 下移位置，保持间距

        // 绘制描述文本 - 自动换行，古籍风格字体
        infoContext.font =
          'bold 48px "FangSong", "STFangsong", KaiTi, STKaiti, serif'
        infoContext.fillStyle = '#2B1600' // 深墨色，与其他文字一致

        // 文本换行算法
        const maxWidth = infoCanvas.width - 140 // 增加左右边距，缩小文本区域
        const lineHeight = 58 // 稍微调整行间距
        const description = exhibit.description || ''
        let y = 400 // 进一步下移描述文本起始位置，避免与年代时期重叠

        // 使用中文字符更适合的文本换行方式
        let words = description.split('')
        let line = ''

        for (let i = 0; i < words.length; i++) {
          let testLine = line + words[i]
          let metrics = infoContext.measureText(testLine)

          if (metrics.width > maxWidth && i > 0) {
            infoContext.fillText(line, 70, y) // 增加左侧边距
            line = words[i]
            y += lineHeight

            // 防止文本超出画布底部
            if (y > infoCanvas.height - 60) {
              // 增加底部边距
              line += '...'
              infoContext.fillText(line, 70, y) // 增加左侧边距，与其他行一致
              break
            }
          } else {
            line = testLine
          }
        }

        // 绘制最后一行
        if (y <= infoCanvas.height - 40) {
          infoContext.fillText(line, 70, y) // 增加左侧边距，与上面保持一致
        }

        // 创建纹理
        infoTexture = new THREE.CanvasTexture(infoCanvas)
        infoTexture.needsUpdate = true
      }

      // 创建右侧信息面板
      if (infoTexture) {
        // 计算信息面板的尺寸和位置
        const infoAreaWidth = boardWidth * 0.6 // 右侧信息区域占展板宽度的60%
        const infoWidth = infoAreaWidth * 0.9 // 信息面板宽度为信息区域的90%
        const infoHeight = boardHeight * 0.85 // 信息面板高度为展板高度的85%

        const infoGeometry = new THREE.PlaneGeometry(infoWidth, infoHeight)
        const infoMaterial = new THREE.MeshBasicMaterial({
          map: infoTexture,
          side: THREE.FrontSide,
          transparent: true,
          opacity: 1.0, // 完全不透明，增强可读性
          color: 0xffffff, // 不添加任何色调，保持清晰度
        })

        // 计算信息面板的水平偏移量 - 将其放置在右侧60%区域
        const infoHorizontalOffset = boardWidth / 2 - infoAreaWidth / 2

        const infoPanel = new THREE.Mesh(infoGeometry, infoMaterial)
        infoPanel.position.set(
          x + infoHorizontalOffset,
          y + 2,
          z - (side === 'left' ? -faceOffset : faceOffset)
        )
        infoPanel.rotation.y = wallRotationY
        infoPanel.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit } // 确保信息面板也添加展品数据
        scene.add(infoPanel)
        exhibitObjects.push(infoPanel) // 添加到可交互对象
      }

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
      scene.add(ambientLight)

      // 更新加载进度
      loadingProgress.value += 100 / exhibits.value.length
    },
    undefined,
    (error) => {
      console.error('加载纹理失败:', error)
    }
  )
}

// 创建古风石柱
const createPillar = (x: number, y: number, z: number) => {
  // 创建石柱身 - 使用八角柱形状更符合古代建筑风格
  const pillarGeometry = new THREE.CylinderGeometry(0.45, 0.5, 4, 8) // 8边形柱体，底部略宽
  const pillarMaterial = new THREE.MeshStandardMaterial({
    color: 0x6e6a61, // 青灰石色
    roughness: 0.9,
    metalness: 0.1,
    bumpScale: 0.05, // 增加石材纹理
  })
  const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial)
  pillar.position.set(x, y + 2, z)
  pillar.castShadow = true
  pillar.receiveShadow = true
  scene.add(pillar)

  // 石柱装饰花纹 - 凸起的雕刻
  const decorGeometry = new THREE.TorusGeometry(0.5, 0.05, 8, 8)
  const decorMaterial = new THREE.MeshStandardMaterial({
    color: 0x7d7468, // 稍微深一点的石色
    roughness: 0.8,
    metalness: 0.2,
  })
  // 上部装饰环
  const upperDecor = new THREE.Mesh(decorGeometry, decorMaterial)
  upperDecor.position.set(x, y + 3.5, z)
  upperDecor.rotation.x = Math.PI / 2 // 水平放置
  scene.add(upperDecor)

  // 下部装饰环
  const lowerDecor = new THREE.Mesh(decorGeometry, decorMaterial)
  lowerDecor.position.set(x, y + 0.5, z)
  lowerDecor.rotation.x = Math.PI / 2 // 水平放置
  scene.add(lowerDecor)

  // 柱顶装饰 - 古代飞檐风格
  const capGeometry = new THREE.BoxGeometry(1.2, 0.25, 1.2)
  const capMaterial = new THREE.MeshStandardMaterial({
    color: 0x592a11, // 深红褐色木雕
    roughness: 0.75,
    metalness: 0.1,
    bumpScale: 0.02,
  })
  const cap = new THREE.Mesh(capGeometry, capMaterial)
  cap.position.set(x, y + 4.1, z)
  cap.castShadow = true
  scene.add(cap)

  // 柱顶装饰二层
  const capTop = new THREE.BoxGeometry(0.9, 0.15, 0.9)
  const capTopMesh = new THREE.Mesh(capTop, capMaterial)
  capTopMesh.position.set(x, y + 4.3, z)
  capTopMesh.castShadow = true
  scene.add(capTopMesh)

  // 柱底座 - 更厚重的石基座
  const baseGeometry = new THREE.BoxGeometry(0.9, 0.2, 0.9)
  const baseMaterial = new THREE.MeshStandardMaterial({
    color: 0x393631, // 深灰色石基座
    roughness: 0.9,
    metalness: 0.1,
  })
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.set(x, y + 0.1, z)
  base.receiveShadow = true
  scene.add(base)

  // 增加一层基座，更稳固
  const lowerBaseGeometry = new THREE.BoxGeometry(1.1, 0.15, 1.1)
  const lowerBase = new THREE.Mesh(lowerBaseGeometry, baseMaterial)
  lowerBase.position.set(x, y + 0, z)
  lowerBase.receiveShadow = true
  scene.add(lowerBase)
}

// 创建古代宫廷吊灯
const createCeilingLight = (x: number, y: number, z: number) => {
  // 主体灯架 - 六角形铜架
  const frameGeometry = new THREE.CylinderGeometry(0.35, 0.35, 0.12, 6)
  const frameMaterial = new THREE.MeshStandardMaterial({
    color: 0x8a6642, // 古铜色
    emissive: 0x3d2e22,
    emissiveIntensity: 0.2,
    roughness: 0.6,
    metalness: 0.7,
  })
  const frame = new THREE.Mesh(frameGeometry, frameMaterial)
  frame.position.set(x, y - 0.15, z)
  frame.castShadow = true
  scene.add(frame)

  // 灯罩 - 古代纸质灯笼，八角形
  const lanternGeometry = new THREE.CylinderGeometry(0.3, 0.25, 0.4, 8)
  const lanternMaterial = new THREE.MeshStandardMaterial({
    color: 0xe8c39e, // 浅米色灯罩
    emissive: 0xd6924d,
    emissiveIntensity: 0.5,
    roughness: 0.9,
    metalness: 0.1,
    transparent: true,
    opacity: 0.9, // 半透明效果
  })
  const lantern = new THREE.Mesh(lanternGeometry, lanternMaterial)
  lantern.position.set(x, y - 0.3, z)
  lantern.castShadow = false // 灯罩不投射阴影
  scene.add(lantern)

  // 顶部装饰
  const topGeometry = new THREE.ConeGeometry(0.15, 0.15, 8)
  const topMaterial = new THREE.MeshStandardMaterial({
    color: 0x7c3a0a, // 褐红色木质顶部
    roughness: 0.8,
    metalness: 0.2,
  })
  const top = new THREE.Mesh(topGeometry, topMaterial)
  top.position.set(x, y + 0.05, z)
  top.castShadow = true
  scene.add(top)

  // 底部装饰
  const bottomGeometry = new THREE.ConeGeometry(0.1, 0.1, 8)
  bottomGeometry.translate(0, -0.05, 0) // 移动几何体原点
  const bottomMaterial = new THREE.MeshStandardMaterial({
    color: 0x7c3a0a, // 与顶部相同
    roughness: 0.8,
    metalness: 0.2,
  })
  const bottom = new THREE.Mesh(bottomGeometry, bottomMaterial)
  bottom.position.set(x, y - 0.5, z)
  bottom.rotation.x = Math.PI // 倒转圆锥
  bottom.castShadow = true
  scene.add(bottom)

  // 灯笼吊绳 - 古代麻绳
  const stringGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.35, 8)
  const stringMaterial = new THREE.MeshStandardMaterial({
    color: 0x5c4033, // 棕色麻绳
    roughness: 1.0,
    metalness: 0,
  })
  const string = new THREE.Mesh(stringGeometry, stringMaterial)
  string.position.set(x, y + 0.175, z)
  scene.add(string)

  // 点光源 - 温暖的火光效果
  const light = new THREE.PointLight(0xffc773, 1.5, 10) // 更温暖的黄色光，增加强度和范围
  light.position.set(x, y - 0.3, z)

  // 为灯光添加细微闪烁效果
  const flickerIntensity = 0.2 // 闪烁强度
  const flickerSpeed = 0.5 // 闪烁速度
  // 添加灯光ID用于动画识别
  light.userData = {
    isFlickering: true,
    originalIntensity: light.intensity,
    flickerIntensity: flickerIntensity,
    flickerSpeed: flickerSpeed,
    time: Math.random() * 1000, // 随机初始时间，让每个灯光闪烁不同步
  }

  scene.add(light)

  // 添加辅助光晕 - 光球
  const glowGeometry = new THREE.SphereGeometry(0.2, 16, 16)
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: 0xffe0a3,
    transparent: true,
    opacity: 0.4,
    blending: THREE.AdditiveBlending,
  })
  const glow = new THREE.Mesh(glowGeometry, glowMaterial)
  glow.position.copy(light.position)
  scene.add(glow)
}

// 创建文本标签
const createTextLabel = (
  text: string,
  x: number,
  y: number,
  z: number,
  rotationY: number,
  scale: number = 1,
  bgColor: string = '#ffffff'
) => {
  // 创建画布并绘制文本
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return

  canvas.width = 256
  canvas.height = 64

  context.fillStyle = bgColor
  context.fillRect(0, 0, canvas.width, canvas.height)

  context.font = '24px KaiTi, STKaiti, serif'
  context.fillStyle = '#000000'
  context.textAlign = 'center'
  context.textBaseline = 'middle'
  context.fillText(text, canvas.width / 2, canvas.height / 2)

  // 创建纹理和材质
  const texture = new THREE.CanvasTexture(canvas)
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.DoubleSide,
    transparent: true,
  })

  // 创建平面几何体
  const geometry = new THREE.PlaneGeometry(1.5 * scale, 0.4 * scale)
  const mesh = new THREE.Mesh(geometry, material)
  mesh.position.set(x, y, z)
  mesh.rotation.y = rotationY

  scene.add(mesh)
  exhibitObjects.push(mesh)
}

// 键盘按下事件处理
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = true
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = true
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = true
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = true
      break
    case 'Space':
      if (canJump.value) {
        velocity.y += 350
      }
      canJump.value = false
      break
    case 'Escape':
      controls.unlock()
      break
  }
}

// 键盘释放事件处理
const onKeyUp = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = false
      break
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = false
      break
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = false
      break
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = false
      break
  }
}

// 鼠标点击事件处理
const onMouseClick = () => {
  if (!controls.isLocked && !isMobileDevice.value) {
    controls.lock()
    return
  }

  checkExhibitInteraction()
}

// 触摸结束事件处理（移动设备）
const onTouchEnd = (event: TouchEvent) => {
  // 防止长按弹出菜单等行为
  event.preventDefault()

  if (event.touches.length > 0 || event.changedTouches.length === 0) return

  // 获取触摸位置
  const touch = event.changedTouches[0]
  checkExhibitInteraction({
    clientX: touch.clientX,
    clientY: touch.clientY,
  })
}

// 检测与展品的交互
const checkExhibitInteraction = (touchPoint?: {
  clientX: number
  clientY: number
}) => {
  // 使用射线检测点击的对象
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()

  // 如果是触摸事件，计算相对坐标
  if (touchPoint) {
    mouse.x = (touchPoint.clientX / window.innerWidth) * 2 - 1
    mouse.y = -(touchPoint.clientY / window.innerHeight) * 2 + 1
  }

  // 设置射线起点和方向（从相机发出）
  raycaster.setFromCamera(mouse, camera)

  // 检测与展品的交叉
  const intersects = raycaster.intersectObjects(exhibitObjects)

  if (intersects.length > 0) {
    const object = intersects[0].object
    if (object.userData && object.userData.isExhibit) {
      // 显示展品详情
      selectedExhibit.value = object.userData.exhibit
      currentImageIndex.value = 0 // 重置图片索引为第一张

      // 桌面设备才需要解锁
      if (!isMobileDevice.value) {
        controls.unlock()
      }
    }
  }
}

// 窗口大小变化处理
const onWindowResize = () => {
  if (!camera || !renderer) return

  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)

  // 更新灯光闪烁效果
  scene.traverse((object) => {
    if (
      object instanceof THREE.PointLight &&
      object.userData &&
      object.userData.isFlickering
    ) {
      const light = object as THREE.PointLight
      // 更新灯光时间
      light.userData.time += 0.016 // 约16ms，60fps

      // 计算闪烁强度变化
      const flickerValue =
        Math.sin(light.userData.time * light.userData.flickerSpeed) *
        light.userData.flickerIntensity

      // 应用闪烁效果到灯光强度
      light.intensity = light.userData.originalIntensity + flickerValue
    }
  })

  if (controls.isLocked) {
    // 计算时间增量
    const time = performance.now()
    const delta = (time - prevTime) / 1000

    // 应用阻尼
    velocity.x -= velocity.x * 10.0 * delta
    velocity.z -= velocity.z * 10.0 * delta
    velocity.y -= 9.8 * 100.0 * delta // 应用重力

    // 根据按键状态计算方向
    direction.z = Number(moveForward.value) - Number(moveBackward.value)
    direction.x = Number(moveRight.value) - Number(moveLeft.value)
    direction.normalize() // 确保对角线移动不会更快

    // 应用移动速度（减缓移动速率）
    if (moveForward.value || moveBackward.value)
      velocity.z -= direction.z * 250.0 * delta
    if (moveLeft.value || moveRight.value)
      velocity.x -= direction.x * 250.0 * delta

    // 移动控制器 - 仅在XZ平面上移动，确保Y轴位置固定
    const oldY = controls.getObject().position.y // 保存当前Y轴位置
    controls.moveRight(-velocity.x * delta)
    controls.moveForward(-velocity.z * delta)
    controls.getObject().position.y = oldY // 恢复Y轴位置，确保不会"飞起来"

    // 限制移动范围（保持在走廊内）
    const position = controls.getObject().position

    // 前后端墙碰撞检测
    if (position.x < boundaries.front) position.x = boundaries.front
    if (position.x > boundaries.end) position.x = boundaries.end

    // Z轴限制（走廊宽度）
    if (position.z < -4.5) position.z = -4.5
    if (position.z > 4.5) position.z = 4.5

    // Y轴限制（高度）
    if (position.y < 1.6) {
      velocity.y = 0
      position.y = 1.6
      canJump.value = true
    }

    prevTime = time
  }

  renderer.render(scene, camera)
}

// 返回首页
const goBack = () => {
  router.push('/')
}

// 关闭展品面板
const closeExhibitPanel = () => {
  selectedExhibit.value = null
  currentImageIndex.value = 0 // 重置图片索引

  // 保存当前控制器位置和旋转，避免视角变化
  const currentPosition = controls.getObject().position.clone()
  const currentRotation = new THREE.Euler().copy(controls.getObject().rotation)

  if (controls && !isMobileDevice.value) {
    controls.lock() // 只在非移动设备上重新锁定控制

    // 恢复之前的位置和旋转
    setTimeout(() => {
      controls.getObject().position.copy(currentPosition)
      controls.getObject().rotation.copy(currentRotation)
    }, 10)
  }
}

// 检测是否为移动设备
const isMobileDevice = ref(false)

// 检测设备类型
const checkMobileDevice = () => {
  isMobileDevice.value =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
}

// 处理移动摇杆触摸开始
const handleMoveJoystickStart = (event: TouchEvent | MouseEvent) => {
  console.log('移动摇杆触摸开始!', event.type)
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸
  moveActive.value = true

  let clientX, clientY

  if ('touches' in event) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else {
    clientX = (event as MouseEvent).clientX
    clientY = (event as MouseEvent).clientY
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveStartPosition.x = clientX - joystickRect.left
  moveStartPosition.y = clientY - joystickRect.top
  moveCurrentPosition.x = moveStartPosition.x
  moveCurrentPosition.y = moveStartPosition.y

  // 更新摇杆位置
  updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)

  // 添加移动和结束事件监听
  document.addEventListener('mousemove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleMoveJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleMoveJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleMoveJoystickEnd, {
    passive: false,
  })
}

// 处理移动摇杆移动
const handleMoveJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!moveActive.value) return
  event.preventDefault()
  // 不再停止事件冒泡，允许同时处理其他触摸

  let clientX, clientY

  if ('touches' in event && event.touches.length > 0) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else if ('changedTouches' in event && event.changedTouches.length > 0) {
    clientX = event.changedTouches[0].clientX
    clientY = event.changedTouches[0].clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = moveControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  moveCurrentPosition.x = clientX - joystickRect.left
  moveCurrentPosition.y = clientY - joystickRect.top

  // 限制在基座范围内
  const maxDistance = 50
  const dx = moveCurrentPosition.x - moveStartPosition.x
  const dy = moveCurrentPosition.y - moveStartPosition.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance > maxDistance) {
    moveCurrentPosition.x = moveStartPosition.x + (dx * maxDistance) / distance
    moveCurrentPosition.y = moveStartPosition.y + (dy * maxDistance) / distance
  }

  // 更新摇杆位置
  updateMoveJoystickPosition(moveCurrentPosition.x, moveCurrentPosition.y)

  // 计算移动方向
  moveDirection.x = (moveCurrentPosition.x - moveStartPosition.x) / maxDistance // 左右移动，-1到1
  moveDirection.z = (moveStartPosition.y - moveCurrentPosition.y) / maxDistance // 前后移动，-1到1

  // 根据摇杆位置设置移动状态
  moveForward.value = moveDirection.z > 0.2
  moveBackward.value = moveDirection.z < -0.2
  moveLeft.value = moveDirection.x < -0.2
  moveRight.value = moveDirection.x > 0.2
}

// 处理移动摇杆结束
const handleMoveJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  moveActive.value = false

  // 停止所有移动
  moveForward.value = false
  moveBackward.value = false
  moveLeft.value = false
  moveRight.value = false

  // 复位方向向量
  moveDirection.x = 0
  moveDirection.z = 0

  // 复位摇杆位置（动画效果）
  const joystickBase = moveControl.value?.querySelector('.joystick-base')
  if (joystickBase) {
    const baseRect = joystickBase.getBoundingClientRect()
    const centerX = baseRect.width / 2
    const centerY = baseRect.height / 2
    updateMoveJoystickPosition(centerX, centerY)
  } else {
    updateMoveJoystickPosition(moveStartPosition.x, moveStartPosition.y)
  }

  // 移除事件监听
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)
}

// 更新移动摇杆位置
const updateMoveJoystickPosition = (x: number, y: number) => {
  moveJoystickStyle.value = {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  }
  console.log('更新移动摇杆位置:', x, y)
}

// 处理摇杆触摸开始
const handleJoystickStart = (event: TouchEvent | MouseEvent) => {
  console.log('摇杆触摸开始!', event.type)
  event.preventDefault()
  event.stopPropagation()
  lookActive.value = true

  let clientX, clientY

  if ('touches' in event) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else {
    clientX = (event as MouseEvent).clientX
    clientY = (event as MouseEvent).clientY
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  lookStartPosition.x = clientX - joystickRect.left
  lookStartPosition.y = clientY - joystickRect.top
  lookCurrentPosition.x = lookStartPosition.x
  lookCurrentPosition.y = lookStartPosition.y

  // 记录当前相机旋转
  if (camera) {
    cameraRotation.x = camera.rotation.x
    cameraRotation.y = camera.rotation.y
  }

  // 更新摇杆位置
  updateJoystickPosition(lookStartPosition.x, lookStartPosition.y)

  // 添加移动和结束事件监听
  document.addEventListener('mousemove', handleJoystickMove, { passive: false })
  document.addEventListener('touchmove', handleJoystickMove, { passive: false })
  document.addEventListener('mouseup', handleJoystickEnd, { passive: false })
  document.addEventListener('touchend', handleJoystickEnd, { passive: false })
}

// 处理摇杆移动
const handleJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!lookActive.value) return
  event.preventDefault()

  let clientX, clientY

  if ('touches' in event && event.touches.length > 0) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else if ('changedTouches' in event && event.changedTouches.length > 0) {
    clientX = event.changedTouches[0].clientX
    clientY = event.changedTouches[0].clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  lookCurrentPosition.x = clientX - joystickRect.left
  lookCurrentPosition.y = clientY - joystickRect.top

  // 限制在基座范围内
  const maxDistance = 50 // 增大移动范围
  const dx = lookCurrentPosition.x - lookStartPosition.x
  const dy = lookCurrentPosition.y - lookStartPosition.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance > maxDistance) {
    lookCurrentPosition.x = lookStartPosition.x + (dx * maxDistance) / distance
    lookCurrentPosition.y = lookStartPosition.y + (dy * maxDistance) / distance
  }

  // 更新摇杆位置
  updateJoystickPosition(lookCurrentPosition.x, lookCurrentPosition.y)

  // 根据摇杆位置旋转相机
  if (controls) {
    // 计算旋转量，降低灵敏度使移动更平滑
    const rotateX = (lookCurrentPosition.x - lookStartPosition.x) / 250 // 减小系数增加平滑度
    const rotateY = (lookCurrentPosition.y - lookStartPosition.y) / 250 // 减小系数增加平滑度

    // 使用控制器的旋转方法
    controls.getObject().rotation.y -= rotateX // 左右移动摇杆，相机左右旋转

    // Y轴旋转（上下看）需要限制在一定范围内
    const currentRotationX = controls.getObject().rotation.x - rotateY // 改变符号使方向一致：上推摇杆=向上看
    const maxRotationX = Math.PI / 3 // 60度
    if (currentRotationX < maxRotationX && currentRotationX > -maxRotationX) {
      controls.getObject().rotation.x = currentRotationX
    }
  }
}

// 处理摇杆结束
const handleJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  lookActive.value = false

  // 复位摇杆位置（动画效果）
  const joystickBase = lookControl.value?.querySelector('.joystick-base')
  if (joystickBase) {
    const baseRect = joystickBase.getBoundingClientRect()
    const centerX = baseRect.width / 2
    const centerY = baseRect.height / 2
    updateJoystickPosition(centerX, centerY)
  } else {
    // 回退到原始复位方法
    updateJoystickPosition(lookStartPosition.x, lookStartPosition.y)
  }

  // 移除事件监听
  document.removeEventListener('mousemove', handleJoystickMove)
  document.removeEventListener('touchmove', handleJoystickMove)
  document.removeEventListener('mouseup', handleJoystickEnd)
  document.removeEventListener('touchend', handleJoystickEnd)
}

// 更新摇杆位置
const updateJoystickPosition = (x: number, y: number) => {
  joystickStyle.value = {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  }
  console.log('更新摇杆位置:', x, y)
}

// 组件挂载时初始化场景
onMounted(() => {
  // 初始化区域信息
  initRegionInfo()

  // 初始化3D场景
  initScene()

  // 为摇杆添加事件监听
  setTimeout(() => {
    if (lookControl.value) {
      console.log('正在绑定摇杆事件...')
      lookControl.value.addEventListener('mousedown', handleJoystickStart, {
        passive: false,
      })
      lookControl.value.addEventListener('touchstart', handleJoystickStart, {
        passive: false,
      })
    } else {
      console.log('摇杆元素未找到!')
    }
  }, 1000)
})

// 移动设备屏幕滑动控制视角的相关变量（已禁用，改为使用右侧摇杆）
// let touchStartX = 0
// let touchStartY = 0
// let touchPreviousX = 0
// let touchPreviousY = 0
// let touchMoving = false
// let activeLookTouchId: number | null = null

// 标准FPS相机控制变量
let cameraYaw = 0 // 水平旋转角度（绕Y轴）
let cameraPitch = 0 // 垂直旋转角度（绕X轴）
const maxPitch = Math.PI / 2 - 0.1 // 最大俯仰角（89度），防止万向锁
const minPitch = -Math.PI / 2 + 0.1 // 最小俯仰角（-89度）
const mouseSensitivity = 0.002 // 鼠标灵敏度
const touchSensitivity = 0.006 // 触摸灵敏度（稍高一些）

// 标准FPS相机更新函数
const updateCameraRotation = (
  deltaX: number,
  deltaY: number,
  sensitivity: number
) => {
  if (!controls) return

  // 在移动设备上，我们不检查 isLocked 状态，因为我们总是希望能够控制视角
  // 在桌面设备上，只有锁定时才允许控制
  if (!isMobileDevice.value && !controls.isLocked) return

  // 更新欧拉角
  cameraYaw -= deltaX * sensitivity
  cameraPitch -= deltaY * sensitivity

  // 限制俯仰角，防止翻转和万向锁
  cameraPitch = Math.max(minPitch, Math.min(maxPitch, cameraPitch))

  // 应用旋转到相机
  const camera = controls.getObject()
  camera.rotation.order = 'YXZ'
  camera.rotation.y = cameraYaw
  camera.rotation.x = cameraPitch
}

// 右侧视角摇杆事件处理
const handleLookJoystickStart = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  // 移除 stopPropagation，允许其他摇杆同时工作
  // event.stopPropagation()
  lookActive.value = true

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 重置摇杆位置
  lookStartPosition.x = joystickRect.width / 2
  lookStartPosition.y = joystickRect.height / 2
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0

  // 更新摇杆位置
  updateLookJoystickPosition(0, 0)

  // 使用不同的事件监听器名称，避免与移动摇杆冲突
  document.addEventListener('mousemove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('touchmove', handleLookJoystickMove, {
    passive: false,
  })
  document.addEventListener('mouseup', handleLookJoystickEnd, {
    passive: false,
  })
  document.addEventListener('touchend', handleLookJoystickEnd, {
    passive: false,
  })
}

const handleLookJoystickMove = (event: TouchEvent | MouseEvent) => {
  if (!lookActive.value) return
  event.preventDefault()

  let clientX, clientY

  if ('touches' in event && event.touches.length > 0) {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  } else if ('clientX' in event) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    return
  }

  const joystickRect = lookControl.value?.getBoundingClientRect()
  if (!joystickRect) return

  // 计算相对于摇杆中心的偏移
  const centerX = joystickRect.left + joystickRect.width / 2
  const centerY = joystickRect.top + joystickRect.height / 2
  const offsetX = clientX - centerX
  const offsetY = clientY - centerY

  // 限制摇杆移动范围
  const maxDistance = 40 // 最大移动距离
  const distance = Math.sqrt(offsetX * offsetX + offsetY * offsetY)

  if (distance <= maxDistance) {
    lookCurrentPosition.x = offsetX
    lookCurrentPosition.y = offsetY
  } else {
    // 限制在圆形范围内
    const angle = Math.atan2(offsetY, offsetX)
    lookCurrentPosition.x = Math.cos(angle) * maxDistance
    lookCurrentPosition.y = Math.sin(angle) * maxDistance
  }

  // 更新摇杆位置
  updateLookJoystickPosition(lookCurrentPosition.x, lookCurrentPosition.y)

  // 应用视角旋转 - 使用与手指触摸相同的灵敏度
  // 将摇杆位置转换为类似手指移动的像素值
  // 摇杆最大移动距离是40px，我们需要将其映射到合理的像素移动范围
  // 原来手指触摸的移动距离通常是几个到几十个像素
  const pixelMultiplier = 0.1 // 降低灵敏度，使其更接近手指触摸的感觉
  const deltaX = lookCurrentPosition.x * pixelMultiplier
  const deltaY = lookCurrentPosition.y * pixelMultiplier

  // 使用与手指触摸相同的灵敏度
  updateCameraRotation(deltaX, deltaY, touchSensitivity)
}

const handleLookJoystickEnd = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()
  lookActive.value = false

  // 复位摇杆位置（动画效果）
  lookCurrentPosition.x = 0
  lookCurrentPosition.y = 0
  updateLookJoystickPosition(0, 0)

  // 移除事件监听
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)
}

const updateLookJoystickPosition = (x: number, y: number) => {
  if (lookJoystickHandle.value) {
    lookJoystickStyle.value = {
      left: '50%',
      top: '50%',
      transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`,
    }
  }
}

// 处理Canvas触摸开始（已禁用，改为使用右侧摇杆控制视角）
/*
const handleCanvasTouchStart = (event: TouchEvent) => {
  // 获取当前触摸点ID，支持多点触控
  for (let i = 0; i < event.changedTouches.length; i++) {
    const touch = event.changedTouches[i]

    // 检查是否触摸在摇杆上，如果是则忽略
    const target = document.elementFromPoint(
      touch.clientX,
      touch.clientY
    ) as HTMLElement
    if (
      target &&
      (target.closest('.move-joystick') || target.closest('.joystick-base'))
    ) {
      continue // 忽略摇杆区域的触摸
    }

    // 记录这个触摸点作为视角控制触摸点
    touchStartX = touch.clientX
    touchStartY = touch.clientY
    touchPreviousX = touchStartX
    touchPreviousY = touchStartY
    touchMoving = true

    // 存储此触摸点ID，用于后续跟踪
    activeLookTouchId = touch.identifier

    // 添加移动和结束事件监听
    renderer.domElement.addEventListener('touchmove', handleCanvasTouchMove, {
      passive: false,
    })
    renderer.domElement.addEventListener('touchend', handleCanvasTouchEnd, {
      passive: false,
    })
    renderer.domElement.addEventListener('touchcancel', handleCanvasTouchEnd, {
      passive: false,
    })

    break // 只需要找到一个可用的触摸点用于视角控制
  }
}

// 处理Canvas触摸移动
const handleCanvasTouchMove = (event: TouchEvent) => {
  if (!touchMoving || activeLookTouchId === null) return

  // 寻找我们正在跟踪的触摸点
  let foundTouch = false
  let touchX = 0
  let touchY = 0

  for (let i = 0; i < event.touches.length; i++) {
    const touch = event.touches[i]
    if (touch.identifier === activeLookTouchId) {
      touchX = touch.clientX
      touchY = touch.clientY
      foundTouch = true
      break
    }
  }

  if (!foundTouch) return // 没有找到我们正在跟踪的触摸点

  // 计算触摸点的移动距离
  const movementX = touchX - touchPreviousX
  const movementY = touchY - touchPreviousY

  // 更新前一个触摸点的位置
  touchPreviousX = touchX
  touchPreviousY = touchY

  // 调整旋转灵敏度
  const rotationSpeed = 0.002

  // 应用旋转 - 与鼠标类似的旋转逻辑，但增加灵敏度
  if (controls && controls.isLocked) {
    // 水平旋转（左右）- 增加旋转速度
    controls.getObject().rotation.y -= movementX * rotationSpeed * 3.0

    // 垂直旋转（上下）- 有角度限制，增加旋转速度
    const currentRotationX =
      controls.getObject().rotation.x - movementY * rotationSpeed * 3.0
    const maxRotationX = Math.PI / 3 // 60度
    if (currentRotationX < maxRotationX && currentRotationX > -maxRotationX) {
      controls.getObject().rotation.x = currentRotationX
    }
  }
}

// 处理Canvas触摸结束
const handleCanvasTouchEnd = (event: TouchEvent) => {
  // 检查是否是我们跟踪的触摸点结束了
  let isActiveTouchEnded = false

  for (let i = 0; i < event.changedTouches.length; i++) {
    const touch = event.changedTouches[i]
    if (activeLookTouchId !== null && touch.identifier === activeLookTouchId) {
      isActiveTouchEnded = true
      break
    }
  }

  // 如果不是我们跟踪的触摸点，不做任何处理
  if (!isActiveTouchEnded) return

  // 重置状态
  touchMoving = false
  activeLookTouchId = null

  // 移除事件监听
  renderer.domElement.removeEventListener('touchmove', handleCanvasTouchMove)
  renderer.domElement.removeEventListener('touchend', handleCanvasTouchEnd)
  renderer.domElement.removeEventListener('touchcancel', handleCanvasTouchEnd)
}
*/

// 组件卸载前清理资源
onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown)
  document.removeEventListener('keyup', onKeyUp)
  document.removeEventListener('mousemove', handleLookJoystickMove)
  document.removeEventListener('touchmove', handleLookJoystickMove)
  document.removeEventListener('mouseup', handleLookJoystickEnd)
  document.removeEventListener('touchend', handleLookJoystickEnd)
  document.removeEventListener('mousemove', handleMoveJoystickMove)
  document.removeEventListener('touchmove', handleMoveJoystickMove)
  document.removeEventListener('mouseup', handleMoveJoystickEnd)
  document.removeEventListener('touchend', handleMoveJoystickEnd)

  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener('click', onMouseClick)
    if (isMobileDevice.value) {
      renderer.domElement.removeEventListener('touchend', onTouchEnd)
      // 触摸控制事件监听器已被注释掉，无需移除
      // renderer.domElement.removeEventListener('touchstart', handleCanvasTouchStart)
      // renderer.domElement.removeEventListener('touchmove', handleCanvasTouchMove)
      // renderer.domElement.removeEventListener('touchend', handleCanvasTouchEnd)
    }
  }

  window.removeEventListener('resize', onWindowResize)

  cancelAnimationFrame(animationFrameId)

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }
})
</script>

<style scoped lang="scss">
.gallery-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.scene-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;

  & > * {
    pointer-events: auto;
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .icon {
    margin-right: 5px;
    font-size: 1.2rem;
  }
}

.gallery-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

  h1 {
    font-size: 2rem;
    margin: 0 0 5px 0;
    font-weight: bold;
  }

  p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }
}

.loading-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;

  .loading-text {
    margin-bottom: 10px;
    font-size: 1.2rem;
  }

  .progress-bar {
    width: 300px;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;

    .progress {
      height: 100%;
      background-color: #7c4700; // 与面板标题色一致
      transition: width 0.3s ease;
    }
  }
}

.exhibit-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 900px;
  max-height: 85vh;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;

  &.active {
    opacity: 1;
    visibility: visible;
  }

  .panel-header {
    padding: 15px 20px;
    background-color: #7c4700; // 古铜色，更古朴的风格
    color: #fff3dd; // 温暖的米色文字
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 4px solid rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      font-family: 'STKaiti', 'KaiTi', serif;
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 1.8rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
      }
    }
  }

  .panel-content {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .image-carousel {
      flex-shrink: 0;
      margin-bottom: 20px;
      height: 380px; /* 固定高度确保可见性 */
    }

    .exhibit-info-container {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;

      .exhibit-info {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .info-card {
          background: linear-gradient(
            135deg,
            rgba(255, 248, 235, 0.95),
            rgba(245, 235, 215, 0.9)
          );
          border-radius: 12px;
          border: 1px solid rgba(124, 71, 0, 0.15);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
          }

          .card-header {
            background: linear-gradient(135deg, #7c4700, #a0621a);
            color: white;
            padding: 12px 16px;

            h3 {
              margin: 0;
              font-size: 1.1rem;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;
              font-family: 'STKaiti', 'KaiTi', serif;

              i {
                font-size: 1rem;
                opacity: 0.9;
              }
            }
          }

          .card-content {
            padding: 16px;

            .info-row {
              display: flex;
              align-items: flex-start;
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                font-weight: 600;
                color: #7c4700;
                min-width: 80px;
                font-size: 0.95rem;
              }

              .value {
                flex: 1;
                color: #333;
                font-size: 0.95rem;
                line-height: 1.5;

                &.year-badge {
                  background: linear-gradient(135deg, #8b4513, #cd853f);
                  color: white;
                  padding: 4px 12px;
                  border-radius: 20px;
                  font-size: 0.85rem;
                  font-weight: 500;
                  display: inline-block;
                }

                &.period-badge {
                  background: linear-gradient(135deg, #b8860b, #daa520);
                  color: white;
                  padding: 3px 10px;
                  border-radius: 15px;
                  font-size: 0.8rem;
                  font-weight: 500;
                  display: inline-block;
                }
              }
            }

            .history-description {
              font-size: 1rem;
              line-height: 1.7;
              color: #333;
              font-family: 'STKaiti', 'KaiTi', serif;
              text-align: justify;
              margin: 0;
            }

            .content-text {
              font-size: 0.95rem;
              line-height: 1.7;
              color: #444;
              text-align: justify;

              h4 {
                color: #7c4700;
                font-size: 1rem;
                margin: 16px 0 8px 0;
                font-weight: 600;
                border-left: 4px solid #7c4700;
                padding-left: 12px;
              }

              p {
                margin: 8px 0;
              }
            }

            .historical-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              margin-bottom: 12px;

              .historical-tag {
                background: linear-gradient(
                  135deg,
                  rgba(124, 71, 0, 0.1),
                  rgba(160, 98, 26, 0.1)
                );
                color: #7c4700;
                padding: 4px 10px;
                border-radius: 15px;
                font-size: 0.8rem;
                font-weight: 500;
                border: 1px solid rgba(124, 71, 0, 0.2);
              }
            }

            .significance-text,
            .impact-text,
            .context-text,
            .background-text {
              font-size: 0.95rem;
              line-height: 1.6;
              color: #444;
              text-align: justify;
              margin: 0;
              font-style: italic;
            }
          }
        }

        .meta-info {
          margin-top: 20px;
          padding: 12px 16px;
          background: rgba(240, 235, 220, 0.5);
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.1);

          .meta-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.85rem;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              font-weight: 500;
              color: #666;
              min-width: 70px;
            }

            .meta-value {
              color: #888;
            }
          }
        }
      }
    }
  }

  .panel-actions {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    .detail-btn {
      padding: 10px 25px;
      background-color: #7c4700; // 古铜色
      color: #fff3dd; // 暖色调
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.3s ease;
      border: none;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); // 增强阴影效果

      &:hover {
        background-color: darken(#7c4700, 10%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 768px) {
    width: 95%;
    max-height: 90vh;

    .panel-content {
      padding: 15px;

      .image-carousel {
        height: 300px;
      }
    }
  }

  @media screen and (max-width: 480px) {
    .panel-content {
      .image-carousel {
        height: 220px;
      }

      .exhibit-info-container .exhibit-info {
        .description {
          font-size: 1rem;
        }
      }
    }

    .panel-header h2 {
      font-size: 1.3rem;
    }
  }
}

.controls-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.move-joystick {
  position: absolute;
  bottom: 80px;
  left: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      pointer-events: none;
      transition: transform 0.05s ease-out;
    }
  }
}

.image-carousel {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  .exhibit-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: rgba(0, 0, 0, 0.05);
    transition: opacity 0.3s ease;
  }

  .thumbnail-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 0;

    .thumbnails {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 100%;
      padding: 0 15px;
      overflow-x: auto;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        height: 5px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 3px;
      }

      .thumbnail {
        flex: 0 0 auto;
        width: 60px;
        height: 50px;
        border: 2px solid transparent;
        border-radius: 3px;
        overflow: hidden;
        transition: all 0.2s ease;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        &:hover {
          transform: translateY(-2px);
        }

        &.active {
          border-color: #7c4700; // 古铜色
          transform: translateY(-3px);
        }
      }
    }
  }

  .carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 10;
    padding: 0 15px;

    .control-btn {
      background-color: rgba(0, 0, 0, 0.6);
      border: none;
      color: white;
      font-size: 1.8rem;
      font-weight: bold;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(124, 71, 0, 0.8); // 古铜色
        transform: scale(1.1);
      }
    }
  }
}

.detail-content {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 5px;

  h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #7c4700; // 古铜色
    border-bottom: 1px solid rgba(124, 71, 0, 0.3); // 更明显的分隔线
    padding-bottom: 5px;
  }

  .content-text {
    font-size: 1rem;
    line-height: 1.8;
    color: #333;
    text-align: justify;
  }
}

.fullscreen-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;

  .fullscreen-image-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;

    img {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
      border: 2px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    }

    .close-fullscreen-btn {
      position: absolute;
      top: -20px;
      right: -20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid white;
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #7c4700; // 古铜色
        transform: rotate(90deg);
      }
    }
  }
}

/* 图标样式 */
.icon-clock::before {
  content: '🕰️';
}
.icon-scroll::before {
  content: '📜';
}
.icon-book::before {
  content: '📖';
}
.icon-star::before {
  content: '⭐';
}
.icon-people::before {
  content: '👥';
}
.icon-context::before {
  content: '🏛️';
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .exhibit-panel {
    .panel-content {
      .exhibit-info-container .exhibit-info {
        gap: 15px;

        .info-card {
          .card-header h3 {
            font-size: 1rem;
          }

          .card-content {
            padding: 12px;

            .info-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .label {
                min-width: auto;
                font-size: 0.9rem;
              }

              .value {
                font-size: 0.9rem;
              }
            }

            .history-description {
              font-size: 0.95rem;
            }

            .content-text {
              font-size: 0.9rem;

              h4 {
                font-size: 0.95rem;
              }
            }
          }
        }

        .meta-info {
          padding: 10px 12px;

          .meta-row {
            font-size: 0.8rem;

            .meta-label {
              min-width: 60px;
            }
          }
        }
      }
    }
  }
}

/* 自定义滚动条样式 */
.exhibit-info-container::-webkit-scrollbar {
  width: 6px;
}

.exhibit-info-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.exhibit-info-container::-webkit-scrollbar-thumb {
  background: rgba(124, 71, 0, 0.5); // 古铜色
  border-radius: 3px;
}

.exhibit-info-container::-webkit-scrollbar-thumb:hover {
  background: rgba(124, 71, 0, 0.7); // 深一点的古铜色
}

.look-joystick {
  position: absolute;
  bottom: 80px;
  right: 40px;
  width: 150px;
  height: 150px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  user-select: none;

  .joystick-base {
    width: 120px;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 100, 100, 0.6);
    border-radius: 50%;
    position: relative;
    touch-action: none;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 100, 100, 0.2);
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .joystick-handle {
      width: 50px;
      height: 50px;
      background-color: rgba(255, 100, 100, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      touch-action: none;
      transition: transform 0.05s ease-out;
    }
  }
}
</style>

