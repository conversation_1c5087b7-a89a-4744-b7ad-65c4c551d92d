<template>
  <view class="archives-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <image class="header-bg"
             src="/static/images/archives-bg.jpg"
             mode="aspectFill">
      </image>
      <view class="header-overlay">
        <text class="header-title">档案故事</text>
        <text class="header-subtitle">历史档案文献记录</text>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-box">
        <image src="/static/icons/search.svg"
               class="search-icon"></image>
        <input v-model="searchKeyword"
               placeholder="搜索档案标题、年代、类型..."
               class="search-input"
               @input="onSearchInput" />
        <text v-if="searchKeyword"
              @click="clearSearch"
              class="clear-btn">×</text>
      </view>

      <view class="search-suggestions"
            v-if="showSuggestions">
        <text v-for="suggestion in searchSuggestions"
              :key="suggestion"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)">
          {{ suggestion }}
        </text>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-section">
      <scroll-view class="category-scroll"
                   scroll-x="true"
                   show-scrollbar="false">
        <view class="category-list">
          <view v-for="category in categories"
                :key="category.value"
                :class="['category-item', { active: currentCategory === category.value }]"
                @click="switchCategory(category.value)">
            <image :src="category.icon"
                   class="category-icon"></image>
            <text class="category-label">{{ category.label }}</text>
            <text class="category-count">{{ category.count }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 时间轴导航 -->
    <view class="timeline-nav">
      <text class="timeline-title">历史时期</text>
      <scroll-view class="timeline-scroll"
                   scroll-x="true"
                   show-scrollbar="false">
        <view class="timeline-list">
          <view v-for="period in timePeriods"
                :key="period.value"
                :class="['period-item', { active: currentPeriod === period.value }]"
                @click="switchPeriod(period.value)">
            {{ period.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 档案列表 -->
    <view class="content-section">
      <view v-if="loading"
            class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else
            class="archives-list">
        <view v-for="archive in filteredArchives"
              :key="archive.id"
              class="archive-item"
              @click="viewArchiveDetail(archive)">

          <!-- 档案封面 -->
          <view class="archive-cover">
            <image :src="archive.coverImage"
                   mode="aspectFill"
                   class="cover-image"
                   @error="onImageError">
            </image>
            <view class="cover-overlay">
              <view class="archive-type">{{ archive.type }}</view>
              <view class="archive-period">{{ archive.period }}</view>
            </view>
            <view v-if="archive.isDigitized"
                  class="digitized-mark">
              <image src="/static/icons/digital.svg"
                     class="mark-icon"></image>
            </view>
          </view>

          <!-- 档案信息 -->
          <view class="archive-info">
            <text class="archive-title">{{ archive.title }}</text>
            <text class="archive-year">{{ archive.year }}</text>
            <text class="archive-desc">{{ archive.description }}</text>

            <!-- 标签 -->
            <view class="archive-tags">
              <text v-for="tag in archive.tags"
                    :key="tag"
                    class="archive-tag">{{ tag }}</text>
            </view>

            <!-- 统计信息 -->
            <view class="archive-stats">
              <view class="stat-item">
                <image src="/static/icons/page.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ archive.pageCount }}页</text>
              </view>
              <view class="stat-item">
                <image src="/static/icons/view.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ archive.viewCount }}次浏览</text>
              </view>
              <view class="stat-item">
                <image src="/static/icons/date.svg"
                       class="stat-icon"></image>
                <text class="stat-text">{{ archive.lastUpdated }}</text>
              </view>
            </view>

            <!-- 历史背景简介 -->
            <view class="historical-context"
                  v-if="archive.historicalContext">
              <text class="context-label">历史背景</text>
              <text class="context-text">{{ archive.historicalContext }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="archive-actions">
            <view class="action-btn primary"
                  @click.stop="readArchive(archive)">
              <image src="/static/icons/read.svg"
                     class="action-icon"></image>
              <text>查看详情</text>
            </view>
            <view class="action-btn secondary"
                  @click.stop="showTimeline(archive)">
              <image src="/static/icons/timeline.svg"
                     class="action-icon"></image>
              <text>时间线</text>
            </view>
            <view class="action-btn tertiary"
                  @click.stop="shareArchive(archive)">
              <image src="/static/icons/share.svg"
                     class="action-icon"></image>
              <text>分享</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && filteredArchives.length === 0"
            class="empty-state">
        <image src="/static/icons/empty-archives.svg"
               class="empty-icon"></image>
        <text class="empty-title">暂无档案数据</text>
        <text class="empty-desc">{{ getEmptyStateText() }}</text>
      </view>
    </view>

    <!-- 历史时间线浮层 -->
    <view v-if="showTimelineModal"
          class="timeline-overlay"
          @click="closeTimeline">
      <view class="timeline-panel"
            @click.stop>
        <view class="panel-header">
          <text class="panel-title">{{ currentArchive.title }} - 历史时间线</text>
          <text @click="closeTimeline"
                class="close-btn">×</text>
        </view>

        <scroll-view class="timeline-content"
                     scroll-y="true">
          <view class="timeline">
            <view v-for="(event, index) in currentArchive.timeline"
                  :key="index"
                  class="timeline-event">
              <view class="event-marker">
                <view class="event-dot"></view>
                <view v-if="index !== currentArchive.timeline.length - 1"
                      class="event-line"></view>
              </view>
              <view class="event-content">
                <text class="event-date">{{ event.date }}</text>
                <text class="event-title">{{ event.title }}</text>
                <text class="event-desc">{{ event.description }}</text>
                <image v-if="event.image"
                       :src="event.image"
                       class="event-image"
                       mode="aspectFill">
                </image>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 筛选浮层 -->
    <view v-if="showFilterModal"
          class="filter-overlay"
          @click="closeFilter">
      <view class="filter-panel"
            @click.stop>
        <view class="panel-header">
          <text class="panel-title">筛选条件</text>
          <text @click="closeFilter"
                class="close-btn">×</text>
        </view>

        <view class="filter-content">
          <!-- 年代范围 -->
          <view class="filter-section">
            <text class="filter-label">年代范围</text>
            <view class="year-range">
              <input v-model="filterOptions.startYear"
                     type="number"
                     placeholder="起始年份"
                     class="year-input" />
              <text class="range-separator">至</text>
              <input v-model="filterOptions.endYear"
                     type="number"
                     placeholder="结束年份"
                     class="year-input" />
            </view>
          </view>

          <!-- 档案类型 -->
          <view class="filter-section">
            <text class="filter-label">档案类型</text>
            <view class="checkbox-group">
              <view v-for="type in archiveTypes"
                    :key="type"
                    :class="['checkbox-item', { checked: filterOptions.types.includes(type) }]"
                    @click="toggleType(type)">
                <image :src="filterOptions.types.includes(type) ? '/static/icons/checkbox-checked.svg' : '/static/icons/checkbox.svg'"
                       class="checkbox-icon">
                </image>
                <text class="checkbox-label">{{ type }}</text>
              </view>
            </view>
          </view>

          <!-- 地区选择 -->
          <view class="filter-section">
            <text class="filter-label">相关地区</text>
            <picker :value="filterOptions.regionIndex"
                    :range="regions"
                    @change="onRegionChange">
              <view class="picker-input">
                {{ filterOptions.regionIndex >= 0 ? regions[filterOptions.regionIndex] : '请选择地区' }}
              </view>
            </picker>
          </view>
        </view>

        <view class="filter-actions">
          <button @click="resetFilter"
                  class="reset-btn">重置</button>
          <button @click="applyFilter"
                  class="apply-btn">应用</button>
        </view>
      </view>
    </view>

    <!-- 底部工具栏 -->
    <view class="toolbar">
      <view class="tool-item"
            @click="showFilter">
        <image src="/static/icons/filter.svg"
               class="tool-icon"></image>
        <text class="tool-text">筛选</text>
      </view>
      <view class="tool-item"
            @click="showMap">
        <image src="/static/icons/map.svg"
               class="tool-icon"></image>
        <text class="tool-text">地图</text>
      </view>
      <view class="tool-item"
            @click="showStatistics">
        <image src="/static/icons/chart.svg"
               class="tool-icon"></image>
        <text class="tool-text">统计</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Archives',
  data () {
    return {
      loading: true,
      searchKeyword: '',
      showSuggestions: false,
      currentCategory: 'all',
      currentPeriod: 'all',
      showTimelineModal: false,
      showFilterModal: false,
      currentArchive: null,

      searchSuggestions: ['土地契约', '官府文书', '地方志', '族谱', '商业档案'],

      categories: [
        { label: '全部', value: 'all', icon: '/static/icons/all.svg', count: 156 },
        { label: '契约文书', value: 'contract', icon: '/static/icons/contract.svg', count: 45 },
        { label: '地方志', value: 'chronicle', icon: '/static/icons/chronicle.svg', count: 23 },
        { label: '官府档案', value: 'official', icon: '/static/icons/official.svg', count: 34 },
        { label: '族谱家谱', value: 'genealogy', icon: '/static/icons/genealogy.svg', count: 18 },
        { label: '商业档案', value: 'business', icon: '/static/icons/business.svg', count: 25 },
        { label: '教育文献', value: 'education', icon: '/static/icons/education.svg', count: 11 }
      ],

      timePeriods: [
        { label: '全部', value: 'all' },
        { label: '明代', value: 'ming' },
        { label: '清代', value: 'qing' },
        { label: '民国', value: 'republic' },
        { label: '新中国', value: 'modern' }
      ],

      filterOptions: {
        startYear: '',
        endYear: '',
        types: [],
        regionIndex: -1
      },

      archiveTypes: ['契约文书', '地方志', '官府档案', '族谱家谱', '商业档案', '教育文献'],
      regions: ['全部地区', '北京', '上海', '广州', '杭州', '南京', '苏州', '西安'],

      archives: [
        {
          id: 1,
          title: '清代土地买卖契约',
          year: '清光绪三年（1877年）',
          period: '清代',
          type: '契约文书',
          description: '记录清代某地主与农民之间的土地买卖交易，包含完整的交易条款和见证人签字。',
          coverImage: '/static/images/archives/contract1.jpg',
          pageCount: 3,
          viewCount: 234,
          lastUpdated: '2024-01-15',
          isDigitized: true,
          tags: ['土地交易', '清代法律', '农业历史'],
          historicalContext: '光绪年间正值洋务运动时期，传统的土地制度面临着西方文明的冲击，这份契约反映了当时社会的变迁。',
          timeline: [
            {
              date: '1877年3月',
              title: '土地买卖协商',
              description: '买卖双方开始协商土地价格和交易条件',
              image: '/static/images/timeline/negotiate.jpg'
            },
            {
              date: '1877年4月',
              title: '契约签订',
              description: '在见证人见证下正式签订土地买卖契约',
              image: '/static/images/timeline/sign.jpg'
            },
            {
              date: '1877年5月',
              title: '官府备案',
              description: '到当地官府进行契约备案和土地过户手续',
              image: '/static/images/timeline/register.jpg'
            }
          ]
        },
        {
          id: 2,
          title: '民国时期某县志',
          year: '民国十二年（1923年）',
          period: '民国',
          type: '地方志',
          description: '详细记录了民国时期某县的地理、历史、人物、风俗等各方面情况，是研究近代地方史的重要文献。',
          coverImage: '/static/images/archives/chronicle1.jpg',
          pageCount: 156,
          viewCount: 189,
          lastUpdated: '2024-01-12',
          isDigitized: true,
          tags: ['地方史', '民国文献', '社会风俗'],
          historicalContext: '民国十二年正值北洋政府时期，地方自治运动兴起，编修县志成为展现地方文化的重要方式。',
          timeline: [
            {
              date: '1922年',
              title: '启动编修',
              description: '县政府决定重新编修县志，委派专人负责'
            },
            {
              date: '1923年',
              title: '完成编修',
              description: '历时一年完成县志编修工作'
            },
            {
              date: '1924年',
              title: '正式刊印',
              description: '县志正式刊印发行'
            }
          ]
        },
        {
          id: 3,
          title: '明代科举考试档案',
          year: '明万历十五年（1587年）',
          period: '明代',
          type: '教育文献',
          description: '保存完好的明代科举考试相关档案，包括考生名单、试题、评分标准等珍贵资料。',
          coverImage: '/static/images/archives/exam1.jpg',
          pageCount: 89,
          viewCount: 156,
          lastUpdated: '2024-01-10',
          isDigitized: true,
          tags: ['科举制度', '明代教育', '考试制度'],
          historicalContext: '万历年间是明代科举制度的鼎盛时期，这份档案反映了当时的教育制度和人才选拔机制。',
          timeline: [
            {
              date: '1587年春',
              title: '乡试准备',
              description: '各地开始准备当年的乡试工作'
            },
            {
              date: '1587年秋',
              title: '乡试举行',
              description: '全国各地同时举行乡试'
            },
            {
              date: '1588年春',
              title: '会试进京',
              description: '乡试中举者进京参加会试'
            }
          ]
        }
      ]
    }
  },

  computed: {
    filteredArchives () {
      let result = this.archives

      // 按分类筛选
      if (this.currentCategory !== 'all') {
        const categoryMap = {
          'contract': '契约文书',
          'chronicle': '地方志',
          'official': '官府档案',
          'genealogy': '族谱家谱',
          'business': '商业档案',
          'education': '教育文献'
        }
        result = result.filter(archive => archive.type === categoryMap[this.currentCategory])
      }

      // 按时期筛选
      if (this.currentPeriod !== 'all') {
        const periodMap = {
          'ming': '明代',
          'qing': '清代',
          'republic': '民国',
          'modern': '新中国'
        }
        result = result.filter(archive => archive.period === periodMap[this.currentPeriod])
      }

      // 按关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(archive =>
          archive.title.toLowerCase().includes(keyword) ||
          archive.description.toLowerCase().includes(keyword) ||
          archive.type.toLowerCase().includes(keyword) ||
          archive.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      }

      return result
    }
  },

  methods: {
    onSearchInput () {
      this.showSuggestions = this.searchKeyword.length > 0
      // 搜索防抖
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.loadArchives()
      }, 300)
    },

    clearSearch () {
      this.searchKeyword = ''
      this.showSuggestions = false
      this.loadArchives()
    },

    selectSuggestion (suggestion) {
      this.searchKeyword = suggestion
      this.showSuggestions = false
      this.loadArchives()
    },

    switchCategory (category) {
      this.currentCategory = category
      this.loadArchives()
    },

    switchPeriod (period) {
      this.currentPeriod = period
      this.loadArchives()
    },

    viewArchiveDetail (archive) {
      uni.navigateTo({
        url: `/pages/culture/archive-detail?id=${archive.id}`
      })
    },

    readArchive (archive) {
      uni.navigateTo({
        url: `/pages/culture/archive-reader?id=${archive.id}`
      })
    },

    showTimeline (archive) {
      this.currentArchive = archive
      this.showTimelineModal = true
    },

    closeTimeline () {
      this.showTimelineModal = false
    },

    shareArchive (archive) {
      uni.share({
        provider: 'weixin',
        type: 0,
        title: archive.title,
        summary: archive.description,
        imageUrl: archive.coverImage
      })
    },

    showFilter () {
      this.showFilterModal = true
    },

    closeFilter () {
      this.showFilterModal = false
    },

    toggleType (type) {
      const index = this.filterOptions.types.indexOf(type)
      if (index > -1) {
        this.filterOptions.types.splice(index, 1)
      } else {
        this.filterOptions.types.push(type)
      }
    },

    onRegionChange (e) {
      this.filterOptions.regionIndex = e.detail.value
    },

    resetFilter () {
      this.filterOptions = {
        startYear: '',
        endYear: '',
        types: [],
        regionIndex: -1
      }
    },

    applyFilter () {
      this.closeFilter()
      this.loadArchives()
    },

    showMap () {
      uni.navigateTo({
        url: '/pages/culture/archive-map'
      })
    },

    showStatistics () {
      uni.navigateTo({
        url: '/pages/culture/archive-statistics'
      })
    },

    getEmptyStateText () {
      if (this.searchKeyword) {
        return `未找到包含"${this.searchKeyword}"的档案`
      } else if (this.currentCategory !== 'all') {
        const category = this.categories.find(c => c.value === this.currentCategory)
        return `当前分类"${category?.label}"下暂无档案`
      } else if (this.currentPeriod !== 'all') {
        const period = this.timePeriods.find(p => p.value === this.currentPeriod)
        return `当前时期"${period?.label}"下暂无档案`
      }
      return '暂无档案数据'
    },

    onImageError () {
      console.log('图片加载失败')
    },

    async loadArchives () {
      this.loading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.loading = false
      } catch (error) {
        console.error('加载档案数据失败:', error)
        this.loading = false
      }
    }
  },

  onLoad () {
    this.loadArchives()
  }
}
</script>

<style scoped>
.archives-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 页面头部 */
.page-header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(74, 144, 226, 0.8),
    rgba(52, 152, 219, 0.6)
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 搜索栏 */
.search-section {
  position: relative;
  background: white;
  padding: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.search-suggestions {
  position: absolute;
  top: 130rpx;
  left: 30rpx;
  right: 30rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 300rpx;
  overflow-y: auto;
}

.suggestion-item {
  display: block;
  padding: 24rpx 30rpx;
  font-size: 26rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f8f9fa;
}

/* 分类导航 */
.category-section {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  height: 140rpx;
}

.category-list {
  display: flex;
  gap: 24rpx;
  padding: 0 30rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #e3f2fd;
  color: #4a90e2;
}

.category-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.category-label {
  font-size: 22rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.category-count {
  font-size: 18rpx;
  color: #999;
}

/* 时间轴导航 */
.timeline-nav {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.timeline-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.timeline-scroll {
  height: 60rpx;
}

.timeline-list {
  display: flex;
  gap: 24rpx;
}

.period-item {
  font-size: 26rpx;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.period-item.active {
  color: white;
  background: #4a90e2;
}

/* 内容区域 */
.content-section {
  padding: 20rpx 30rpx;
  padding-bottom: 200rpx;
}

.loading-container {
  padding: 100rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 档案列表 */
.archives-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.archive-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.archive-item:active {
  transform: translateY(-2rpx);
}

/* 档案封面 */
.archive-cover {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.cover-overlay {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.archive-type {
  background: rgba(74, 144, 226, 0.9);
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.archive-period {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

.digitized-mark {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(76, 175, 80, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mark-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 档案信息 */
.archive-info {
  padding: 30rpx;
}

.archive-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.archive-year {
  font-size: 24rpx;
  color: #4a90e2;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.archive-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 标签 */
.archive-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.archive-tag {
  font-size: 20rpx;
  background: #e3f2fd;
  color: #4a90e2;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

/* 统计信息 */
.archive-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #999;
}

/* 历史背景 */
.historical-context {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #4a90e2;
}

.context-label {
  font-size: 22rpx;
  color: #4a90e2;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.context-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.archive-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  flex: 1;
  background: #4a90e2;
  color: white;
}

.action-btn.secondary {
  flex: 1;
  background: #e0e0e0;
  color: #333;
}

.action-btn.tertiary {
  background: #f0f0f0;
  color: #666;
  padding: 16rpx;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 时间线浮层 */
.timeline-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.timeline-panel {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.timeline-content {
  height: 60vh;
  padding: 30rpx;
}

.timeline {
  position: relative;
}

.timeline-event {
  display: flex;
  margin-bottom: 40rpx;
}

.event-marker {
  position: relative;
  margin-right: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.event-dot {
  width: 20rpx;
  height: 20rpx;
  background: #4a90e2;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 0 0 2rpx #4a90e2;
}

.event-line {
  width: 2rpx;
  flex: 1;
  background: #e0e0e0;
  margin-top: 12rpx;
  min-height: 60rpx;
}

.event-content {
  flex: 1;
}

.event-date {
  font-size: 22rpx;
  color: #4a90e2;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.event-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.event-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.event-image {
  width: 200rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 筛选浮层 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.filter-panel {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.filter-content {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.year-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.year-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.range-separator {
  font-size: 24rpx;
  color: #666;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx 0;
}

.checkbox-item.checked .checkbox-label {
  color: #4a90e2;
  font-weight: 500;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
}

.checkbox-label {
  font-size: 26rpx;
  color: #333;
}

.picker-input {
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  color: #333;
}

.filter-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f0f0f0;
  color: #666;
}

.apply-btn {
  background: #4a90e2;
  color: white;
}

/* 底部工具栏 */
.toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.tool-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  transition: background-color 0.3s ease;
}

.tool-item:active {
  background-color: #f8f9fa;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 22rpx;
  color: #666;
}
</style> 