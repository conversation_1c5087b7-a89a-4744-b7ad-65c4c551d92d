from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn
import os

from app.api.api import api_router
from app.core.exceptions import CityApiException
from app.schemas.admin_division_schemas import ErrorResponse

# 创建FastAPI应用
app = FastAPI(
    title="中国行政区划查询服务",
    description="提供中国省份、城市、区县、街道等行政区划数据的RESTful API",
    version="1.0.0",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，生产环境应限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router)


# 全局异常处理
@app.exception_handler(CityApiException)
async def city_api_exception_handler(request: Request, exc: CityApiException):
    """处理自定义API异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error_code": exc.error_code, "message": exc.message},
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证异常"""
    error_messages = []
    for error in exc.errors():
        error_messages.append(f"{'/'.join(str(loc) for loc in error['loc'])}: {error['msg']}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"error_code": "VALIDATION_ERROR", "message": "; ".join(error_messages)},
    )


@app.get("/", tags=["健康检查"])
async def root():
    """
    API健康检查
    """
    return {"status": "ok", "message": "中国行政区划查询服务运行正常"}


@app.get("/health", tags=["健康检查"])
async def health_check():
    """
    系统健康检查（包括Minio连接）
    """
    try:
        # 检查Minio连接
        from app.core.minio_client_safe import minio_client
        
        # 获取Minio状态
        minio_status_info = minio_client.get_status()
        
        if minio_status_info["available"]:
            minio_status = "connected"
            minio_message = f"Minio连接正常，endpoint: {minio_status_info['endpoint']}, bucket: {minio_status_info['bucket']}"
        else:
            minio_status = "unavailable"
            minio_message = f"Minio服务不可用，endpoint: {minio_status_info['endpoint']}"
            
    except Exception as e:
        minio_status = "error"
        minio_message = f"Minio检查失败: {str(e)}"
    
    return {
        "status": "ok",
        "message": "系统健康检查",
        "services": {
            "api": {"status": "ok", "message": "API服务正常"},
            "minio": {"status": minio_status, "message": minio_message}
        }
    }


if __name__ == "__main__":
    # 获取端口配置
    port = int(os.getenv("PORT", "8000"))
    
    # 启动服务器
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)