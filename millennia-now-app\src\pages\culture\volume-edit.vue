<template>
  <view class="volume-edit-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="book-info">
        <text class="book-title">{{ (bookInfo && bookInfo.title) || '加载中...' }}</text>
        <text class="book-author">{{ (bookInfo && bookInfo.author) || '' }}</text>
      </view>
    </view>

    <!-- 基本信息表单 -->
    <view class="form-section">
      <text class="section-title">基本信息</text>

      <view class="form-group">
        <text class="label">卷册编号</text>
        <input class="input"
               v-model="formData.volume_number"
               type="number"
               placeholder="请输入卷册编号"
               @input="markFormAsModified" />
      </view>

      <view class="form-group">
        <text class="label">卷册标题</text>
        <input class="input"
               v-model="formData.title"
               placeholder="请输入卷册标题"
               @input="markFormAsModified" />
      </view>

      <view class="form-group">
        <text class="label">状态</text>
        <picker :range="statusOptions"
                :range-key="'label'"
                :value="getStatusIndex(formData.status)"
                @change="onStatusChange">
          <view class="picker-input">
            {{ getStatusLabel(formData.status) }}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="label">起始页码</text>
        <input class="input"
               v-model="formData.start_page_number"
               type="number"
               placeholder="请输入起始页码"
               @input="markFormAsModified" />
      </view>

      <view class="form-group">
        <text class="label">结束页码</text>
        <input class="input"
               v-model="formData.end_page_number"
               type="number"
               placeholder="请输入结束页码"
               @input="markFormAsModified" />
      </view>

      <view class="form-group">
        <text class="label">描述</text>
        <textarea class="textarea"
                  v-model="formData.description"
                  placeholder="请输入卷册描述"
                  maxlength="500"
                  @input="markFormAsModified" />
      </view>
    </view>

    <!-- 内容管理 -->
    <view class="content-section">
      <view class="section-header">
        <text class="section-title">内容管理</text>
        <view class="page-stats">
          <text class="stats-text">共 {{ pages.length }} 页</text>
          <text class="stats-text">已校对 {{ correctedPagesCount }} 页</text>
        </view>
      </view>

      <!-- 页面导航 -->
      <view class="page-navigation">
        <view class="nav-controls">
          <button class="nav-btn ancient-btn"
                  :disabled="currentPageIndex === 0"
                  @click="previousPage">
            <text class="btn-text">上一页</text>
          </button>

          <view class="page-indicator">
            <text class="page-text">第 {{ currentPageIndex + 1 }} 页 / 共 {{ pages.length }} 页</text>
          </view>

          <button class="nav-btn ancient-btn"
                  :disabled="currentPageIndex === pages.length - 1"
                  @click="nextPage">
            <text class="btn-text">下一页</text>
          </button>
        </view>

        <!-- 跳转控制 -->
        <view class="jump-controls">
          <text class="jump-label">跳转到第</text>
          <input class="jump-input"
                 v-model="jumpPageNumber"
                 type="number"
                 :placeholder="`1-${pages.length}`" />
          <text class="jump-label">页</text>
          <button class="ancient-btn jump-btn"
                  @click="jumpToPage">
            <text class="btn-text">前往</text>
          </button>
          <button class="ancient-btn add-btn"
                  @click="addNewPage">
            <text class="btn-text">添页</text>
          </button>
        </view>
      </view>

      <!-- 当前页面内容 -->
      <view v-if="currentPage"
            class="current-page"
            :key="currentPage.page_number">
        <view class="page-header">
          <text class="page-number">第 {{ currentPage.page_number }} 页</text>
          <button class="ancient-btn delete-page-btn"
                  @click="deleteCurrentPage">
            <text class="btn-text">删页</text>
          </button>
        </view>

        <view class="page-content">
          <!-- 页面类型 -->
          <view class="form-group">
            <text class="label">页面类型</text>
            <picker :range="pageTypeOptions"
                    :range-key="'label'"
                    :value="getPageTypeIndex(currentPage.page_type)"
                    @change="onCurrentPageTypeChange">
              <view class="picker-input">
                {{ getPageTypeLabel(currentPage.page_type) }}
              </view>
            </picker>
          </view>

          <!-- 校对标识 -->
          <view class="form-group">
            <view class="switch-group">
              <switch :checked="currentPageCorrectionStatus"
                      :key="`switch-${currentPage.id || currentPage.page_number}`"
                      @change="onCurrentCorrectionChange" />
              <text class="switch-label">已人工校对</text>
              <text class="debug-text">
                (调试: {{ currentPageCorrectionStatus ? '开' : '关' }})
              </text>
            </view>
          </view>

          <!-- 原文图像 -->
          <view class="form-group">
            <text class="label">原文图像</text>
            <view class="image-upload-area">
              <view v-if="currentPage.image_url"
                    class="image-preview">
                <image :src="currentPage.image_url"
                       class="preview-image"
                       mode="aspectFit"
                       @click="previewImage(currentPage.image_url)" />
                <view class="image-actions">
                  <button class="action-btn ancient-btn"
                          @click="changeCurrentPageImage">
                    <text class="btn-text">更换</text>
                  </button>
                  <button class="action-btn ancient-btn"
                          @click="removeCurrentPageImage">
                    <text class="btn-text">删除</text>
                  </button>
                </view>
              </view>
              <view v-else
                    class="upload-placeholder"
                    @click="uploadCurrentPageImage">
                <text class="upload-icon">📷</text>
                <text class="upload-text">点击上传图片</text>
              </view>
            </view>
          </view>

          <!-- OCR文本内容 -->
          <view class="form-group">
            <view class="ocr-header">
              <text class="label">智能识别内容</text>
              <button class="ancient-btn ocr-btn"
                      @click="performCurrentPageOCR"
                      :disabled="!currentPage.image_url || currentPage.ocr_processing">
                <text class="btn-text">
                  {{ currentPage.ocr_processing ? '识别中...' : '智能识别' }}
                </text>
              </button>
            </view>
            <view class="ocr-info"
                  v-if="currentPage.ocr_confidence">
              <text v-if="currentPage.ocr_confidence"
                    class="confidence-text">
                置信度: {{ (currentPage.ocr_confidence * 100).toFixed(1) }}%
              </text>
            </view>
            <textarea class="ocr-textarea"
                      v-model="currentPage.ocr_text"
                      @input="onOCRTextChange"
                      placeholder="智能识别的文本内容将显示在这里，您可以手动编辑校对"
                      maxlength="10000" />
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else
            class="empty-state">
        <text class="empty-text">暂无页面内容</text>
        <button class="add-first-btn ancient-btn"
                @click="addNewPage">
          <text class="btn-text">添加首页</text>
        </button>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="save-btn ancient-btn"
              @click="saveVolume"
              :disabled="saving">
        <text class="btn-text">{{ saving ? '保存中...' : '保存卷册' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import {
  getAncientBookDetail,
  getVolumeDetail,
  createVolume,
  updateVolume,
  uploadAncientBookImage,
  performOCR as performOCRAPI,
  createPage,
  updatePage,
  deletePage,
  getPages,
  getVolumeStats,
  saveVolumeWithPages,
  batchCreatePages,
  batchUpdatePages,
  batchDeletePages
} from '@/api/ancient_book_volumes'
import { getImageProxyUrl } from '@/utils/image'

export default {
  data () {
    return {
      bookId: null,
      volumeId: null,
      isEditMode: false,
      bookInfo: { title: '加载中...', author: '' },
      formData: {
        volume_number: 1,
        title: '',
        status: 'draft',
        start_page_number: 1,
        end_page_number: 1,
        description: ''
      },
      pages: [],
      currentPageIndex: 0,
      jumpPageNumber: 1,
      saving: false,
      loading: false,
      uploading: false,
      statusOptions: [
        { value: 'draft', label: '草稿' },
        { value: 'published', label: '已发布' },
        { value: 'archived', label: '已归档' }
      ],
      pageTypeOptions: [
        { value: 'cover', label: '封面' },
        { value: 'content', label: '正文' },
        { value: 'illustration', label: '插图' },
        { value: 'table_of_contents', label: '目录' },
        { value: 'preface', label: '序言' },
        { value: 'appendix', label: '附录' }
      ],
      // 页面变更跟踪
      pageChanges: {
        created: [], // 新创建的页面
        updated: [], // 修改的页面
        deleted: []  // 删除的页面ID
      },
      originalPages: [], // 原始页面数据，用于比较变更
      originalVolumeData: null, // 原始卷册数据，用于比较变更
      autoSaveTimer: null, // 自动保存定时器
      hasUnsavedChanges: false, // 是否有未保存的更改
      isInitializing: false // 是否正在初始化数据
    }
  },
  computed: {
    correctedPagesCount () {
      return this.pages.filter(page => page.is_manually_corrected).length
    },
    currentPage () {
      const page = this.pages[this.currentPageIndex] || null
      if (page) {
        console.log('currentPage computed:', {
          pageId: page.id,
          pageNumber: page.page_number,
          is_manually_corrected: page.is_manually_corrected,
          currentPageIndex: this.currentPageIndex
        })
      }
      return page
    },
    currentPageCorrectionStatus () {
      const status = this.currentPage?.is_manually_corrected || false
      console.log('currentPageCorrectionStatus computed:', {
        pageId: this.currentPage?.id,
        pageNumber: this.currentPage?.page_number,
        status: status
      })
      return status
    }
  },
  onLoad (options) {
    this.bookId = options.bookId
    this.volumeId = options.volumeId
    this.isEditMode = !!this.volumeId

    // 确保数据结构初始化
    this.initializeDataStructures()

    if (this.bookId) {
      this.loadBookInfo()
    }

    if (this.volumeId) {
      this.loadVolumeData()
    } else {
      // 新建卷册时添加第一页（封面）
      this.addNewPage('cover')
    }
  },
  methods: {
    // 初始化数据结构
    initializeDataStructures () {
      // 确保 pageChanges 对象结构正确
      if (!this.pageChanges || typeof this.pageChanges !== 'object') {
        this.pageChanges = {
          created: [],
          updated: [],
          deleted: []
        }
      }

      // 确保数组存在
      if (!Array.isArray(this.pageChanges.created)) {
        this.pageChanges.created = []
      }
      if (!Array.isArray(this.pageChanges.updated)) {
        this.pageChanges.updated = []
      }
      if (!Array.isArray(this.pageChanges.deleted)) {
        this.pageChanges.deleted = []
      }

      // 确保 pages 数组存在
      if (!Array.isArray(this.pages)) {
        this.pages = []
      }
    },

    async loadBookInfo () {
      try {
        this.loading = true
        const response = await getAncientBookDetail(this.bookId)
        console.log('古籍信息响应:', response)

        // 根据实际响应结构提取数据
        if (response && response.data) {
          this.bookInfo = response.data
        } else if (response && response.title) {
          this.bookInfo = response
        } else {
          console.warn('无法识别的响应结构:', response)
          this.bookInfo = { title: '数据格式错误' }
        }

        console.log('设置的bookInfo:', this.bookInfo)
      } catch (error) {
        console.error('加载古籍信息失败:', error)
        this.bookInfo = { title: '加载失败' }
        uni.showToast({
          title: '加载古籍信息失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    async loadVolumeData () {
      try {
        this.isInitializing = true
        this.loading = true
        const response = await getVolumeDetail(this.volumeId)
        console.log('卷册数据响应:', response)

        // 根据实际响应结构提取数据
        let volumeData = null
        if (response && response.data) {
          volumeData = response.data
        } else if (response && response.volume_number !== undefined) {
          volumeData = response
        } else {
          console.warn('无法识别的卷册响应结构:', response)
          throw new Error('卷册数据格式错误')
        }

        // 设置卷册基本信息
        this.formData = {
          volume_number: volumeData.volume_number || 1,
          title: volumeData.title || volumeData.volume_title || '',
          status: volumeData.status || 'draft',
          start_page_number: volumeData.start_page_number || volumeData.start_page || 1,
          end_page_number: volumeData.end_page_number || volumeData.end_page || 1,
          description: volumeData.description || volumeData.content_description || ''
        }

        // 保存原始卷册数据用于比较变更
        this.originalVolumeData = JSON.parse(JSON.stringify(this.formData))

        // 设置页面数据
        this.pages = (volumeData.pages || []).map(page => {
          // 深度复制，确保每个页面对象完全独立
          console.log('页面数据映射:', {
            pageId: page.id,
            pageNumber: page.page_number,
            rawIsCorrected: page.is_corrected,
            convertedValue: page.is_corrected === 1 || page.is_corrected === true
          })
          return {
            id: page.id,
            volume_id: page.volume_id,
            page_number: page.page_number,
            page_type: page.page_type || 'content',
            image_url: page.image_url ? getImageProxyUrl(page.image_url) : '',
            ocr_text: page.ocr_text || '',
            ocr_confidence: page.ocr_confidence || null,
            ocr_processing: false,
            is_new: false,
            manual_text: page.manual_text || page.corrected_text || page.ocr_text || '',
            is_manually_corrected: page.is_corrected === 1 || page.is_corrected === true // 兼容int和boolean
          }
        })

        // 保存原始页面数据用于比较变更（保存原始URL，不使用代理URL）
        this.originalPages = (volumeData.pages || []).map(page => {
          return {
            id: page.id,
            volume_id: page.volume_id,
            page_number: page.page_number,
            page_type: page.page_type || 'content',
            image_url: page.image_url || '',
            ocr_text: page.ocr_text || '',
            ocr_confidence: page.ocr_confidence || null,
            is_new: false,
            manual_text: page.manual_text || page.corrected_text || page.ocr_text || '',
            is_manually_corrected: page.is_corrected === 1 || page.is_corrected === true
          }
        })

        console.log('设置的formData:', this.formData)
        console.log('设置的pages:', this.pages)

        // 强制更新视图，确保 switch 组件状态正确
        this.$nextTick(() => {
          this.$forceUpdate()
          // 初始化完成
          setTimeout(() => {
            this.isInitializing = false
            console.log('数据初始化完成')
          }, 100)
        })
      } catch (error) {
        console.error('加载卷册数据失败:', error)
        uni.showToast({
          title: '加载卷册数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 页面导航
    previousPage () {
      if (this.currentPageIndex > 0) {
        this.currentPageIndex--
        this.jumpPageNumber = this.currentPageIndex + 1
        console.log('切换到上一页:', this.currentPageIndex, this.currentPage?.is_manually_corrected)
      }
    },

    nextPage () {
      if (this.currentPageIndex < this.pages.length - 1) {
        this.currentPageIndex++
        this.jumpPageNumber = this.currentPageIndex + 1
        console.log('切换到下一页:', this.currentPageIndex, this.currentPage?.is_manually_corrected)
      }
    },

    jumpToPage () {
      const pageNum = parseInt(this.jumpPageNumber)
      if (pageNum >= 1 && pageNum <= this.pages.length) {
        this.currentPageIndex = pageNum - 1
        console.log('跳转到页面:', this.currentPageIndex, this.currentPage?.is_manually_corrected)
      } else {
        uni.showToast({
          title: `请输入1-${this.pages.length}之间的页码`,
          icon: 'none'
        })
      }
    },

    addNewPage (pageType = 'content') {
      // 确保页面类型是有效的
      const validPageTypes = ['cover', 'content', 'illustration', 'table_of_contents', 'preface', 'appendix']
      const finalPageType = validPageTypes.includes(pageType) ? pageType : 'content'

      const newPage = {
        id: null, // 新页面没有ID
        page_number: this.pages.length + 1,
        page_type: this.pages.length === 0 ? 'cover' : finalPageType,
        image_url: '',
        ocr_text: '',
        ocr_confidence: null,
        is_manually_corrected: false, // 新页面默认未校对
        ocr_processing: false,
        manual_text: '', // 手动编辑的文本
        is_new: true // 标记为新页面
      }

      console.log('添加新页面:', newPage)
      this.pages.push(newPage)

      // 跳转到新添加的页面
      this.currentPageIndex = this.pages.length - 1
      this.jumpPageNumber = this.currentPageIndex + 1

      // 记录变更 - 新页面自动添加到创建列表
      this.pageChanges.created.push(newPage)

      // 标记有未保存的更改
      this.hasUnsavedChanges = true
    },

    deleteCurrentPage () {
      if (this.pages.length === 0) return

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这一页吗？',
        success: (res) => {
          if (res.confirm) {
            const deletedPage = this.pages[this.currentPageIndex]

            // 如果是已存在的页面，记录删除
            if (deletedPage.id && !deletedPage.is_new) {
              this.pageChanges.deleted.push(deletedPage.id)
            }

            // 从创建列表中移除（如果是新创建的）
            if (deletedPage.is_new) {
              const createIndex = this.pageChanges.created.findIndex(p => p === deletedPage)
              if (createIndex > -1) {
                this.pageChanges.created.splice(createIndex, 1)
              }
            }

            // 删除页面
            this.pages.splice(this.currentPageIndex, 1)

            // 重新编号
            this.pages.forEach((page, idx) => {
              page.page_number = idx + 1
            })

            // 调整当前页面索引
            if (this.currentPageIndex >= this.pages.length) {
              this.currentPageIndex = Math.max(0, this.pages.length - 1)
            }
            this.jumpPageNumber = this.currentPageIndex + 1
          }
        }
      })
    },

    // 当前页面操作
    async uploadCurrentPageImage () {
      if (!this.currentPage) return

      try {
        this.uploading = true
        const result = await uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })

        if (result.tempFilePaths && result.tempFilePaths.length > 0) {
          const filePath = result.tempFilePaths[0]
          const uploadedUrl = await uploadAncientBookImage(filePath)

          console.log('图片上传成功:', uploadedUrl)

          // 保存原始URL和代理URL
          const originalImageUrl = this.currentPage.image_url
          this.currentPage.image_url = getImageProxyUrl(uploadedUrl)

          console.log('图片URL变更:', {
            pageId: this.currentPage.id,
            pageNumber: this.currentPage.page_number,
            原始URL: originalImageUrl,
            新URL: this.currentPage.image_url,
            上传URL: uploadedUrl
          })

          // 强制标记页面已修改
          this.markPageAsModified(this.currentPage)

          uni.showToast({
            title: '图片上传成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        uni.showToast({
          title: '上传图片失败',
          icon: 'none'
        })
      } finally {
        this.uploading = false
      }
    },

    changeCurrentPageImage () {
      this.uploadCurrentPageImage()
    },

    removeCurrentPageImage () {
      if (!this.currentPage) return

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            console.log('删除图片:', {
              pageId: this.currentPage.id,
              pageNumber: this.currentPage.page_number,
              原图片URL: this.currentPage.image_url
            })

            this.currentPage.image_url = ''
            this.currentPage.ocr_text = ''
            this.currentPage.ocr_confidence = null

            // 标记页面已修改
            this.markPageAsModified(this.currentPage)
          }
        }
      })
    },

    previewImage (imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },

    async performCurrentPageOCR () {
      if (!this.currentPage || !this.currentPage.image_url) {
        uni.showToast({
          title: '请先上传图片',
          icon: 'none'
        })
        return
      }

      this.currentPage.ocr_processing = true

      try {
        // 如果页面已经保存到数据库，使用真实的OCR API
        if (this.currentPage.id && !this.currentPage.is_new) {
          const response = await performOCRAPI(this.currentPage.id)
          this.currentPage.ocr_text = response.data.ocr_text || response.ocr_text
          this.currentPage.ocr_confidence = response.data.confidence || response.confidence
        } else {
          // 新页面暂时使用模拟OCR
          await new Promise(resolve => setTimeout(resolve, 2000))
          this.currentPage.ocr_text = '这是模拟的OCR识别结果，页面保存后将使用真实OCR...'
          this.currentPage.ocr_confidence = 0.85
        }

        // 标记页面已修改
        this.markPageAsModified(this.currentPage)

        uni.showToast({
          title: 'OCR识别完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('OCR识别失败:', error)
        uni.showToast({
          title: 'OCR识别失败',
          icon: 'none'
        })
      } finally {
        this.currentPage.ocr_processing = false
      }
    },

    onCurrentPageTypeChange (e) {
      if (!this.currentPage) return
      const index = e.detail.value
      const newPageType = this.pageTypeOptions[index]?.value || 'content'

      console.log('页面类型变更:', {
        oldType: this.currentPage.page_type,
        newType: newPageType,
        index: index,
        options: this.pageTypeOptions
      })

      this.currentPage.page_type = newPageType
      this.markPageAsModified(this.currentPage)
    },

    onCurrentCorrectionChange (e) {
      console.log('onCurrentCorrectionChange', e)
      if (!this.currentPage) return

      // switch 组件的 change 事件直接返回 boolean
      const checked = e.detail.value

      console.log('人工校验状态变更:', {
        pageId: this.currentPage.id,
        pageNumber: this.currentPage.page_number,
        oldValue: this.currentPage.is_manually_corrected,
        newValue: checked,
        rawEventValue: e.detail.value,
        eventType: typeof e.detail.value
      })

      this.currentPage.is_manually_corrected = checked
      this.markPageAsModified(this.currentPage)
    },

    // 监听OCR文本变化
    onOCRTextChange () {
      try {
        if (this.currentPage) {
          // 防止页面加载时误触发
          if (this.loading || this.isInitializing) {
            console.log('数据初始化中，忽略OCR文本变化')
            return
          }

          console.log('OCR文本变更:', this.currentPage.ocr_text)
          this.currentPage.manual_text = this.currentPage.ocr_text
          // 只有当用户真正编辑时才标记为已校对
          // 可以通过检查文本是否与原始OCR文本不同来判断
          const originalText = this.originalPages.find(p => p.id === this.currentPage.id)?.ocr_text || ''
          if (this.currentPage.ocr_text !== originalText) {
            console.log('检测到用户手动编辑，标记为已校对')
            this.currentPage.is_manually_corrected = true
          }
          this.markPageAsModified(this.currentPage)
        }
      } catch (error) {
        console.error('OCR文本变化处理失败:', error)
      }
    },

    // 标记页面已修改
    markPageAsModified (page) {
      if (!page) {
        console.warn('markPageAsModified: page is null or undefined')
        return
      }

      console.log('标记页面已修改:', {
        pageId: page.id,
        isNew: page.is_new,
        pageType: page.page_type,
        pageNumber: page.page_number
      })

      if (page.is_new) {
        // 新页面，检查是否已经在创建列表中
        const existingIndex = this.pageChanges.created.findIndex(p => p && p === page)
        if (existingIndex === -1) {
          this.pageChanges.created.push(page)
          console.log('页面添加到创建列表')
        }
      } else if (page.id) {
        // 已存在的页面，强制添加到更新列表（避免重复检查）
        const existingIndex = this.pageChanges.updated.findIndex(p => p && p.id === page.id)
        if (existingIndex === -1) {
          this.pageChanges.updated.push(page)
          console.log('页面添加到更新列表')
        } else {
          console.log('页面已在更新列表中，强制更新引用')
          // 更新引用，确保使用最新的页面数据
          this.pageChanges.updated[existingIndex] = page
        }
      }

      // 标记有未保存的更改
      this.hasUnsavedChanges = true

      // 设置自动保存
      this.scheduleAutoSave()
    },

    // 标记表单已修改
    markFormAsModified () {
      this.hasUnsavedChanges = true
      this.scheduleAutoSave()
    },

    // 安排自动保存
    scheduleAutoSave () {
      // 清除之前的定时器
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer)
      }

      // 设置新的自动保存定时器（5分钟后）
      this.autoSaveTimer = setTimeout(() => {
        if (this.hasUnsavedChanges && !this.saving) {
          this.autoSave()
        }
      }, 5 * 60 * 1000) // 5分钟
    },

    // 自动保存
    async autoSave () {
      if (this.saving) return

      try {
        console.log('执行自动保存...')
        await this.saveVolume()

        uni.showToast({
          title: '已自动保存',
          icon: 'success',
          duration: 1000
        })
      } catch (error) {
        console.error('自动保存失败:', error)
        // 自动保存失败不显示错误提示，避免干扰用户
      }
    },

    // 数据验证
    validateVolumeData () {
      const errors = []

      if (!this.formData.title.trim()) {
        errors.push('请输入卷册标题')
      }

      if (!this.formData.volume_number || this.formData.volume_number < 1) {
        errors.push('请输入有效的卷册编号')
      }

      if (this.formData.start_page_number && this.formData.end_page_number) {
        if (this.formData.start_page_number > this.formData.end_page_number) {
          errors.push('起始页码不能大于结束页码')
        }
      }

      // 验证页面数据
      for (let i = 0; i < this.pages.length; i++) {
        const page = this.pages[i]
        if (!page) {
          errors.push(`第${i + 1}页数据缺失`)
          continue
        }
        if (!page.page_type) {
          errors.push(`第${i + 1}页缺少页面类型`)
        }
      }

      return errors
    },

    // 检查未上传图片的页面
    checkMissingImages () {
      const missingImagePages = []

      this.pages.forEach((page, index) => {
        if (!page.image_url || page.image_url.trim() === '') {
          missingImagePages.push({
            pageNumber: page.page_number || (index + 1),
            pageType: this.getPageTypeLabel(page.page_type)
          })
        }
      })

      return missingImagePages
    },

    // 比较页面数据是否有变化
    hasPageChanged (currentPage, originalPage) {
      if (!originalPage) return true // 新页面

      // 比较关键字段
      const fieldsToCompare = [
        'page_number', 'page_type', 'image_url',
        'ocr_text', 'manual_text', 'is_manually_corrected'
      ]

      for (const field of fieldsToCompare) {
        let currentValue = currentPage[field]
        let originalValue = originalPage[field]

        // 特殊处理图片URL，需要提取原始URL进行比较
        if (field === 'image_url') {
          // 从当前页面的代理URL中提取原始URL
          if (currentValue && currentValue.includes('/api/v1/proxy/image/')) {
            currentValue = currentValue.replace(/.*\/api\/v1\/proxy\/image\//, '')
          }
          // 原始页面的URL应该已经是原始URL
        }

        // 处理null/undefined/空字符串的情况
        const normalizedCurrent = currentValue || ''
        const normalizedOriginal = originalValue || ''

        if (normalizedCurrent !== normalizedOriginal) {
          console.log(`页面字段 ${field} 已变化:`, {
            原值: originalValue,
            新值: currentValue,
            页面ID: currentPage.id,
            页码: currentPage.page_number
          })
          return true
        }
      }

      return false
    },

    // 智能检测页面变更
    detectPageChanges () {
      const changes = {
        created: [],
        updated: [],
        deleted: []
      }

      // 检测新创建的页面
      this.pages.forEach(page => {
        if (page.is_new || !page.id) {
          changes.created.push(page)
        }
      })

      // 检测已更新的页面
      this.pages.forEach(page => {
        if (page.id && !page.is_new) {
          const originalPage = this.originalPages.find(p => p.id === page.id)
          if (this.hasPageChanged(page, originalPage)) {
            console.log(`页面 ${page.page_number} 有变化，添加到更新列表`)
            changes.updated.push(page)
          } else {
            console.log(`页面 ${page.page_number} 无变化`)
          }
        }
      })

      // 检测已删除的页面
      this.originalPages.forEach(originalPage => {
        if (!this.pages.find(p => p.id === originalPage.id)) {
          changes.deleted.push(originalPage.id)
        }
      })

      console.log('智能检测到的页面变更:', changes)
      return changes
    },

    // 检查卷册基本信息是否有变化
    hasVolumeDataChanged () {
      if (!this.isEditMode) return true // 新建卷册肯定有变化

      // 如果没有原始数据，认为有变化
      if (!this.originalVolumeData) return true

      const fieldsToCompare = ['volume_number', 'title', 'status', 'start_page_number', 'end_page_number', 'description']

      for (const field of fieldsToCompare) {
        const currentValue = this.formData[field]
        const originalValue = this.originalVolumeData[field]

        // 处理null/undefined/空字符串的情况
        const normalizedCurrent = currentValue || ''
        const normalizedOriginal = originalValue || ''

        if (normalizedCurrent !== normalizedOriginal) {
          console.log(`卷册字段 ${field} 已变化:`, { 原值: originalValue, 新值: currentValue })
          return true
        }
      }

      return false
    },

    // 检查重复页码
    checkDuplicatePageNumbers () {
      const pageNumbers = this.pages.filter(p => p != null).map(p => p.page_number).filter(num => num != null)
      const duplicates = pageNumbers.filter((num, index) => pageNumbers.indexOf(num) !== index)

      if (duplicates.length > 0) {
        return `存在重复的页码: ${duplicates.join(', ')}`
      }

      return null
    },

    // 表单操作
    onStatusChange (e) {
      const index = e.detail.value
      this.formData.status = this.statusOptions[index].value
      this.markFormAsModified()
    },

    getStatusIndex (status) {
      return this.statusOptions.findIndex(option => option.value === status)
    },

    getStatusLabel (status) {
      const option = this.statusOptions.find(option => option.value === status)
      return option ? option.label : '草稿'
    },

    getPageTypeIndex (pageType) {
      return this.pageTypeOptions.findIndex(option => option.value === pageType)
    },

    getPageTypeLabel (pageType) {
      const option = this.pageTypeOptions.find(option => option.value === pageType)
      return option ? option.label : '正文'
    },

    // 保存卷册和页面
    async saveVolume () {
      if (this.saving) return

      console.log('开始保存卷册，当前状态:', {
        bookId: this.bookId,
        volumeId: this.volumeId,
        isEditMode: this.isEditMode,
        formData: this.formData,
        pageChanges: this.pageChanges,
        pages: this.pages
      })

      // 1. 检查未上传图片的页面
      const missingImagePages = this.checkMissingImages()
      if (missingImagePages.length > 0) {
        const missingList = missingImagePages.map(page =>
          `第${page.pageNumber}页 (${page.pageType})`
        ).join('\n')

        uni.showModal({
          title: '请上传原文图像',
          content: `以下页面还未上传原文图像：\n${missingList}\n\n请先上传图像后再保存。`,
          showCancel: false
        })
        return
      }

      // 2. 数据验证
      const validationErrors = this.validateVolumeData()
      if (validationErrors.length > 0) {
        uni.showModal({
          title: '数据验证失败',
          content: validationErrors.join('\n'),
          showCancel: false
        })
        return
      }

      // 3. 检查重复页码
      const duplicateError = this.checkDuplicatePageNumbers()
      if (duplicateError) {
        uni.showModal({
          title: '页码重复',
          content: duplicateError,
          showCancel: false
        })
        return
      }

      this.saving = true

      try {
        // 准备卷册数据
        const volumeData = {
          book_id: parseInt(this.bookId),
          volume_number: this.formData.volume_number,
          volume_title: this.formData.title.trim(),
          status: this.formData.status,
          start_page: String(this.formData.start_page_number || 1),
          end_page: String(this.formData.end_page_number || 1),
          content_description: this.formData.description.trim() || null
        }

        // 4. 智能检测页面变更（只保存有变化的内容）
        const pageChanges = this.detectPageChanges()

        // 统计变更信息
        const changeCount = pageChanges.created.length + pageChanges.updated.length + pageChanges.deleted.length
        console.log(`检测到 ${changeCount} 个页面变更:`, {
          新增: pageChanges.created.length,
          修改: pageChanges.updated.length,
          删除: pageChanges.deleted.length
        })

        // 如果没有任何变更，只保存卷册基本信息
        if (changeCount === 0) {
          console.log('没有页面变更，只保存卷册基本信息')
        } else {
          // 显示变更摘要
          console.log('=== 页面变更摘要 ===')
          if (pageChanges.created.length > 0) {
            console.log(`新增页面 ${pageChanges.created.length} 个:`,
              pageChanges.created.map(p => `第${p.page_number}页(${p.page_type})`))
          }
          if (pageChanges.updated.length > 0) {
            console.log(`修改页面 ${pageChanges.updated.length} 个:`,
              pageChanges.updated.map(p => `第${p.page_number}页(${p.page_type})`))
          }
          if (pageChanges.deleted.length > 0) {
            console.log(`删除页面 ${pageChanges.deleted.length} 个:`, pageChanges.deleted)
          }
          console.log('==================')
        }

        console.log('准备保存数据:', { volumeData, pageChanges })

        // 检查卷册数据是否有变化（在保存前检查）
        const volumeChanged = this.hasVolumeDataChanged()

        // 使用简化的分步保存逻辑
        let response = await this.saveVolumeStepByStep(volumeData, pageChanges, volumeChanged)

        // 保存响应中包含变更信息
        response.volumeChanged = volumeChanged
        response.pageChanges = pageChanges

        // 重新加载数据以获取最新状态
        console.log('开始重新加载数据...')
        try {
          await this.loadVolumeData()
          console.log('数据重新加载完成')
        } catch (loadError) {
          console.error('重新加载数据失败:', loadError)
          // 即使重新加载失败，也继续执行
        }

        // 显示保存成功信息
        const totalChanges = response.pageChanges.created.length + response.pageChanges.updated.length + response.pageChanges.deleted.length

        let successMessage = '保存成功'
        const details = []

        // 使用响应中的变更信息
        if (response.volumeChanged) {
          details.push('卷册信息')
        }
        if (totalChanges > 0) {
          details.push(`${totalChanges}个页面变更`)
        }

        if (details.length > 0) {
          successMessage += `，更新了${details.join('和')}`
        } else {
          successMessage = '保存成功，没有变更'
        }

        uni.showToast({
          title: successMessage,
          icon: 'success'
        })

        // 清空变更记录
        this.pageChanges = {
          created: [],
          updated: [],
          deleted: []
        }

        // 更新原始数据，用于下次比较
        this.originalPages = JSON.parse(JSON.stringify(this.pages))
        this.originalVolumeData = JSON.parse(JSON.stringify(this.formData))

        // 清除页面的 is_new 标记
        this.pages.forEach(page => {
          if (page.is_new) {
            page.is_new = false
          }
        })

        // 重置未保存状态
        this.hasUnsavedChanges = false

        // 清除自动保存定时器
        if (this.autoSaveTimer) {
          clearTimeout(this.autoSaveTimer)
          this.autoSaveTimer = null
        }

        // 延迟返回
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败: ' + (error.message || '未知错误'),
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },

    // 分步保存（备用方案）
    async saveVolumeStepByStep (volumeData, pageChanges, volumeChanged) {
      console.log('开始分步保存，isEditMode:', this.isEditMode, 'volumeId:', this.volumeId)

      try {
        // 1. 保存卷册基本信息（只在有变化时保存）
        let volumeResponse

        if (volumeChanged) {
          if (this.isEditMode && this.volumeId) {
            console.log('更新现有卷册:', this.volumeId)
            volumeResponse = await updateVolume(this.volumeId, volumeData)
          } else {
            console.log('创建新卷册')
            volumeResponse = await createVolume(volumeData)
            this.volumeId = volumeResponse.data?.id || volumeResponse.id
            this.isEditMode = true
            console.log('新卷册ID:', this.volumeId)
          }
          console.log('卷册保存响应:', volumeResponse)
        } else {
          console.log('卷册基本信息没有变化，跳过保存')
          volumeResponse = { success: true, message: '卷册信息无变化' }
        }

        // 2. 处理页面变更（只在有变更时执行）
        if (pageChanges.created.length > 0 || pageChanges.updated.length > 0 || pageChanges.deleted.length > 0) {
          console.log('开始处理页面变更...')
          await this.savePageChanges(pageChanges)
          console.log('页面变更处理完成')
        } else {
          console.log('没有页面变更，跳过页面保存')
        }

        return volumeResponse
      } catch (error) {
        console.error('分步保存失败:', error)
        throw error
      }
    },

    // 保存页面变更
    async savePageChanges (pageChanges = null) {
      // 使用传入的变更数据，如果没有则使用实例变量
      const changes = pageChanges || this.pageChanges
      console.log('页面变更数据:', changes)

      // 直接使用单个操作，因为批量API未实现
      await this.savePageChangesIndividually(changes)
    },

    // 清理页面数据
    cleanPageData (page) {
      const validPageTypes = ['cover', 'content', 'illustration', 'table_of_contents', 'preface', 'appendix']

      // 处理图片URL，移除代理前缀以获得原始URL
      let cleanImageUrl = null
      if (page.image_url && page.image_url.trim()) {
        cleanImageUrl = page.image_url.trim()
        // 如果是代理URL，提取原始URL
        if (cleanImageUrl.includes('/api/v1/proxy/image/')) {
          cleanImageUrl = cleanImageUrl.replace(/.*\/api\/v1\/proxy\/image\//, '')
        }
      }

      // 基础字段
      const cleanedData = {
        volume_id: this.volumeId,
        page_number: parseInt(page.page_number) || 1,
        page_type: validPageTypes.includes(page.page_type) ? page.page_type : 'content',
        image_url: cleanImageUrl, // 允许更新图片
        ocr_confidence: (typeof page.ocr_confidence === 'number') ? page.ocr_confidence : null,
        is_corrected: page.is_manually_corrected ? 1 : 0 // boolean转int：true为1，false为0
      }

      if (page.is_manually_corrected) {
        cleanedData.corrected_text = (page.manual_text && page.manual_text.trim()) || null
      } else {
        cleanedData.ocr_text = (page.ocr_text && page.ocr_text.trim()) || null
      }

      console.log('清理后的页面数据:', cleanedData)
      return cleanedData
    },

    // 单个页面操作（备用方案）
    async savePageChangesIndividually (pageChanges = null) {
      // 使用传入的变更数据，如果没有则使用实例变量
      const changes = pageChanges || this.pageChanges

      console.log('开始单个保存页面变更')
      console.log('删除的页面:', changes.deleted)
      console.log('创建的页面:', changes.created)
      console.log('更新的页面:', changes.updated)

      const promises = []

      // 处理删除的页面
      for (const pageId of changes.deleted) {
        if (pageId != null) {
          console.log('删除页面:', pageId)
          promises.push(deletePage(pageId))
        }
      }

      // 处理新创建的页面
      for (const page of changes.created) {
        if (page != null) {
          const pageData = this.cleanPageData(page)
          console.log('创建页面数据:', pageData)
          promises.push(createPage(pageData))
        }
      }

      // 处理更新的页面
      for (const page of changes.updated) {
        if (page != null && page.id) {
          const pageData = this.cleanPageData(page)
          // 更新时不需要 volume_id
          delete pageData.volume_id

          // 详细的调试信息
          console.log('更新页面详细信息:', {
            pageId: page.id,
            pageNumber: page.page_number,
            原始页面数据: page,
            清理后数据: pageData,
            原始图片URL: page.image_url,
            清理后图片URL: pageData.image_url
          })

          promises.push(updatePage(page.id, pageData))
        }
      }

      // 等待所有页面操作完成
      if (promises.length > 0) {
        try {
          const results = await Promise.allSettled(promises)

          // 检查操作结果
          let successCount = 0
          let failureCount = 0
          const errors = []

          results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              successCount++
            } else {
              failureCount++
              errors.push(`操作 ${index + 1}: ${result.reason}`)
              console.error(`操作 ${index + 1} 失败:`, result.reason)
            }
          })

          console.log(`页面变更保存完成: 成功 ${successCount}, 失败 ${failureCount}`)

          if (failureCount > 0) {
            throw new Error(`部分操作失败:\n${errors.join('\n')}`)
          }

        } catch (error) {
          console.error('保存页面变更失败:', error)
          throw error
        }
      }
    },

    // 获取卷册统计信息
    async loadVolumeStats () {
      if (!this.volumeId) return

      try {
        const stats = await getVolumeStats(this.volumeId)
        console.log('卷册统计:', stats)
        // 可以在这里更新统计信息的显示
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }
  },

  // 监听OCR文本变化
  watch: {
    'currentPage.ocr_text': {
      handler: 'onOCRTextChange',
      deep: true
    }
  },

  // 页面卸载时清理
  onUnload () {
    // 清除自动保存定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
  },

  // 页面隐藏时清理
  onHide () {
    // 清除自动保存定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
  }
}
</script>

<style scoped>
.volume-edit-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.book-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.book-author {
  font-size: 28rpx;
  color: #666;
}

.form-section,
.content-section {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-stats {
  display: flex;
  gap: 20rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.input,
.textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  min-height: auto;
  line-height: 1.5;
  border-color: #deb887;
  background: #fefefe;
  transition: border-color 0.3s ease;
}

.input:focus,
.textarea:focus {
  border-color: #8b4513;
  outline: none;
  box-shadow: 0 0 8rpx rgba(139, 69, 19, 0.2);
}

.input {
  min-height: 80rpx;
}

.textarea {
  min-height: 120rpx;
  max-height: 300rpx;
  resize: none;
  overflow-y: auto;
}

.picker-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.switch-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.debug-text {
  font-size: 20rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 古籍风格按钮基础样式 */
.ancient-btn {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
  color: #f5f5dc;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 2rpx solid #654321;
  box-shadow: 0 4rpx 8rpx rgba(139, 69, 19, 0.3);
  font-weight: bold;
  letter-spacing: 1rpx;
  transition: all 0.3s ease;
}

.ancient-btn:active {
  background: linear-gradient(135deg, #654321 0%, #8b4513 100%);
  box-shadow: 0 2rpx 4rpx rgba(139, 69, 19, 0.5);
  transform: translateY(2rpx);
}

.ancient-btn:disabled {
  background: #d3d3d3;
  color: #999;
  border-color: #ccc;
  box-shadow: none;
}

/* 页面导航样式 */
.page-navigation {
  background: linear-gradient(135deg, #f5f5dc 0%, #faf0e6 100%);
  border: 2rpx solid #deb887;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(222, 184, 135, 0.2);
}

.nav-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.nav-btn.ancient-btn {
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

.page-indicator {
  flex: 1;
  text-align: center;
}

.page-text {
  font-size: 28rpx;
  color: #8b4513;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(139, 69, 19, 0.1);
}

.jump-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
  justify-content: center;
}

.jump-label {
  font-size: 24rpx;
  color: #8b4513;
  font-weight: 500;
}

.jump-input {
  width: 120rpx;
  padding: 10rpx;
  border: 2rpx solid #deb887;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  background: #fefefe;
}

.jump-btn.ancient-btn {
  padding: 10rpx 20rpx;
}

.add-btn.ancient-btn {
  background: linear-gradient(135deg, #8b7355 0%, #a0845c 100%);
  border-color: #6b5b47;
  padding: 10rpx 20rpx;
}

.add-btn.ancient-btn:active {
  background: linear-gradient(135deg, #6b5b47 0%, #8b7355 100%);
}

/* 当前页面样式 */
.current-page {
  border: 2rpx solid #deb887;
  border-radius: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fefefe 0%, #f9f9f9 100%);
  box-shadow: 0 4rpx 12rpx rgba(222, 184, 135, 0.15);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #deb887;
}

.page-number {
  font-size: 30rpx;
  font-weight: bold;
  color: #8b4513;
  text-shadow: 1rpx 1rpx 2rpx rgba(139, 69, 19, 0.1);
}

.delete-page-btn.ancient-btn {
  background: linear-gradient(135deg, #8b6f47 0%, #a0845c 100%);
  border-color: #6b5b47;
  padding: 10rpx 20rpx;
}

.delete-page-btn.ancient-btn:active {
  background: linear-gradient(135deg, #6b5b47 0%, #8b6f47 100%);
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 图片上传区域样式 */
.image-upload-area {
  border: 2rpx dashed #deb887;
  border-radius: 12rpx;
  min-height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fefefe 0%, #f9f9f9 100%);
  transition: all 0.3s ease;
}

.image-upload-area:hover {
  border-color: #8b4513;
  background: linear-gradient(135deg, #f5f5dc 0%, #faf0e6 100%);
}

.image-preview {
  position: relative;
  width: 100%;
}

.preview-image {
  width: 100%;
  max-height: 400rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.image-actions {
  display: flex;
  gap: 15rpx;
  margin-top: 15rpx;
  justify-content: center;
}

.action-btn.ancient-btn {
  background: linear-gradient(135deg, #8b7355 0%, #a0845c 100%);
  border-color: #6b5b47;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.action-btn.ancient-btn:active {
  background: linear-gradient(135deg, #6b5b47 0%, #8b7355 100%);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  color: #8b4513;
  cursor: pointer;
}

.upload-text {
  font-size: 28rpx;
  color: #8b4513;
  font-weight: 500;
  margin-top: 10rpx;
}

.upload-icon {
  font-size: 48rpx;
  color: #deb887;
  margin-bottom: 10rpx;
}

/* 智能识别区域样式 */
.ocr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: linear-gradient(135deg, #f5f2e8 0%, #f0ead6 100%);
  border: 2rpx solid #deb887;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(222, 184, 135, 0.3);
}

.ocr-header .label {
  font-size: 30rpx;
  font-weight: bold;
  color: #8b4513;
  margin-bottom: 0;
}

.ocr-btn.ancient-btn {
  background: linear-gradient(135deg, #8b7355 0%, #a0845c 100%);
  border-color: #6b5b47;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.ocr-btn.ancient-btn:active {
  background: linear-gradient(135deg, #6b5b47 0%, #8b7355 100%);
}

.ocr-info {
  margin-bottom: 15rpx;
  text-align: center;
}

.confidence-text {
  font-size: 24rpx;
  color: #8b4513;
  font-weight: 500;
  background: #f5f2e8;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #deb887;
  display: inline-block;
}

.ocr-textarea {
  min-height: 200rpx;
  max-height: 400rpx;
  resize: none;
  background: #fefefe;
  border: 2rpx solid #deb887;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  background: linear-gradient(135deg, #f5f5dc 0%, #faf0e6 100%);
  border-radius: 12rpx;
  border: 2rpx dashed #deb887;
}

.empty-text {
  font-size: 28rpx;
  color: #8b4513;
  margin-bottom: 30rpx;
  display: block;
  font-weight: 500;
}

.add-first-btn.ancient-btn {
  background: linear-gradient(135deg, #8b7355 0%, #a0845c 100%);
  border-color: #6b5b47;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.bottom-actions {
  position: sticky;
  bottom: 0;
  background: linear-gradient(135deg, #f5f5dc 0%, #faf0e6 100%);
  padding: 30rpx;
  border-top: 2rpx solid #deb887;
  box-shadow: 0 -4rpx 12rpx rgba(222, 184, 135, 0.2);
}

.save-btn.ancient-btn {
  width: 100%;
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
  border-color: #654321;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 16rpx rgba(139, 69, 19, 0.4);
}

.save-btn.ancient-btn:active {
  background: linear-gradient(135deg, #654321 0%, #8b4513 100%);
  box-shadow: 0 3rpx 8rpx rgba(139, 69, 19, 0.6);
}

.btn-text {
  font-size: inherit;
}
</style> 