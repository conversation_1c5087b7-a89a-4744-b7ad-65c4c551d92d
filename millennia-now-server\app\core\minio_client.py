import os
import uuid
import aiofiles
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>
from minio import Minio
from minio.error import S3Error
from PIL import Image
import io
import logging
from .config import get_settings

logger = logging.getLogger(__name__)

class MinioClient:
    """Minio客户端封装类"""
    
    def __init__(self):
        # 从配置文件读取Minio配置
        config = get_settings().get_minio_config()
        
        self.endpoint = config.endpoint
        self.access_key = config.access_key
        self.secret_key = config.secret_key
        self.secure = config.secure
        self.bucket_name = config.bucket_name
        
        # 文件上传配置
        self.max_file_size = config.max_file_size
        self.allowed_extensions = config.allowed_extensions
        
        # 图片处理配置
        self.max_width = config.max_width
        self.quality = config.quality
        
        # 修复端口配置 - 确保使用正确的API端口
        if ':' not in self.endpoint:
            self.endpoint = f"{self.endpoint}:9000"
        
        # 如果使用的是9001端口（控制台端口），自动切换到9000（API端口）
        if self.endpoint.endswith(':9001'):
            self.endpoint = self.endpoint.replace(':9001', ':9000')
            logger.info(f"自动切换到API端口: {self.endpoint}")
        
        # 延迟初始化客户端
        self.client = None
        self._initialized = False
        self._available = False
    
    def _initialize_client(self):
        """延迟初始化Minio客户端"""
        if self._initialized:
            return self._available
        
        try:
            logger.info(f"正在连接Minio服务: {self.endpoint}")
            logger.info(f"连接参数: access_key={self.access_key}, secure={self.secure}")
            
            # 尝试多个可能的端点配置
            endpoints_to_try = [
                self.endpoint,
                self.endpoint.replace(':9001', ':9000'),  # 如果误用了控制台端口
                'localhost:9000',  # 默认API端口
                '127.0.0.1:9000',  # 本地IP
            ]
            
            # 去重
            endpoints_to_try = list(dict.fromkeys(endpoints_to_try))
            
            for endpoint in endpoints_to_try:
                try:
                    logger.info(f"尝试连接端点: {endpoint}")
                    
                    # 初始化Minio客户端
                    test_client = Minio(
                        endpoint=endpoint,
                        access_key=self.access_key,
                        secret_key=self.secret_key,
                        secure=self.secure
                    )
                    
                    # 测试连接
                    list(test_client.list_buckets())
                    
                    # 连接成功
                    self.client = test_client
                    self.endpoint = endpoint
                    logger.info(f"成功连接到: {endpoint}")
                    break
                    
                except Exception as e:
                    logger.warning(f"端点 {endpoint} 连接失败: {e}")
                    continue
            else:
                # 所有端点都失败了
                raise Exception("所有Minio端点连接均失败")
            
            # 确保bucket存在
            self._ensure_bucket_exists()
            self._available = True
            self._initialized = True
            logger.info("Minio客户端初始化成功")
            
        except Exception as e:
            logger.warning(f"Minio服务连接失败: {e}，图片上传功能将不可用")
            logger.warning("请确保Minio服务正在运行，或检查配置参数")
            # 不抛出异常，允许应用启动，但上传功能将不可用
            self.client = None
            self._available = False
            self._initialized = True
        
        return self._available
    
    def _ensure_bucket_exists(self):
        """确保bucket存在并设置正确的访问权限"""
        import json
        
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created bucket: {self.bucket_name}")
                
                # 设置存储桶策略为公共读取
                policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"AWS": "*"},
                            "Action": ["s3:GetObject"],
                            "Resource": [f"arn:aws:s3:::{self.bucket_name}/*"]
                        }
                    ]
                }
                
                try:
                    self.client.set_bucket_policy(self.bucket_name, json.dumps(policy))
                    logger.info(f"Set bucket policy for public read access: {self.bucket_name}")
                except Exception as e:
                    logger.warning(f"Failed to set bucket policy: {e}")
                    logger.warning("Images may not be publicly accessible. Please set bucket policy manually.")
            else:
                # 检查现有存储桶的策略
                try:
                    current_policy = self.client.get_bucket_policy(self.bucket_name)
                    if not current_policy:
                        logger.warning(f"Bucket {self.bucket_name} has no access policy. Images may not be publicly accessible.")
                except Exception as e:
                    logger.warning(f"Could not check bucket policy: {e}")
                    
        except S3Error as e:
            logger.error(f"Error creating bucket: {e}")
            raise
    
    def _generate_filename(self, original_filename: str, prefix: str = "") -> str:
        """生成唯一的文件名"""
        # 获取文件扩展名
        ext = os.path.splitext(original_filename)[1].lower()
        if not ext:
            ext = '.jpg'  # 默认扩展名
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        
        if prefix:
            filename = f"{prefix}/{timestamp}_{unique_id}{ext}"
        else:
            filename = f"{timestamp}_{unique_id}{ext}"
        
        return filename
    
    def _validate_image(self, file_content: bytes) -> Tuple[bool, str]:
        """验证图片文件"""
        try:
            # 检查文件大小
            if len(file_content) > self.max_file_size:
                size_mb = self.max_file_size / (1024 * 1024)
                return False, f"文件大小不能超过{size_mb:.0f}MB"
            
            # 检查文件类型
            image = Image.open(io.BytesIO(file_content))
            image_format = image.format.lower() if image.format else 'unknown'
            
            # 将扩展名转换为格式名进行比较
            allowed_formats = []
            for ext in self.allowed_extensions:
                ext_lower = ext.lower().lstrip('.')
                if ext_lower == 'jpg':
                    allowed_formats.append('jpeg')
                else:
                    allowed_formats.append(ext_lower)
            
            if image_format not in allowed_formats:
                allowed_exts = ', '.join(self.allowed_extensions)
                return False, f"只支持{allowed_exts}格式的图片"
            
            return True, "验证通过"
        except Exception as e:
            return False, f"无效的图片文件: {str(e)}"
    
    def _optimize_image(self, file_content: bytes, max_width: Optional[int] = None, quality: Optional[int] = None) -> bytes:
        """优化图片大小和质量"""
        try:
            # 使用配置文件中的默认值
            if max_width is None:
                max_width = self.max_width
            if quality is None:
                quality = self.quality
            
            image = Image.open(io.BytesIO(file_content))
            
            # 转换RGBA到RGB（如果需要）
            if image.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # 调整大小
            if image.width > max_width:
                ratio = max_width / image.width
                new_height = int(image.height * ratio)
                image = image.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # 保存优化后的图片
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            return output.getvalue()
        except Exception as e:
            logger.warning(f"Image optimization failed: {e}, using original")
            return file_content
    
    async def upload_image(self, file_content: bytes, original_filename: str, 
                          prefix: str = "images", optimize: bool = True) -> Tuple[bool, str]:
        """
        上传图片到Minio
        
        Args:
            file_content: 文件内容
            original_filename: 原始文件名
            prefix: 文件路径前缀
            optimize: 是否优化图片
        
        Returns:
            (success, message_or_url)
        """
        try:
            # 确保客户端已初始化
            self._initialize_client()
            
            if not self.client:
                return False, "Minio服务不可用，请检查Minio服务是否正常运行"
            
            # 验证图片
            is_valid, message = self._validate_image(file_content)
            if not is_valid:
                return False, message
            
            # 优化图片
            if optimize:
                file_content = self._optimize_image(file_content)
            
            # 生成文件名
            filename = self._generate_filename(original_filename, prefix)
            
            # 上传到Minio
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=filename,
                data=io.BytesIO(file_content),
                length=len(file_content),
                content_type='image/jpeg'
            )
            
            # 返回直接的Minio URL，前端直接访问Minio服务器
            protocol = "https" if self.secure else "http"
            direct_url = f"{protocol}://{self.endpoint}/{self.bucket_name}/{filename}"
            
            logger.info(f"Image uploaded successfully: {filename}")
            return True, direct_url
            
        except S3Error as e:
            logger.error(f"Minio upload error: {e}")
            return False, f"上传失败: {str(e)}"
        except Exception as e:
            logger.error(f"Upload error: {e}")
            return False, f"上传失败: {str(e)}"
    
    async def delete_image(self, url: str) -> bool:
        """
        删除图片
        
        Args:
            url: 图片URL
        
        Returns:
            是否删除成功
        """
        try:
            # 确保客户端已初始化
            self._initialize_client()
            
            if not self.client:
                logger.error("Minio客户端未初始化")
                return False
            
            # 从URL中提取object_name
            if f"/{self.bucket_name}/" in url:
                object_name = url.split(f"/{self.bucket_name}/")[1]
            elif "/api/v1/images/image/" in url:
                # 兼容旧的代理URL格式
                object_name = url.split("/api/v1/images/image/")[1]
            else:
                logger.error(f"无法从URL提取对象名称: {url}")
                return False
            
            # 删除对象
            self.client.remove_object(self.bucket_name, object_name)
            logger.info(f"Image deleted successfully: {object_name}")
            return True
            
        except S3Error as e:
            logger.error(f"Minio delete error: {e}")
            return False
        except Exception as e:
            logger.error(f"Delete error: {e}")
            return False
    
    def get_presigned_url(self, object_name: str, expires: timedelta = timedelta(hours=1)) -> Optional[str]:
        """
        获取预签名URL
        
        Args:
            object_name: 对象名称
            expires: 过期时间
        
        Returns:
            预签名URL
        """
        try:
            return self.client.presigned_get_object(self.bucket_name, object_name, expires)
        except S3Error as e:
            logger.error(f"Presigned URL error: {e}")
            return None
    
    def get_status(self) -> dict:
        """获取Minio服务状态"""
        self._initialize_client()
        return {
            "available": self._available,
            "endpoint": self.endpoint,
            "bucket": self.bucket_name,
            "initialized": self._initialized,
            "secure": self.secure
        }
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试Minio连接"""
        try:
            if self._initialize_client():
                # 尝试执行一个简单的操作
                buckets = list(self.client.list_buckets())
                return True, f"连接成功，找到 {len(buckets)} 个buckets"
            else:
                return False, "Minio服务不可用"
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

# 全局Minio客户端实例（延迟初始化）
minio_client = MinioClient()