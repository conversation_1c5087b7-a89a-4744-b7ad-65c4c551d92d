-- 古籍典藏数据库设计
-- 支持多册管理和卷册收藏功能

-- 1. 古籍主表
CREATE TABLE ancient_books (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '古籍ID',
    title VARCHAR(200) NOT NULL COMMENT '古籍名称',
    author VA<PERSON>HA<PERSON>(100) DEFAULT NULL COMMENT '责任者',
    dynasty VARCHAR(50) DEFAULT NULL COMMENT '朝代',
    
    -- 版本信息
    edition TEXT DEFAULT NULL COMMENT '版本项',
    publication TEXT DEFAULT NULL COMMENT '出版发行项',
    bibliographic_note TEXT DEFAULT NULL COMMENT '版本书目史注',
    
    -- 分类编号
    sibu_category CHAR(1) DEFAULT NULL COMMENT '四部分类号（A经部、B史部、C子部、D集部）',
    rare_book_number VARCHAR(50) DEFAULT NULL UNIQUE COMMENT '善本书号',
    
    -- 内容描述
    description TEXT DEFAULT NULL COMMENT '简介',
    content_summary TEXT DEFAULT NULL COMMENT '内容摘要',
    keywords VARCHAR(500) DEFAULT NULL COMMENT '关键词',
    
    -- 物理信息（总体信息）
    total_volumes INT DEFAULT NULL COMMENT '总册数',
    size_info VARCHAR(100) DEFAULT NULL COMMENT '尺寸信息',
    preservation_status VARCHAR(50) DEFAULT NULL COMMENT '保存状态',
    
    -- 基础数字化信息
    cover_image VARCHAR(500) DEFAULT NULL COMMENT '封面图片URL',
    
    -- 统计信息
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    favorite_count INT DEFAULT 0 COMMENT '收藏次数',
    
    -- 管理信息
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态（active/inactive/archived）',
    visibility VARCHAR(20) DEFAULT 'public' COMMENT '可见性（public/private/restricted）',
    created_by INT DEFAULT NULL COMMENT '创建者ID',
    updated_by INT DEFAULT NULL COMMENT '更新者ID',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_dynasty (dynasty),
    INDEX idx_sibu_category (sibu_category),
    INDEX idx_rare_book_number (rare_book_number),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- 检查约束
    CHECK (sibu_category IN ('A', 'B', 'C', 'D')),
    CHECK (status IN ('active', 'inactive', 'archived')),
    CHECK (visibility IN ('public', 'private', 'restricted'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='古籍典藏主表';

-- 2. 古籍卷册表
CREATE TABLE ancient_book_volumes (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '卷册ID',
    book_id INT NOT NULL COMMENT '古籍ID',
    volume_number INT NOT NULL COMMENT '卷册序号（第几册）',
    volume_title VARCHAR(200) DEFAULT NULL COMMENT '卷册标题（如：卷一、上册等）',
    
    -- 物理信息
    page_count INT DEFAULT NULL COMMENT '页数',
    start_page VARCHAR(20) DEFAULT NULL COMMENT '起始页码',
    end_page VARCHAR(20) DEFAULT NULL COMMENT '结束页码',
    
    -- 数字化信息
    cover_image VARCHAR(500) DEFAULT NULL COMMENT '卷册封面图片URL',
    images TEXT DEFAULT NULL COMMENT '页面图片URL列表（JSON格式）',
    is_digitized BOOLEAN DEFAULT FALSE COMMENT '是否已数字化',
    digitization_quality VARCHAR(20) DEFAULT NULL COMMENT '数字化质量（high/medium/low）',
    
    -- 内容信息
    content_description TEXT DEFAULT NULL COMMENT '卷册内容描述',
    chapter_info TEXT DEFAULT NULL COMMENT '章节信息（JSON格式）',
    
    -- 统计信息
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    favorite_count INT DEFAULT 0 COMMENT '收藏次数',
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态（active/inactive/maintenance）',
    availability VARCHAR(20) DEFAULT 'available' COMMENT '可用性（available/restricted/damaged）',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (book_id) REFERENCES ancient_books(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_book_id (book_id),
    INDEX idx_volume_number (volume_number),
    INDEX idx_is_digitized (is_digitized),
    INDEX idx_status (status),
    INDEX idx_availability (availability),
    
    -- 唯一约束
    UNIQUE KEY uk_book_volume (book_id, volume_number),
    
    -- 检查约束
    CHECK (volume_number > 0),
    CHECK (digitization_quality IN ('high', 'medium', 'low')),
    CHECK (status IN ('active', 'inactive', 'maintenance')),
    CHECK (availability IN ('available', 'restricted', 'damaged'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='古籍卷册表';

-- 3. 用户卷册收藏表
CREATE TABLE user_book_volume_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    user_id INT NOT NULL COMMENT '用户ID',
    volume_id INT NOT NULL COMMENT '卷册ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    
    -- 外键约束
    FOREIGN KEY (volume_id) REFERENCES ancient_book_volumes(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_volume_id (volume_id),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束（防止重复收藏）
    UNIQUE KEY uk_user_volume (user_id, volume_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户卷册收藏表';

-- 4. 卷册浏览历史表
CREATE TABLE book_volume_view_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '浏览记录ID',
    user_id INT DEFAULT NULL COMMENT '用户ID（可为空，支持匿名浏览）',
    volume_id INT NOT NULL COMMENT '卷册ID',
    view_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    view_duration INT DEFAULT NULL COMMENT '浏览时长（秒）',
    last_page INT DEFAULT NULL COMMENT '最后浏览的页码',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    
    -- 外键约束
    FOREIGN KEY (volume_id) REFERENCES ancient_book_volumes(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_volume_id (volume_id),
    INDEX idx_view_time (view_time),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卷册浏览历史表';

-- 5. 触发器：自动更新卷册收藏数
DELIMITER //
CREATE TRIGGER update_volume_favorite_count_insert
AFTER INSERT ON user_book_volume_favorites
FOR EACH ROW
BEGIN
    UPDATE ancient_book_volumes 
    SET favorite_count = favorite_count + 1 
    WHERE id = NEW.volume_id;
END//

CREATE TRIGGER update_volume_favorite_count_delete
AFTER DELETE ON user_book_volume_favorites
FOR EACH ROW
BEGIN
    UPDATE ancient_book_volumes 
    SET favorite_count = GREATEST(favorite_count - 1, 0) 
    WHERE id = OLD.volume_id;
END//
DELIMITER ;

-- 6. 视图：古籍统计信息
CREATE VIEW ancient_books_stats AS
SELECT 
    ab.id,
    ab.title,
    ab.author,
    ab.dynasty,
    ab.sibu_category,
    ab.rare_book_number,
    ab.total_volumes,
    ab.view_count as book_view_count,
    ab.favorite_count as book_favorite_count,
    COUNT(abv.id) as actual_volumes,
    COUNT(CASE WHEN abv.is_digitized = TRUE THEN 1 END) as digitized_volumes,
    SUM(abv.page_count) as total_pages,
    SUM(abv.view_count) as total_volume_views,
    SUM(abv.favorite_count) as total_volume_favorites
FROM ancient_books ab
LEFT JOIN ancient_book_volumes abv ON ab.id = abv.book_id AND abv.status = 'active'
WHERE ab.status = 'active'
GROUP BY ab.id;

-- 7. 示例数据插入
INSERT INTO ancient_books (
    title, author, dynasty, sibu_category, rare_book_number, 
    description, total_volumes, cover_image
) VALUES 
('四书章句集注', '朱熹', '宋', 'A', 'SB001', 
 '朱熹对《大学》《中庸》《论语》《孟子》的注释，是理学的重要典籍。', 
 4, '/images/books/sishu.jpg'),
('史记', '司马迁', '汉', 'B', 'SB002', 
 '中国历史上第一部纪传体通史，记载了从上古传说中的黄帝时代到汉武帝的三千多年历史。', 
 10, '/images/books/shiji.jpg'),
('老子道德经', '老子', '春秋', 'C', NULL, 
 '道家哲学思想的重要源头，阐述了道的概念和无为而治的思想。', 
 1, '/images/books/daodejing.jpg'),
('全唐诗', '彭定求等', '清', 'D', 'SB003', 
 '清代编纂的唐诗总集，收录唐代诗人2200多家，诗48900多首。', 
 20, '/images/books/quantangshi.jpg');

-- 插入卷册数据
INSERT INTO ancient_book_volumes (
    book_id, volume_number, volume_title, page_count, is_digitized, 
    digitization_quality, cover_image
) VALUES 
-- 四书章句集注
(1, 1, '大学章句', 168, TRUE, 'high', '/images/volumes/sishu_v1.jpg'),
(1, 2, '中庸章句', 172, TRUE, 'high', '/images/volumes/sishu_v2.jpg'),
(1, 3, '论语集注', 186, TRUE, 'high', '/images/volumes/sishu_v3.jpg'),
(1, 4, '孟子集注', 154, FALSE, NULL, '/images/volumes/sishu_v4.jpg'),

-- 史记
(2, 1, '本纪', 152, TRUE, 'medium', '/images/volumes/shiji_v1.jpg'),
(2, 2, '世家上', 148, TRUE, 'medium', '/images/volumes/shiji_v2.jpg'),
(2, 3, '世家下', 156, FALSE, NULL, '/images/volumes/shiji_v3.jpg'),
(2, 4, '列传一', 164, TRUE, 'medium', '/images/volumes/shiji_v4.jpg'),
(2, 5, '列传二', 158, TRUE, 'medium', '/images/volumes/shiji_v5.jpg'),

-- 老子道德经
(3, 1, '道德经', 86, TRUE, 'high', '/images/volumes/daodejing_v1.jpg'),

-- 全唐诗（部分卷册）
(4, 1, '卷一至卷十', 198, TRUE, 'high', '/images/volumes/quantangshi_v1.jpg'),
(4, 2, '卷十一至卷二十', 201, TRUE, 'high', '/images/volumes/quantangshi_v2.jpg'),
(4, 3, '卷二十一至卷三十', 195, FALSE, NULL, '/images/volumes/quantangshi_v3.jpg');

-- 8. 常用查询示例

-- 查询所有古籍及其卷册信息
SELECT 
    ab.id,
    ab.title,
    ab.author,
    ab.dynasty,
    ab.sibu_category,
    ab.rare_book_number,
    COUNT(abv.id) as volume_count,
    COUNT(CASE WHEN abv.is_digitized = TRUE THEN 1 END) as digitized_count,
    SUM(abv.page_count) as total_pages
FROM ancient_books ab
LEFT JOIN ancient_book_volumes abv ON ab.id = abv.book_id AND abv.status = 'active'
WHERE ab.status = 'active'
GROUP BY ab.id
ORDER BY ab.created_at DESC;

-- 查询特定古籍的所有卷册
SELECT 
    abv.*,
    ab.title as book_title,
    ab.author as book_author
FROM ancient_book_volumes abv
JOIN ancient_books ab ON abv.book_id = ab.id
WHERE ab.id = 1 AND abv.status = 'active'
ORDER BY abv.volume_number;

-- 查询用户收藏的卷册
SELECT 
    ubvf.id as favorite_id,
    ubvf.created_at as favorite_time,
    abv.id as volume_id,
    abv.volume_number,
    abv.volume_title,
    abv.page_count,
    ab.title as book_title,
    ab.author as book_author
FROM user_book_volume_favorites ubvf
JOIN ancient_book_volumes abv ON ubvf.volume_id = abv.id
JOIN ancient_books ab ON abv.book_id = ab.id
WHERE ubvf.user_id = 1
ORDER BY ubvf.created_at DESC;

-- 查询热门卷册（按浏览次数排序）
SELECT 
    abv.id,
    abv.volume_title,
    abv.view_count,
    abv.favorite_count,
    ab.title as book_title,
    ab.author as book_author
FROM ancient_book_volumes abv
JOIN ancient_books ab ON abv.book_id = ab.id
WHERE abv.status = 'active' AND ab.status = 'active'
ORDER BY abv.view_count DESC
LIMIT 10;

-- 查询四部分类统计
SELECT 
    sibu_category,
    CASE 
        WHEN sibu_category = 'A' THEN '经部'
        WHEN sibu_category = 'B' THEN '史部'
        WHEN sibu_category = 'C' THEN '子部'
        WHEN sibu_category = 'D' THEN '集部'
        ELSE '未分类'
    END as category_name,
    COUNT(*) as book_count,
    SUM(total_volumes) as total_volumes
FROM ancient_books
WHERE status = 'active'
GROUP BY sibu_category
ORDER BY book_count DESC;

-- 查询数字化进度
SELECT 
    COUNT(*) as total_volumes,
    COUNT(CASE WHEN is_digitized = TRUE THEN 1 END) as digitized_volumes,
    ROUND(COUNT(CASE WHEN is_digitized = TRUE THEN 1 END) * 100.0 / COUNT(*), 2) as digitization_rate
FROM ancient_book_volumes
WHERE status = 'active';

-- 9. 性能优化建议
-- 创建复合索引
CREATE INDEX idx_book_volume_status ON ancient_book_volumes(book_id, status, volume_number);
CREATE INDEX idx_volume_digitized_status ON ancient_book_volumes(is_digitized, status);
CREATE INDEX idx_user_favorite_time ON user_book_volume_favorites(user_id, created_at DESC);

-- 10. 数据维护脚本
-- 清理过期的浏览历史（保留最近3个月）
DELETE FROM book_volume_view_history 
WHERE view_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 更新古籍的总册数
UPDATE ancient_books ab 
SET total_volumes = (
    SELECT COUNT(*) 
    FROM ancient_book_volumes abv 
    WHERE abv.book_id = ab.id AND abv.status = 'active'
)
WHERE ab.status = 'active'; 