<template>
  <view class="module-permissions-test">
    <view class="header">
      <text class="title">模块权限测试页面</text>
    </view>

    <view class="section">
      <text class="section-title">当前用户信息</text>
      <view v-if="currentUser"
            class="user-info">
        <text>用户ID: {{ currentUser.id }}</text>
        <text>角色: {{ currentUser.role }}</text>
        <text>昵称: {{ currentUser.nickname || '未设置' }}</text>
      </view>
      <text v-else
            class="error">未登录</text>
    </view>

    <view class="section">
      <text class="section-title">可用模块列表</text>
      <view v-if="availableModules.length > 0"
            class="modules-list">
        <view v-for="module in availableModules"
              :key="module.key"
              class="module-item">
          <text class="module-name">{{ module.name }}</text>
          <text class="module-desc">{{ module.description }}</text>
        </view>
      </view>
      <button @click="loadModules"
              class="action-btn">加载模块列表</button>
    </view>

    <view class="section">
      <text class="section-title">用户权限测试</text>
      <view class="form-group">
        <text>测试用户ID:</text>
        <input v-model="testUserId"
               type="number"
               placeholder="输入用户ID"
               class="input" />
      </view>
      <button @click="loadUserPermissions"
              class="action-btn">获取用户权限</button>

      <view v-if="userPermissions"
            class="permissions-display">
        <text class="sub-title">用户权限:</text>
        <view class="permission-item">
          <text>古籍管理: {{ userPermissions.ancient_books ? '✓' : '✗' }}</text>
        </view>
        <view class="permission-item">
          <text>书画珍品: {{ userPermissions.paintings ? '✓' : '✗' }}</text>
        </view>
        <view class="permission-item">
          <text>档案故事: {{ userPermissions.archives ? '✓' : '✗' }}</text>
        </view>
        <view class="permission-item">
          <text>影像文献: {{ userPermissions.videos ? '✓' : '✗' }}</text>
        </view>
      </view>
    </view>

    <view class="section">
      <text class="section-title">权限更新测试 (仅超级管理员)</text>
      <view class="permission-controls">
        <view class="control-item">
          <text>古籍管理:</text>
          <switch v-model="testPermissions.ancient_books" />
        </view>
        <view class="control-item">
          <text>书画珍品:</text>
          <switch v-model="testPermissions.paintings" />
        </view>
        <view class="control-item">
          <text>档案故事:</text>
          <switch v-model="testPermissions.archives" />
        </view>
        <view class="control-item">
          <text>影像文献:</text>
          <switch v-model="testPermissions.videos" />
        </view>
      </view>
      <button @click="updateUserPermissions"
              class="action-btn">更新权限</button>
    </view>

    <view class="section">
      <text class="section-title">操作日志</text>
      <view class="logs">
        <text v-for="(log, index) in logs"
              :key="index"
              class="log-item">{{ log }}</text>
      </view>
      <button @click="clearLogs"
              class="clear-btn">清空日志</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

// 模拟的接口和类型定义
interface ModulePermissions {
  ancient_books: boolean
  paintings: boolean
  archives: boolean
  videos: boolean
}

interface ModuleInfo {
  key: string
  name: string
  description: string
}

interface UserInfo {
  id: number
  nickname?: string
  role: string
}

// 数据状态
const currentUser = ref<UserInfo | null>(null)
const availableModules = ref<ModuleInfo[]>([])
const userPermissions = ref<ModulePermissions | null>(null)
const testUserId = ref<number>(1)
const logs = ref<string[]>([])

const testPermissions = reactive<ModulePermissions>({
  ancient_books: false,
  paintings: false,
  archives: false,
  videos: false,
})

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 模拟加载当前用户
const loadCurrentUser = () => {
  // 从存储中获取用户信息（模拟）
  const token = uni.getStorageSync('access_token')
  if (token) {
    currentUser.value = {
      id: 1,
      nickname: '测试管理员',
      role: 'super_admin',
    }
    addLog('当前用户信息加载成功')
  } else {
    addLog('未找到登录信息')
  }
}

// 加载可用模块
const loadModules = async () => {
  try {
    addLog('开始加载可用模块...')

    // 模拟API调用
    const mockModules: ModuleInfo[] = [
      {
        key: 'ancient_books',
        name: '古籍管理',
        description: '管理古籍典藏模块，包括古籍录入、编辑、删除等操作',
      },
      {
        key: 'paintings',
        name: '书画珍品',
        description: '管理书画珍品模块，包括书画作品录入、编辑、删除等操作',
      },
      {
        key: 'archives',
        name: '档案故事',
        description: '管理档案故事模块，包括档案录入、编辑、删除等操作',
      },
      {
        key: 'videos',
        name: '影像文献',
        description: '管理影像文献模块，包括视频录入、编辑、删除等操作',
      },
    ]

    availableModules.value = mockModules
    addLog(`成功加载 ${mockModules.length} 个模块`)
  } catch (error) {
    addLog(`加载模块失败: ${error}`)
  }
}

// 加载用户权限
const loadUserPermissions = async () => {
  try {
    if (!testUserId.value) {
      addLog('请输入用户ID')
      return
    }

    addLog(`开始加载用户 ${testUserId.value} 的权限...`)

    // 模拟API调用
    const mockPermissions: ModulePermissions = {
      ancient_books: true,
      paintings: false,
      archives: true,
      videos: false,
    }

    userPermissions.value = mockPermissions

    // 同步到测试权限控制器
    Object.assign(testPermissions, mockPermissions)

    addLog(`成功加载用户权限`)
  } catch (error) {
    addLog(`加载用户权限失败: ${error}`)
  }
}

// 更新用户权限
const updateUserPermissions = async () => {
  try {
    if (!testUserId.value) {
      addLog('请输入用户ID')
      return
    }

    if (!currentUser.value || currentUser.value.role !== 'super_admin') {
      addLog('只有超级管理员可以更新权限')
      return
    }

    addLog(`开始更新用户 ${testUserId.value} 的权限...`)

    // 模拟API调用
    const updateData = { ...testPermissions }

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    userPermissions.value = updateData
    addLog('权限更新成功')

    uni.showToast({
      title: '权限更新成功',
      icon: 'success',
    })
  } catch (error) {
    addLog(`更新权限失败: ${error}`)
    uni.showToast({
      title: '更新失败',
      icon: 'none',
    })
  }
}

// 页面初始化
onMounted(() => {
  loadCurrentUser()
  loadModules()
})
</script>

<style scoped>
.module-permissions-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.sub-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 15rpx;
}

.user-info text {
  display: block;
  margin-bottom: 10rpx;
  color: #666;
}

.modules-list {
  margin-bottom: 20rpx;
}

.module-item {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.module-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.module-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.form-group text {
  width: 200rpx;
  color: #666;
}

.input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.permissions-display {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.permission-item {
  margin-bottom: 10rpx;
}

.permission-item text {
  color: #666;
}

.permission-controls {
  margin-bottom: 20rpx;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.control-item:last-child {
  border-bottom: none;
}

.control-item text {
  color: #666;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  background-color: #c8161e;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.clear-btn {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.logs {
  max-height: 400rpx;
  overflow-y: auto;
  margin-bottom: 20rpx;
}

.log-item {
  display: block;
  padding: 10rpx;
  margin-bottom: 5rpx;
  background-color: #f8f9fa;
  border-radius: 4rpx;
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

.error {
  color: #ff4444;
  font-weight: 500;
}
</style> 