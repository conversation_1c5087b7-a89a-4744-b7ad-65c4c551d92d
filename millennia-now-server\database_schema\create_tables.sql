-- 千年文旅系统 - 完整数据库表结构
-- 账号密码登录系统版本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 用户相关表
-- ================================

-- 1. 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号（登录账号）',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知,1-男,2-女',
    `role` ENUM('GUEST', 'DISTRICT_ADMIN', 'CITY_ADMIN', 'PROVINCE_ADMIN', 'SUPER_ADMIN') NOT NULL DEFAULT 'GUEST' COMMENT '用户角色',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    `province_id` SMALLINT DEFAULT NULL COMMENT '管理的省份ID，仅管理员有效',
    `city_id` SMALLINT DEFAULT NULL COMMENT '管理的城市ID，仅管理员有效',
    `district_id` SMALLINT DEFAULT NULL COMMENT '管理的区县ID，仅管理员有效',
    `module_permissions` JSON DEFAULT NULL COMMENT '模块管理权限JSON：{"ancient_books":true,"paintings":false,"archives":true,"videos":false}',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    
    INDEX `idx_phone` (`phone`),
    INDEX `idx_role` (`role`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_province_id` (`province_id`),
    INDEX `idx_city_id` (`city_id`),
    INDEX `idx_district_id` (`district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户会话表
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(255) NOT NULL UNIQUE COMMENT '会话标识',
    `access_token` VARCHAR(500) NOT NULL COMMENT '访问令牌',
    `refresh_token` VARCHAR(500) NOT NULL UNIQUE COMMENT '刷新令牌',
    `device_info` JSON DEFAULT NULL COMMENT '设备信息',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_access_token` (`access_token`(100)),
    INDEX `idx_refresh_token` (`refresh_token`(100)),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 3. 登录日志表
DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID，可为空（登录失败时）',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '尝试登录的手机号',
    `login_type` VARCHAR(20) NOT NULL DEFAULT 'password' COMMENT '登录方式：password',
    `success` BOOLEAN NOT NULL COMMENT '是否成功',
    `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_login_type` (`login_type`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- ================================
-- 行政区划表
-- ================================

-- 4. 省份表
DROP TABLE IF EXISTS `provinces`;
CREATE TABLE `provinces` (
    `ProvinceId` SMALLINT PRIMARY KEY COMMENT '省份ID',
    `Name` VARCHAR(50) NOT NULL COMMENT '省份名称',
    `Abbr` VARCHAR(10) DEFAULT NULL COMMENT '省份简称',
    `Alias` VARCHAR(50) DEFAULT NULL COMMENT '省份别名',
    `Acronym` VARCHAR(10) DEFAULT NULL COMMENT '省份缩写',
    
    INDEX `idx_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省份表';

-- 5. 城市表
DROP TABLE IF EXISTS `cities`;
CREATE TABLE `cities` (
    `ProvinceId` SMALLINT NOT NULL COMMENT '省份ID',
    `CityId` SMALLINT NOT NULL COMMENT '城市ID',
    `Name` VARCHAR(50) NOT NULL COMMENT '城市名称',
    `Acronym` VARCHAR(10) DEFAULT NULL COMMENT '城市缩写',
    
    PRIMARY KEY (`ProvinceId`, `CityId`),
    FOREIGN KEY (`ProvinceId`) REFERENCES `provinces`(`ProvinceId`) ON DELETE CASCADE,
    INDEX `idx_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='城市表';

-- 6. 区县表
DROP TABLE IF EXISTS `districts`;
CREATE TABLE `districts` (
    `ProvinceId` SMALLINT NOT NULL COMMENT '省份ID',
    `CityId` SMALLINT NOT NULL COMMENT '城市ID',
    `DistrictId` SMALLINT NOT NULL COMMENT '区县ID',
    `Name` VARCHAR(50) NOT NULL COMMENT '区县名称',
    `Acronym` VARCHAR(10) DEFAULT NULL COMMENT '区县缩写',
    `PostalCode` VARCHAR(10) DEFAULT NULL COMMENT '邮政编码',
    
    PRIMARY KEY (`ProvinceId`, `CityId`, `DistrictId`),
    FOREIGN KEY (`ProvinceId`, `CityId`) REFERENCES `cities`(`ProvinceId`, `CityId`) ON DELETE CASCADE,
    INDEX `idx_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='区县表';

-- 7. 街道表
DROP TABLE IF EXISTS `streets`;
CREATE TABLE `streets` (
    `ProvinceId` SMALLINT NOT NULL COMMENT '省份ID',
    `CityId` SMALLINT NOT NULL COMMENT '城市ID',
    `DistrictId` SMALLINT NOT NULL COMMENT '区县ID',
    `StreetId` SMALLINT NOT NULL COMMENT '街道ID',
    `Name` VARCHAR(100) NOT NULL COMMENT '街道名称',
    `Acronym` VARCHAR(10) DEFAULT NULL COMMENT '街道缩写',
    `PostalCode` VARCHAR(10) DEFAULT NULL COMMENT '邮政编码',
    
    PRIMARY KEY (`ProvinceId`, `CityId`, `DistrictId`, `StreetId`),
    FOREIGN KEY (`ProvinceId`, `CityId`, `DistrictId`) REFERENCES `districts`(`ProvinceId`, `CityId`, `DistrictId`) ON DELETE CASCADE,
    INDEX `idx_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='街道表';

-- ================================
-- 文化遗产相关表
-- ================================

-- 8. 文化遗产地点表
DROP TABLE IF EXISTS `heritage_places`;
CREATE TABLE `heritage_places` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '地点ID',
    `name` VARCHAR(200) NOT NULL COMMENT '地点名称',
    `description` TEXT COMMENT '地点描述',
    `province_id` SMALLINT COMMENT '省份ID',
    `city_id` SMALLINT COMMENT '城市ID',
    `district_id` SMALLINT COMMENT '区县ID',
    `address` VARCHAR(500) COMMENT '详细地址',
    `latitude` DECIMAL(10, 8) COMMENT '纬度',
    `longitude` DECIMAL(11, 8) COMMENT '经度',
    `images` JSON COMMENT '图片列表',
    `status` ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    `created_by` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_name` (`name`),
    INDEX `idx_province_city_district` (`province_id`, `city_id`, `district_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文化遗产地点表';

-- ================================
-- 插入默认数据
-- ================================

-- 插入默认管理员账号
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`) 
VALUES (
    '13800000000', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '系统管理员', 
    'SUPER_ADMIN',
    TRUE
) ON DUPLICATE KEY UPDATE 
    `nickname` = '系统管理员',
    `role` = 'SUPER_ADMIN',
    `is_active` = TRUE;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT 'Database tables created successfully!' as message;
SELECT 
    COUNT(*) as total_tables,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name LIKE '%user%') as user_tables,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name LIKE '%heritage%') as heritage_tables
FROM information_schema.tables 
WHERE table_schema = DATABASE();

-- 显示默认管理员信息
SELECT 
    '默认管理员账号已创建' as note,
    '手机号: 13800000000' as phone,
    '密码: admin123' as password,
    '⚠️ 请在生产环境中修改默认密码！' as warning;
