// 用户认证API接口
// 导入统一的API配置
import { getBaseURL } from '@/config/api'

const BASE_URL = getBaseURL()

// 用户角色枚举
export enum UserRole {
  GUEST = 'GUEST',
  DISTRICT_ADMIN = 'DISTRICT_ADMIN',
  CITY_ADMIN = 'CITY_ADMIN',
  PROVINCE_ADMIN = 'PROVINCE_ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}

// 模块权限接口
export interface ModulePermissions {
  ancient_books: boolean
  paintings: boolean
  archives: boolean
  videos: boolean
}

// 用户信息接口
export interface UserInfo {
  id: number
  openid: string
  nickname?: string
  avatar_url?: string
  gender: number
  phone?: string
  role: string
  is_active: boolean
  province_id?: number
  city_id?: number
  district_id?: number
  province_name?: string  // 省份名称
  city_name?: string      // 城市名称
  district_name?: string  // 区县名称
  module_permissions?: ModulePermissions  // 模块权限
  created_at: string
  last_login_at?: string
}

// 登录响应接口
export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: UserInfo
}

// 微信登录请求
export interface WechatLoginRequest {
  code: string
  encrypted_data?: string
  iv?: string
}

// 手机号绑定请求
export interface PhoneBindRequest {
  code: string
  encrypted_data: string
  iv: string
}

// Token刷新请求
export interface TokenRefreshRequest {
  refresh_token: string
}

// 用户更新请求
export interface UserUpdateRequest {
  nickname?: string
  avatar_url?: string
  gender?: number
  phone?: string
}

// 微信小程序登录
export const wechatLogin = async (request: WechatLoginRequest): Promise<LoginResponse> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/auth/wechat/login`,
      method: 'POST',
      data: request,
      header: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '登录失败')
    }
  } catch (error) {
    console.error('微信登录失败:', error)
    throw error
  }
}

// 绑定手机号
export const bindPhone = async (request: PhoneBindRequest): Promise<LoginResponse> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/auth/phone/bind`,
      method: 'POST',
      data: request,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '绑定手机号失败')
    }
  } catch (error) {
    console.error('绑定手机号失败:', error)
    throw error
  }
}

// 刷新Token
export const refreshToken = async (refresh_token: string): Promise<{
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/auth/refresh`,
      method: 'POST',
      data: { refresh_token },
      header: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '刷新Token失败')
    }
  } catch (error) {
    console.error('刷新Token失败:', error)
    throw error
  }
}

// 用户登出
export const logout = async (refresh_token: string): Promise<void> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/auth/logout`,
      method: 'POST',
      data: { refresh_token },
      header: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.statusCode !== 200) {
      throw new Error(response.data?.detail || '登出失败')
    }
  } catch (error) {
    console.error('登出失败:', error)
    throw error
  }
}

// 获取当前用户信息
export const getCurrentUser = async (): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/auth/me`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

// 更新当前用户信息
export const updateCurrentUser = async (request: UserUpdateRequest): Promise<UserInfo> => {
  try {
    const token = uni.getStorageSync('access_token')
    const response = await uni.request({
      url: `${BASE_URL}/auth/me`,
      method: 'PUT',
      data: request,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '更新用户信息失败')
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    throw error
  }
}

// 账号密码登录请求接口
export interface PasswordLoginRequest {
  phone: string
  password: string
}

// 用户注册请求接口
export interface RegisterRequest {
  phone: string
  password: string
  nickname?: string
}

// 账号密码登录
export const loginWithPassword = async (request: PasswordLoginRequest): Promise<LoginResponse> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/auth/password/login`,
      method: 'POST',
      data: request,
      header: {
        'Content-Type': 'application/json'
      }
    })

    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '登录失败')
    }
  } catch (error) {
    console.error('账号密码登录失败:', error)
    throw error
  }
}

// 用户注册
export const registerUser = async (request: RegisterRequest): Promise<LoginResponse> => {
  try {
    const response = await uni.request({
      url: `${BASE_URL}/auth/register`,
      method: 'POST',
      data: request,
      header: {
        'Content-Type': 'application/json'
      }
    })

    if (response.statusCode === 201) {
      return response.data
    } else {
      throw new Error(response.data?.detail || '注册失败')
    }
  } catch (error) {
    console.error('用户注册失败:', error)
    throw error
  }
}