-- 古籍卷册表重构
-- 支持多图片页面和OCR文字识别

-- 古籍卷册表
CREATE TABLE IF NOT EXISTS ancient_book_volumes (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL REFERENCES ancient_books(id) ON DELETE CASCADE,
    volume_number INTEGER NOT NULL, -- 卷册序号
    volume_title VARCHAR(200), -- 卷册标题
    total_pages INTEGER DEFAULT 0, -- 总页数
    
    -- 版本信息
    start_page VARCHAR(20), -- 起始页码标识
    end_page VARCHAR(20), -- 结束页码标识
    
    -- 内容描述
    content_description TEXT, -- 内容描述
    notes TEXT, -- 备注信息
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- draft, published, archived
    
    -- 元数据
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(book_id, volume_number),
    CHECK (volume_number > 0),
    CHECK (total_pages >= 0)
);

-- 古籍页面表（存储每一页的图片和OCR文字）
CREATE TABLE IF NOT EXISTS ancient_book_pages (
    id SERIAL PRIMARY KEY,
    volume_id INTEGER NOT NULL REFERENCES ancient_book_volumes(id) ON DELETE CASCADE,
    page_number INTEGER NOT NULL, -- 页码序号
    page_label VARCHAR(50), -- 页码标识（如：第一页、封面、目录等）
    
    -- 图片信息
    image_url VARCHAR(500) NOT NULL, -- 页面图片URL
    image_width INTEGER, -- 图片宽度
    image_height INTEGER, -- 图片高度
    image_size INTEGER, -- 图片文件大小（字节）
    
    -- OCR文字识别
    ocr_text TEXT, -- OCR识别的文字内容
    ocr_confidence DECIMAL(5,4), -- OCR识别置信度 (0-1)
    ocr_processed_at TIMESTAMP, -- OCR处理时间
    ocr_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    
    -- 人工校对
    corrected_text TEXT, -- 人工校对后的文字
    is_corrected BOOLEAN DEFAULT FALSE, -- 是否已人工校对
    corrected_by INTEGER REFERENCES users(id), -- 校对人员
    corrected_at TIMESTAMP, -- 校对时间
    
    -- 页面类型
    page_type VARCHAR(20) DEFAULT 'content', -- cover, contents, preface, content, appendix, colophon
    
    -- 元数据
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(volume_id, page_number),
    CHECK (page_number > 0),
    CHECK (ocr_confidence >= 0 AND ocr_confidence <= 1)
);

-- 古籍章节表（可选，用于组织页面结构）
CREATE TABLE IF NOT EXISTS ancient_book_chapters (
    id SERIAL PRIMARY KEY,
    volume_id INTEGER NOT NULL REFERENCES ancient_book_volumes(id) ON DELETE CASCADE,
    chapter_number INTEGER NOT NULL, -- 章节序号
    chapter_title VARCHAR(200) NOT NULL, -- 章节标题
    start_page_id INTEGER REFERENCES ancient_book_pages(id), -- 起始页面
    end_page_id INTEGER REFERENCES ancient_book_pages(id), -- 结束页面
    
    -- 内容信息
    description TEXT, -- 章节描述
    
    -- 元数据
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(volume_id, chapter_number),
    CHECK (chapter_number > 0)
);

-- OCR处理任务表
CREATE TABLE IF NOT EXISTS ocr_tasks (
    id SERIAL PRIMARY KEY,
    page_id INTEGER NOT NULL REFERENCES ancient_book_pages(id) ON DELETE CASCADE,
    task_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    
    -- 任务信息
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    
    -- OCR服务信息
    ocr_provider VARCHAR(50), -- 使用的OCR服务提供商
    processing_time_ms INTEGER, -- 处理时间（毫秒）
    
    -- 结果统计
    character_count INTEGER, -- 识别的字符数
    confidence_avg DECIMAL(5,4), -- 平均置信度
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_volumes_book_id ON ancient_book_volumes(book_id);
CREATE INDEX IF NOT EXISTS idx_volumes_status ON ancient_book_volumes(status);
CREATE INDEX IF NOT EXISTS idx_pages_volume_id ON ancient_book_pages(volume_id);
CREATE INDEX IF NOT EXISTS idx_pages_page_number ON ancient_book_pages(volume_id, page_number);
CREATE INDEX IF NOT EXISTS idx_pages_ocr_status ON ancient_book_pages(ocr_status);
CREATE INDEX IF NOT EXISTS idx_pages_page_type ON ancient_book_pages(page_type);
CREATE INDEX IF NOT EXISTS idx_chapters_volume_id ON ancient_book_chapters(volume_id);
CREATE INDEX IF NOT EXISTS idx_ocr_tasks_page_id ON ocr_tasks(page_id);
CREATE INDEX IF NOT EXISTS idx_ocr_tasks_status ON ocr_tasks(task_status);

-- 全文搜索索引（PostgreSQL）
CREATE INDEX IF NOT EXISTS idx_pages_ocr_text_gin ON ancient_book_pages USING gin(to_tsvector('chinese', ocr_text));
CREATE INDEX IF NOT EXISTS idx_pages_corrected_text_gin ON ancient_book_pages USING gin(to_tsvector('chinese', corrected_text));

-- 触发器：更新卷册总页数
CREATE OR REPLACE FUNCTION update_volume_page_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE ancient_book_volumes 
    SET total_pages = (
        SELECT COUNT(*) 
        FROM ancient_book_pages 
        WHERE volume_id = COALESCE(NEW.volume_id, OLD.volume_id)
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = COALESCE(NEW.volume_id, OLD.volume_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_volume_page_count
    AFTER INSERT OR DELETE ON ancient_book_pages
    FOR EACH ROW EXECUTE FUNCTION update_volume_page_count();

-- 触发器：更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_volumes_updated_at
    BEFORE UPDATE ON ancient_book_volumes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_pages_updated_at
    BEFORE UPDATE ON ancient_book_pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_chapters_updated_at
    BEFORE UPDATE ON ancient_book_chapters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 示例数据
INSERT INTO ancient_book_volumes (book_id, volume_number, volume_title, content_description, created_by) VALUES
(1, 1, '第一册', '包含序言和前三章内容', 1),
(1, 2, '第二册', '包含第四章至第六章内容', 1);

-- 示例页面数据
INSERT INTO ancient_book_pages (volume_id, page_number, page_label, image_url, page_type, created_by) VALUES
(1, 1, '封面', '/images/volumes/vol1/page001.jpg', 'cover', 1),
(1, 2, '目录', '/images/volumes/vol1/page002.jpg', 'contents', 1),
(1, 3, '序言', '/images/volumes/vol1/page003.jpg', 'preface', 1),
(1, 4, '第一页', '/images/volumes/vol1/page004.jpg', 'content', 1); 