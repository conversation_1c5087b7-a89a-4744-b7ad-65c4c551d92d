/**
 * 图片URL处理工具函数
 * 直接使用后端返回的MinIO原始URL地址，不再通过nginx代理
 */

/**
 * 处理图片URL，修复旧的minio内部URL并返回可用的代理URL
 * @param url 后端返回的图片URL
 * @returns 可直接使用的图片URL
 */
export function getImageProxyUrl(url: string): string {
  if (!url) return ''

  // 使用统一的图片URL处理函数
  return getDisplayImageUrl(url)
}

/**
 * 获取图片缩略图URL
 * @param url 原始图片URL
 * @param size 缩略图尺寸（默认200px）
 * @returns 缩略图URL
 */
export function getThumbnailUrl(url: string, _size: number = 200): string {
  if (!url) return ''

  // 先处理URL格式，然后返回（如果需要缩略图功能，可以在Minio配置中处理）
  return getDisplayImageUrl(url)
}

/**
 * 获取优化后的图片URL
 * 由于现在直接使用Minio URL，暂时直接返回原URL
 * @param url 原始图片URL
 * @param width 目标宽度
 * @param height 目标高度
 * @param quality 图片质量 (1-100)
 * @returns 优化后的图片URL
 */
export function getOptimizedImageUrl(
  url: string,
  _width?: number,
  _height?: number,
  _quality?: number
): string {
  if (!url) return ''

  // 先处理URL格式，然后返回（如果需要优化功能，可以在Minio或CDN层处理）
  return getDisplayImageUrl(url)
}

/**
 * 检查图片服务是否可用
 * @returns Promise<boolean> 服务是否可用
 */
export async function checkImageServiceStatus(): Promise<boolean> {
  // 由于现在直接访问Minio，暂时返回true
  // 如果需要检查Minio服务状态，可以ping Minio服务器
  return true
}

/**
 * 预加载图片
 * @param url 图片URL
 * @returns Promise<boolean> 是否加载成功
 */
export function preloadImage(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false)
      return
    }
    
    // 在小程序中使用 uni.getImageInfo 来预加载图片
    uni.getImageInfo({
      src: url,
      success: () => resolve(true),
      fail: () => resolve(false)
    })
  })
}

/**
 * 批量预加载图片
 * @param urls 图片URL数组
 * @returns Promise<number> 成功加载的图片数量
 */
export async function preloadImages(urls: string[]): Promise<number> {
  if (!urls || urls.length === 0) return 0
  
  const results = await Promise.all(
    urls.map(url => preloadImage(url))
  )
  
  return results.filter(success => success).length
}

/**
 * 获取图片显示尺寸
 * @param url 图片URL
 * @returns Promise<{width: number, height: number} | null>
 */
export function getImageSize(url: string): Promise<{width: number, height: number} | null> {
  return new Promise((resolve) => {
    if (!url) {
      resolve(null)
      return
    }
    
    uni.getImageInfo({
      src: url,
      success: (res) => {
        resolve({
          width: res.width,
          height: res.height
        })
      },
      fail: () => resolve(null)
    })
  })
}

/**
 * 获取可显示的图片URL
 * 处理后端返回的图片URL，支持base64格式和普通URL
 * @param url 后端返回的图片URL（可能是base64格式或普通URL）
 * @returns 可显示的图片URL
 */
export function getDisplayImageUrl(url: string): string {
  if (!url) return ''

  // 如果是base64格式，直接返回
  if (url.startsWith('data:image/')) {
    return url
  }

  // 如果已经是完整的URL，直接返回（后端现在返回MinIO直接URL）
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果是静态文件路径，直接返回
  if (url.startsWith('/static/')) {
    return url
  }

  // 其他情况直接返回
  return url
}

/**
 * 获取静态资源图片URL
 * @param imageSrc 图片路径
 * @returns 处理后的图片URL
 */
export function getStaticImageUrl(imageSrc: string | undefined): string {
  if (!imageSrc) {
    // #ifdef APP-PLUS
    return 'images/no-image.svg'
    // #endif
    // #ifndef APP-PLUS
    return '/static/images/no-image.svg'
    // #endif
  }
  // 使用通用的图片URL处理函数
  return getDisplayImageUrl(imageSrc)
}