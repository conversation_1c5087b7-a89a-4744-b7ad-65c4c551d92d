-- 千年文旅系统 - 用户认证表结构
-- 只包含用户认证相关的核心表

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号（登录账号）',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知,1-男,2-女',
    `role` ENUM('GUEST', 'DISTRICT_ADMIN', 'CITY_ADMIN', 'PROVINCE_ADMIN', 'SUPER_ADMIN') NOT NULL DEFAULT 'GUEST' COMMENT '用户角色',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    `province_id` SMALLINT DEFAULT NULL COMMENT '管理的省份ID，仅管理员有效',
    `city_id` SMALLINT DEFAULT NULL COMMENT '管理的城市ID，仅管理员有效',
    `district_id` SMALLINT DEFAULT NULL COMMENT '管理的区县ID，仅管理员有效',
    `module_permissions` JSON DEFAULT NULL COMMENT '模块管理权限JSON：{"ancient_books":true,"paintings":false,"archives":true,"videos":false}',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    
    INDEX `idx_phone` (`phone`),
    INDEX `idx_role` (`role`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户会话表
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(255) NOT NULL UNIQUE COMMENT '会话标识',
    `access_token` VARCHAR(500) NOT NULL COMMENT '访问令牌',
    `refresh_token` VARCHAR(500) NOT NULL UNIQUE COMMENT '刷新令牌',
    `device_info` JSON DEFAULT NULL COMMENT '设备信息',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_access_token` (`access_token`(100)),
    INDEX `idx_refresh_token` (`refresh_token`(100)),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 3. 登录日志表
DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID，可为空（登录失败时）',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '尝试登录的手机号',
    `login_type` VARCHAR(20) NOT NULL DEFAULT 'password' COMMENT '登录方式：password',
    `success` BOOLEAN NOT NULL COMMENT '是否成功',
    `failure_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_login_type` (`login_type`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- 插入默认管理员账号
-- 密码：admin123
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `role`, `is_active`) 
VALUES (
    '13800000000', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS',
    '系统管理员', 
    'super_admin', 
    TRUE
);

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证创建结果
SELECT 'Auth tables created successfully!' as message;
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as admin_count
FROM users;

SELECT 
    '默认管理员账号信息:' as info,
    '手机号: 13800000000' as phone,
    '密码: admin123' as password,
    '⚠️ 请在生产环境中修改默认密码！' as warning;
