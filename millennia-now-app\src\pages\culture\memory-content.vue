<template>
  <view class="memory-content-page">
    <!-- 顶部封面区域 -->
    <view class="cover-section">
      <image class="cover-image"
             :src="headerBgImage"
             mode="aspectFill"></image>
      <view class="cover-overlay"></view>
      <view class="cover-content">
        <text class="place-name">{{ placeName }}</text>
        <text class="place-desc">{{ placeDesc }}</text>
      </view>

    </view>

    <!-- 简介 -->
    <view class="intro-section"
          v-if="introduction">
      <text class="intro-text">{{ introduction }}</text>
    </view>

    <!-- 时间轴装饰 -->
    <view class="timeline-decoration">
      <view class="timeline-line"></view>
      <view class="time-node"></view>
    </view>

    <!-- 城市记忆内容 -->
    <view class="memory-section">
      <view class="section-title">
        <text>当代城市记忆</text>
      </view>

      <view class="memory-timeline">
        <!-- 无数据提示 -->
        <view class="empty-tip"
              v-if="!memoryItems || memoryItems.length === 0">
          <text class="empty-text">暂无城市记忆数据</text>
        </view>

        <view v-for="(item, index) in memoryItems"
              :key="index"
              class="memory-item"
              :style="{ animationDelay: `${index * 0.2}s` }"
              @click="navigateToDetail(item.id)">
          <view class="time-marker">
            <view class="year-dot"></view>
            <view class="year-line"
                  v-if="index !== memoryItems.length - 1"></view>
          </view>

          <view class="memory-card">
            <view class="memory-year">{{ item.year }}</view>
            <view class="memory-content">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="memory-image"
                     @error="onImageError"></image>
              <view class="memory-info">
                <text class="memory-title">{{ item.title }}</text>
                <text class="memory-desc">{{ item.description || '点击查看详情' }}</text>
              </view>
            </view>

            <!-- 水墨效果 -->
            <view class="ink-effect"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer"
          v-if="footerText">
      <text class="footer-text">{{ footerText }}</text>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="photo-loading">
        <view class="photo-frame"></view>
        <view class="photo-develop"></view>
      </view>
      <text class="loading-text">记忆加载中...</text>
    </view>

    <!-- 动画装饰元素 -->
    <view class="animated-elements">
      <view class="floating-photo"
            v-for="n in 3"
            :key="n"></view>
      <view class="ink-wash"
            v-for="n in 5"
            :key="`ink-${n}`"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHeritagePageDataByRegion, MemoryItem } from '../../api/heritage'
import { getImageProxyUrl } from '../../utils/image'
import { regionManager } from '../../store/modules/region'
import { getBaseURL } from '../../config/api'

// 页面状态
const isLoading = ref(true)
const memoryItems = ref<MemoryItem[]>([])

// 地点信息
const placeName = ref('')
const placeDesc = ref('')
const headerBgImage = ref(
  `${getBaseURL().replace('/api/', '')}/static/image/memory.jpg`
)
const introduction = ref('')
const footerText = ref('')

// 初始化页面
onMounted(async () => {
  // 获取区域的城市记忆数据
  await loadRegionMemoryData()
})

// 加载特定区域的城市记忆列表
const loadRegionMemoryData = async () => {
  isLoading.value = true
  try {
    const regionIds = regionManager.currentRegionIds

    if (regionIds.provinceId) {
      const pageData = await getHeritagePageDataByRegion({
        province_id: regionIds.provinceId,
        city_id: regionIds.cityId,
        district_id: regionIds.districtId,
      })

      if (pageData) {
        // 设置地点信息
        placeName.value =
          pageData.place_info?.place_name ||
          regionManager.fullRegionName ||
          '城市记忆'
        placeDesc.value =
          pageData.place_info?.place_desc || '城市变迁的影像档案'
        headerBgImage.value = pageData.place_info?.header_bg_image
          ? getImageProxyUrl(pageData.place_info.header_bg_image)
          : `${getBaseURL().replace('/api', '')}/static/image/memory.jpg`
        introduction.value =
          pageData.place_info?.introduction ||
          '城市记忆是城市的灵魂，记录着这座城市的变迁、成长和故事。通过图像、文字和影音，我们共同守护这座城市的集体回忆。'
        footerText.value = pageData.place_info?.footer_text || ''

        // 设置城市记忆列表，按 sort_order 从小到大排序
        if (pageData.memory_data && pageData.memory_data.length > 0) {
          memoryItems.value = pageData.memory_data.sort(
            (a, b) => (a.sort_order || 0) - (b.sort_order || 0)
          )
        }
      }
    }
  } catch (error) {
    console.error('获取区域城市记忆数据失败:', error)
  } finally {
    isLoading.value = false

    // 延迟启动动画效果
    setTimeout(() => {
      startAnimations()
    }, 500)
  }
}

// 启动动画效果
const startAnimations = () => {
  // 这里可以添加一些控制动画的逻辑
  console.log('记忆动画已启动')
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  if (!imageSrc) {
    return '/static/images/no-image.svg'
  }
  // 使用图片代理URL处理函数
  return getImageProxyUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event: any) => {
  // 将图片源替换为错误占位图
  const target = event.target || event.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}

// 导航到城市记忆详情页面
const navigateToDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/culture/memory-detail?memory_id=${id}`,
  })
}
</script>

<style>
.memory-content-page {
  background-color: #f8f9fa;
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 动画装饰元素 */
.animated-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-photo {
  position: absolute;
  width: 150rpx;
  height: 150rpx;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
  opacity: 0.3;
  transform: rotate(0deg);
}

.floating-photo:nth-child(1) {
  top: 15%;
  left: 10%;
  animation: floatPhoto 15s ease-in-out infinite;
}

.floating-photo:nth-child(2) {
  top: 40%;
  right: 5%;
  animation: floatPhoto 18s ease-in-out 2s infinite reverse;
}

.floating-photo:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation: floatPhoto 20s ease-in-out 5s infinite;
}

@keyframes floatPhoto {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(30rpx, -20rpx) rotate(5deg);
  }
  50% {
    transform: translate(0rpx, 30rpx) rotate(2deg);
  }
  75% {
    transform: translate(-20rpx, -10rpx) rotate(-3deg);
  }
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
}

.ink-wash {
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.02) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.5);
}

.ink-wash:nth-child(1) {
  top: 10%;
  left: 20%;
  animation: inkWash 20s ease-in-out 0s infinite;
}

.ink-wash:nth-child(2) {
  top: 50%;
  left: 80%;
  animation: inkWash 25s ease-in-out 2s infinite;
}

.ink-wash:nth-child(3) {
  top: 80%;
  left: 15%;
  animation: inkWash 22s ease-in-out 5s infinite;
}

.ink-wash:nth-child(4) {
  top: 30%;
  left: 60%;
  animation: inkWash 18s ease-in-out 7s infinite;
}

.ink-wash:nth-child(5) {
  top: 70%;
  left: 40%;
  animation: inkWash 23s ease-in-out 10s infinite;
}

@keyframes inkWash {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  40% {
    opacity: 0.4;
  }
  80% {
    opacity: 0;
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
}

/* 顶部封面区域 */
.cover-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
  animation: fadeIn 1.5s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.cover-image {
  width: 100%;
  height: 100%;
  transform: scale(1.1);
  filter: sepia(0.2);
  animation: zoomOut 2s ease-out forwards;
}

@keyframes zoomOut {
  0% {
    transform: scale(1.1);
    filter: sepia(0.5) brightness(0.8);
  }
  100% {
    transform: scale(1);
    filter: sepia(0.2) brightness(1);
  }
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

.cover-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 1.5s ease-out forwards;
  animation-delay: 0.5s;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.place-name {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 10rpx;
}

.place-desc {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 简介样式 */
.intro-section {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 1.5s ease-out forwards;
  animation-delay: 0.8s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.intro-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(0, 0, 0, 0.02) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  animation: inkFade 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.intro-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  position: relative;
  z-index: 1;
}

/* 时间轴装饰 */
.timeline-decoration {
  position: relative;
  height: 60rpx;
  margin: 0 40rpx;
  opacity: 0;
  animation: fadeIn 1s ease-out forwards;
  animation-delay: 1.5s;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2rpx;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0.3),
    rgba(0, 0, 0, 0)
  );
  transform: scaleX(0);
  animation: lineExpand 2s ease-out forwards;
  animation-delay: 1.8s;
}

.time-node {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16rpx;
  height: 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: nodePulse 1.5s ease-out infinite;
  animation-delay: 2.3s;
}

@keyframes lineExpand {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

@keyframes nodePulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.5;
  }
}

/* 城市记忆区域样式 */
.memory-section {
  padding: 30rpx 20rpx;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 1s;
  position: relative;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  width: 0;
  height: 6rpx;
  background-color: #c8161e;
  border-radius: 3rpx;
  transform: translateX(-50%);
  animation: lineExpand 1.5s ease-out forwards;
  animation-delay: 1.5s;
}

.memory-timeline {
  padding: 20rpx 0;
  position: relative;
}

.empty-tip {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 记忆项时间轴样式 */
.memory-item {
  display: flex;
  margin-bottom: 40rpx;
  opacity: 0;
  animation: memoryFadeIn 0.8s ease-out forwards;
}

@keyframes memoryFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.time-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40rpx;
  margin-right: 20rpx;
}

.year-dot {
  width: 24rpx;
  height: 24rpx;
  background-color: #333;
  border-radius: 50%;
  margin-top: 40rpx;
  position: relative;
  z-index: 2;
  box-shadow: 0 0 0 rgba(0, 0, 0, 0.2);
  animation: dotPulse 2s infinite;
}

@keyframes dotPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

.year-line {
  width: 4rpx;
  background-color: #ddd;
  flex: 1;
  margin-top: 10rpx;
  position: relative;
  overflow: hidden;
}

.year-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background-color: #999;
  animation: lineGrow 2s ease-out forwards;
}

@keyframes lineGrow {
  0% {
    height: 0%;
  }
  100% {
    height: 100%;
  }
}

/* 记忆卡片样式 */
.memory-card {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.memory-card:active {
  transform: translateY(2rpx) scale(0.99);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.memory-year {
  font-size: 26rpx;
  font-weight: bold;
  color: #c8161e;
  margin-bottom: 15rpx;
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: rgba(200, 22, 30, 0.08);
  border-radius: 6rpx;
}

.memory-content {
  display: flex;
}

.memory-image {
  width: 180rpx;
  height: 140rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  object-fit: cover;
  filter: sepia(0.2);
  transition: filter 0.3s ease;
}

.memory-card:hover .memory-image {
  filter: sepia(0);
}

.memory-info {
  flex: 1;
}

.memory-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.memory-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 水墨效果 */
.ink-effect {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(
    ellipse at bottom right,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  animation: inkReveal 2s ease-out forwards;
  animation-delay: 0.5s;
}

@keyframes inkReveal {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 60rpx 0;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 2s;
}

.footer-text {
  font-size: 28rpx;
  color: #999;
}

/* 照片冲洗风格的加载动画 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.photo-loading {
  position: relative;
  width: 200rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.photo-frame {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 180rpx;
  height: 140rpx;
  border: 4rpx solid #333;
  background-color: #f5f5f5;
  animation: frameSway 3s ease-in-out infinite alternate;
}

.photo-develop {
  position: absolute;
  top: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0%;
  height: 140rpx;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.1)
  );
  animation: photoDevelop 2.5s linear infinite;
}

@keyframes frameSway {
  0% {
    transform: translateX(-50%) rotate(-2deg);
  }
  100% {
    transform: translateX(-50%) rotate(2deg);
  }
}

@keyframes photoDevelop {
  0% {
    width: 0%;
    opacity: 0.8;
  }
  85% {
    width: 100%;
    opacity: 0.5;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  font-family: 'KaiTi', 'STKaiti', serif;
  letter-spacing: 2rpx;
}
</style> 