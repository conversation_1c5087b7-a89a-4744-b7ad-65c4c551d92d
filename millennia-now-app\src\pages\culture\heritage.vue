<template>
  <view class="container">
    <!-- 调试信息
    <view v-if="!pageData && !isManageMode"
          style="padding: 20rpx; background: #f0f0f0; margin: 20rpx;">
      <text>调试信息：页面正在加载...</text>
      <text>isManageMode: {{ isManageMode }}</text>
      <text>pageData: {{ pageData ? '有数据' : '无数据' }}</text>
    </view> -->

    <!-- 管理模式区域选择器 -->
    <view class="region-selector"
          v-if="isManageMode">
      <view class="region-info">
        <text class="current-region">当前管理区域：{{ getCurrentRegionText() }}</text>
      </view>
      <view class="region-actions">
        <text class="region-btn"
              @click="selectRegion">切换区域</text>
      </view>
    </view>

    <!-- 区域选择弹窗 -->
    <view v-if="showRegionSelectPopup"
          class="popup-overlay"
          @click="closeRegionSelect">
      <view class="region-select-popup"
            @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择管理区域</text>
          <text @click="closeRegionSelect"
                class="close-btn">×</text>
        </view>

        <view class="region-form">
          <!-- 省份选择 -->
          <view v-if="canSelectProvince"
                class="form-item">
            <text class="form-label">省份</text>
            <view class="picker-wrapper">
              <!-- 使用原生方式选择 -->
              <view class="form-input picker-input"
                    @click="showNativePicker('province')">
                {{ regionForm.provinceIndex >= 0 && availableProvinces.length > 0 
                  ? availableProvinces[regionForm.provinceIndex]?.name 
                  : '请选择省份' }}
                <text class="picker-arrow">▼</text>
              </view>
            </view>
          </view>

          <!-- 城市选择 -->
          <view v-if="canSelectCity"
                class="form-item">
            <text class="form-label">城市</text>
            <view class="picker-wrapper">
              <view class="form-input picker-input"
                    @click="showNativePicker('city')">
                {{ regionForm.cityIndex >= 0 && availableCities.length > 0
                  ? availableCities[regionForm.cityIndex]?.name 
                  : '请选择城市' }}
                <text class="picker-arrow">▼</text>
              </view>
            </view>
          </view>

          <!-- 区县选择 -->
          <view v-if="canSelectDistrict"
                class="form-item">
            <text class="form-label">区县</text>
            <view class="picker-wrapper">
              <view class="form-input picker-input"
                    @click="showNativePicker('district')">
                {{ regionForm.districtIndex >= 0 && availableDistricts.length > 0
                  ? availableDistricts[regionForm.districtIndex]?.name 
                  : '请选择区县' }}
                <text class="picker-arrow">▼</text>
              </view>
            </view>
          </view>

          <!-- 返回上级选项 -->
          <view v-if="canReturnToParent"
                class="form-item">
            <button @click="returnToParentLevel"
                    class="return-btn">返回{{ getParentLevelName() }}</button>
          </view>

          <!-- 当前选择信息 -->
          <view class="current-selection">
            <text class="selection-text">当前选择：{{ getCurrentSelectionText() }}</text>
          </view>
        </view>

        <view class="popup-actions">
          <button @click="closeRegionSelect"
                  class="cancel-btn">取消</button>
          <button @click="confirmRegionSelect"
                  class="confirm-btn">确定</button>
        </view>
      </view>
    </view>

    <!-- 管理模式无数据提示 -->
    <view class="no-data-tip"
          v-if="isManageMode && !pageData">
      <view class="tip-content">
        <text class="tip-icon">📝</text>
        <text class="tip-title">当前区域暂无文源纪数据</text>
        <text class="tip-desc">您可以为当前区域创建文源纪内容</text>
        <view class="tip-actions">
          <text class="create-btn"
                @click="createNewPlace">创建地点信息</text>
        </view>
      </view>
    </view>

    <!-- 页面顶部信息 -->
    <view class="header-section"
          v-if="pageData"
          :style="{ marginTop: isManageMode ? '72rpx' : '0' }">
      <image class="header-bg"
             :src="pageData.headerBgImage"
             mode="aspectFill"></image>
      <view class="header-content">
        <text class="place-name">{{pageData.placeName}}</text>
        <text class="place-desc">{{pageData.placeDesc}}</text>
      </view>
      <!-- 管理模式下的编辑按钮 -->
      <view class="manage-overlay"
            v-if="isManageMode"
            @click="editPlace">
        <text class="edit-icon">✏️</text>
        <text class="edit-text">编辑地点信息</text>
      </view>
    </view>

    <!-- 简介 -->
    <view class="intro-section"
          v-if="pageData">
      <text class="intro-text">{{pageData?.introduction || ''}}</text>
    </view>

    <!-- 时间轴 -->
    <view class="timeline-section"
          v-if="pageData || isManageMode">
      <view class="timeline-title">
        <text>历史文脉溯源</text>
        <!-- 管理模式下添加新增按钮 -->
        <view class="icon-add-btn"
              v-if="isManageMode"
              @click="addTimelineItem">
          <image src="/static/icons/add.svg"
                 class="add-btn-icon"
                 mode="aspectFit" />
        </view>
      </view>

      <view class="timeline">
        <!-- 无数据提示 -->
        <view class="empty-tip"
              v-if="!pageData || !pageData.timelineData || pageData.timelineData.length === 0">
          <text class="empty-text">{{ isManageMode ? '暂无时间轴数据，点击右上角"+"按钮创建' : '暂无时间轴数据' }}</text>
        </view>

        <view class="timeline-item"
              v-for="(item, index) in pageData?.timelineData || []"
              :key="index"
              v-show="visibleTimelineItems.includes(index)">
          <!-- 时间标记 -->
          <view class="time-marker">
            <view class="time-dot"></view>
            <view class="time-line"
                  v-if="index !== (pageData?.timelineData?.length || 0) - 1"></view>
          </view>

          <!-- 内容卡片 -->
          <view class="time-card">
            <view class="time-period">
              <text class="period-name">{{ item.period }}</text>
              <text class="period-year">{{ item.year }}</text>
              <!-- 管理模式下的操作按钮 -->
              <view class="manage-actions timeline-actions"
                    v-if="isManageMode">
                <view class="icon-btn edit-btn"
                      @click.stop="editTimelineItem(index)">
                  <image src="/static/icons/edit.svg"
                         class="btn-icon"
                         mode="aspectFit" />
                </view>
                <view class="icon-btn delete-btn"
                      @click.stop="deleteTimelineItemHandler(index)">
                  <image src="/static/icons/delete.svg"
                         class="btn-icon"
                         mode="aspectFit" />
                </view>
              </view>
            </view>
            <view class="card-content">
              <image :src="getImageSrc(item.image)"
                     mode="aspectFill"
                     class="card-image"
                     @error="(e) => onImageError(e, item)"></image>
              <view class="card-text">
                <text class="card-title">{{ item.title }}</text>
                <text class="card-desc">{{ item.description }}</text>
              </view>
            </view>

            <!-- 文化遗产标签 -->
            <view class="heritage-tags"
                  v-if="item.heritages && item.heritages.length > 0">
              <view class="tag"
                    v-for="(tag, tagIndex) in item.heritages"
                    :key="tagIndex">
                {{ tag }}
              </view>
            </view>

            <!-- 展开更多 -->
            <view class="expand-section"
                  v-if="item.hasDetail"
                  @click.stop="expandDetail(index)">
              <text class="expand-text">{{ item.isExpanded ? '收起' : '展开更多' }}</text>
              <text class="expand-icon">{{ item.isExpanded ? '▲' : '▼' }}</text>
            </view>

            <!-- 详细内容 -->
            <view class="detail-content"
                  v-if="item.isExpanded && (item.detail || (item.detailImages && item.detailImages.length > 0))">
              <!-- 详细文本内容 -->
              <rich-text v-if="item.detail"
                         :nodes="item.detail"></rich-text>

              <!-- 相关图片展示 -->
              <view class="detail-images"
                    v-if="item.detailImages && item.detailImages.length > 0">
                <image v-for="(img, imgIndex) in item.detailImages"
                       :key="imgIndex"
                       :src="img"
                       mode="aspectFill"
                       class="detail-image"
                       @click="previewImage(item.detailImages, imgIndex)"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 现代文化传承 -->
    <view class="modern-section"
          v-if="pageData || isManageMode">
      <view class="section-title">
        <text>当代文化传承</text>
      </view>

      <view class="heritage-list">
        <!-- 无数据提示 -->
        <view class="empty-tip"
              v-if="!pageData || !pageData.heritageData || pageData.heritageData.length === 0">
          <text class="empty-text">{{ isManageMode ? '暂无文化传承数据，点击下方"+"新建文化传承' : '暂无文化传承数据' }}</text>
        </view>

        <view class="heritage-item"
              v-for="(item, index) in pageData?.heritageData || []"
              :key="index"
              @click="navigateToDetail(item.id)">
          <image :src="getImageSrc(item.image)"
                 mode="aspectFill"
                 class="heritage-image"
                 @error="(e) => onImageError(e, item)"></image>
          <view class="heritage-info">
            <text class="heritage-title">{{ item.title }}</text>
            <text class="heritage-type">{{ item.type }}</text>
            <text class="heritage-brief">{{ item.brief }}</text>
          </view>
          <!-- 管理模式下的操作按钮 -->
          <view class="manage-actions card-actions"
                v-if="isManageMode">
            <view class="icon-btn edit-btn"
                  @click.stop="editHeritageItem(index)">
              <image src="/static/icons/edit.svg"
                     class="btn-icon"
                     mode="aspectFit" />
            </view>
            <view class="icon-btn delete-btn"
                  @click.stop="deleteHeritageItemHandler(index)">
              <image src="/static/icons/delete.svg"
                     class="btn-icon"
                     mode="aspectFit" />
            </view>
          </view>
        </view>

        <!-- 管理模式下的新增按钮 - 放在卡片右上方 -->
        <view class="add-heritage-card"
              v-if="isManageMode"
              @click="addHeritageItem">
          <view class="add-content">
            <image src="/static/icons/add.svg"
                   class="add-icon-svg"
                   mode="aspectFit" />
            <text class="add-card-text">新增文化传承</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 当代城市记忆 -->
    <view class="memory-section"
          v-if="pageData || isManageMode">
      <view class="section-title">
        <text>当代城市记忆</text>
        <!-- 管理模式下添加新增按钮 -->
        <view class="icon-add-btn"
              v-if="isManageMode"
              @click="addMemoryItem">
          <image src="/static/icons/add.svg"
                 class="add-btn-icon"
                 mode="aspectFit" />
        </view>
      </view>

      <!-- 无数据提示 -->
      <view class="empty-tip"
            v-if="!pageData || !pageData.memoryData || pageData.memoryData.length === 0">
        <text class="empty-text">{{ isManageMode ? '暂无城市记忆数据，点击右上角"+"按钮创建' : '暂无城市记忆数据' }}</text>
      </view>

      <scroll-view scroll-x
                   class="memory-scroll"
                   v-if="pageData && pageData.memoryData && pageData.memoryData.length > 0">
        <view class="memory-item"
              v-for="(item, index) in pageData?.memoryData || []"
              :key="index"
              @click="navigateToMemory(item.id)">
          <image :src="getImageSrc(item.image)"
                 mode="aspectFill"
                 class="memory-image"
                 @error="(e) => onImageError(e, item)"></image>
          <view class="memory-title">{{ item.title }}</view>
          <view class="memory-year">{{ item.year }}</view>
          <!-- 管理模式下的操作按钮 -->
          <view class="manage-actions overlay"
                v-if="isManageMode">
            <view class="icon-btn edit-btn"
                  @click.stop="editMemoryItem(index)">
              <image src="/static/icons/edit.svg"
                     class="btn-icon"
                     mode="aspectFit" />
            </view>
            <view class="icon-btn delete-btn"
                  @click.stop="deleteMemoryItem(index)">
              <image src="/static/icons/delete.svg"
                     class="btn-icon"
                     mode="aspectFit" />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部信息 -->
    <view class="footer"
          v-if="pageData">
      <text class="footer-text">{{pageData?.footerText || ''}}</text>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, getCurrentInstance } from 'vue'
import { regionManager } from '../../store/modules/region'
import {
  getHeritagePageData,
  getHeritagePageDataByRegion,
  deleteTimelineItem,
  createHeritageItem,
  updateHeritageItem,
  deleteHeritageItem,
  deleteMemoryItem as deleteMemoryItemAPI,
  type HeritagePageData,
} from '../../api/heritage'
import {
  getProvinces,
  getCities,
  getDistricts,
  type Province,
  type City,
  type District,
} from '../../api/admin_divisions'
import { getStaticImageUrl } from '../../utils/image'

// 定义页面数据类型接口
interface TimelineItem {
  id?: number
  period: string
  year: string
  title: string
  description: string
  image: string
  heritages?: string[]
  hasDetail: boolean
  isExpanded: boolean
  detail?: string
  detailImages?: string[]
  sortOrder?: number
}

interface HeritageItem {
  id: number
  title: string
  type: string
  brief: string
  image: string
  sortOrder?: number
}

interface MemoryItem {
  id: number
  title: string
  year: string
  image: string
  sortOrder?: number
}

interface PageData {
  placeId?: number
  placeName: string
  placeDesc: string
  headerBgImage: string
  introduction: string
  footerText: string
  timelineData: TimelineItem[]
  heritageData: HeritageItem[]
  memoryData: MemoryItem[]
}

// 页面数据
const pageData = ref<PageData | null>(null)
const isLoading = ref(true)
// 添加时间轴动画控制变量
const timelineAnimationStarted = ref(false)
const visibleTimelineItems = ref<number[]>([])

// 管理模式相关
const isManageMode = ref(false)
const userInfo = ref({
  role: '',
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
  user_id: '',
})

// 当前管理的区域（可能与用户权限区域不同，但必须在权限范围内）
const currentManageRegion = ref({
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
})

// 区域选择弹窗相关状态
const showRegionSelectPopup = ref(false)
const regionForm = ref({
  provinceIndex: -1,
  cityIndex: -1,
  districtIndex: -1,
})

// 可选择的区域数据
const availableProvinces = ref<any[]>([])
const availableCities = ref<any[]>([])
const availableDistricts = ref<any[]>([])

// 计算属性：是否可以选择省份
const canSelectProvince = computed(() => {
  const user = userInfo.value
  return user.role === 'SUPER_ADMIN'
})

// 计算属性：是否可以选择城市
const canSelectCity = computed(() => {
  const user = userInfo.value
  return (
    ['SUPER_ADMIN', 'PROVINCE_ADMIN'].includes(user.role) &&
    availableCities.value.length > 0
  )
})

// 计算属性：是否可以选择区县
const canSelectDistrict = computed(() => {
  const user = userInfo.value
  return (
    ['SUPER_ADMIN', 'PROVINCE_ADMIN', 'CITY_ADMIN'].includes(user.role) &&
    availableDistricts.value.length > 0
  )
})

// 计算属性：是否可以返回上级
const canReturnToParent = computed(() => {
  const current = currentManageRegion.value
  const user = userInfo.value

  // 如果当前有区县，可以返回市级
  if (current.district_id) return true

  // 如果当前有城市但没有区县
  if (current.city_id && !current.district_id) {
    // 超级管理员和省级管理员可以返回省级
    if (['SUPER_ADMIN', 'PROVINCE_ADMIN'].includes(user.role)) return true
  }

  // 如果当前有省份但没有城市，超级管理员可以返回全国
  if (current.province_id && !current.city_id && user.role === 'SUPER_ADMIN')
    return true

  return false
})

// 展开详情
const expandDetail = (index: number) => {
  if (
    pageData.value &&
    pageData.value.timelineData &&
    pageData.value.timelineData[index]
  ) {
    const item = pageData.value.timelineData[index]

    // 确保该索引已经被添加到可见项目中（编辑模式下可能需要）
    if (!visibleTimelineItems.value.includes(index)) {
      visibleTimelineItems.value.push(index)
    }

    // 切换展开状态
    const oldExpanded = pageData.value.timelineData[index].isExpanded
    pageData.value.timelineData[index].isExpanded = !oldExpanded

    // 强制触发响应式更新
    pageData.value = { ...pageData.value }
  } else {
    // 无法展开详情，数据不存在
  }
}

// 启动时间轴动画，从古至今依次显示时间轴项目
const startTimelineAnimation = () => {
  if (timelineAnimationStarted.value || !pageData.value?.timelineData?.length)
    return

  timelineAnimationStarted.value = true
  visibleTimelineItems.value = []

  // 在编辑模式下，直接显示所有项目，不使用动画
  if (isManageMode.value) {
    pageData.value.timelineData.forEach((_, index) => {
      visibleTimelineItems.value.push(index)
    })
    return
  }

  // 在展示模式下，按照时间顺序显示每个时间轴项目
  pageData.value.timelineData.forEach((_, index) => {
    setTimeout(() => {
      visibleTimelineItems.value.push(index)
    }, 800 * (index + 1)) // 每800毫秒显示一个
  })
}

// 图片预览
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: images[current],
  })
}

// 获取图片源地址
const getImageSrc = (imageSrc: string | undefined) => {
  return getStaticImageUrl(imageSrc)
}

// 图片加载错误处理
const onImageError = (event: any, item: any) => {
  // 将图片源替换为错误占位图
  const target = event.target || event.currentTarget
  if (target) {
    target.src = '/static/images/no-image.svg'
  }
}

// 跳转到非遗详情
const navigateToDetail = (id: number) => {
  if (!id) {
    uni.showToast({
      title: '文化遗产ID缺失',
      icon: 'none',
    })
    return
  }

  uni.navigateTo({
    url: `/pages/culture/heritage-detail?heritage_id=${id}`,
  })
}

// 跳转到城市记忆详情
const navigateToMemory = (id: number) => {
  uni.navigateTo({
    url: `/pages/culture/memory-detail?memory_id=${id}`,
  })
}

// 获取页面数据
const fetchPageData = async () => {
  isLoading.value = true
  try {
    let data: HeritagePageData | null = null

    // 从pageOptions获取placeId (App环境下更可靠)
    const urlPlaceId = pageOptions.value?.placeId || pageOptions.value?.place_id

    if (urlPlaceId) {
      // 如果URL中有placeId，直接使用该ID获取数据
      data = await getHeritagePageData(urlPlaceId)
    } else if (isManageMode.value) {
      // 管理模式：使用当前管理区域，而不是当前定位
      const region = currentManageRegion.value

      // 如果有区域ID则获取数据，否则也允许进入管理模式（用于创建新数据）
      if (region.province_id) {
        try {
          const provinceId = parseInt(region.province_id) || undefined
          const cityId = region.city_id
            ? parseInt(region.city_id) || undefined
            : undefined
          const districtId = region.district_id
            ? parseInt(region.district_id) || undefined
            : undefined

          data = await getHeritagePageDataByRegion({
            province_id: provinceId,
            city_id: cityId,
            district_id: districtId,
          })
        } catch (err) {
          // 即使获取数据失败，也不阻止进入管理模式
        }
      } else {
        // 当前未选择区域，可以创建新数据
      }

      // 管理模式下，即使没有数据也继续显示管理界面
      if (!data && isManageMode.value) {
        // 当前区域无数据，允许进入创建
        pageData.value = null
        isLoading.value = false
        return
      }
    } else {
      // 普通模式：优先使用当前选择的区域ID获取数据
      const regionIds = regionManager.currentRegionIds

      if (regionIds.provinceId) {
        data = await getHeritagePageDataByRegion({
          province_id: regionIds.provinceId || undefined,
          city_id: regionIds.cityId || undefined,
          district_id: regionIds.districtId || undefined,
        })
      } else {
        // 如果没有区域ID，尝试使用缓存的heritage place
        const currentHeritagePlace = regionManager.currentHeritagePlace

        if (currentHeritagePlace) {
          data = await getHeritagePageData(currentHeritagePlace.id)
        }
      }
    }

    if (!data) {
      // 管理模式下，即使没有数据也允许进入页面
      if (isManageMode.value) {
        // 当前区域无数据，允许进入创建
        pageData.value = null
        isLoading.value = false
        return
      }

      // 普通模式下，如果没有获取到数据，显示提示并返回首页
      uni.showToast({
        title: '当前地区未构建该功能',
        icon: 'none',
        duration: 2000,
      })

      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }, 2000)
      return
    }

    // 转换API数据为页面数据格式
    pageData.value = {
      placeId: data.place_info.id, // 添加地点ID
      placeName: data.place_info.place_name,
      placeDesc: data.place_info.place_desc || '',
      headerBgImage:
        data.place_info.header_bg_image || '/static/images/no-image.svg',
      introduction: data.place_info.introduction || '',
      footerText: data.place_info.footer_text || '',
      timelineData: (data.timeline_data || [])
        .map((item: any) => ({
          id: item.id,
          period: item.period,
          year: item.year,
          title: item.title,
          description: item.description,
          image: item.image || '/static/images/no-image.svg',
          heritages: item.heritage_tags || [],
          hasDetail: Boolean(
            item.has_detail &&
              (item.detail ||
                (item.detail_images && item.detail_images.length > 0))
          ),
          isExpanded: false,
          detail: item.detail,
          detailImages: item.detail_images || [],
          sortOrder: item.sort_order || 0,
        }))
        .sort((a, b) => a.sortOrder - b.sortOrder), // 按 sort_order 从小到大排序
      heritageData: (data.heritage_data || [])
        .map((item: any) => ({
          id: item.id,
          title: item.title,
          type: item.type,
          brief: item.brief,
          image: item.image || '/static/images/no-image.svg',
          sortOrder: item.sort_order || 0,
        }))
        .sort((a, b) => a.sortOrder - b.sortOrder), // 按 sort_order 从小到大排序
      memoryData: (data.memory_data || [])
        .map((item: any) => ({
          id: item.id,
          title: item.title,
          year: item.year,
          image: item.image || '/static/images/no-image.svg',
          sortOrder: item.sort_order || 0,
        }))
        .sort((a, b) => a.sortOrder - b.sortOrder), // 按 sort_order 从小到大排序
    }

    isLoading.value = false

    // 重置时间轴动画状态并延迟启动动画
    timelineAnimationStarted.value = false
    // 延迟启动时间轴动画，等待页面渲染完成
    setTimeout(() => {
      startTimelineAnimation()
    }, 500)
  } catch (error: any) {
    // 管理模式下，即使加载失败也允许进入页面
    if (isManageMode.value) {
      // 数据加载失败，但允许进入创建
      pageData.value = null
      isLoading.value = false
      return
    }

    uni.showToast({
      title: '当前地区未构建该功能',
      icon: 'none',
      duration: 2000,
    })

    // 延迟返回首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 2000)

    // 其他错误，直接返回
    isLoading.value = false
  }
}

// 页面参数(App环境下使用)
const pageOptions = ref<any>({})

// 初始化管理模式
const initManageMode = () => {
  // 优先使用onLoad中保存的参数
  const options = pageOptions.value || {}

  // 检查是否为管理模式 - 支持多种可能的参数格式
  if (
    options.manage_mode === 'true' ||
    options.manage_mode === true ||
    options.manage_mode === '1' ||
    options.manage === 'true' ||
    options.manage === true
  ) {
    isManageMode.value = true

    // 获取用户信息 - 保持角色大写格式
    userInfo.value = {
      role: options.role || '', // 保持原始角色格式
      province_name: decodeURIComponent(options.province_name || ''),
      province_id: options.province_id || '',
      city_name: decodeURIComponent(options.city_name || ''),
      city_id: options.city_id || '',
      district_name: decodeURIComponent(options.district_name || ''),
      district_id: options.district_id || '',
      user_id: options.user_id || '',
    }

    // 直接从参数中获取区域信息，忽略角色判断
    if (options.province_id || options.province_name) {
      currentManageRegion.value = {
        province_name: userInfo.value.province_name,
        province_id: userInfo.value.province_id,
        city_name: userInfo.value.city_name,
        city_id: userInfo.value.city_id,
        district_name: userInfo.value.district_name,
        district_id: userInfo.value.district_id,
      }
    }
    // 根据管理员角色初始化管理区域（如果上面没有设置）
    else if (userInfo.value.role === 'SUPER_ADMIN') {
      // 超级管理员：默认不选择具体区域，显示全国或让其选择
      currentManageRegion.value = {
        province_name: '',
        province_id: '',
        city_name: '',
        city_id: '',
        district_name: '',
        district_id: '',
      }

      // 显示提示信息，提醒管理员选择区域
      setTimeout(() => {
        uni.showToast({
          title: '请选择要管理的区域',
          icon: 'none',
          duration: 2000,
        })
      }, 1000)
    } else if (userInfo.value.role === 'PROVINCE_ADMIN') {
      // 省级管理员：显示省级数据
      currentManageRegion.value = {
        province_name: userInfo.value.province_name,
        province_id: userInfo.value.province_id,
        city_name: '',
        city_id: '',
        district_name: '',
        district_id: '',
      }
    } else if (userInfo.value.role === 'CITY_ADMIN') {
      // 市级管理员：显示市级数据
      currentManageRegion.value = {
        province_name: userInfo.value.province_name,
        province_id: userInfo.value.province_id,
        city_name: userInfo.value.city_name,
        city_id: userInfo.value.city_id,
        district_name: '',
        district_id: '',
      }
    } else if (userInfo.value.role === 'DISTRICT_ADMIN') {
      // 区县管理员：显示区县数据
      currentManageRegion.value = {
        province_name: userInfo.value.province_name,
        province_id: userInfo.value.province_id,
        city_name: userInfo.value.city_name,
        city_id: userInfo.value.city_id,
        district_name: userInfo.value.district_name,
        district_id: userInfo.value.district_id,
      }
    }

    // 如果是从参数中获取的区域信息，显示当前管理区域
    if (options.manage_province_id || options.province_id) {
      setTimeout(() => {
        uni.showToast({
          title: `当前管理区域: ${getCurrentRegionText()}`,
          icon: 'none',
          duration: 2000,
        })
      }, 500)
    }
  } else {
    // 尝试通过角色信息识别管理模式
    if (options.role && options.role !== 'guest') {
      isManageMode.value = true

      // 获取用户信息
      userInfo.value = {
        role: options.role || '',
        province_name: decodeURIComponent(options.province_name || ''),
        province_id: options.province_id || '',
        city_name: decodeURIComponent(options.city_name || ''),
        city_id: options.city_id || '',
        district_name: decodeURIComponent(options.district_name || ''),
        district_id: options.district_id || '',
        user_id: options.user_id || '',
      }

      // 初始化管理区域
      const user = userInfo.value
      if (user.role === 'SUPER_ADMIN') {
        // 超级管理员：默认不选择具体区域
        currentManageRegion.value = {
          province_name: '',
          province_id: '',
          city_name: '',
          city_id: '',
          district_name: '',
          district_id: '',
        }
      } else if (user.role === 'PROVINCE_ADMIN') {
        // 省级管理员：显示省级数据
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: '',
          city_id: '',
          district_name: '',
          district_id: '',
        }
      } else if (user.role === 'CITY_ADMIN') {
        // 市级管理员：显示市级数据
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: user.city_name,
          city_id: user.city_id,
          district_name: '',
          district_id: '',
        }
      } else if (user.role === 'DISTRICT_ADMIN') {
        // 区县管理员：显示区县数据
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: user.city_name,
          city_id: user.city_id,
          district_name: user.district_name,
          district_id: user.district_id,
        }
      }
    }
  }

  // 调试和应急方案：确保总是能识别管理模式
  // 检查页面参数中是否有管理员角色但没有启用管理模式
  if (!isManageMode.value) {
    // 从URL或页面参数中查找role参数
    const pathWithParams = uni.getStorageSync('__current_path__') || ''

    if (
      pathWithParams &&
      pathWithParams.includes('role=') &&
      (pathWithParams.includes('PROVINCE_ADMIN') ||
        pathWithParams.includes('CITY_ADMIN') ||
        pathWithParams.includes('DISTRICT_ADMIN') ||
        pathWithParams.includes('SUPER_ADMIN'))
    ) {
      isManageMode.value = true

      // 使用简单的默认用户信息
      userInfo.value = {
        role: 'admin',
        province_name: '',
        province_id: '',
        city_name: '',
        city_id: '',
        district_name: '',
        district_id: '',
        user_id: '',
      }

      // 根据URL参数尝试解析角色类型
      if (pathWithParams.includes('SUPER_ADMIN')) {
        userInfo.value.role = 'SUPER_ADMIN'
      } else if (pathWithParams.includes('PROVINCE_ADMIN')) {
        userInfo.value.role = 'PROVINCE_ADMIN'
      } else if (pathWithParams.includes('CITY_ADMIN')) {
        userInfo.value.role = 'CITY_ADMIN'
      } else if (pathWithParams.includes('DISTRICT_ADMIN')) {
        userInfo.value.role = 'DISTRICT_ADMIN'
      }
    }
  }
}

// ========== 管理功能方法 ==========

// 编辑地点信息
const editPlace = () => {
  console.log('🔍 editPlace 被调用')
  console.log('🔍 isManageMode:', isManageMode.value)
  console.log('🔍 userInfo:', userInfo.value)
  console.log('🔍 pageData:', pageData.value)

  if (!pageData.value) {
    uni.showToast({
      title: '没有可编辑的地点信息',
      icon: 'none',
    })
    return
  }

  try {
    // 构建参数
    const user = userInfo.value
    const region = currentManageRegion.value
    const place = pageData.value
    // 构建编辑数据
    const editData = {
      // 用户信息
      role: user.role || '',
      province_name: user.province_name || '',
      province_id: user.province_id || '',
      city_name: user.city_name || '',
      city_id: user.city_id || '',
      district_name: user.district_name || '',
      district_id: user.district_id || '',
      // 当前管理区域
      manage_province_name: region.province_name || '',
      manage_province_id: region.province_id || '',
      manage_city_name: region.city_name || '',
      manage_city_id: region.city_id || '',
      manage_district_name: region.district_name || '',
      manage_district_id: region.district_id || '',
      // 地点完整信息
      place_id: (place.placeId || 1).toString(),
      place_name: place.placeName || '',
      place_desc: place.placeDesc || '',
      header_bg_image: place.headerBgImage || '',
      introduction: place.introduction || '',
      footer_text: place.footerText || '',
      // 确保所有值都有效
      manage_mode: 'true',
      // 添加时间戳确保数据是最新的
      timestamp: Date.now(),
    }

    console.log('地点编辑跳转数据:', editData)

    // 将完整数据存储到全局App实例
    const app = getApp()
    const globalData = app.globalData || (app.globalData = {})
    globalData.placeEditData = editData

    console.log('地点编辑全局数据已设置:', globalData.placeEditData)

    // 使用全局数据传递，确保移动端兼容性
    uni.navigateTo({
      url: `/pages/culture/place-edit?from=heritage&t=${Date.now()}`,
      success: () => {
        console.log('地点编辑页面跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } catch (err) {
    console.error('编辑处理失败', err)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  }
}

// 新增时间轴项目
const addTimelineItem = () => {
  if (!pageData.value?.placeId) {
    uni.showToast({
      title: '缺少地点信息',
      icon: 'none',
    })
    return
  }

  // 构建参数
  const user = userInfo.value
  const place = pageData.value

  // 计算新的 sort_order（当前最大值 + 1）
  const maxSortOrder = Math.max(
    0,
    ...(pageData.value.timelineData || []).map((item) => item.sortOrder || 0)
  )
  const newSortOrder = maxSortOrder + 1

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.timelineEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 地点信息
    place_id: (place.placeId || 0).toString(),
    place_name: place.placeName || '',
    // 创建模式：不传递timeline_id
    manage_mode: 'true',
    // 新项目的排序值
    sort_order: newSortOrder,
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  // 跳转到编辑页面，只传递最小参数标识数据来源
  uni.navigateTo({
    url: `/pages/culture/timeline-edit?from=heritage&t=${Date.now()}`,
  })
}

// 编辑时间轴项目
const editTimelineItem = (index: number) => {
  console.log('🔍 editTimelineItem 被调用, index:', index)
  if (!pageData.value?.timelineData || !pageData.value.timelineData[index]) {
    uni.showToast({
      title: '时间轴项目不存在',
      icon: 'none',
    })
    return
  }

  const timelineItem = pageData.value.timelineData[index]

  if (!timelineItem.id) {
    uni.showToast({
      title: '时间轴项目ID缺失，无法编辑',
      icon: 'none',
    })
    return
  }

  try {
    // 构建参数
    const user = userInfo.value
    const place = pageData.value

    // 构建编辑数据
    const editData = {
      // 用户信息
      role: user.role || '',
      province_name: user.province_name || '',
      province_id: user.province_id || '',
      city_name: user.city_name || '',
      city_id: user.city_id || '',
      district_name: user.district_name || '',
      district_id: user.district_id || '',
      // 地点信息
      place_id: (place.placeId || 0).toString(),
      place_name: place.placeName || '',
      // 时间轴项目完整信息
      timeline_id: timelineItem.id.toString(),
      period: timelineItem.period || '',
      year: timelineItem.year || '',
      title: timelineItem.title || '',
      description: timelineItem.description || '',
      image: timelineItem.image || '',
      has_detail: timelineItem.hasDetail || false,
      detail: timelineItem.detail || '',
      detail_images: timelineItem.detailImages || [],
      heritage_tags: timelineItem.heritages || [],
      is_active: true, // 默认激活状态
      sort_order: timelineItem.sortOrder || 0,
      // 确保所有值都有效
      manage_mode: 'true',
      // 添加时间戳确保数据是最新的
      timestamp: Date.now(),
    }

    console.log('历史文脉编辑跳转数据:', editData)

    // 将完整数据存储到全局App实例
    const app = getApp()
    const globalData = app.globalData || (app.globalData = {})
    globalData.timelineEditData = editData

    console.log('历史文脉编辑全局数据已设置:', globalData.timelineEditData)

    // 使用全局数据传递，确保移动端兼容性
    uni.navigateTo({
      url: `/pages/culture/timeline-edit?from=heritage&t=${Date.now()}`,
      success: () => {
        console.log('历史文脉编辑页面跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } catch (err) {
    console.error('编辑处理失败', err)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  }
}

// 删除时间轴项目
const deleteTimelineItemHandler = (index: number) => {
  if (!pageData.value?.timelineData || !pageData.value.timelineData[index]) {
    uni.showToast({
      title: '时间轴项目不存在',
      icon: 'none',
    })
    return
  }

  const timelineItem = pageData.value.timelineData[index]

  if (!timelineItem.id) {
    uni.showToast({
      title: '时间轴项目ID缺失',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: `确定要删除时间轴项目"${timelineItem.title}"吗？删除后无法恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用删除API
          const success = await deleteTimelineItem(timelineItem.id!)

          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })

            // 删除成功，刷新页面数据
            await fetchPageData()
          }
        } catch (error: any) {
          console.error('删除时间轴项目失败:', error)

          let errorMessage = '删除失败，请重试'
          if (error.response?.data?.detail) {
            errorMessage = error.response.data.detail
          } else if (error.message) {
            errorMessage = error.message
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
          })
        }
      }
    },
  })
}

// 新增文化遗产项目
const addHeritageItem = () => {
  if (!isManageMode.value || !pageData.value?.placeId) {
    uni.showToast({
      title: '当前不是管理模式或缺少地点信息',
      icon: 'none',
    })
    return
  }

  // 构建参数
  const user = userInfo.value
  const place = pageData.value

  // 计算新的 sort_order（当前最大值 + 1）
  const maxSortOrder = Math.max(
    0,
    ...(pageData.value.heritageData || []).map((item) => item.sortOrder || 0)
  )
  const newSortOrder = maxSortOrder + 1

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.heritageEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 地点信息
    place_id: (place.placeId || 0).toString(),
    place_name: place.placeName || '',
    // 创建模式标记
    manage_mode: 'true',
    // 新项目的排序值
    sort_order: newSortOrder,
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  console.log('文化传承编辑跳转，全局数据:', globalData.heritageEditData)

  // 使用全局数据传递，确保移动端兼容性
  uni.navigateTo({
    url: `/pages/culture/heritage-edit?from=heritage&t=${Date.now()}`,
    success: () => {
      console.log('文化传承编辑页面跳转成功')
    },
    fail: (err) => {
      console.error('文化传承编辑页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 编辑文化遗产项目
const editHeritageItem = (index: number) => {
  if (!pageData.value?.heritageData || !pageData.value.heritageData[index]) {
    uni.showToast({
      title: '文化遗产项目不存在',
      icon: 'none',
    })
    return
  }

  const heritageItem = pageData.value.heritageData[index]

  if (!heritageItem.id) {
    uni.showToast({
      title: '文化遗产项目ID缺失，无法编辑',
      icon: 'none',
    })
    return
  }

  // 构建参数
  const user = userInfo.value
  const place = pageData.value

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.heritageEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 地点信息
    place_id: (place.placeId || 0).toString(),
    place_name: place.placeName || '',
    // 文化传承项目完整信息
    heritage_id: heritageItem.id.toString(),
    title: heritageItem.title || '',
    brief: heritageItem.brief || '',
    image: heritageItem.image || '',
    type: heritageItem.type || '',
    is_active: true, // 默认激活状态
    sort_order: heritageItem.sortOrder || 0,
    // 确保所有值都有效
    manage_mode: 'true',
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  console.log(
    '文化传承编辑跳转（编辑模式），全局数据:',
    globalData.heritageEditData
  )

  // 使用全局数据传递，确保移动端兼容性
  uni.navigateTo({
    url: `/pages/culture/heritage-edit?from=heritage&t=${Date.now()}`,
    success: () => {
      console.log('文化传承编辑页面跳转成功（编辑模式）')
    },
    fail: (err) => {
      console.error('文化传承编辑页面跳转失败（编辑模式）:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 删除文化遗产项目
const deleteHeritageItemHandler = (index: number) => {
  if (!pageData.value?.heritageData || !pageData.value.heritageData[index]) {
    uni.showToast({
      title: '文化遗产项目不存在',
      icon: 'none',
    })
    return
  }

  const heritageItem = pageData.value.heritageData[index]

  if (!heritageItem.id) {
    uni.showToast({
      title: '文化遗产项目ID缺失',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: `确定要删除文化遗产项目"${heritageItem.title}"吗？删除后无法恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用删除API
          const success = await deleteHeritageItem(heritageItem.id)

          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })

            // 删除成功，刷新页面数据
            await fetchPageData()
          }
        } catch (error: any) {
          console.error('删除文化遗产项目失败:', error)

          let errorMessage = '删除失败，请重试'
          if (error.response?.data?.detail) {
            errorMessage = error.response.data.detail
          } else if (error.message) {
            errorMessage = error.message
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
          })
        }
      }
    },
  })
}

// 新增城市记忆项目
const addMemoryItem = () => {
  if (!isManageMode.value || !pageData.value?.placeId) {
    uni.showToast({
      title: '当前不是管理模式或缺少地点信息',
      icon: 'none',
    })
    return
  }

  // 构建参数
  const user = userInfo.value
  const place = pageData.value

  // 计算新的 sort_order（当前最大值 + 1）
  const maxSortOrder = Math.max(
    0,
    ...(pageData.value.memoryData || []).map((item) => item.sortOrder || 0)
  )
  const newSortOrder = maxSortOrder + 1

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.memoryEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 地点信息
    place_id: (place.placeId || 0).toString(),
    place_name: place.placeName || '',
    // 创建模式标记
    manage_mode: 'true',
    // 新项目的排序值
    sort_order: newSortOrder,
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  console.log('当代记忆编辑跳转，全局数据:', globalData.memoryEditData)

  // 使用全局数据传递，确保移动端兼容性
  uni.navigateTo({
    url: `/pages/culture/memory-edit?from=heritage&t=${Date.now()}`,
    success: () => {
      console.log('当代记忆编辑页面跳转成功')
    },
    fail: (err) => {
      console.error('当代记忆编辑页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 编辑城市记忆项目
const editMemoryItem = (index: number) => {
  if (!pageData.value?.memoryData || !pageData.value.memoryData[index]) {
    uni.showToast({
      title: '城市记忆项目不存在',
      icon: 'none',
    })
    return
  }

  const memoryItem = pageData.value.memoryData[index]

  if (!memoryItem.id) {
    uni.showToast({
      title: '城市记忆项目ID缺失，无法编辑',
      icon: 'none',
    })
    return
  }

  // 构建参数
  const user = userInfo.value
  const place = pageData.value

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.memoryEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 地点信息
    place_id: (place.placeId || 0).toString(),
    place_name: place.placeName || '',
    // 当代记忆项目完整信息
    memory_id: memoryItem.id.toString(),
    title: memoryItem.title || '',
    year: memoryItem.year || '',
    image: memoryItem.image || '',
    is_active: true, // 默认激活状态
    sort_order: memoryItem.sortOrder || 0,
    // 确保所有值都有效
    manage_mode: 'true',
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  console.log(
    '当代记忆编辑跳转（编辑模式），全局数据:',
    globalData.memoryEditData
  )

  // 使用全局数据传递，确保移动端兼容性
  uni.navigateTo({
    url: `/pages/culture/memory-edit?from=heritage&t=${Date.now()}`,
    success: () => {
      console.log('当代记忆编辑页面跳转成功（编辑模式）')
    },
    fail: (err) => {
      console.error('当代记忆编辑页面跳转失败（编辑模式）:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 删除城市记忆项目
const deleteMemoryItem = (index: number) => {
  if (!pageData.value?.memoryData || !pageData.value.memoryData[index]) {
    uni.showToast({
      title: '城市记忆项目不存在',
      icon: 'none',
    })
    return
  }

  const memoryItem = pageData.value.memoryData[index]

  if (!memoryItem.id) {
    uni.showToast({
      title: '城市记忆项目ID缺失',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: `确定要删除城市记忆项目"${memoryItem.title}"吗？删除后无法恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用删除API
          const success = await deleteMemoryItemAPI(memoryItem.id)

          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })

            // 删除成功，刷新页面数据
            await fetchPageData()
          }
        } catch (error: any) {
          console.error('删除城市记忆项目失败:', error)

          let errorMessage = '删除失败，请重试'
          if (error.response?.data?.detail) {
            errorMessage = error.response.data.detail
          } else if (error.message) {
            errorMessage = error.message
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
          })
        }
      }
    },
  })
}

// 选择区域
const selectRegion = async () => {
  const user = userInfo.value

  if (user.role === 'DISTRICT_ADMIN') {
    // 区县管理员：无法切换区域
    uni.showToast({
      title: '区县管理员无法切换管理区域',
      icon: 'none',
    })
    return
  }

  try {
    // 先确保弹窗状态重置
    showRegionSelectPopup.value = false

    // 初始化区域选择弹窗
    await initRegionSelectPopup()

    // 确保弹窗显示正确的当前选择
    syncCurrentRegionToForm()

    // 显示区域选择弹窗，延迟一下确保状态更新
    setTimeout(() => {
      showRegionSelectPopup.value = true
    }, 100)

    // 强制显示弹窗，解决在原生App下可能显示不出来的问题
    if (!showRegionSelectPopup.value) {
      showRegionSelectPopup.value = true
    }
  } catch (error) {
    uni.showToast({
      title: '获取区域数据失败，请稍后重试',
      icon: 'none',
    })
  }
}

// 将当前区域同步到表单
const syncCurrentRegionToForm = () => {
  const region = currentManageRegion.value
  const user = userInfo.value

  // 重置索引
  regionForm.value = {
    provinceIndex: -1,
    cityIndex: -1,
    districtIndex: -1,
  }

  // 如果有省份ID，找到对应的索引
  if (region.province_id && availableProvinces.value.length > 0) {
    const provinceIndex = availableProvinces.value.findIndex(
      (p) => p.province_id.toString() === region.province_id.toString()
    )
    if (provinceIndex >= 0) {
      regionForm.value.provinceIndex = provinceIndex
    }
  }

  // 如果有城市ID，找到对应的索引
  if (region.city_id && availableCities.value.length > 0) {
    const cityIndex = availableCities.value.findIndex(
      (c) => c.city_id.toString() === region.city_id.toString()
    )
    if (cityIndex >= 0) {
      regionForm.value.cityIndex = cityIndex
    }
  }

  // 如果有区县ID，找到对应的索引
  if (region.district_id && availableDistricts.value.length > 0) {
    const districtIndex = availableDistricts.value.findIndex(
      (d) => d.district_id.toString() === region.district_id.toString()
    )
    if (districtIndex >= 0) {
      regionForm.value.districtIndex = districtIndex
    }
  }
}

// 初始化区域选择弹窗
const initRegionSelectPopup = async () => {
  const user = userInfo.value

  // 清空数据
  availableProvinces.value = []
  availableCities.value = []
  availableDistricts.value = []

  try {
    if (user.role === 'SUPER_ADMIN') {
      // 超级管理员：可以选择所有省份
      availableProvinces.value = await getProvinces()

      // 如果省份为空，手动设置一些测试数据（应急方案）
      if (availableProvinces.value.length === 0) {
        availableProvinces.value = [
          { province_id: 51, name: '四川省' },
          { province_id: 11, name: '北京市' },
          { province_id: 31, name: '上海市' },
        ]
      }
    } else if (user.role === 'PROVINCE_ADMIN') {
      // 省级管理员：可以在自己的省份下选择城市，以及进一步选择区县
      if (user.province_id) {
        availableCities.value = await getCities(parseInt(user.province_id))

        // 如果城市为空，手动设置一些测试数据
        if (availableCities.value.length === 0) {
          availableCities.value = [
            { city_id: 4, name: '攀枝花市' },
            { city_id: 1, name: '成都市' },
          ]
        }
      }
    } else if (user.role === 'CITY_ADMIN') {
      // 市级管理员：只能在自己的城市下选择区县
      if (user.province_id && user.city_id) {
        availableDistricts.value = await getDistricts(
          parseInt(user.province_id),
          parseInt(user.city_id)
        )

        // 如果区县为空，手动设置一些测试数据
        if (availableDistricts.value.length === 0) {
          availableDistricts.value = [
            { district_id: 21, name: '米易县' },
            { district_id: 22, name: '盐边县' },
          ]
        }
      }
    }
  } catch (err) {
    // 不抛出异常，提供默认值
    if (user.role === 'SUPER_ADMIN' && availableProvinces.value.length === 0) {
      availableProvinces.value = [
        { province_id: 51, name: '四川省' },
        { province_id: 11, name: '北京市' },
        { province_id: 31, name: '上海市' },
      ]
    }
  }
}

// 关闭区域选择弹窗
const closeRegionSelect = () => {
  showRegionSelectPopup.value = false

  // 清空临时数据
  regionForm.value = {
    provinceIndex: -1,
    cityIndex: -1,
    districtIndex: -1,
  }
}

// 省份选择变化
const onProvinceChange = async (e: any) => {
  try {
    regionForm.value.provinceIndex = parseInt(e.detail.value)
    regionForm.value.cityIndex = -1
    regionForm.value.districtIndex = -1
    availableDistricts.value = []

    if (
      regionForm.value.provinceIndex >= 0 &&
      availableProvinces.value.length > 0
    ) {
      const province = availableProvinces.value[regionForm.value.provinceIndex]

      try {
        // 尝试获取该省的城市
        availableCities.value = await getCities(province.province_id)

        // 如果没有获取到城市，添加默认数据
        if (availableCities.value.length === 0) {
          availableCities.value = [
            { city_id: 1, name: '成都市' },
            { city_id: 4, name: '攀枝花市' },
          ]
        }
      } catch (err) {
        // 设置应急数据
        availableCities.value = [
          { city_id: 1, name: '成都市' },
          { city_id: 4, name: '攀枝花市' },
        ]
      }
    } else {
      availableCities.value = []
    }

    // 强制触发UI更新
    availableCities.value = [...availableCities.value]
  } catch (err) {
    // 继续执行
  }
}

// 城市选择变化
const onCityChange = async (e: any) => {
  try {
    regionForm.value.cityIndex = parseInt(e.detail.value)
    regionForm.value.districtIndex = -1

    if (regionForm.value.cityIndex >= 0 && availableCities.value.length > 0) {
      // 确保有合法的省份选择
      if (
        regionForm.value.provinceIndex < 0 ||
        !availableProvinces.value.length
      ) {
        return
      }

      const province = availableProvinces.value[regionForm.value.provinceIndex]
      const city = availableCities.value[regionForm.value.cityIndex]

      try {
        // 加载区县数据
        availableDistricts.value = await getDistricts(
          province.province_id,
          city.city_id
        )

        // 如果没有获取到区县，添加默认数据
        if (availableDistricts.value.length === 0) {
          availableDistricts.value = [
            { district_id: 21, name: '米易县' },
            { district_id: 22, name: '盐边县' },
          ]
        }
      } catch (err) {
        // 设置应急数据
        availableDistricts.value = [
          { district_id: 21, name: '米易县' },
          { district_id: 22, name: '盐边县' },
        ]
      }
    } else {
      availableDistricts.value = []
    }

    // 强制触发UI更新
    availableDistricts.value = [...availableDistricts.value]
  } catch (err) {
    // 继续执行
  }
}

// 区县选择变化
const onDistrictChange = (e: any) => {
  try {
    regionForm.value.districtIndex = parseInt(e.detail.value)
  } catch (err) {
    // 继续执行
  }
}

// 弹出原生选择器 (在部分App环境下可能更可靠)
const showNativePicker = (type: string) => {
  try {
    // 使用原生方式显示选择器
    if (type === 'province') {
      const items = availableProvinces.value.map((p) => p.name)
      if (items.length === 0) return

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          regionForm.value.provinceIndex = res.tapIndex
          onProvinceChange({ detail: { value: res.tapIndex.toString() } })
        },
      })
    } else if (type === 'city') {
      const items = availableCities.value.map((c) => c.name)
      if (items.length === 0) return

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          regionForm.value.cityIndex = res.tapIndex
          onCityChange({ detail: { value: res.tapIndex.toString() } })
        },
      })
    } else if (type === 'district') {
      const items = availableDistricts.value.map((d) => d.name)
      if (items.length === 0) return

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          regionForm.value.districtIndex = res.tapIndex
          onDistrictChange({ detail: { value: res.tapIndex.toString() } })
        },
      })
    }
  } catch (err) {
    // 继续执行
  }
}

// 获取当前区域文本 (用于显示在弹窗中)
const getCurrentSelectionText = () => {
  let text = ''

  if (
    regionForm.value.provinceIndex >= 0 &&
    availableProvinces.value.length > 0
  ) {
    text += availableProvinces.value[regionForm.value.provinceIndex].name

    if (regionForm.value.cityIndex >= 0 && availableCities.value.length > 0) {
      text += ' ' + availableCities.value[regionForm.value.cityIndex].name

      if (
        regionForm.value.districtIndex >= 0 &&
        availableDistricts.value.length > 0
      ) {
        text +=
          ' ' + availableDistricts.value[regionForm.value.districtIndex].name
      }
    }
  }

  return text || '未选择'
}

// 返回上级
const returnToParentLevel = () => {
  const current = currentManageRegion.value
  const user = userInfo.value

  if (current.district_id) {
    // 从区县返回市级
    currentManageRegion.value = {
      province_name: current.province_name,
      province_id: current.province_id,
      city_name: current.city_name,
      city_id: current.city_id,
      district_name: '',
      district_id: '',
    }
  } else if (current.city_id) {
    // 从市级返回省级（超级管理员和省级管理员都可以）
    if (['SUPER_ADMIN', 'PROVINCE_ADMIN'].includes(user.role)) {
      currentManageRegion.value = {
        province_name: current.province_name,
        province_id: current.province_id,
        city_name: '',
        city_id: '',
        district_name: '',
        district_id: '',
      }
    }
  } else if (current.province_id && user.role === 'SUPER_ADMIN') {
    // 超级管理员从省级返回全国
    currentManageRegion.value = {
      province_name: '',
      province_id: '',
      city_name: '',
      city_id: '',
      district_name: '',
      district_id: '',
    }
  }

  closeRegionSelect()

  // 重新加载数据
  fetchPageData()
    .then(() => {
      uni.showToast({
        title: '已返回上级区域',
        icon: 'success',
      })
    })
    .catch((err) => {
      console.error('重新加载数据失败:', err)
      uni.showToast({
        title: '数据加载失败',
        icon: 'none',
      })
    })
}

// 获取上级名称
const getParentLevelName = () => {
  const current = currentManageRegion.value
  const user = userInfo.value

  if (current.district_id) {
    return current.city_name || '市级'
  } else if (current.city_id) {
    // 超级管理员和省级管理员都可以返回省级
    if (['SUPER_ADMIN', 'PROVINCE_ADMIN'].includes(user.role)) {
      return current.province_name || '省级'
    }
  } else if (current.province_id && user.role === 'SUPER_ADMIN') {
    return '全国'
  }
  return '上级'
}

// 确认区域选择
const confirmRegionSelect = () => {
  const user = userInfo.value

  // 保存旧的区域信息，用于比较是否有变化
  const oldRegion = { ...currentManageRegion.value }

  try {
    if (user.role === 'SUPER_ADMIN') {
      // 超级管理员可以选择省份
      if (regionForm.value.provinceIndex >= 0) {
        const selectedProvince =
          availableProvinces.value[regionForm.value.provinceIndex]

        if (regionForm.value.cityIndex >= 0) {
          const selectedCity = availableCities.value[regionForm.value.cityIndex]

          if (regionForm.value.districtIndex >= 0) {
            // 选择了区县
            const selectedDistrict =
              availableDistricts.value[regionForm.value.districtIndex]
            currentManageRegion.value = {
              province_name: selectedProvince.name,
              province_id: selectedProvince.province_id.toString(),
              city_name: selectedCity.name,
              city_id: selectedCity.city_id.toString(),
              district_name: selectedDistrict.name,
              district_id: selectedDistrict.district_id.toString(),
            }
          } else {
            // 选择了城市
            currentManageRegion.value = {
              province_name: selectedProvince.name,
              province_id: selectedProvince.province_id.toString(),
              city_name: selectedCity.name,
              city_id: selectedCity.city_id.toString(),
              district_name: '',
              district_id: '',
            }
          }
        } else {
          // 只选择了省份
          currentManageRegion.value = {
            province_name: selectedProvince.name,
            province_id: selectedProvince.province_id.toString(),
            city_name: '',
            city_id: '',
            district_name: '',
            district_id: '',
          }
        }
      }
    } else if (user.role === 'PROVINCE_ADMIN') {
      // 省级管理员选择城市
      if (regionForm.value.cityIndex >= 0) {
        const selectedCity = availableCities.value[regionForm.value.cityIndex]

        if (regionForm.value.districtIndex >= 0) {
          // 选择了区县
          const selectedDistrict =
            availableDistricts.value[regionForm.value.districtIndex]
          currentManageRegion.value = {
            province_name: user.province_name,
            province_id: user.province_id,
            city_name: selectedCity.name,
            city_id: selectedCity.city_id.toString(),
            district_name: selectedDistrict.name,
            district_id: selectedDistrict.district_id.toString(),
          }
        } else {
          // 只选择了城市
          currentManageRegion.value = {
            province_name: user.province_name,
            province_id: user.province_id,
            city_name: selectedCity.name,
            city_id: selectedCity.city_id.toString(),
            district_name: '',
            district_id: '',
          }
        }
      } else {
        // 返回省级
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: '',
          city_id: '',
          district_name: '',
          district_id: '',
        }
      }
    } else if (user.role === 'CITY_ADMIN') {
      // 市级管理员选择区县
      if (regionForm.value.districtIndex >= 0) {
        const selectedDistrict =
          availableDistricts.value[regionForm.value.districtIndex]
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: user.city_name,
          city_id: user.city_id,
          district_name: selectedDistrict.name,
          district_id: selectedDistrict.district_id.toString(),
        }
      } else {
        // 返回市级
        currentManageRegion.value = {
          province_name: user.province_name,
          province_id: user.province_id,
          city_name: user.city_name,
          city_id: user.city_id,
          district_name: '',
          district_id: '',
        }
      }
    }
  } catch (err) {
    uni.showToast({
      title: '区域选择处理失败',
      icon: 'none',
    })
  }

  closeRegionSelect()

  // 检查区域是否有变化
  const hasChanged =
    oldRegion.province_id !== currentManageRegion.value.province_id ||
    oldRegion.city_id !== currentManageRegion.value.city_id ||
    oldRegion.district_id !== currentManageRegion.value.district_id

  // 只有在区域变化时才重新加载数据
  if (hasChanged) {
    // 重新加载数据
    fetchPageData()
      .then(() => {
        uni.showToast({
          title: `已切换到${getCurrentRegionText()}`,
          icon: 'success',
        })
      })
      .catch((err) => {
        uni.showToast({
          title: '数据加载失败',
          icon: 'none',
        })
      })
  } else {
    uni.showToast({
      title: `当前区域: ${getCurrentRegionText()}`,
      icon: 'none',
    })
  }
}

// 旧的选择函数已被新的弹窗选择器替代

// 获取当前区域文本
const getCurrentRegionText = () => {
  const region = currentManageRegion.value

  if (!region.province_id && !region.city_id && !region.district_id) {
    return '未选择区域'
  }

  if (region.district_name) {
    return `${region.province_name} ${region.city_name} ${region.district_name}`
  } else if (region.city_name) {
    return `${region.province_name} ${region.city_name}`
  } else if (region.province_name) {
    return region.province_name
  }
  return '未选择区域'
}

// 创建新地点
const createNewPlace = () => {
  // 构建参数
  const user = userInfo.value
  const region = currentManageRegion.value

  // 将完整数据存储到全局App实例
  const globalData = getApp().globalData || (getApp().globalData = {})
  globalData.placeEditData = {
    // 用户信息
    role: user.role || '',
    province_name: user.province_name || '',
    province_id: user.province_id || '',
    city_name: user.city_name || '',
    city_id: user.city_id || '',
    district_name: user.district_name || '',
    district_id: user.district_id || '',
    // 当前管理区域
    manage_province_name: region.province_name || '',
    manage_province_id: region.province_id || '',
    manage_city_name: region.city_name || '',
    manage_city_id: region.city_id || '',
    manage_district_name: region.district_name || '',
    manage_district_id: region.district_id || '',
    // 确保所有值都有效
    manage_mode: 'true',
    // 添加时间戳确保数据是最新的
    timestamp: Date.now(),
  }

  // 跳转到编辑页面，只传递最小参数标识数据来源
  uni.navigateTo({
    url: `/pages/culture/place-edit?from=heritage&t=${Date.now()}`,
  })
}

// 页面是否已经初始化的标志
const isPageInitialized = ref(false)

onMounted(() => {
  // 使用uni-app兼容的方式获取参数，避免使用window.location
  try {
    // 尝试使用uni.getLaunchOptionsSync获取参数
    const launchOptions = uni.getLaunchOptionsSync()

    // 尝试使用getCurrentPages获取当前页面实例（某些平台可能支持）
    try {
      const pages = getCurrentPages() as any[]
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1] as any
        if (currentPage && currentPage.options) {
          // 如果找到了role参数，可能是管理模式
          if (currentPage.options.role && !pageOptions.value.role) {
            pageOptions.value = { ...currentPage.options }
          }
        }
      }
    } catch (err) {
      // 忽略错误
    }

    // 如果我们处于管理模式但没有完整参数，尝试从页面实例获取
    if (launchOptions && launchOptions.query && launchOptions.query.role) {
      pageOptions.value = { ...launchOptions.query }
    }
  } catch (err) {
    // 忽略错误
  }

  initManageMode()
  fetchPageData()
  isPageInitialized.value = true

  // 在页面初始化后显示当前区域信息
  setTimeout(() => {
    const regionText = getCurrentRegionText()

    if (isManageMode.value && regionText !== '未选择区域') {
      uni.showToast({
        title: `当前管理区域: ${regionText}`,
        icon: 'none',
        duration: 2000,
      })
    }
  }, 1000)
})

// 页面卸载时清理
onUnmounted(() => {
  // 清理工作
})

// 定义页面生命周期函数供 uni-app 调用
;(getCurrentInstance()?.proxy as any).$options.onShow = () => {
  // 页面显示时检查是否需要刷新数据
  // 只有在页面已初始化且为管理模式时才刷新
  if (isPageInitialized.value && isManageMode.value) {
    console.log('从编辑页面返回，刷新数据')
    fetchPageData()
  }
}

// 添加onLoad生命周期函数，这在App环境下更可靠
;(getCurrentInstance()?.proxy as any).$options.onLoad = function (
  options: any
) {
  pageOptions.value = options || {}

  // 保存当前完整路径参数到本地存储，用于应急情况
  try {
    // 将参数转换为查询字符串
    const queryParams: string[] = []
    for (const key in options) {
      if (options[key] !== undefined && options[key] !== null) {
        queryParams.push(`${key}=${encodeURIComponent(options[key])}`)
      }
    }
    const queryString = queryParams.join('&')
    if (queryString) {
      const pathWithParams = `/pages/culture/heritage?${queryString}`
      uni.setStorageSync('__current_path__', pathWithParams)
    }
  } catch (err) {
    // 忽略错误
  }

  // 强制启用管理模式（临时调试用）
  if (options && options.role && !options.manage_mode) {
    pageOptions.value.manage_mode = 'true'
  }

  // 立即初始化管理模式
  initManageMode()

  // 如果已经初始化完成，重新获取数据
  if (isPageInitialized.value) {
    fetchPageData()
  }
}

// 添加页面显示时的逻辑，确保每次显示页面都检查参数
;(getCurrentInstance()?.proxy as any).$options.onShow = function () {
  // 页面显示时检查是否需要刷新数据
  // 只有在页面已初始化且为管理模式时才刷新
  if (isPageInitialized.value) {
    if (isManageMode.value) {
      fetchPageData()
    } else if (pageOptions.value.role && !pageOptions.value.manage_mode) {
      pageOptions.value.manage_mode = 'true'
      initManageMode()
      if (isManageMode.value) {
        fetchPageData()
      }
    }
  }
}
</script>

<style>
.container {
  background-color: #f8f8f8;
  position: relative;
  min-height: 100vh;
}

/* 区域选择器 */
.region-selector {
  /* position: fixed; */
  top: 88rpx;
  left: 0;
  right: 0;
  height: 72rpx;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  z-index: 998;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.region-info {
  flex: 1;
}

.region-actions {
  flex-shrink: 0;
}

.current-region {
  font-size: 26rpx;
  color: #333;
}

.change-region,
.region-btn {
  font-size: 24rpx;
  color: #007aff;
}

/* 无数据提示 */
.no-data-tip {
  position: fixed;
  top: 160rpx;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.tip-content {
  text-align: center;
  padding: 60rpx 40rpx;
}

.tip-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 30rpx;
}

.tip-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 40rpx;
}

.tip-actions {
  text-align: center;
}

.create-btn {
  background-color: #007aff;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: inline-block;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 管理模式覆盖层 */
.manage-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.edit-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.edit-text {
  font-size: 28rpx;
}

/* 顶部样式 */
.header-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
  animation: fadeIn 1.5s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.header-bg {
  width: 100%;
  height: 100%;
  transform: scale(1.1);
  animation: zoomOut 2s ease-out forwards;
}

@keyframes zoomOut {
  0% {
    transform: scale(1.1);
    filter: brightness(0.8) saturate(1.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
  }
}

.header-content {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 1.5s ease-out forwards;
  animation-delay: 0.5s;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.place-name {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 10rpx;
}

.place-desc {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 简介样式 */
.intro-section {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 1.5s ease-out forwards;
  animation-delay: 0.8s;
  position: relative;
  overflow: hidden;
}

.intro-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  animation: inkFade 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.intro-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  position: relative;
  z-index: 1;
}

/* 时间轴样式 */
.timeline-section {
  padding: 30rpx 20rpx;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 1s;
}

.timeline-title,
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-title:after,
.section-title:after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  width: 0;
  height: 6rpx;
  background-color: #c8161e;
  border-radius: 3rpx;
  transform: translateX(-50%);
  animation: lineExpand 1.5s ease-out forwards;
  animation-delay: 1.5s;
}

@keyframes lineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 80rpx;
  }
}

/* 墨滴效果 */
.timeline-title::before {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(200, 22, 30, 0.7) 0%,
    rgba(200, 22, 30, 0) 70%
  );
  border-radius: 50%;
  transform: translateX(-50%);
  opacity: 0;
  z-index: 0;
  animation: inkDrop 2s ease-out forwards;
  animation-delay: 1.2s;
}

@keyframes inkDrop {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    width: 100rpx;
    height: 100rpx;
    opacity: 0;
  }
}

/* 管理模式新增按钮 */
.timeline-title,
.section-title {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.add-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background-color: #007aff;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.add-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.add-text {
  font-size: 24rpx;
}

/* SVG图标新增按钮 */
.icon-add-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background-color: #007aff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.add-btn-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

/* 管理操作按钮 */
.manage-actions {
  display: flex;
  gap: 10rpx;
}

.manage-actions.vertical {
  flex-direction: column;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.manage-actions.card-actions {
  flex-direction: column;
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  gap: 8rpx;
}

.manage-actions.timeline-actions {
  flex-direction: row;
  position: absolute;
  right: 0;
  top: 0;
  gap: 8rpx;
}

.manage-actions.overlay {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 8rpx;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 60rpx;
}

.action-btn.edit {
  background-color: #007aff;
  color: #fff;
}

.action-btn.delete {
  background-color: #ff3b30;
  color: #fff;
}

/* SVG图标操作按钮 */
.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.icon-btn.edit-btn {
  background-color: #007aff;
}

.icon-btn.delete-btn {
  background-color: #ff3b30;
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.timeline {
  padding: 20rpx 0 20rpx 20rpx;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
  opacity: 0;
  transform: translateX(-50px);
  animation: slideIn 0.8s forwards;
  animation-fill-mode: both;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.time-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  width: 30rpx;
}

.time-dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #c8161e;
  margin-top: 30rpx;
  z-index: 2;
  position: relative;
  box-shadow: 0 0 0 rgba(200, 22, 30, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(200, 22, 30, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(200, 22, 30, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(200, 22, 30, 0);
  }
}

.time-line {
  width: 4rpx;
  background-color: #e0e0e0;
  flex: 1;
  margin-top: 10rpx;
  position: relative;
  overflow: hidden;
}

.time-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background-color: #c8161e;
  animation: lineGrow 2s ease-out forwards;
}

@keyframes lineGrow {
  0% {
    height: 0%;
  }
  100% {
    height: 100%;
  }
}

.time-card {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 水墨扩散效果 */
.time-card::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: 0;
  animation: inkSpread 1.5s ease-out forwards;
}

@keyframes inkSpread {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    width: 300%;
    height: 300%;
    opacity: 0;
  }
}

.time-period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.period-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.period-year {
  font-size: 28rpx;
  color: #999;
}

.card-content {
  display: flex;
  margin-bottom: 20rpx;
}

.card-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.card-text {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.card-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.heritage-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.tag {
  background-color: #f0f5ff;
  color: #3370ff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.expand-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
  padding: 10rpx 0;
  border-top: 1px dashed #efefef;
  cursor: pointer;
  position: relative;
  z-index: 10;
  background-color: transparent;
}

.expand-section:hover {
  background-color: rgba(51, 112, 255, 0.05);
}

.expand-section:active {
  background-color: rgba(51, 112, 255, 0.1);
}

.expand-text {
  font-size: 26rpx;
  color: #3370ff;
  margin-right: 10rpx;
}

.expand-icon {
  font-size: 20rpx;
  color: #3370ff;
}

.detail-content {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px dashed #efefef;
}

.detail-content rich-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.detail-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

/* 非遗传承样式 */
.modern-section,
.memory-section {
  padding: 40rpx 20rpx;
}

.heritage-list {
  padding: 0 20rpx;
}

.heritage-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.heritage-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 30rpx;
}

.heritage-info {
  flex: 1;
}

.heritage-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.heritage-type {
  font-size: 24rpx;
  color: #c8161e;
  background-color: #fff0f0;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  display: inline-block;
  margin-bottom: 16rpx;
}

.heritage-brief {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 新增文化传承卡片 */
.add-heritage-card {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2rpx dashed #007aff;
  border-radius: 12rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
  min-height: 200rpx;
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #007aff;
}

.add-icon-svg {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1028%)
    hue-rotate(204deg) brightness(95%) contrast(101%);
}

.add-card-text {
  font-size: 28rpx;
  color: #007aff;
  font-weight: 500;
}

/* 当代记忆样式 */
.memory-scroll {
  white-space: nowrap;
  padding: 20rpx;
}

.memory-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.memory-image {
  width: 280rpx;
  height: 200rpx;
}

.memory-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 16rpx 20rpx 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.memory-year {
  font-size: 24rpx;
  color: #999;
  padding: 0 20rpx 16rpx;
}

/* 底部样式 */
.footer {
  text-align: center;
  padding: 60rpx 0;
}

.footer-text {
  font-size: 28rpx;
  color: #999;
}

/* 区域选择弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.region-select-popup {
  background-color: #fff;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.close-btn:active {
  background-color: #f5f5f5;
}

.region-form {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
}

.picker-input {
  background-color: #f8f9fa;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.return-btn {
  width: 100%;
  padding: 20rpx;
  background-color: #6c757d;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.popup-actions {
  display: flex;
  border-top: 1px solid #eee;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  text-align: center;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #6c757d;
}

.confirm-btn {
  background-color: #007aff;
  color: #fff;
}

.current-selection {
  margin-top: 20rpx;
  padding: 15rpx;
  background-color: #f0f9ff;
  border-radius: 8rpx;
  border: 1px solid #d0e6ff;
}

.selection-text {
  font-size: 26rpx;
  color: #007aff;
}

/* 加载中样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #c8161e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.picker-wrapper {
  position: relative;
  width: 100%;
}

.picker-backup-btn {
  display: none;
}
</style>
