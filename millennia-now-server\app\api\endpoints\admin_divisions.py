from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional

from app.database.db import get_db
from app.models.admin_divisions import Province, City, District, Street
from app.schemas.admin_division_schemas import (
    ProvinceListResponse, CityListResponse, DistrictListResponse, StreetListResponse,
    ProvinceResponse, CityResponse, DistrictResponse, StreetResponse, FlattenedAddressInfo
)
from app.core.exceptions import ResourceNotFoundException, InvalidParameterException, DatabaseException
from app.core.cache import cached

router = APIRouter(tags=["行政区划"])


@router.get("/provinces", response_model=ProvinceListResponse, summary="获取所有省份")
@cached(ttl=86400)  # 省份数据缓存一天
async def get_provinces(
    db: Session = Depends(get_db)
):
    """
    获取所有省份信息
    
    返回中国所有省、自治区、直辖市和特别行政区的列表
    """
    try:
        provinces = db.query(Province).all()
        return ProvinceListResponse(
            total=len(provinces),
            items=provinces
        )
    except SQLAlchemyError as e:
        raise DatabaseException(f"获取省份列表失败: {str(e)}")


@router.get("/cities", response_model=CityListResponse, summary="获取指定省份下的城市")
@cached(ttl=86400)  # 城市数据缓存一天
async def get_cities(
    province_id: int = Query(..., description="省份编码"),
    db: Session = Depends(get_db)
):
    """
    获取指定省份下的所有城市
    
    - **province_id**: 省份编码
    """
    try:
        # 验证省份是否存在
        province = db.query(Province).filter(Province.province_id == province_id).first()
        if not province:
            raise ResourceNotFoundException("Province", province_id)
        
        cities = db.query(City).filter(City.province_id == province_id).all()
        return CityListResponse(
            total=len(cities),
            items=cities
        )
    except ResourceNotFoundException:
        raise
    except SQLAlchemyError as e:
        raise DatabaseException(f"获取城市列表失败: {str(e)}")


@router.get("/districts", response_model=DistrictListResponse, summary="获取指定城市下的区县")
@cached(ttl=86400)  # 区县数据缓存一天
async def get_districts(
    province_id: int = Query(..., description="省份编码"),
    city_id: int = Query(..., description="城市编码"),
    db: Session = Depends(get_db)
):
    """
    获取指定城市下的所有区县
    
    - **province_id**: 省份编码
    - **city_id**: 城市编码
    """
    try:
        # 验证城市是否存在
        city = db.query(City).filter(
            City.province_id == province_id,
            City.city_id == city_id
        ).first()
        if not city:
            raise ResourceNotFoundException("City", f"{province_id}-{city_id}")
        
        districts = db.query(District).filter(
            District.province_id == province_id,
            District.city_id == city_id
        ).all()
        
        return DistrictListResponse(
            total=len(districts),
            items=districts
        )
    except ResourceNotFoundException:
        raise
    except SQLAlchemyError as e:
        raise DatabaseException(f"获取区县列表失败: {str(e)}")


@router.get("/streets", response_model=StreetListResponse, summary="获取指定区县下的街道")
@cached(ttl=86400)  # 街道数据缓存一天
async def get_streets(
    province_id: int = Query(..., description="省份编码"),
    city_id: int = Query(..., description="城市编码"),
    district_id: int = Query(..., description="区县编码"),
    db: Session = Depends(get_db)
):
    """
    获取指定区县下的所有街道
    
    - **province_id**: 省份编码
    - **city_id**: 城市编码
    - **district_id**: 区县编码
    """
    try:
        # 验证区县是否存在
        district = db.query(District).filter(
            District.province_id == province_id,
            District.city_id == city_id,
            District.district_id == district_id
        ).first()
        if not district:
            raise ResourceNotFoundException("District", f"{province_id}-{city_id}-{district_id}")
        
        streets = db.query(Street).filter(
            Street.province_id == province_id,
            Street.city_id == city_id,
            Street.district_id == district_id
        ).all()
        
        return StreetListResponse(
            total=len(streets),
            items=streets
        )
    except ResourceNotFoundException:
        raise
    except SQLAlchemyError as e:
        raise DatabaseException(f"获取街道列表失败: {str(e)}")


@router.get("/districts/search", response_model=DistrictListResponse, summary="在指定省份下搜索区县")
@cached(ttl=86400)  # 区县搜索结果缓存一天
async def search_districts_by_name(
    province_id: int = Query(..., description="省份编码"),
    district_name: str = Query(..., description="区县名称"),
    db: Session = Depends(get_db)
):
    """
    在指定省份下搜索匹配的区县信息
    
    - **province_id**: 省份编码
    - **district_name**: 区县名称（支持模糊匹配）
    """
    try:
        # 验证省份是否存在
        province = db.query(Province).filter(Province.province_id == province_id).first()
        if not province:
            raise ResourceNotFoundException("Province", province_id)
        
        # 在该省份下搜索所有匹配的区县
        districts = db.query(District).filter(
            District.province_id == province_id
        ).all()
        
        # 进行名称匹配
        matched_districts = []
        
        # 精确匹配
        exact_match = [d for d in districts if d.name == district_name]
        if exact_match:
            matched_districts.extend(exact_match)
        else:
            # 模糊匹配（去掉区县市等后缀）
            clean_name = district_name.replace('区', '').replace('县', '').replace('市', '').replace('旗', '')
            for district in districts:
                district_clean_name = district.name.replace('区', '').replace('县', '').replace('市', '').replace('旗', '')
                if (district_clean_name == clean_name or 
                    district.name.find(clean_name) != -1 or 
                    clean_name.find(district_clean_name) != -1):
                    matched_districts.append(district)
        
        return DistrictListResponse(
            total=len(matched_districts),
            items=matched_districts
        )
    except ResourceNotFoundException:
        raise
    except SQLAlchemyError as e:
        raise DatabaseException(f"搜索区县失败: {str(e)}")


@router.get("/address-info", response_model=FlattenedAddressInfo, summary="获取完整地址信息")
@cached(ttl=86400)  # 地址信息缓存一天
async def get_address_info(
    province_id: int = Query(..., description="省份编码"),
    city_id: Optional[int] = Query(None, description="城市编码"),
    district_id: Optional[int] = Query(None, description="区县编码"),
    street_id: Optional[int] = Query(None, description="街道编码"),
    db: Session = Depends(get_db)
):
    """
    根据编码获取完整的地址信息，支持任意级别的编码组合
    
    - **province_id**: 省份编码
    - **city_id**: 城市编码 (可选)
    - **district_id**: 区县编码 (可选)
    - **street_id**: 街道编码 (可选)
    """
    try:
        # 获取省份信息
        province = db.query(Province).filter(Province.province_id == province_id).first()
        if not province:
            raise ResourceNotFoundException("Province", province_id)
        
        result = FlattenedAddressInfo(
            province_id=province.province_id,
            province_name=province.name
        )
        
        # 如果提供了城市编码，获取城市信息
        if city_id:
            city = db.query(City).filter(
                City.province_id == province_id,
                City.city_id == city_id
            ).first()
            if not city:
                raise ResourceNotFoundException("City", f"{province_id}-{city_id}")
            
            result.city_id = city.city_id
            result.city_name = city.name
            
            # 如果提供了区县编码，获取区县信息
            if district_id:
                district = db.query(District).filter(
                    District.province_id == province_id,
                    District.city_id == city_id,
                    District.district_id == district_id
                ).first()
                if not district:
                    raise ResourceNotFoundException("District", f"{province_id}-{city_id}-{district_id}")
                
                result.district_id = district.district_id
                result.district_name = district.name
                result.postal_code = district.postal_code
                
                # 如果提供了街道编码，获取街道信息
                if street_id:
                    street = db.query(Street).filter(
                        Street.province_id == province_id,
                        Street.city_id == city_id,
                        Street.district_id == district_id,
                        Street.street_id == street_id
                    ).first()
                    if not street:
                        raise ResourceNotFoundException("Street", f"{province_id}-{city_id}-{district_id}-{street_id}")
                    
                    result.street_id = street.street_id
                    result.street_name = street.name
                    if not result.postal_code and street.postal_code:
                        result.postal_code = street.postal_code
        
        return result
    except ResourceNotFoundException:
        raise
    except SQLAlchemyError as e:
        raise DatabaseException(f"获取地址信息失败: {str(e)}")