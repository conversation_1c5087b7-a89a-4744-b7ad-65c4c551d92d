from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator

# 基础模式
class AncientBookVolumeBase(BaseModel):
    volume_number: int = Field(..., gt=0, description="卷册序号")
    volume_title: Optional[str] = Field(None, max_length=200, description="卷册标题")
    start_page: Optional[str] = Field(None, max_length=20, description="起始页码标识")
    end_page: Optional[str] = Field(None, max_length=20, description="结束页码标识")
    content_description: Optional[str] = Field(None, description="内容描述")
    notes: Optional[str] = Field(None, description="备注信息")
    status: str = Field("draft", description="状态")

class AncientBookVolumeCreate(AncientBookVolumeBase):
    book_id: int = Field(..., gt=0, description="古籍ID")

class AncientBookVolumeUpdate(BaseModel):
    volume_number: Optional[int] = Field(None, gt=0)
    volume_title: Optional[str] = Field(None, max_length=200)
    start_page: Optional[str] = Field(None, max_length=20)
    end_page: Optional[str] = Field(None, max_length=20)
    content_description: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = None

class AncientBookVolumeResponse(AncientBookVolumeBase):
    id: int
    book_id: int
    total_pages: int
    created_by: Optional[int]
    updated_by: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 页面相关模式
class AncientBookPageBase(BaseModel):
    page_number: int = Field(..., gt=0, description="页码序号")
    page_label: Optional[str] = Field(None, max_length=50, description="页码标识")
    page_type: str = Field("content", description="页面类型")

class AncientBookPageCreate(AncientBookPageBase):
    volume_id: int = Field(..., gt=0, description="卷册ID")
    image_url: str = Field(..., max_length=500, description="页面图片URL")
    image_width: Optional[int] = Field(None, ge=0)
    image_height: Optional[int] = Field(None, ge=0)
    image_size: Optional[int] = Field(None, ge=0, description="图片文件大小（字节）")

class AncientBookPageUpdate(BaseModel):
    page_label: Optional[str] = Field(None, max_length=50)
    page_type: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=500)
    ocr_text: Optional[str] = None
    corrected_text: Optional[str] = None
    is_corrected: Optional[int] = Field(None, ge=0, le=1)
    ocr_confidence: Optional[Decimal] = Field(None, ge=0, le=1)

class AncientBookPageResponse(AncientBookPageBase):
    id: int
    volume_id: int
    image_url: str
    image_width: Optional[int]
    image_height: Optional[int]
    image_size: Optional[int]
    ocr_text: Optional[str]
    ocr_confidence: Optional[Decimal]
    ocr_processed_at: Optional[datetime]
    ocr_status: str
    corrected_text: Optional[str]
    is_corrected: bool
    corrected_by: Optional[int]
    corrected_at: Optional[datetime]
    created_by: Optional[int]
    updated_by: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 章节相关模式
class AncientBookChapterBase(BaseModel):
    chapter_number: int = Field(..., gt=0, description="章节序号")
    chapter_title: str = Field(..., max_length=200, description="章节标题")
    description: Optional[str] = Field(None, description="章节描述")

class AncientBookChapterCreate(AncientBookChapterBase):
    volume_id: int = Field(..., gt=0, description="卷册ID")
    start_page_id: Optional[int] = Field(None, gt=0, description="起始页面ID")
    end_page_id: Optional[int] = Field(None, gt=0, description="结束页面ID")

class AncientBookChapterUpdate(BaseModel):
    chapter_title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    start_page_id: Optional[int] = Field(None, gt=0)
    end_page_id: Optional[int] = Field(None, gt=0)

class AncientBookChapterResponse(AncientBookChapterBase):
    id: int
    volume_id: int
    start_page_id: Optional[int]
    end_page_id: Optional[int]
    created_by: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# OCR任务相关模式
class OCRTaskBase(BaseModel):
    ocr_provider: Optional[str] = Field(None, max_length=50, description="OCR服务提供商")

class OCRTaskCreate(OCRTaskBase):
    page_id: int = Field(..., gt=0, description="页面ID")

class OCRTaskUpdate(BaseModel):
    task_status: Optional[str] = None
    error_message: Optional[str] = None
    ocr_provider: Optional[str] = Field(None, max_length=50)
    processing_time_ms: Optional[int] = Field(None, ge=0)
    character_count: Optional[int] = Field(None, ge=0)
    confidence_avg: Optional[Decimal] = Field(None, ge=0, le=1)

class OCRTaskResponse(OCRTaskBase):
    id: int
    page_id: int
    task_status: str
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    processing_time_ms: Optional[int]
    character_count: Optional[int]
    confidence_avg: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 批量操作模式
class BatchPageUpload(BaseModel):
    volume_id: int = Field(..., gt=0, description="卷册ID")
    pages: List[Dict[str, Any]] = Field(..., description="页面数据列表")
    
    @validator('pages')
    def validate_pages(cls, v):
        if not v:
            raise ValueError('页面列表不能为空')
        if len(v) > 100:
            raise ValueError('一次最多上传100个页面')
        return v

class BatchPageUploadResponse(BaseModel):
    success_count: int
    failed_count: int
    total_count: int
    success_pages: List[AncientBookPageResponse]
    failed_pages: List[Dict[str, Any]]

# OCR批量处理模式
class BatchOCRRequest(BaseModel):
    page_ids: List[int] = Field(..., description="页面ID列表")
    ocr_provider: Optional[str] = Field("default", description="OCR服务提供商")
    
    @validator('page_ids')
    def validate_page_ids(cls, v):
        if not v:
            raise ValueError('页面ID列表不能为空')
        if len(v) > 50:
            raise ValueError('一次最多处理50个页面')
        return v

class BatchOCRResponse(BaseModel):
    task_count: int
    task_ids: List[int]
    message: str

# 详细卷册信息（包含页面和章节）
class AncientBookVolumeDetail(AncientBookVolumeResponse):
    pages: List[AncientBookPageResponse] = []
    chapters: List[AncientBookChapterResponse] = []
    
    class Config:
        from_attributes = True

# 搜索和筛选模式
class VolumeSearchParams(BaseModel):
    book_id: Optional[int] = None
    status: Optional[str] = None
    page_type: Optional[str] = None
    ocr_status: Optional[str] = None
    search_text: Optional[str] = None  # 在OCR文字中搜索
    skip: int = Field(0, ge=0)
    limit: int = Field(20, ge=1, le=100)

class PageSearchParams(BaseModel):
    volume_id: Optional[int] = None
    page_type: Optional[str] = None
    ocr_status: Optional[str] = None
    is_corrected: Optional[bool] = None
    search_text: Optional[str] = None
    skip: int = Field(0, ge=0)
    limit: int = Field(50, ge=1, le=200)

# 统计信息模式
class VolumeStatistics(BaseModel):
    total_volumes: int
    total_pages: int
    ocr_completed_pages: int
    corrected_pages: int
    ocr_completion_rate: float
    correction_rate: float

class PageStatistics(BaseModel):
    total_pages: int
    by_type: Dict[str, int]
    by_ocr_status: Dict[str, int]
    avg_confidence: Optional[float]

# 响应列表模式
class VolumeListResponse(BaseModel):
    items: List[AncientBookVolumeResponse]
    total: int
    skip: int
    limit: int

class PageListResponse(BaseModel):
    items: List[AncientBookPageResponse]
    total: int
    skip: int
    limit: int

class ChapterListResponse(BaseModel):
    items: List[AncientBookChapterResponse]
    total: int
    skip: int
    limit: int 