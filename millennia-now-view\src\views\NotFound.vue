<template>
  <div class="not-found">
    <!-- 使用小程序返回按钮组件 -->
    <MiniappBackButton />
    
    <div class="content">
      <h1 class="title">404</h1>
      <p class="message">页面未找到</p>
      <p class="description">您访问的页面不存在或已被移除</p>
      <div class="actions">
        <router-link to="/" class="home-link">返回首页</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import MiniappBackButton from '../components/MiniappBackButton.vue';

// 小程序环境标记
const isFromMiniapp = ref(false);

// 检查是否从小程序进入
const checkFromMiniapp = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const fromParam = urlParams.get('from') === 'miniapp';
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true';
  
  isFromMiniapp.value = fromParam || fromStorage;
};

// 组件挂载时
onMounted(() => {
  checkFromMiniapp();
});
</script>

<style scoped lang="scss">
.not-found {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(200, 22, 30, 0.2) 0%, rgba(0, 0, 0, 0.9) 70%);
    z-index: 1;
  }
}

.content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.title {
  font-size: 8rem;
  font-weight: bold;
  margin: 0;
  color: var(--primary-color);
  text-shadow: 0 0 20px rgba(200, 22, 30, 0.6);
  animation: glow 2s ease-in-out infinite alternate;
}

.message {
  font-size: 2rem;
  margin: 0 0 1rem;
  font-weight: bold;
}

.description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.home-link {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 30px;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    background-color: lighten(#C8161E, 5%);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px rgba(200, 22, 30, 0.6);
  }
  to {
    text-shadow: 0 0 20px rgba(200, 22, 30, 1), 0 0 30px rgba(200, 22, 30, 0.6);
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 6rem;
  }
  
  .message {
    font-size: 1.5rem;
  }
  
  .description {
    font-size: 1rem;
  }
}
</style> 