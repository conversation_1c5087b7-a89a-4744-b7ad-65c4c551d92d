#!/bin/bash

# 清理并重新部署脚本
# 解决ContainerConfig错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

echo "🧹 清理并重新部署 Millennia Now"
echo "================================"

# 1. 停止并删除所有容器
log_info "停止并删除现有容器..."
docker-compose down --remove-orphans || true

# 2. 清理悬空的镜像和容器
log_info "清理Docker资源..."
docker system prune -f

# 3. 删除可能损坏的镜像
log_info "删除应用镜像..."
docker rmi millennia-now-server_app 2>/dev/null || true

# 4. 检查环境配置
log_info "检查环境配置..."
if [ ! -f ".env" ]; then
    if [ -f ".env.production" ]; then
        log_info "复制生产环境配置..."
        cp .env.production .env
    else
        log_error "未找到环境配置文件"
        exit 1
    fi
fi

# 5. 检查SSL证书
log_info "检查SSL证书..."
if [ ! -f "ssl/luckyzyn.top.pem" ] || [ ! -f "ssl/luckyzyn.top.key" ]; then
    log_error "SSL证书文件缺失"
    echo "请确保以下文件存在："
    echo "  ssl/luckyzyn.top.pem"
    echo "  ssl/luckyzyn.top.key"
    exit 1
fi

# 6. 创建必要目录
log_info "创建必要目录..."
mkdir -p static minio/data minio/config

# 7. 重新构建并启动服务
log_info "重新构建并启动服务..."
docker-compose up -d --build

# 8. 等待服务启动
log_info "等待服务启动..."
sleep 30

# 9. 检查服务状态
log_info "检查服务状态..."
docker-compose ps

# 10. 获取Dify容器IP并更新nginx配置
log_info "获取Dify容器IP地址..."

# 等待一下确保容器完全启动
sleep 10

# 检查Dify容器是否运行
if docker ps --format "table {{.Names}}" | grep -q "docker_api_1"; then
    API_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' docker_api_1 2>/dev/null || echo "")
    WEB_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' docker_web_1 2>/dev/null || echo "")
    PLUGIN_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' docker_plugin_daemon_1 2>/dev/null || echo "")
    
    if [ -n "$API_IP" ] && [ -n "$WEB_IP" ] && [ -n "$PLUGIN_IP" ]; then
        log_success "获取到Dify容器IP地址"
        echo "  docker_api_1: $API_IP"
        echo "  docker_web_1: $WEB_IP"
        echo "  docker_plugin_daemon_1: $PLUGIN_IP"
        
        # 更新nginx配置
        log_info "更新nginx配置中的IP地址..."
        sed -i "s/server docker_api_1:5001/server $API_IP:5001/g" nginx.conf
        sed -i "s/server docker_web_1:3000/server $WEB_IP:3000/g" nginx.conf
        sed -i "s/server docker_plugin_daemon_1:5002/server $PLUGIN_IP:5002/g" nginx.conf
        
        # 重启nginx
        log_info "重启nginx容器..."
        docker-compose restart nginx
        
        # 等待nginx启动
        sleep 10
    else
        log_warning "无法获取Dify容器IP，将使用容器名称"
    fi
else
    log_warning "Dify容器未运行，nginx将使用容器名称连接"
fi

# 11. 测试服务
log_info "测试服务连接..."

# 等待服务完全启动
sleep 15

# 测试本地连接
if curl -f http://localhost:8001/health > /dev/null 2>&1; then
    log_success "FastAPI服务正常"
else
    log_warning "FastAPI服务可能未完全启动"
fi

if curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; then
    log_success "MinIO服务正常"
else
    log_warning "MinIO服务可能未完全启动"
fi

# 测试nginx代理
if curl -f -k https://localhost/ > /dev/null 2>&1; then
    log_success "Nginx HTTPS代理正常"
elif curl -f http://localhost/ > /dev/null 2>&1; then
    log_success "Nginx HTTP代理正常"
else
    log_warning "Nginx代理可能有问题"
fi

echo ""
log_success "部署完成！"
echo ""

log_info "服务访问地址："
echo "  🌐 主站: https://luckyzyn.top"
echo "  🤖 Dify API: https://luckyzyn.top/api"
echo "  📱 Millennia API: https://luckyzyn.top/millennia-api"
echo "  📚 API文档: https://luckyzyn.top/millennia-docs"
echo "  🗄️ MinIO控制台: https://luckyzyn.top/minio-console"
echo ""

log_info "本地调试地址："
echo "  📱 FastAPI: http://localhost:8001"
echo "  🗄️ MinIO API: http://localhost:9000"
echo "  🎛️ MinIO控制台: http://localhost:9001"
echo "  🗃️ MySQL: localhost:3307"
echo ""

log_info "常用命令："
echo "  查看日志: docker-compose logs -f"
echo "  查看状态: docker-compose ps"
echo "  重启服务: docker-compose restart"
echo "  停止服务: docker-compose down"
echo ""

if [ -n "$API_IP" ]; then
    log_info "Dify容器IP地址已更新到nginx配置："
    echo "  docker_api_1: $API_IP:5001"
    echo "  docker_web_1: $WEB_IP:3000"
    echo "  docker_plugin_daemon_1: $PLUGIN_IP:5002"
    echo ""
fi

log_info "如果服务无法访问，请检查："
echo "  1. 防火墙设置: ufw status"
echo "  2. 域名解析: nslookup luckyzyn.top"
echo "  3. SSL证书: openssl x509 -in ssl/luckyzyn.top.pem -text -noout"
echo "  4. 服务日志: docker-compose logs -f nginx"
echo ""
