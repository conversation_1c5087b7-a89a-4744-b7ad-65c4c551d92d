<template>
  <view class="page-wrapper">
    <view class="container">
      <!-- 表单内容 -->
      <view class="form-container"
            :class="{ 'content-hidden': showTemplateModal }">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>

          <view class="form-item">
            <text class="form-label">遗产名称 (当前值: {{ formData.title }})</text>
            <input v-model="formData.title"
                   class="form-input"
                   placeholder="请输入遗产名称"
                   maxlength="200" />
          </view>

          <view class="form-item">
            <text class="form-label">遗产类型</text>
            <picker :value="typeIndex"
                    :range="heritageTypes"
                    @change="onTypeChange">
              <view class="form-input">
                {{ formData.type || '请选择遗产类型' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">简介</text>
            <textarea v-model="formData.brief"
                      class="form-textarea"
                      placeholder="请输入简介"
                      maxlength="1000"
                      :auto-height="true"
                      :min-height="120"
                      :show-count="true" />
          </view>

          <view class="form-item">
            <text class="form-label">封面图片</text>
            <view class="image-upload">
              <view class="image-preview"
                    v-if="formData.image">
                <image :src="formData.image"
                       mode="aspectFill"
                       class="preview-image" />
                <view class="image-actions">
                  <text class="action-btn"
                        @click="changeMainImage">更换</text>
                  <text class="action-btn delete"
                        @click="removeMainImage">删除</text>
                </view>
              </view>
              <view class="upload-btn"
                    v-else
                    @click="uploadMainImage"
                    :class="{ disabled: uploading }">
                <text class="upload-icon">{{ uploading ? '...' : '+' }}</text>
                <text class="upload-text">{{ uploading ? '上传中' : '上传封面图片' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 详细信息 -->
        <view class="form-section">
          <view class="section-title">详细信息</view>

          <view class="form-item">
            <view class="form-label-with-button">
              <text class="form-label">详细内容（HTML格式）</text>
              <view class="button-group">
                <button class="template-btn"
                        @click="showTemplateSelector">选择模板</button>
                <button class="clear-btn"
                        @click="clearDetailContent"
                        v-if="formData.detail_content.trim()">清空</button>
              </view>
            </view>
            <textarea v-model="formData.detail_content"
                      class="form-textarea"
                      placeholder="请输入详细内容，支持HTML格式，或点击上方选择模板按钮快速插入模板"
                      maxlength="10000"
                      :auto-height="true"
                      :min-height="150"
                      :show-count="true" />
          </view>

          <view class="form-item">
            <text class="form-label">详细图片</text>
            <view class="detail-images">
              <!-- 已上传的图片 -->
              <view class="detail-image-item"
                    v-for="(img, index) in formData.detail_images"
                    :key="index">
                <image :src="img"
                       mode="aspectFill"
                       class="detail-image" />
                <view class="detail-image-actions">
                  <text class="action-btn delete"
                        @click="removeDetailImage(index)">删除</text>
                </view>
              </view>
              <!-- 添加图片按钮 -->
              <view class="add-detail-image"
                    @click="addDetailImage"
                    v-if="formData.detail_images.length < 9"
                    :class="{ disabled: uploading }">
                <text class="add-icon">{{ uploading ? '...' : '+' }}</text>
                <text class="add-text">{{ uploading ? '上传中' : '添加图片' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 设置 -->
        <view class="form-section">
          <view class="section-title">设置</view>

          <view class="form-item">
            <view class="switch-item">
              <text class="switch-label">启用状态</text>
              <switch :checked="formData.is_active"
                      @change="onActiveChange" />
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">排序</text>
            <input v-model.number="formData.sort_order"
                   class="form-input"
                   type="number"
                   placeholder="数值越大排序越靠前" />
          </view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-section">
        <button class="save-button"
                @click="saveHeritage"
                :disabled="isLoading">
          {{ isLoading ? (isEdit ? '保存中...' : '创建中...') : (isEdit ? '保存' : '创建') }}
        </button>
      </view>
    </view>

    <!-- HTML模板选择弹窗 - 使用全屏覆盖方式 -->
    <view v-if="showTemplateModal"
          class="fullscreen-modal">
      <!-- 模板弹窗内容 -->
      <view class="template-modal">
        <view class="modal-header">
          <text class="modal-title">
            选择HTML模板
            <text class="template-count">({{ getDisplayTemplates().length }}个)</text>
          </text>
          <text @tap="closeTemplateSelector"
                class="close-btn">×</text>
        </view>

        <view class="search-section">
          <input v-model="templateSearchKeyword"
                 placeholder="搜索模板..."
                 class="search-input"
                 @input="onTemplateSearch" />
        </view>

        <!-- 模板列表 -->
        <!-- #ifdef H5 -->
        <view class="template-list-container native-scroll">
          <view class="template-item"
                v-for="(template, index) in getDisplayTemplates()"
                :key="index"
                @click="selectTemplate(template)">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.description }}</text>
            <view class="template-preview">{{ template.preview }}</view>
          </view>

          <!-- 无搜索结果提示 -->
          <view v-if="templateSearchKeyword.trim() && getDisplayTemplates().length === 0"
                class="no-results">
            <text class="no-results-text">未找到相关模板</text>
            <text class="no-results-desc">请尝试其他关键词</text>
          </view>
        </view>
        <!-- #endif -->

        <!-- #ifndef H5 -->
        <scroll-view class="template-list"
                     scroll-y
                     enable-back-to-top
                     enhanced
                     :show-scrollbar="true"
                     :scroll-with-animation="true"
                     :enable-passive="false">
          <view class="template-item"
                v-for="(template, index) in getDisplayTemplates()"
                :key="index"
                @tap="selectTemplate(template)">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.description }}</text>
            <view class="template-preview">{{ template.preview }}</view>
          </view>

          <!-- 无搜索结果提示 -->
          <view v-if="templateSearchKeyword.trim() && getDisplayTemplates().length === 0"
                class="no-results">
            <text class="no-results-text">未找到相关模板</text>
            <text class="no-results-desc">请尝试其他关键词</text>
          </view>
        </scroll-view>
        <!-- #endif -->

        <view class="modal-actions">
          <button @tap="closeTemplateSelector"
                  class="cancel-btn">取消</button>
        </view>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container"
          v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  createHeritageItem,
  updateHeritageItem,
  getHeritageItemDetail,
  type HeritageItemCreateRequest,
  type HeritageItemUpdateRequest,
  type HeritageItem,
} from '../../api/heritage'
import {
  htmlTemplates,
  searchTemplates,
  type TemplateItem,
} from '../../config/htmlTemplates'
import { getImageProxyUrl } from '../../utils/image'
import { uploadImage } from '../../utils/upload'

// 页面状态
const isEdit = ref(false)
const isLoading = ref(false)
const loadingText = ref('加载中...')
const heritageId = ref<number | null>(null)
const placeId = ref<number | null>(null)
const uploading = ref(false)

// HTML模板选择器相关
const showTemplateModal = ref(false)
const templateSearchKeyword = ref('')
const filteredTemplates = ref<TemplateItem[]>([])

// 遗产类型选择
const typeIndex = ref(-1)
const heritageTypes = ref([
  '国家级非物质文化遗产',
  '省级非物质文化遗产',
  '市级非物质文化遗产',
  '区县级非物质文化遗产',
  '世界文化遗产',
  '国家重点文物保护单位',
  '省级文物保护单位',
  '市县级文物保护单位',
  '历史文化名城',
  '历史文化街区',
  '传统手工艺',
  '民族文化',
  '古建筑群',
  '文化景观',
  '民俗活动',
  '传统技艺',
  '口传文学',
  '传统音乐',
  '传统舞蹈',
  '传统戏剧',
  '传统体育',
  '传统美术',
  '传统医药',
  '其他',
])

// 用户信息和地点信息
const userInfo = ref({
  role: '',
  province_name: '',
  province_id: '',
  city_name: '',
  city_id: '',
  district_name: '',
  district_id: '',
})

const placeInfo = ref({
  place_id: '',
  place_name: '',
})

// 表单数据
const formData = ref({
  title: '',
  type: '',
  brief: '',
  image: '',
  detail_content: '',
  detail_images: [] as string[],
  is_active: true,
  sort_order: 0,
})

// 添加onLoad生命周期函数
const onLoad = (options: any) => {
  console.log('🔍 heritage-edit onLoad 接收到的参数:', options)

  // 设置导航栏标题（先设置一个默认标题）
  uni.setNavigationBarTitle({
    title: '文化传承编辑',
  })
}

// 初始化页面
onMounted(async () => {
  const app = getApp()

  // 优先使用全局数据
  if (app.globalData && app.globalData.heritageEditData) {
    const options = app.globalData.heritageEditData
    console.log('🔍 heritage-edit 接收到的全局数据:', options)

    // 获取用户信息
    userInfo.value = {
      role: options.role || '',
      province_name: options.province_name || '',
      province_id: options.province_id || '',
      city_name: options.city_name || '',
      city_id: options.city_id || '',
      district_name: options.district_name || '',
      district_id: options.district_id || '',
    }

    // 获取地点信息
    placeInfo.value = {
      place_id: options.place_id || '',
      place_name: options.place_name || '',
    }

    placeId.value = parseInt(placeInfo.value.place_id) || null

    // 检查是否为编辑模式
    if (options.heritage_id) {
      isEdit.value = true
      heritageId.value = parseInt(options.heritage_id)

      // 直接使用全局数据填充表单
      console.log('🔍 使用全局数据填充文化传承表单')

      // 逐个设置属性，确保响应式更新
      formData.value.title = options.title || ''
      formData.value.type = options.type || ''
      formData.value.brief = options.brief || ''
      formData.value.image = options.image || ''
      formData.value.detail_content = options.detail_content || ''
      formData.value.detail_images = options.detail_images || []
      formData.value.is_active =
        options.is_active !== undefined ? options.is_active : true
      formData.value.sort_order = options.sort_order || 0

      console.log('🔍 文化传承表单数据已填充:', formData.value)
    }
  } else {
    // 如果没有全局数据，尝试从URL参数获取
    const pages = getCurrentPages() as any[]
    const currentPage = pages[pages.length - 1] as any
    const options = currentPage?.options || {}

    console.log('🔍 使用URL参数初始化heritage-edit')
    // 这里可以添加URL参数处理逻辑
  }

  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: isEdit.value ? '编辑文化遗产' : '新增文化遗产',
  })

  // 设置导航栏颜色
  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#C8161E',
  })
})

// 加载文化遗产数据
const loadHeritageData = async () => {
  if (!heritageId.value) return

  try {
    isLoading.value = true
    loadingText.value = '加载数据中...'

    const data = await getHeritageItemDetail(heritageId.value)
    if (data) {
      formData.value = {
        title: data.title,
        type: data.type,
        brief: data.brief,
        image: data.image ? getImageProxyUrl(data.image) : '',
        detail_content: data.detail_content || '',
        detail_images: data.detail_images
          ? data.detail_images.map((url) => getImageProxyUrl(url))
          : [],
        is_active: data.is_active,
        sort_order: data.sort_order,
      }
      console.log('formData.value', formData.value)
      // 设置类型选择器的索引
      const index = heritageTypes.value.findIndex((type) => type === data.type)
      typeIndex.value = index >= 0 ? index : -1
    }
  } catch (error) {
    console.error('加载文化遗产数据失败:', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// 类型选择变化
const onTypeChange = (e: any) => {
  typeIndex.value = parseInt(e.detail.value)
  formData.value.type = heritageTypes.value[typeIndex.value]
}

// 启用状态变化
const onActiveChange = (e: any) => {
  formData.value.is_active = e.detail.value
}

// 上传图片到服务器 - 使用统一的上传工具
const uploadImageToServer = async (
  type: 'main' | 'detail',
  filePath: string
) => {
  uploading.value = true

  try {
    // 直接上传指定的文件
    const result = await uploadImage(filePath, 'heritage')

    if (result.success && result.url) {
      // 使用工具函数处理图片URL
      const imageUrl = getImageProxyUrl(result.url)

      if (type === 'main') {
        formData.value.image = imageUrl
        uni.showToast({
          title: '封面图片上传成功',
          icon: 'success',
          duration: 1500,
        })
      } else {
        // detail类型的图片会在addDetailImage函数中处理
        return imageUrl
      }
    } else {
      throw new Error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    })
    throw error
  } finally {
    uploading.value = false
  }
}

// 上传主图片
const uploadMainImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      await uploadImageToServer('main', res.tempFilePaths[0])
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 更换主图片
const changeMainImage = () => {
  uploadMainImage()
}

// 删除主图片
const removeMainImage = () => {
  formData.value.image = ''

  // 显示删除成功的提示
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 添加详细图片
const addDetailImage = async () => {
  if (uploading.value) return

  try {
    const res = await uni.chooseImage({
      count: 9 - formData.value.detail_images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      // 显示上传进度
      uni.showLoading({
        title: '上传中...',
      })

      try {
        // 逐一上传图片
        for (const filePath of res.tempFilePaths) {
          const imageUrl = await uploadImageToServer('detail', filePath)
          if (imageUrl) {
            formData.value.detail_images.push(imageUrl)
          }
        }

        uni.hideLoading()
        uni.showToast({
          title: `成功上传${res.tempFilePaths.length}张图片`,
          icon: 'success',
          duration: 1500,
        })
      } catch (error) {
        uni.hideLoading()
        console.error('上传详细图片失败:', error)
      }
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 删除详细图片
const removeDetailImage = (index: number) => {
  formData.value.detail_images.splice(index, 1)

  // 显示删除成功的提示
  uni.showToast({
    title: '图片已删除',
    icon: 'success',
    duration: 1000,
  })
}

// 表单验证
const validateForm = () => {
  if (!formData.value.title.trim()) {
    uni.showToast({
      title: '请输入遗产名称',
      icon: 'none',
    })
    return false
  }

  if (!formData.value.type.trim()) {
    uni.showToast({
      title: '请选择遗产类型',
      icon: 'none',
    })
    return false
  }

  if (!formData.value.brief.trim()) {
    uni.showToast({
      title: '请输入简介',
      icon: 'none',
    })
    return false
  }

  return true
}

// 保存文化遗产
const saveHeritage = async () => {
  if (!validateForm()) return

  if (!placeId.value) {
    uni.showToast({
      title: '缺少地点信息',
      icon: 'none',
    })
    return
  }

  try {
    isLoading.value = true
    loadingText.value = isEdit.value ? '更新中...' : '创建中...'

    // 处理详细内容：如果为空则传undefined，确保后端删除该字段
    const trimmedDetailContent = formData.value.detail_content.trim()
    const detailContentValue =
      trimmedDetailContent === '' ? undefined : trimmedDetailContent

    const requestData = {
      title: formData.value.title.trim(),
      type: formData.value.type,
      brief: formData.value.brief.trim(),
      image:
        formData.value.image && formData.value.image.trim()
          ? formData.value.image.trim()
          : undefined,
      detail_content: detailContentValue,
      detail_images:
        formData.value.detail_images.length > 0
          ? formData.value.detail_images
          : undefined,
      is_active: formData.value.is_active,
      sort_order: formData.value.sort_order,
    }

    if (isEdit.value && heritageId.value) {
      // 更新
      await updateHeritageItem(heritageId.value, requestData)
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 创建
      await createHeritageItem(placeId.value, requestData)
      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 延迟返回
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error: any) {
    console.error('保存文化遗产失败:', error)

    let errorMessage = '保存失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

// HTML模板选择相关函数
const showTemplateSelector = () => {
  showTemplateModal.value = true
  templateSearchKeyword.value = ''
  filteredTemplates.value = htmlTemplates

  // 隐藏键盘和处理页面状态
  try {
    // 隐藏键盘
    uni.hideKeyboard()

    // 延迟执行，确保弹窗已经显示
    setTimeout(() => {
      // 在H5环境下才操作document
      // #ifdef H5
      if (typeof document !== 'undefined') {
        // 模糊所有输入框
        const inputs = document.querySelectorAll('input, textarea')
        inputs.forEach((input) => {
          if (input instanceof HTMLElement) {
            input.blur()
          }
        })
      }
      // #endif
    }, 100)
  } catch (error) {
    console.log('处理页面状态时出错:', error)
  }
}

const closeTemplateSelector = () => {
  showTemplateModal.value = false
  templateSearchKeyword.value = ''
}

const onTemplateSearch = () => {
  if (templateSearchKeyword.value.trim()) {
    filteredTemplates.value = searchTemplates(
      templateSearchKeyword.value.trim()
    )
  } else {
    filteredTemplates.value = htmlTemplates
  }
}

const getDisplayTemplates = () => {
  return templateSearchKeyword.value.trim()
    ? filteredTemplates.value
    : htmlTemplates
}

const selectTemplate = (template: TemplateItem) => {
  // 如果有内容则换行添加，否则直接添加
  const currentContent = formData.value.detail_content.trim()
  if (currentContent) {
    formData.value.detail_content = currentContent + '\n\n' + template.content
  } else {
    formData.value.detail_content = template.content
  }

  uni.showToast({
    title: '模板已插入',
    icon: 'success',
    duration: 1000,
  })

  closeTemplateSelector()
}

// 清空详细内容
const clearDetailContent = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空详细内容吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm) {
        formData.value.detail_content = ''
        uni.showToast({
          title: '内容已清空',
          icon: 'success',
          duration: 1000,
        })
      }
    },
  })
}

// 注册onLoad到组件实例
defineExpose({
  onLoad,
})
</script>

<style>
.page-wrapper {
  min-height: 100vh;
}

.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 表单容器 */
.form-container {
  padding-bottom: 40rpx;
}

.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

/* 必填项标记 */

/* 标签和按钮组合 */
.form-label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.button-group {
  display: flex;
  gap: 10rpx;
  align-items: center;
}

.template-btn {
  background-color: #007aff;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

.clear-btn {
  background-color: #ff4d4f;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

.form-input,
.form-textarea {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
  line-height: 1.6;
}

.form-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  margin-left: 20rpx;
}

/* 图片上传 */
.image-upload {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 300rpx;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: 80rpx;
  text-align: center;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.8);
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  color: #999;
}

.upload-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
}

/* 详细图片 */
.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.detail-image-item {
  position: relative;
  width: 200rpx;
  height: 150rpx;
}

.detail-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.detail-image-actions {
  position: absolute;
  top: 5rpx;
  right: 5rpx;
}

.add-detail-image {
  width: 200rpx;
  height: 150rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-detail-image.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.add-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 开关项 */
.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

/* 保存按钮 */
.save-section {
  padding: 30rpx 20rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.save-button:disabled {
  background-color: #ccc;
  color: #999;
}

/* 加载中 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* HTML模板选择器样式 - 全屏模式 */
.fullscreen-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  z-index: 9999 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: hidden !important;
}

.template-modal {
  background-color: #fff !important;
  border-radius: 20rpx !important;
  width: 90% !important;
  max-width: 800rpx !important;
  max-height: 85vh !important;
  min-height: 70vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3) !important;
  position: relative !important;
  z-index: 10000 !important;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.template-count {
  font-size: 24rpx;
  font-weight: normal;
  opacity: 0.8;
  margin-left: 10rpx;
}

.close-btn {
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  padding: 10rpx;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.template-list-container {
  flex: 1 !important;
  height: calc(85vh - 200rpx) !important;
  overflow: hidden !important;
  background-color: #fff !important;
}

/* H5端原生滚动 */
.native-scroll {
  overflow-y: auto !important;
  padding: 20rpx !important;
  box-sizing: border-box !important;
}

/* 小程序端scroll-view */
.template-list {
  width: 100% !important;
  height: calc(85vh - 200rpx) !important;
  padding: 20rpx !important;
  background-color: #fff !important;
  box-sizing: border-box !important;
}

/* PC端滚动条样式 */
.native-scroll::-webkit-scrollbar,
.template-list::-webkit-scrollbar {
  width: 8rpx;
}

.native-scroll::-webkit-scrollbar-track,
.template-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4rpx;
}

.native-scroll::-webkit-scrollbar-thumb,
.template-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4rpx;
}

.native-scroll::-webkit-scrollbar-thumb:hover,
.template-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.template-item {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  position: relative;
}

.template-item:active {
  background-color: #f8f9fa;
  border-color: #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.15);
}

.template-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.template-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.template-preview {
  font-size: 22rpx;
  color: #888;
  background-color: #f8f9fa;
  padding: 15rpx;
  border-radius: 8rpx;
  border-left: 6rpx solid #007aff;
  overflow: hidden;
  max-height: 60rpx;
  line-height: 1.4;
}

/* 搜索区域样式 */
.search-section {
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 35rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

/* 无搜索结果样式 */
.no-results {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-results-text {
  display: block;
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.no-results-desc {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

.modal-actions {
  display: flex;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.cancel-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  text-align: center;
  background-color: transparent;
  color: #666;
}

/* 当模板弹窗显示时，完全隐藏页面内容 */
.content-hidden {
  display: none !important;
}
</style> 