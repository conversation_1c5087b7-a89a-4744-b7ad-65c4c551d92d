// 高德地图API接口
// 请在高德开放平台申请Web服务类型的API Key
// https://lbs.amap.com/
const AMAP_KEY = '2e6a6e6c5af4d7d5456c113456076707' // 请替换为你的Web服务API Key
const AMAP_BASE_URL = 'https://restapi.amap.com/v3'

// 逆地理编码响应接口
export interface RegeocodeResponse {
  status: string
  info: string
  infocode: string
  regeocode: {
    addressComponent: {
      country: string
      province: string
      city: string | []
      citycode: string
      district: string
      adcode: string
      township: string
      towncode: string
      neighborhood: {
        name: string[]
        type: string[]
      }
      building: {
        name: string[]
        type: string[]
      }
      streetNumber: {
        street: string[]
        number: string[]
        location: string
        direction: string
        distance: string
      }
      businessAreas: Array<{
        location: string
        name: string
        id: string
      }>
    }
    formatted_address: string
    roads: any[]
    roadinters: any[]
    pois: any[]
  }
}

// 地理编码响应接口
export interface GeocodeResponse {
  status: string
  info: string
  infocode: string
  geocodes: Array<{
    formatted_address: string
    country: string
    province: string
    citycode: string
    city: string
    district: string
    township: any[]
    neighborhood: {
      name: any[]
      type: any[]
    }
    building: {
      name: any[]
      type: any[]
    }
    adcode: string
    street: any[]
    number: any[]
    location: string
    level: string
  }>
}

// IP定位响应接口
export interface IPLocationResponse {
  status: string
  info: string
  infocode: string
  province: string
  city: string
  adcode: string
  rectangle: string
}

// 逆地理编码 - 将经纬度转换为地址
export const reverseGeocode = async (longitude: number, latitude: number): Promise<RegeocodeResponse> => {
  try {
    const response = await uni.request({
      url: `${AMAP_BASE_URL}/geocode/regeo`,
      method: 'GET',
      data: {
        key: AMAP_KEY,
        location: `${longitude},${latitude}`,
        poitype: '',
        radius: 1000,
        extensions: 'base',
        batch: false,
        roadlevel: 0
      }
    })
    
    if (response.statusCode === 200 && (response.data as any).status === '1') {
      return response.data as RegeocodeResponse
    } else {
      throw new Error((response.data as any).info || '逆地理编码失败')
    }
  } catch (error) {
    console.error('逆地理编码失败:', error)
    throw error
  }
}

// 地理编码 - 将地址转换为经纬度
export const geocode = async (address: string, city?: string): Promise<GeocodeResponse> => {
  try {
    const params: any = {
      key: AMAP_KEY,
      address: address,
      batch: false
    }
    
    if (city) {
      params.city = city
    }
    
    const response = await uni.request({
      url: `${AMAP_BASE_URL}/geocode/geo`,
      method: 'GET',
      data: params
    })
    
    if (response.statusCode === 200 && (response.data as any).status === '1') {
      return response.data as GeocodeResponse
    } else {
      throw new Error((response.data as any).info || '地理编码失败')
    }
  } catch (error) {
    console.error('地理编码失败:', error)
    throw error
  }
}

// IP定位 - 根据IP获取大概位置
export const getLocationByIP = async (): Promise<IPLocationResponse> => {
  try {
    const response = await uni.request({
      url: `${AMAP_BASE_URL}/ip`,
      method: 'GET',
      data: {
        key: AMAP_KEY,
        output: 'json'
      }
    })
    
    if (response.statusCode === 200 && (response.data as any).status === '1') {
      return response.data as IPLocationResponse
    } else {
      throw new Error((response.data as any).info || 'IP定位失败')
    }
  } catch (error) {
    console.error('IP定位失败:', error)
    throw error
  }
}

// 解析地址组件，提取省市区信息
export const parseAddressComponent = (addressComponent: any) => {
  const province = addressComponent.province || ''
  let city = addressComponent.city
  const district = addressComponent.district || ''
  
  // 处理直辖市情况，city可能是空数组
  if (Array.isArray(city) && city.length === 0) {
    city = province
  }
  
  return {
    province: province.replace(/省|市|自治区|特别行政区/g, ''),
    city: city.replace(/市|区|县|自治州|地区|盟/g, ''),
    district: district.replace(/区|县|市/g, ''),
    fullAddress: addressComponent.formatted_address || `${province}${city}${district}`
  }
}

// 坐标转换 - 将其他坐标系转换为高德坐标系
export const convertCoordinate = async (
  longitude: number, 
  latitude: number, 
  coordsys: 'gps' | 'mapbar' | 'baidu' | 'autonavi' = 'gps'
): Promise<{status: string, locations: string}> => {
  try {
    const response = await uni.request({
      url: `${AMAP_BASE_URL}/assistant/coordinate/convert`,
      method: 'GET',
      data: {
        key: AMAP_KEY,
        locations: `${longitude},${latitude}`,
        coordsys: coordsys,
        output: 'json'
      }
    })
    
    if (response.statusCode === 200 && (response.data as any).status === '1') {
      return response.data as {status: string, locations: string}
    } else {
      throw new Error((response.data as any).info || '坐标转换失败')
    }
  } catch (error) {
    console.error('坐标转换失败:', error)
    throw error
  }
}

// 获取当前位置的详细地址信息
export const getCurrentLocationInfo = async (retryCount = 2): Promise<{
  longitude: number
  latitude: number
  province: string
  city: string
  district: string
  fullAddress: string
  formattedAddress: string
}> => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02', // 高德地图使用gcj02坐标系
      highAccuracyExpireTime: 3000, // 高精度定位超时时间
      success: async (res) => {
        try {
          const { longitude, latitude } = res
          
          // 验证坐标有效性
          if (!longitude || !latitude || longitude === 0 || latitude === 0) {
            throw new Error('获取到的坐标无效')
          }
          
          // 调用逆地理编码获取地址信息，支持重试
          let regeocodeResult
          let lastError
          
          for (let i = 0; i <= retryCount; i++) {
            try {
              regeocodeResult = await reverseGeocode(longitude, latitude)
              break
            } catch (error) {
              lastError = error
              if (i < retryCount) {
                console.warn(`逆地理编码第${i + 1}次尝试失败，准备重试:`, error)
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))) // 延迟重试
              }
            }
          }
          
          if (!regeocodeResult) {
            throw lastError || new Error('逆地理编码失败')
          }
          
          const addressInfo = parseAddressComponent(regeocodeResult.regeocode.addressComponent)
          
          resolve({
            longitude,
            latitude,
            province: addressInfo.province,
            city: addressInfo.city,
            district: addressInfo.district,
            fullAddress: addressInfo.fullAddress,
            formattedAddress: regeocodeResult.regeocode.formatted_address
          })
        } catch (error) {
          console.error('获取位置信息失败:', error)
          reject(error)
        }
      },
      fail: (error) => {
        console.error('GPS定位失败:', error)
        // 提供更友好的错误信息
        if (error.errMsg?.includes('auth deny')) {
          reject(new Error('定位权限被拒绝，请在设置中开启定位权限'))
        } else if (error.errMsg?.includes('timeout')) {
          reject(new Error('定位超时，请检查网络连接'))
        } else {
          reject(new Error('定位失败，请检查GPS是否开启'))
        }
      }
    })
  })
} 