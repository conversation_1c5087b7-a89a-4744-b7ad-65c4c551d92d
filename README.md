# 千年古韵 - 古籍数字化管理系统

## 项目简介

千年古韵是一个专为古籍数字化管理而设计的综合性系统，旨在保护和传承中华文化瑰宝。系统支持古籍的数字化录入、OCR智能识别、人工校对、以及多媒体内容管理等功能。

## 项目结构

```
millennia-now/
├── millennia-now-app/          # 前端应用 (uni-app + Vue)
│   ├── src/
│   │   ├── api/               # API接口
│   │   ├── components/        # 组件
│   │   ├── pages/            # 页面
│   │   ├── store/            # 状态管理
│   │   └── utils/            # 工具函数
│   └── package.json
├── millennia-now-server/       # 后端服务 (FastAPI + Python)
│   ├── app/
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心功能
│   │   ├── database/         # 数据库
│   │   ├── models/           # 数据模型
│   │   └── schemas/          # 数据结构
│   └── requirements.txt
└── README.md
```

## 主要功能

### 前端功能
- 🏛️ 古籍管理：古籍信息录入、编辑、查看
- 📖 卷册管理：卷册创建、编辑、页面管理
- 🔍 OCR识别：图像智能文字识别
- ✏️ 人工校对：OCR结果人工校对和修正
- 📱 响应式设计：支持移动端和桌面端
- 🎨 古典UI：符合古籍文化特色的界面设计

### 后端功能
- 🚀 FastAPI：高性能异步API框架
- 🗄️ 数据库：PostgreSQL数据存储
- 🔐 权限管理：用户认证和权限控制
- 📁 文件管理：MinIO对象存储
- 🤖 OCR服务：图像文字识别
- 📊 数据统计：古籍和卷册统计信息

## 技术栈

### 前端
- **框架**: uni-app + Vue.js
- **语言**: TypeScript
- **构建工具**: Vite
- **状态管理**: Vuex
- **UI组件**: 自定义古典风格组件

### 后端
- **框架**: FastAPI
- **语言**: Python 3.8+
- **数据库**: PostgreSQL
- **ORM**: SQLAlchemy
- **对象存储**: MinIO
- **认证**: JWT

## 快速开始

### 前端启动

```bash
cd millennia-now-app
npm install
npm run dev
```

### 后端启动

```bash
cd millennia-now-server
pip install -r requirements.txt
python -m uvicorn app.main:app --reload
```

## 环境要求

- Node.js 16+
- Python 3.8+
- PostgreSQL 12+
- MinIO

## 开发文档

详细的开发文档请参考各子项目的README文件：
- [前端文档](./millennia-now-app/README.md)
- [后端文档](./millennia-now-server/README.md)

## 贡献指南

欢迎贡献代码和提出建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件

---

*传承千年文化，守护古籍瑰宝* 🏛️📚 