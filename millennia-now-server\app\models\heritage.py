from sqlalchemy import Column, Integer, String, Text, ForeignKey, Boolean, DateTime, func, TIMESTAMP, SmallInteger, Enum
from sqlalchemy.orm import relationship
from app.database.db import Base
import enum


class HeritagePlace(Base):
    """文化遗产地点表"""
    __tablename__ = "heritage_places"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    place_name = Column(String(100), nullable=False, comment="地点名称")
    place_desc = Column(String(255), nullable=True, comment="地点描述")
    header_bg_image = Column(String(500), nullable=True, comment="头部背景图片URL")
    introduction = Column(Text, nullable=True, comment="简介")
    footer_text = Column(String(500), nullable=True, comment="底部文本")
    
    # 关联的行政区域
    province_id = Column(SmallInteger, nullable=True, comment="省份ID")
    city_id = Column(SmallInteger, nullable=True, comment="城市ID")
    district_id = Column(SmallInteger, nullable=True, comment="区县ID")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    timeline_items = relationship("TimelineItem", back_populates="place", cascade="all, delete-orphan")
    heritage_items = relationship("HeritageItem", back_populates="place", cascade="all, delete-orphan")
    memory_items = relationship("MemoryItem", back_populates="place", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<HeritagePlace(id={self.id}, place_name='{self.place_name}')>"


class TimelineItem(Base):
    """历史时间轴项目表"""
    __tablename__ = "timeline_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    place_id = Column(Integer, ForeignKey("heritage_places.id"), nullable=False, comment="关联地点ID")
    
    # 时期信息
    period = Column(String(100), nullable=False, comment="时期名称")
    year = Column(String(100), nullable=False, comment="年份范围")
    title = Column(String(200), nullable=False, comment="标题")
    description = Column(Text, nullable=False, comment="描述")
    image = Column(String(500), nullable=True, comment="主图片URL")
    
    # 详细信息
    has_detail = Column(Boolean, default=False, comment="是否有详细内容")
    detail = Column(Text, nullable=True, comment="详细内容（HTML格式）")
    detail_images = Column(Text, nullable=True, comment="详细图片URLs，JSON数组格式")
    
    # 文化遗产标签
    heritage_tags = Column(Text, nullable=True, comment="文化遗产标签，JSON数组格式")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    place = relationship("HeritagePlace", back_populates="timeline_items")
    
    def __repr__(self):
        return f"<TimelineItem(id={self.id}, period='{self.period}', title='{self.title}')>"


class HeritageItem(Base):
    """文化遗产项目表"""
    __tablename__ = "heritage_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    place_id = Column(Integer, ForeignKey("heritage_places.id"), nullable=False, comment="关联地点ID")
    
    # 基本信息
    title = Column(String(200), nullable=False, comment="遗产名称")
    type = Column(String(100), nullable=False, comment="遗产类型")
    brief = Column(Text, nullable=False, comment="简介")
    image = Column(String(500), nullable=True, comment="图片URL")
    
    # 详细信息
    detail_content = Column(Text, nullable=True, comment="详细内容")
    detail_images = Column(Text, nullable=True, comment="详细图片URLs，JSON数组格式")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    place = relationship("HeritagePlace", back_populates="heritage_items")
    
    def __repr__(self):
        return f"<HeritageItem(id={self.id}, title='{self.title}', type='{self.type}')>"


class MemoryItem(Base):
    """城市记忆项目表"""
    __tablename__ = "memory_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    place_id = Column(Integer, ForeignKey("heritage_places.id"), nullable=False, comment="关联地点ID")
    
    # 基本信息
    title = Column(String(200), nullable=False, comment="记忆标题")
    year = Column(String(50), nullable=False, comment="年份")
    image = Column(String(500), nullable=True, comment="图片URL")
    
    # 详细信息
    description = Column(Text, nullable=True, comment="简要描述")
    detail_content = Column(Text, nullable=True, comment="详细内容（HTML格式）")
    detail_images = Column(Text, nullable=True, comment="详细图片URLs，JSON数组格式")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间字段
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # 关系
    place = relationship("HeritagePlace", back_populates="memory_items")
    
    def __repr__(self):
        return f"<MemoryItem(id={self.id}, title='{self.title}', year='{self.year}')>" 