# 生产环境配置文件
# 请根据实际情况修改以下配置

# 数据库配置 (Docker内部网络)
DATABASE_URL=mysql+pymysql://root:1qaz2wsx@mysql:3306/millennia_now

# 微信小程序配置
WECHAT_APP_ID=wx7827d9f8bd827e3f
WECHAT_APP_SECRET=c10bc52d75428a192b5ee5289c155bf7

# JWT配置 - 生产环境请使用更复杂的密钥
JWT_SECRET_KEY=millennia-now-production-jwt-secret-key-2024-luckyzyn-top

# MinIO对象存储配置 (Docker内部网络)
MINIO_ENDPOINT=minio:9000  # 内部连接地址
MINIO_PUBLIC_ENDPOINT=47.109.155.100:9000  # 外部访问地址（返回给前端的URL）
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=admin123456
MINIO_SECURE=false
MINIO_BUCKET=millennia-now

# 服务端口
PORT=8000

# 环境
ENV=production

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# 日志级别
LOG_LEVEL=INFO

# 域名配置
DOMAIN_NAME=luckyzyn.top
API_BASE_URL=https://luckyzyn.top/millennia-api

# 跨域配置
CORS_ORIGINS=https://luckyzyn.top,https://www.luckyzyn.top,https://servicewechat.com

# 文件上传配置
MAX_FILE_SIZE=100MB
