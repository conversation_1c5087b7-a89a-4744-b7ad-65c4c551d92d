<template>
  <view class="volume-list">
    <!-- 头部信息 -->
    <view class="header-section">
      <view class="book-info">
        <text class="book-title">{{ bookInfo.title || '加载中...' }}</text>
        <text class="book-author">{{ bookInfo.author || '佚名' }} · {{ bookInfo.dynasty || '未知' }}</text>
      </view>
      <button class="add-btn"
              @click="addNewVolume">
        <uni-icons type="plus"
                   size="16"
                   color="#fff" />
        <text>新增卷册</text>
      </button>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ volumes.length }}</text>
        <text class="stat-label">卷册总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ totalPages }}</text>
        <text class="stat-label">总页数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ publishedVolumes }}</text>
        <text class="stat-label">已发布</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ ocrProgress }}%</text>
        <text class="stat-label">OCR进度</text>
      </view>
    </view>

    <!-- 卷册列表 -->
    <view class="volume-container">
      <view v-for="volume in volumes"
            :key="volume.id"
            class="volume-card"
            @click="viewVolumeDetail(volume)">

        <!-- 卷册基本信息 -->
        <view class="volume-header">
          <view class="volume-number">
            <text class="number-text">第{{ volume.volume_number }}册</text>
            <view class="status-badge"
                  :class="volume.status">
              {{ getStatusLabel(volume.status) }}
            </view>
          </view>

          <view class="volume-info">
            <text class="volume-title">{{ volume.volume_title || '未命名卷册' }}</text>
            <text class="page-range"
                  v-if="volume.start_page || volume.end_page">
              {{ volume.start_page || '?' }} - {{ volume.end_page || '?' }}
            </text>
            <text class="volume-desc"
                  v-if="volume.content_description">
              {{ volume.content_description }}
            </text>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <view class="action-btn edit-btn"
                  @click.stop="editVolume(volume)">
              <uni-icons type="compose"
                         size="16"
                         color="#007aff" />
            </view>
            <view class="action-btn delete-btn"
                  @click.stop="confirmDeleteVolume(volume)">
              <uni-icons type="trash"
                         size="16"
                         color="#ff4757" />
            </view>
          </view>
        </view>

        <!-- 卷册统计 -->
        <view class="volume-stats">
          <view class="stat-item">
            <uni-icons type="image"
                       size="12"
                       color="#999" />
            <text class="stat-text">{{ volume.total_pages || 0 }} 页</text>
          </view>
          <view class="stat-item">
            <uni-icons type="scan"
                       size="12"
                       color="#999" />
            <text class="stat-text">OCR {{ volume.ocr_completed_pages || 0 }}/{{ volume.total_pages || 0 }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="checkmarkempty"
                       size="12"
                       color="#999" />
            <text class="stat-text">校对 {{ volume.corrected_pages || 0 }}</text>
          </view>
          <view class="stat-item">
            <uni-icons type="calendar"
                       size="12"
                       color="#999" />
            <text class="stat-text">{{ formatDate(volume.updated_at) }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="volumes.length === 0 && !loading"
            class="empty-state">
        <uni-icons type="folder-add"
                   size="80"
                   color="#ccc" />
        <text class="empty-text">暂无卷册数据</text>
        <text class="empty-hint">点击右上角"新增卷册"开始添加</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading"
          class="loading-container">
      <uni-load-more status="loading" />
    </view>
  </view>
</template>

<script>
import {
  getAncientBookDetail,
  getBookVolumes,
  deleteVolume as deleteVolumeAPI
} from '@/api/ancient_book_volumes'

export default {
  name: 'VolumeList',
  data () {
    return {
      bookId: null,
      bookInfo: {},
      volumes: [],
      loading: false,

      statusOptions: [
        { value: 'draft', label: '草稿' },
        { value: 'published', label: '已发布' },
        { value: 'archived', label: '已归档' }
      ]
    }
  },

  computed: {
    totalPages () {
      return this.volumes.reduce((sum, vol) => sum + (vol.total_pages || 0), 0)
    },

    publishedVolumes () {
      return this.volumes.filter(vol => vol.status === 'published').length
    },

    ocrProgress () {
      const totalPages = this.totalPages
      if (totalPages === 0) return 0

      const ocrPages = this.volumes.reduce((sum, vol) => sum + (vol.ocr_completed_pages || 0), 0)
      return Math.round((ocrPages / totalPages) * 100)
    }
  },

  onLoad (options) {
    console.log('卷册列表页面接收参数:', options)

    this.bookId = parseInt(options.bookId)
    if (!this.bookId) {
      uni.showToast({
        title: '缺少古籍ID参数',
        icon: 'error'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return
    }

    this.loadBookInfo()
    this.loadVolumes()
  },

  onShow () {
    // 页面显示时刷新数据
    this.loadVolumes()
  },

  methods: {
    async loadBookInfo () {
      try {
        console.log('开始加载古籍信息, bookId:', this.bookId)
        const response = await getAncientBookDetail(this.bookId)

        console.log('古籍信息响应:', response)

        // 根据实际响应结构提取数据
        let bookData = null
        if (response && response.data) {
          bookData = response.data
        } else if (response && response.title) {
          bookData = response
        } else {
          console.warn('无法识别的响应结构:', response)
          bookData = { title: '数据格式错误' }
        }

        this.bookInfo = bookData
        console.log('设置的bookInfo:', this.bookInfo)
      } catch (error) {
        console.error('获取古籍信息失败:', error)
        this.bookInfo = { title: '加载失败' }
        uni.showToast({
          title: '获取古籍信息失败',
          icon: 'none'
        })
      }
    },

    async loadVolumes () {
      this.loading = true
      try {
        const response = await getBookVolumes(this.bookId)

        console.log('卷册列表响应:', response)
        this.volumes = response.items || response || []
        console.log('加载的卷册列表:', this.volumes)
      } catch (error) {
        console.error('获取卷册列表失败:', error)
        this.volumes = []
        uni.showToast({
          title: '获取卷册列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 新增卷册
    addNewVolume () {
      uni.navigateTo({
        url: `/pages/culture/volume-edit?bookId=${this.bookId}&book_title=${encodeURIComponent(this.bookInfo.title || '')}`
      })
    },

    // 编辑卷册
    editVolume (volume) {
      uni.navigateTo({
        url: `/pages/culture/volume-edit?bookId=${this.bookId}&volumeId=${volume.id}&book_title=${encodeURIComponent(this.bookInfo.title || '')}`
      })
    },

    // 查看卷册详情（跳转到阅读页面）
    viewVolumeDetail (volume) {
      uni.navigateTo({
        url: `/pages/culture/volume-detail?volumeId=${volume.id}&bookId=${this.bookId}`
      })
    },

    // 确认删除卷册
    confirmDeleteVolume (volume) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除第${volume.volume_number}册吗？此操作不可恢复。`,
        confirmText: '删除',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.deleteVolume(volume)
          }
        }
      })
    },

    // 删除卷册
    async deleteVolume (volume) {
      try {
        uni.showLoading({
          title: '删除中...'
        })

        await deleteVolumeAPI(volume.id)

        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 刷新列表
        this.loadVolumes()
      } catch (error) {
        console.error('删除卷册失败:', error)
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 工具方法
    getStatusLabel (status) {
      const option = this.statusOptions.find(opt => opt.value === status)
      return option ? option.label : status
    },

    formatDate (dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.volume-list {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部区域 */
.header-section {
  background: #fff;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.book-author {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.add-btn:active {
  transform: scale(0.95);
}

/* 统计区域 */
.stats-section {
  display: flex;
  background: #fff;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  display: block;
}

/* 卷册列表 */
.volume-container {
  padding: 0 16rpx;
}

.volume-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

.volume-card:active {
  transform: translateY(1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.volume-header {
  display: flex;
  padding: 24rpx;
  align-items: flex-start;
}

.volume-number {
  margin-right: 16rpx;
  text-align: center;
  min-width: 120rpx;
}

.number-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}

.status-badge.draft {
  background: #999;
}

.status-badge.published {
  background: #52c41a;
}

.status-badge.archived {
  background: #faad14;
}

.volume-info {
  flex: 1;
  margin-right: 16rpx;
}

.volume-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.page-range {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.volume-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.edit-btn {
  background: #e6f7ff;
}

.delete-btn {
  background: #fff2f0;
}

.volume-stats {
  display: flex;
  justify-content: space-around;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #f0f0f0;
}

.volume-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 40rpx;
  text-align: center;
  background: #fff;
  border-radius: 12rpx;
  margin: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin: 20rpx 0 8rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 加载状态 */
.loading-container {
  padding: 40rpx;
  text-align: center;
}
</style> 