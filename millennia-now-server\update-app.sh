#!/bin/bash

# 快速更新应用容器脚本
# 只重新构建和重启 millennia-now-server_app 容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

echo "🔄 快速更新 Millennia Now 应用容器"
echo "=================================="

# 1. 拉取最新代码
log_info "拉取最新代码..."
git pull origin master

# 2. 强制停止并删除应用容器
log_info "强制停止并删除应用容器..."
docker-compose rm -f app 2>/dev/null || true

# 3. 删除旧的应用镜像和相关镜像
log_info "删除旧的应用镜像..."
docker rmi millennia-now-server_app 2>/dev/null || true
docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true

# 4. 重新构建应用容器
log_info "重新构建应用容器..."
docker-compose build app

# 5. 启动应用容器
log_info "启动应用容器..."
docker-compose up -d app

# 6. 等待应用启动
log_info "等待应用启动..."
sleep 15

# 7. 检查应用状态
log_info "检查应用状态..."
if docker-compose ps app | grep -q "Up"; then
    log_success "应用容器启动成功"
else
    log_error "应用容器启动失败"
    docker-compose logs app
    exit 1
fi

# 8. 测试应用健康状态
log_info "测试应用健康状态..."
sleep 5

if curl -f http://localhost:8001/health > /dev/null 2>&1; then
    log_success "应用服务正常运行"
else
    log_warning "应用服务可能未完全启动，请稍等片刻再测试"
fi

# 9. 测试外部访问
if curl -f https://luckyzyn.top/millennia-api/health > /dev/null 2>&1; then
    log_success "外部访问正常"
else
    log_warning "外部访问可能需要等待nginx更新"
fi

echo ""
log_success "应用更新完成！"
echo ""

log_info "应用访问地址："
echo "  📱 本地: http://localhost:8001"
echo "  🌐 外部: https://luckyzyn.top/millennia-api"
echo "  📚 文档: https://luckyzyn.top/millennia-docs"
echo ""

log_info "查看应用日志："
echo "  docker-compose logs -f app"
echo ""

log_info "如果外部访问有问题，可能需要重启nginx："
echo "  docker-compose restart nginx"
echo ""
