<template>
  <view class="ancient-book-edit">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 古籍名称 -->
        <view class="form-item">
          <view class="label">古籍名称 <text class="required">*</text></view>
          <input v-model="formData.title"
                 class="input"
                 placeholder="请输入古籍名称"
                 maxlength="200" />
        </view>

        <!-- 责任者 -->
        <view class="form-item">
          <view class="label">责任者</view>
          <input v-model="formData.author"
                 class="input"
                 placeholder="请输入责任者"
                 maxlength="100" />
        </view>

        <!-- 朝代 -->
        <view class="form-item">
          <view class="label">朝代</view>
          <input v-model="formData.dynasty"
                 class="input"
                 placeholder="请输入朝代"
                 maxlength="50" />
        </view>

        <!-- 四部分类 -->
        <view class="form-item">
          <view class="label">四部分类</view>
          <picker :value="sibuCategoryIndex"
                  :range="sibuCategoryOptions"
                  range-key="label"
                  @change="onSibuCategoryChange">
            <view class="picker">
              {{ formData.sibu_category ? getSibuCategoryLabel(formData.sibu_category) : '请选择四部分类' }}
            </view>
          </picker>
        </view>

        <!-- 是否善本 -->
        <view class="form-item">
          <view class="label">是否善本</view>
          <view class="switch-container">
            <switch :checked="formData.is_rare_book"
                    @change="onRareBookChange"
                    color="#007aff" />
            <text class="switch-text">{{ formData.is_rare_book ? '是' : '否' }}</text>
          </view>
        </view>

        <!-- 善本书号 -->
        <view class="form-item"
              v-if="formData.is_rare_book">
          <view class="label">善本书号</view>
          <input v-model="formData.rare_book_number"
                 class="input"
                 placeholder="请输入善本书号"
                 maxlength="50" />
        </view>
      </view>

      <!-- 版本信息 -->
      <view class="form-section">
        <view class="section-title">版本信息</view>

        <!-- 版本项 -->
        <view class="form-item">
          <view class="label">版本项</view>
          <textarea v-model="formData.edition"
                    class="textarea"
                    placeholder="请输入版本项"
                    maxlength="500" />
        </view>

        <!-- 出版发行项 -->
        <view class="form-item">
          <view class="label">出版发行项</view>
          <textarea v-model="formData.publication"
                    class="textarea"
                    placeholder="请输入出版发行项"
                    maxlength="500" />
        </view>

        <!-- 版本书目史注 -->
        <view class="form-item">
          <view class="label">版本书目史注</view>
          <textarea v-model="formData.bibliographic_note"
                    class="textarea"
                    placeholder="请输入版本书目史注"
                    maxlength="1000" />
        </view>
      </view>

      <!-- 物理信息 -->
      <view class="form-section">
        <view class="section-title">物理信息</view>

        <!-- 总册数 -->
        <view class="form-item">
          <view class="label">总册数</view>
          <input v-model.number="formData.total_volumes"
                 class="input"
                 type="number"
                 placeholder="请输入总册数" />
        </view>

        <!-- 尺寸信息 -->
        <view class="form-item">
          <view class="label">尺寸信息</view>
          <input v-model="formData.size_info"
                 class="input"
                 placeholder="请输入尺寸信息"
                 maxlength="100" />
        </view>

        <!-- 保存状态 -->
        <view class="form-item">
          <view class="label">保存状态</view>
          <picker :value="preservationStatusIndex"
                  :range="preservationStatusOptions"
                  range-key="label"
                  @change="onPreservationStatusChange">
            <view class="picker">
              {{ formData.preservation_status ? getPreservationStatusLabel(formData.preservation_status) : '请选择保存状态' }}
            </view>
          </picker>
        </view>
      </view>

      <!-- 内容描述 -->
      <view class="form-section">
        <view class="section-title">内容描述</view>

        <!-- 简介 -->
        <view class="form-item">
          <view class="label">简介</view>
          <textarea v-model="formData.description"
                    class="textarea large"
                    placeholder="请输入古籍简介"
                    maxlength="1000" />
        </view>

        <!-- 内容摘要 -->
        <view class="form-item">
          <view class="label">内容摘要</view>
          <textarea v-model="formData.content_summary"
                    class="textarea large"
                    placeholder="请输入内容摘要"
                    maxlength="2000" />
        </view>

        <!-- 关键词 -->
        <view class="form-item">
          <view class="label">关键词</view>
          <input v-model="formData.keywords"
                 class="input"
                 placeholder="请输入关键词，多个关键词用逗号分隔"
                 maxlength="500" />
        </view>
      </view>

      <!-- 封面图片 -->
      <view class="form-section">
        <view class="section-title">封面图片</view>
        <view class="image-upload">
          <view class="image-preview"
                v-if="formData.cover_image">
            <image :src="formData.cover_image"
                   mode="aspectFill"
                   class="preview-image" />
            <view class="image-actions">
              <text class="action-btn"
                    @click="changeMainImage">更换</text>
              <text class="action-btn delete"
                    @click="removeMainImage">删除</text>
            </view>
          </view>
          <view class="upload-btn"
                v-else
                @click="uploadMainImage"
                :class="{ disabled: uploading }">
            <text class="upload-icon">{{ uploading ? '...' : '+' }}</text>
            <text class="upload-text">{{ uploading ? '上传中' : '上传封面图片' }}</text>
          </view>
        </view>
        <view class="form-tips">
          <text class="tips-text">建议上传高清封面图片，支持JPG、PNG格式</text>
        </view>
      </view>

      <!-- 可见性设置 -->
      <view class="form-section">
        <view class="section-title">可见性设置</view>
        <view class="form-item">
          <view class="label">可见性 <text class="required">*</text></view>
          <picker :value="visibilityIndex"
                  :range="visibilityOptions"
                  range-key="label"
                  @change="onVisibilityChange">
            <view class="picker">
              {{ formData.visibility ? getVisibilityLabel(formData.visibility) : '请选择可见性' }}
            </view>
          </picker>
          <view class="form-tips">
            <text class="tips-text">公众可见：所有用户都可以查看</text>
            <text class="tips-text">研究员可见：只有研究员和管理员可以查看</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="bottom-actions">
      <button class="save-btn"
              :disabled="saving || !isFormValid"
              @click="handleSave">
        <text class="save-text">{{ saving ? '保存中...' : '保存' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getImageProxyUrl } from '../../utils/image'
import {
  createAncientBook,
  updateAncientBook,
  getAncientBookDetail,
  SIBU_CATEGORIES,
  PRESERVATION_STATUS_OPTIONS,
  VISIBILITY_OPTIONS
} from '../../api/ancient_books'

export default {
  name: 'AncientBookEdit',
  setup () {

    // 页面参数
    const pageParams = ref({
      mode: 'create', // create | edit
      book_id: null
    })

    // 表单数据
    const formData = reactive({
      title: '',
      author: '',
      dynasty: '',
      edition: '',
      publication: '',
      bibliographic_note: '',
      sibu_category: '',
      rare_book_number: '',
      description: '',
      content_summary: '',
      keywords: '',
      total_volumes: null,
      size_info: '',
      preservation_status: '',
      cover_image: '',
      visibility: 'public',
      is_rare_book: false
    })

    // 页面状态
    const loading = ref(false)
    const saving = ref(false)
    const uploading = ref(false)
    const originalData = ref(null)

    // 选择器选项
    const sibuCategoryOptions = SIBU_CATEGORIES
    const preservationStatusOptions = PRESERVATION_STATUS_OPTIONS
    const visibilityOptions = VISIBILITY_OPTIONS

    // 选择器索引
    const sibuCategoryIndex = computed(() => {
      return sibuCategoryOptions.findIndex(item => item.value === formData.sibu_category)
    })

    const preservationStatusIndex = computed(() => {
      return preservationStatusOptions.findIndex(item => item.value === formData.preservation_status)
    })

    const visibilityIndex = computed(() => {
      return visibilityOptions.findIndex(item => item.value === formData.visibility)
    })

    // 表单验证
    const isFormValid = computed(() => {
      return formData.title.trim().length > 0
    })

    // 是否为编辑模式
    const isEditMode = computed(() => pageParams.value.mode === 'edit')

    // 页面加载
    onLoad((options) => {
      if (options) {
        pageParams.value = {
          mode: options.mode || 'create',
          book_id: options.book_id ? parseInt(options.book_id) : null
        }

        if (isEditMode.value && pageParams.value.book_id) {
          loadBookData()
        }
      }
    })

    // 加载古籍数据
    const loadBookData = async () => {
      if (!pageParams.value.book_id) return

      loading.value = true
      try {
        const book = await getAncientBookDetail(pageParams.value.book_id)
        if (book) {
          originalData.value = book

          // 填充表单数据
          Object.assign(formData, {
            title: book.title,
            author: book.author || '',
            dynasty: book.dynasty || '',
            edition: book.edition || '',
            publication: book.publication || '',
            bibliographic_note: book.bibliographic_note || '',
            sibu_category: book.sibu_category || '',
            rare_book_number: book.rare_book_number || '',
            description: book.description || '',
            content_summary: book.content_summary || '',
            keywords: book.keywords || '',
            total_volumes: book.total_volumes || null,
            size_info: book.size_info || '',
            preservation_status: book.preservation_status || '',
            cover_image: book.cover_image || '',
            visibility: book.visibility || 'public',
            is_rare_book: book.is_rare_book || false
          })
        } else {
          uni.showToast({
            title: '古籍不存在',
            icon: 'error'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        console.error('加载古籍数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        loading.value = false
      }
    }

    // 四部分类选择
    const onSibuCategoryChange = (e) => {
      const index = e.detail.value
      formData.sibu_category = sibuCategoryOptions[index].value
    }

    // 保存状态选择
    const onPreservationStatusChange = (e) => {
      const index = e.detail.value
      formData.preservation_status = preservationStatusOptions[index].value
    }

    // 可见性选择
    const onVisibilityChange = (e) => {
      const index = e.detail.value
      formData.visibility = visibilityOptions[index].value
    }

    // 善本切换
    const onRareBookChange = (e) => {
      formData.is_rare_book = e.detail.value
      // 如果不是善本，清空善本书号
      if (!formData.is_rare_book) {
        formData.rare_book_number = ''
      }
    }

    // 上传图片到服务器
    const uploadImageToServer = async (filePath) => {
      uploading.value = true

      try {
        const token = uni.getStorageSync('access_token')
        const uploadRes = await uni.uploadFile({
          url: 'http://192.168.8.96:8000/api/v1/upload/ancient-books/image',
          filePath: filePath,
          name: 'file',
          header: {
            Authorization: `Bearer ${token}`,
          },
        })

        const data = JSON.parse(uploadRes.data)
        if (data.success) {
          // 使用工具函数处理图片URL
          const imageUrl = getImageProxyUrl(data.data.url)
          formData.cover_image = imageUrl

          uni.showToast({
            title: '封面图片上传成功',
            icon: 'success',
            duration: 1500,
          })
        } else {
          throw new Error(data.message || '上传失败')
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none',
        })
        throw error
      } finally {
        uploading.value = false
      }
    }

    // 上传主图片
    const uploadMainImage = async () => {
      if (uploading.value) return

      try {
        const res = await uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
        })

        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          await uploadImageToServer(res.tempFilePaths[0])
        }
      } catch (error) {
        console.error('选择图片失败:', error)
        uni.showToast({
          title: '选择图片失败',
          icon: 'none',
        })
      }
    }

    // 更换主图片
    const changeMainImage = () => {
      uploadMainImage()
    }

    // 删除主图片
    const removeMainImage = () => {
      formData.cover_image = ''

      // 显示删除成功的提示
      uni.showToast({
        title: '图片已删除',
        icon: 'success',
        duration: 1000,
      })
    }

    // 获取四部分类标签
    const getSibuCategoryLabel = (value) => {
      const option = sibuCategoryOptions.find(item => item.value === value)
      return option ? option.label : value
    }

    // 获取保存状态标签
    const getPreservationStatusLabel = (value) => {
      const option = preservationStatusOptions.find(item => item.value === value)
      return option ? option.label : value
    }

    // 获取可见性标签
    const getVisibilityLabel = (value) => {
      const option = visibilityOptions.find(item => item.value === value)
      return option ? option.label : value
    }

    // 保存古籍
    const handleSave = async () => {
      if (!isFormValid.value) {
        uni.showToast({
          title: '请填写必填项',
          icon: 'error'
        })
        return
      }

      saving.value = true
      try {
        // 构建请求数据
        const requestData = {
          title: formData.title.trim(),
          author: formData.author.trim() || null,
          dynasty: formData.dynasty.trim() || null,
          edition: formData.edition.trim() || null,
          publication: formData.publication.trim() || null,
          bibliographic_note: formData.bibliographic_note.trim() || null,
          sibu_category: formData.sibu_category || null,
          rare_book_number: formData.is_rare_book ? (formData.rare_book_number.trim() || null) : null,
          description: formData.description.trim() || null,
          content_summary: formData.content_summary.trim() || null,
          keywords: formData.keywords.trim() || null,
          total_volumes: formData.total_volumes || null,
          size_info: formData.size_info.trim() || null,
          preservation_status: formData.preservation_status || null,
          cover_image: formData.cover_image || null,
          visibility: formData.visibility || 'public',
          is_rare_book: formData.is_rare_book || false
        }

        let result
        if (isEditMode.value && pageParams.value.book_id) {
          // 更新古籍
          result = await updateAncientBook(pageParams.value.book_id, requestData)
        } else {
          // 创建古籍
          result = await createAncientBook(requestData)
        }

        if (result) {
          uni.showToast({
            title: isEditMode.value ? '更新成功' : '创建成功',
            icon: 'success'
          })

          // 通知管理页面刷新数据
          uni.$emit('refreshAncientBooksList')

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: isEditMode.value ? '更新失败' : '创建失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('保存古籍失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        saving.value = false
      }
    }

    // 返回上一页
    const goBack = () => {
      uni.navigateBack()
    }

    // 返回响应式数据和方法
    return {
      pageParams,
      formData,
      loading,
      saving,
      uploading,
      originalData,
      sibuCategoryOptions,
      preservationStatusOptions,
      visibilityOptions,
      sibuCategoryIndex,
      preservationStatusIndex,
      visibilityIndex,
      isFormValid,
      isEditMode,
      loadBookData,
      onSibuCategoryChange,
      onPreservationStatusChange,
      onVisibilityChange,
      onRareBookChange,
      uploadMainImage,
      changeMainImage,
      removeMainImage,
      getSibuCategoryLabel,
      getPreservationStatusLabel,
      getVisibilityLabel,
      handleSave,
      goBack
    }
  }
}
</script>

<style scoped>
.ancient-book-edit {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.form-container {
  padding: 20rpx;
}

.form-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.required {
  color: #ff4757;
  margin-left: 5rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.input:focus {
  border-color: #007aff;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  resize: vertical;
}

.textarea.large {
  min-height: 200rpx;
}

.textarea:focus {
  border-color: #007aff;
}

.picker {
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  color: #333;
}

.picker:empty::before {
  content: '请选择';
  color: #999;
}

.form-tips {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 5rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.switch-text {
  font-size: 28rpx;
  color: #333;
}

/* 图片上传 */
.image-upload {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 300rpx;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: 80rpx;
  text-align: center;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.8);
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  color: #999;
}

.upload-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.save-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.save-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

.save-btn:not(:disabled):active {
  transform: scale(0.98);
}

.save-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .form-container {
    padding: 15rpx;
  }

  .form-section {
    padding: 25rpx;
  }

  .section-title {
    font-size: 30rpx;
  }

  .label {
    font-size: 26rpx;
  }

  .input,
  .textarea,
  .picker {
    font-size: 26rpx;
  }

  .save-btn {
    height: 75rpx;
    font-size: 30rpx;
  }
}
</style> 