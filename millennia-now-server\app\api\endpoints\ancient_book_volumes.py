from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from app.database.db import get_db
from app.core.auth import get_current_user
from app.models.users import User
from app.models.ancient_book_volumes import AncientBookVolume, AncientBookPage, AncientBookChapter, OCRTask
from app.schemas.ancient_book_volume_schemas import (
    AncientBookVolumeCreate, AncientBookVolumeUpdate, AncientBookVolumeResponse, AncientBookVolumeDetail,
    AncientBookPageCreate, AncientBookPageUpdate, AncientBookPageResponse,
    AncientBookChapterCreate, AncientBookChapterUpdate, AncientBookChapterResponse,
    OCRTaskCreate, OCRTaskResponse, BatchPageUpload, BatchPageUploadResponse,
    BatchOCRRequest, BatchOCRResponse, VolumeSearchParams, PageSearchParams,
    VolumeListResponse, PageListResponse, ChapterListResponse,
    VolumeStatistics, PageStatistics
)
from app.core.minio_client import minio_client

logger = logging.getLogger(__name__)
router = APIRouter()

# ==================== 卷册管理 ====================

@router.post("/volumes/", response_model=AncientBookVolumeResponse)
async def create_volume(
    volume: AncientBookVolumeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建古籍卷册"""
    try:
        # 检查卷册序号是否已存在
        existing = db.query(AncientBookVolume).filter(
            AncientBookVolume.book_id == volume.book_id,
            AncientBookVolume.volume_number == volume.volume_number
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"卷册序号 {volume.volume_number} 已存在"
            )
        
        db_volume = AncientBookVolume(
            **volume.dict(),
            created_by=current_user.id,
            updated_by=current_user.id
        )
        db.add(db_volume)
        db.commit()
        db.refresh(db_volume)
        
        logger.info(f"User {current_user.id} created volume {db_volume.id}")
        return db_volume
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create volume: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建卷册失败")

@router.get("/volumes/", response_model=VolumeListResponse)
async def list_volumes(
    book_id: Optional[int] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取卷册列表"""
    query = db.query(AncientBookVolume)
    
    if book_id:
        query = query.filter(AncientBookVolume.book_id == book_id)
    if status:
        query = query.filter(AncientBookVolume.status == status)
    
    total = query.count()
    volumes = query.offset(skip).limit(limit).all()
    
    return VolumeListResponse(
        items=volumes,
        total=total,
        skip=skip,
        limit=limit
    )

@router.get("/volumes/{volume_id}", response_model=AncientBookVolumeDetail)
async def get_volume(
    volume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取卷册详情"""
    volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    return volume

@router.put("/volumes/{volume_id}", response_model=AncientBookVolumeResponse)
async def update_volume(
    volume_id: int,
    volume_update: AncientBookVolumeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新卷册信息"""
    volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    try:
        update_data = volume_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(volume, field, value)
        
        volume.updated_by = current_user.id
        db.commit()
        db.refresh(volume)
        
        logger.info(f"User {current_user.id} updated volume {volume_id}")
        return volume
        
    except Exception as e:
        logger.error(f"Failed to update volume {volume_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新卷册失败")

@router.delete("/volumes/{volume_id}")
async def delete_volume(
    volume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除卷册"""
    volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    try:
        db.delete(volume)
        db.commit()
        
        logger.info(f"User {current_user.id} deleted volume {volume_id}")
        return {"message": "卷册删除成功"}
        
    except Exception as e:
        logger.error(f"Failed to delete volume {volume_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除卷册失败")

# ==================== 页面管理 ====================

@router.post("/pages/", response_model=AncientBookPageResponse)
async def create_page(
    page: AncientBookPageCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建页面"""
    try:
        # 检查页码是否已存在
        existing = db.query(AncientBookPage).filter(
            AncientBookPage.volume_id == page.volume_id,
            AncientBookPage.page_number == page.page_number
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"页码 {page.page_number} 已存在"
            )
        
        db_page = AncientBookPage(
            **page.dict(),
            created_by=current_user.id,
            updated_by=current_user.id
        )
        db.add(db_page)
        db.commit()
        db.refresh(db_page)
        
        logger.info(f"User {current_user.id} created page {db_page.id}")
        return db_page
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create page: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建页面失败")

@router.post("/pages/upload", response_model=AncientBookPageResponse)
async def upload_page_image(
    volume_id: int,
    page_number: int,
    page_label: Optional[str] = None,
    page_type: str = "content",
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传页面图片并创建页面记录"""
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只能上传图片文件")
        
        # 检查页码是否已存在
        existing = db.query(AncientBookPage).filter(
            AncientBookPage.volume_id == volume_id,
            AncientBookPage.page_number == page_number
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"页码 {page_number} 已存在"
            )
        
        # 读取文件内容
        file_content = await file.read()
        
        # 上传到Minio
        success, result = await minio_client.upload_image(
            file_content=file_content,
            original_filename=file.filename or f"page_{page_number}.jpg",
            prefix=f"ancient-books/volumes/{volume_id}",
            optimize=True
        )
        
        if not success:
            raise HTTPException(status_code=400, detail=result)
        
        # 创建页面记录
        db_page = AncientBookPage(
            volume_id=volume_id,
            page_number=page_number,
            page_label=page_label,
            page_type=page_type,
            image_url=result,
            image_size=len(file_content),
            created_by=current_user.id,
            updated_by=current_user.id
        )
        db.add(db_page)
        db.commit()
        db.refresh(db_page)
        
        logger.info(f"User {current_user.id} uploaded page image {db_page.id}")
        return db_page
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload page image: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="上传页面图片失败")

@router.post("/pages/batch-upload", response_model=BatchPageUploadResponse)
async def batch_upload_pages(
    volume_id: int,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量上传页面图片"""
    if len(files) > 100:
        raise HTTPException(status_code=400, detail="一次最多上传100个文件")
    
    success_pages = []
    failed_pages = []
    
    for i, file in enumerate(files):
        try:
            # 检查文件类型
            if not file.content_type or not file.content_type.startswith('image/'):
                failed_pages.append({
                    "filename": file.filename,
                    "error": "只能上传图片文件"
                })
                continue
            
            # 自动分配页码
            page_number = i + 1
            existing = db.query(AncientBookPage).filter(
                AncientBookPage.volume_id == volume_id,
                AncientBookPage.page_number == page_number
            ).first()
            
            if existing:
                # 如果页码存在，找下一个可用页码
                max_page = db.query(AncientBookPage.page_number).filter(
                    AncientBookPage.volume_id == volume_id
                ).order_by(AncientBookPage.page_number.desc()).first()
                page_number = (max_page[0] if max_page else 0) + 1
            
            # 读取文件内容
            file_content = await file.read()
            
            # 上传到Minio
            success, result = await minio_client.upload_image(
                file_content=file_content,
                original_filename=file.filename or f"page_{page_number}.jpg",
                prefix=f"ancient-books/volumes/{volume_id}",
                optimize=True
            )
            
            if not success:
                failed_pages.append({
                    "filename": file.filename,
                    "error": result
                })
                continue
            
            # 创建页面记录
            db_page = AncientBookPage(
                volume_id=volume_id,
                page_number=page_number,
                page_label=f"第{page_number}页",
                image_url=result,
                image_size=len(file_content),
                created_by=current_user.id,
                updated_by=current_user.id
            )
            db.add(db_page)
            db.flush()  # 获取ID但不提交
            success_pages.append(db_page)
            
        except Exception as e:
            failed_pages.append({
                "filename": file.filename,
                "error": str(e)
            })
    
    try:
        db.commit()
        logger.info(f"User {current_user.id} batch uploaded {len(success_pages)} pages")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to commit batch upload: {e}")
        raise HTTPException(status_code=500, detail="批量上传失败")
    
    return BatchPageUploadResponse(
        success_count=len(success_pages),
        failed_count=len(failed_pages),
        total_count=len(files),
        success_pages=success_pages,
        failed_pages=failed_pages
    )

@router.get("/pages/", response_model=PageListResponse)
async def list_pages(
    volume_id: Optional[int] = None,
    page_type: Optional[str] = None,
    ocr_status: Optional[str] = None,
    is_corrected: Optional[bool] = None,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取页面列表"""
    query = db.query(AncientBookPage)
    
    if volume_id:
        query = query.filter(AncientBookPage.volume_id == volume_id)
    if page_type:
        query = query.filter(AncientBookPage.page_type == page_type)
    if ocr_status:
        query = query.filter(AncientBookPage.ocr_status == ocr_status)
    if is_corrected is not None:
        query = query.filter(AncientBookPage.is_corrected == is_corrected)
    
    query = query.order_by(AncientBookPage.volume_id, AncientBookPage.page_number)
    
    total = query.count()
    pages = query.offset(skip).limit(limit).all()
    
    return PageListResponse(
        items=pages,
        total=total,
        skip=skip,
        limit=limit
    )

@router.get("/pages/{page_id}", response_model=AncientBookPageResponse)
async def get_page(
    page_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取页面详情"""
    page = db.query(AncientBookPage).filter(AncientBookPage.id == page_id).first()
    if not page:
        raise HTTPException(status_code=404, detail="页面不存在")
    
    return page

@router.put("/pages/{page_id}", response_model=AncientBookPageResponse)
async def update_page(
    page_id: int,
    page_update: AncientBookPageUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新页面信息"""
    page = db.query(AncientBookPage).filter(AncientBookPage.id == page_id).first()
    if not page:
        raise HTTPException(status_code=404, detail="页面不存在")
    
    try:
        update_data = page_update.dict(exclude_unset=True)

        # 先处理 is_corrected 字段，保证可以取消校对
        if 'is_corrected' in update_data:
            is_corrected_value = update_data.pop('is_corrected')
            page.is_corrected = is_corrected_value

            # 若取消校对，清空 corrected_text 及相关元数据
            if is_corrected_value == 0:
                page.corrected_text = None
                page.corrected_by = None
                page.corrected_at = None

        # 其余字段批量更新
        for field, value in update_data.items():
            setattr(page, field, value)

        # 如果更新了校对文字，自动开启校对状态
        if 'corrected_text' in update_data and update_data.get('corrected_text'):
            page.is_corrected = True
            page.corrected_by = current_user.id
            from datetime import datetime
            page.corrected_at = datetime.utcnow()
        
        page.updated_by = current_user.id
        db.commit()
        db.refresh(page)
        
        logger.info(f"User {current_user.id} updated page {page_id}")
        return page
        
    except Exception as e:
        logger.error(f"Failed to update page {page_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新页面失败")

@router.delete("/pages/{page_id}")
async def delete_page(
    page_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除页面"""
    page = db.query(AncientBookPage).filter(AncientBookPage.id == page_id).first()
    if not page:
        raise HTTPException(status_code=404, detail="页面不存在")
    
    try:
        # 删除关联的图片文件
        if page.image_url:
            await minio_client.delete_image(page.image_url)
        
        db.delete(page)
        db.commit()
        
        logger.info(f"User {current_user.id} deleted page {page_id}")
        return {"message": "页面删除成功"}
        
    except Exception as e:
        logger.error(f"Failed to delete page {page_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除页面失败")

# ==================== OCR处理 ====================

@router.post("/pages/{page_id}/ocr", response_model=OCRTaskResponse)
async def start_ocr_task(
    page_id: int,
    background_tasks: BackgroundTasks,
    ocr_provider: str = "default",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动OCR处理任务"""
    page = db.query(AncientBookPage).filter(AncientBookPage.id == page_id).first()
    if not page:
        raise HTTPException(status_code=404, detail="页面不存在")
    
    try:
        # 创建OCR任务
        ocr_task = OCRTask(
            page_id=page_id,
            ocr_provider=ocr_provider,
            task_status="pending"
        )
        db.add(ocr_task)
        db.commit()
        db.refresh(ocr_task)
        
        # 添加后台任务处理OCR
        background_tasks.add_task(process_ocr_task, ocr_task.id, db)
        
        logger.info(f"User {current_user.id} started OCR task {ocr_task.id} for page {page_id}")
        return ocr_task
        
    except Exception as e:
        logger.error(f"Failed to start OCR task: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="启动OCR任务失败")

@router.post("/pages/batch-ocr", response_model=BatchOCRResponse)
async def batch_start_ocr(
    request: BatchOCRRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量启动OCR处理任务"""
    try:
        task_ids = []
        
        for page_id in request.page_ids:
            # 检查页面是否存在
            page = db.query(AncientBookPage).filter(AncientBookPage.id == page_id).first()
            if not page:
                continue
            
            # 创建OCR任务
            ocr_task = OCRTask(
                page_id=page_id,
                ocr_provider=request.ocr_provider,
                task_status="pending"
            )
            db.add(ocr_task)
            db.flush()
            task_ids.append(ocr_task.id)
            
            # 添加后台任务
            background_tasks.add_task(process_ocr_task, ocr_task.id, db)
        
        db.commit()
        
        logger.info(f"User {current_user.id} started {len(task_ids)} OCR tasks")
        return BatchOCRResponse(
            task_count=len(task_ids),
            task_ids=task_ids,
            message=f"成功启动 {len(task_ids)} 个OCR任务"
        )
        
    except Exception as e:
        logger.error(f"Failed to start batch OCR: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="批量启动OCR任务失败")

# ==================== 统计信息 ====================

@router.get("/volumes/{volume_id}/statistics", response_model=VolumeStatistics)
async def get_volume_statistics(
    volume_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取卷册统计信息"""
    volume = db.query(AncientBookVolume).filter(AncientBookVolume.id == volume_id).first()
    if not volume:
        raise HTTPException(status_code=404, detail="卷册不存在")
    
    total_pages = db.query(AncientBookPage).filter(AncientBookPage.volume_id == volume_id).count()
    ocr_completed = db.query(AncientBookPage).filter(
        AncientBookPage.volume_id == volume_id,
        AncientBookPage.ocr_status == "completed"
    ).count()
    corrected = db.query(AncientBookPage).filter(
        AncientBookPage.volume_id == volume_id,
        AncientBookPage.is_corrected == True
    ).count()
    
    return VolumeStatistics(
        total_volumes=1,
        total_pages=total_pages,
        ocr_completed_pages=ocr_completed,
        corrected_pages=corrected,
        ocr_completion_rate=ocr_completed / total_pages if total_pages > 0 else 0,
        correction_rate=corrected / total_pages if total_pages > 0 else 0
    )

# ==================== 辅助函数 ====================

async def process_ocr_task(task_id: int, db: Session):
    """处理OCR任务的后台函数"""
    try:
        task = db.query(OCRTask).filter(OCRTask.id == task_id).first()
        if not task:
            return
        
        # 更新任务状态
        task.task_status = "processing"
        from datetime import datetime
        task.started_at = datetime.utcnow()
        db.commit()
        
        # 这里可以集成实际的OCR服务
        # 示例：调用百度OCR、腾讯OCR等
        # ocr_result = await call_ocr_service(task.page.image_url)
        
        # 模拟OCR处理
        import time
        time.sleep(2)  # 模拟处理时间
        
        # 更新页面OCR结果
        page = task.page
        page.ocr_text = "这是OCR识别的示例文字内容..."  # 实际应该是OCR结果
        page.ocr_confidence = 0.95
        page.ocr_status = "completed"
        page.ocr_processed_at = datetime.utcnow()
        
        # 更新任务状态
        task.task_status = "completed"
        task.completed_at = datetime.utcnow()
        task.processing_time_ms = 2000
        task.character_count = len(page.ocr_text) if page.ocr_text else 0
        task.confidence_avg = page.ocr_confidence
        
        db.commit()
        logger.info(f"OCR task {task_id} completed successfully")
        
    except Exception as e:
        logger.error(f"OCR task {task_id} failed: {e}")
        if task:
            task.task_status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            db.commit() 