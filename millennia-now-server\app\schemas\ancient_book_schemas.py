from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime

# ==================== 卷册相关Schema ====================
# 卷册相关的Schema已移动到 ancient_book_volume_schemas.py 文件中
# 这里保留一个简化版本用于兼容性

class AncientBookVolumeCreate(BaseModel):
    """创建卷册请求模型（兼容性保留）"""
    volume_number: int = Field(..., ge=1, description="卷册序号")
    volume_title: Optional[str] = Field(None, max_length=200, description="卷册标题")
    content_description: Optional[str] = Field(None, description="卷册内容描述")

class AncientBookVolumeUpdate(BaseModel):
    """更新卷册请求模型（兼容性保留）"""
    volume_title: Optional[str] = Field(None, max_length=200, description="卷册标题")
    content_description: Optional[str] = Field(None, description="卷册内容描述")

class AncientBookVolumeResponse(BaseModel):
    """卷册响应模型（兼容性保留）"""
    id: int
    book_id: int
    volume_number: int
    volume_title: Optional[str]
    total_pages: int = Field(default=0, description="总页数")
    status: str = Field(default="draft", description="状态")
    content_description: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 古籍主体Schema ====================

class AncientBookBase(BaseModel):
    """古籍基础模型"""
    title: str = Field(..., min_length=1, max_length=200, description="古籍名称")
    author: Optional[str] = Field(None, max_length=100, description="责任者")
    dynasty: Optional[str] = Field(None, max_length=50, description="朝代")
    
    # 版本信息
    edition: Optional[str] = Field(None, description="版本项")
    publication: Optional[str] = Field(None, description="出版发行项")
    bibliographic_note: Optional[str] = Field(None, description="版本书目史注")
    
    # 分类编号
    sibu_category: Optional[str] = Field(None, pattern="^[ABCD]$", description="四部分类号")
    rare_book_number: Optional[str] = Field(None, max_length=50, description="善本书号")
    
    # 内容描述
    description: Optional[str] = Field(None, description="简介")
    content_summary: Optional[str] = Field(None, description="内容摘要")
    keywords: Optional[str] = Field(None, max_length=500, description="关键词")
    
    # 物理信息
    total_volumes: Optional[int] = Field(None, ge=1, description="总册数")
    size_info: Optional[str] = Field(None, max_length=100, description="尺寸信息")
    preservation_status: Optional[str] = Field(None, max_length=50, description="保存状态")
    
    # 基础数字化信息
    cover_image: Optional[str] = Field(None, max_length=500, description="封面图片URL")
    
    # 可见性设置
    visibility: Optional[str] = Field(None, description="可见性")
    
    @validator('sibu_category')
    def validate_sibu_category(cls, v):
        if v and v not in ['A', 'B', 'C', 'D']:
            raise ValueError('四部分类号必须是A（经部）、B（史部）、C（子部）、D（集部）之一')
        return v
    
    @validator('visibility')
    def validate_visibility(cls, v):
        if v and v not in ['public', 'researcher']:
            raise ValueError('可见性必须是public（公众）或researcher（研究员）')
        return v

class AncientBookCreate(AncientBookBase):
    """创建古籍请求模型"""
    volumes: Optional[List[AncientBookVolumeCreate]] = Field(None, description="卷册列表")

class AncientBookUpdate(BaseModel):
    """更新古籍请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="古籍名称")
    author: Optional[str] = Field(None, max_length=100, description="责任者")
    dynasty: Optional[str] = Field(None, max_length=50, description="朝代")
    
    # 版本信息
    edition: Optional[str] = Field(None, description="版本项")
    publication: Optional[str] = Field(None, description="出版发行项")
    bibliographic_note: Optional[str] = Field(None, description="版本书目史注")
    
    # 分类编号
    sibu_category: Optional[str] = Field(None, pattern="^[ABCD]$", description="四部分类号")
    rare_book_number: Optional[str] = Field(None, max_length=50, description="善本书号")
    
    # 内容描述
    description: Optional[str] = Field(None, description="简介")
    content_summary: Optional[str] = Field(None, description="内容摘要")
    keywords: Optional[str] = Field(None, max_length=500, description="关键词")
    
    # 物理信息
    total_volumes: Optional[int] = Field(None, ge=1, description="总册数")
    size_info: Optional[str] = Field(None, max_length=100, description="尺寸信息")
    preservation_status: Optional[str] = Field(None, max_length=50, description="保存状态")
    
    # 基础数字化信息
    cover_image: Optional[str] = Field(None, max_length=500, description="封面图片URL")
    
    # 可见性设置
    visibility: Optional[str] = Field(None, description="可见性")
    
    @validator('sibu_category')
    def validate_sibu_category(cls, v):
        if v and v not in ['A', 'B', 'C', 'D']:
            raise ValueError('四部分类号必须是A（经部）、B（史部）、C（子部）、D（集部）之一')
        return v
    
    @validator('visibility')
    def validate_visibility(cls, v):
        if v and v not in ['public', 'researcher']:
            raise ValueError('可见性必须是public（公众）或researcher（研究员）')
        return v

class AncientBookResponse(AncientBookBase):
    """古籍响应模型"""
    id: int
    
    # 统计信息
    view_count: int = Field(default=0, description="浏览次数")
    favorite_count: int = Field(default=0, description="收藏次数")
    
    # 管理信息
    status: str = Field(default="active", description="状态")
    visibility: str = Field(default="public", description="可见性")
    created_by: Optional[int] = Field(None, description="创建者ID")
    updated_by: Optional[int] = Field(None, description="更新者ID")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 计算属性
    is_rare_book: bool = Field(..., description="是否为善本")
    sibu_category_name: str = Field(..., description="四部分类名称")
    available_volumes_count: int = Field(..., description="可用册数")
    total_pages: int = Field(..., description="总页数")
    
    # 关联数据
    volumes: List[AncientBookVolumeResponse] = Field(..., description="卷册列表")
    
    class Config:
        from_attributes = True

class AncientBookList(BaseModel):
    """古籍列表响应模型"""
    items: List[AncientBookResponse] = Field(..., description="古籍列表")
    total: int = Field(..., description="总数")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="每页数量")
    
    @property
    def has_next(self) -> bool:
        """是否有下一页"""
        return self.skip + self.limit < self.total
    
    @property
    def has_prev(self) -> bool:
        """是否有上一页"""
        return self.skip > 0

class AncientBookFilter(BaseModel):
    """古籍筛选条件模型"""
    search: Optional[str] = Field(None, description="搜索关键词")
    sibu_category: Optional[str] = Field(None, pattern="^[ABCD]$", description="四部分类")
    dynasty: Optional[str] = Field(None, description="朝代")
    is_rare: Optional[bool] = Field(None, description="是否为善本")
    is_digitized: Optional[bool] = Field(None, description="是否已数字化")
    
    @validator('sibu_category')
    def validate_sibu_category(cls, v):
        if v and v not in ['A', 'B', 'C', 'D']:
            raise ValueError('四部分类号必须是A（经部）、B（史部）、C（子部）、D（集部）之一')
        return v

# ==================== 卷册收藏相关Schema ====================

class VolumeReadRequest(BaseModel):
    """卷册阅读请求模型"""
    volume_id: int = Field(..., description="卷册ID")
    start_page: Optional[int] = Field(1, ge=1, description="开始页码")

class VolumeFavoriteRequest(BaseModel):
    """卷册收藏请求模型"""
    volume_id: int = Field(..., description="卷册ID")

class VolumeViewHistoryResponse(BaseModel):
    """卷册浏览历史响应模型"""
    id: int
    volume_id: int
    view_time: datetime
    view_duration: Optional[int]
    last_page: Optional[int]
    
    class Config:
        from_attributes = True 