<template>
  <view class="book-detail-page">

    <!-- 加载状态 -->
    <view v-if="loading"
          class="loading-container">
      <uni-load-more status="loading" />
    </view>
    <!-- 古籍详情内容 -->
    <view v-else-if="bookDetail"
          class="detail-content">
      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="book-header">
          <view class="book-cover">
            <image :src="bookDetail.cover_image || '/static/images/no-image.svg'"
                   mode="aspectFit"
                   class="cover-image" />
            <view v-if="bookDetail.is_rare_book"
                  class="rare-badge">
              <uni-icons type="star-filled"
                         size="16"
                         color="#FFD700" />
              <text class="rare-text">善本</text>
            </view>
          </view>

          <view class="book-basic-info">
            <text class="book-title">{{ bookDetail.title }}</text>
            <view class="book-meta">
              <view class="meta-item">
                <text class="meta-label">作者</text>
                <text class="meta-value">{{ bookDetail.author || '佚名' }}</text>
              </view>
              <view class="meta-item">
                <text class="meta-label">朝代</text>
                <text class="meta-value">{{ bookDetail.dynasty || '未知' }}</text>
              </view>
              <view class="meta-item">
                <text class="meta-label">分类</text>
                <text class="meta-value category-highlight">{{ bookDetail.sibu_category_name }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-number">{{ bookDetail.available_volumes_count }}</text>
            <text class="stat-label">册数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ bookDetail.total_pages }}</text>
            <text class="stat-label">总页数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ bookDetail.view_count }}</text>
            <text class="stat-label">浏览量</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ bookDetail.favorite_count }}</text>
            <text class="stat-label">收藏数</text>
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="detail-card">
        <view class="card-title">
          <uni-icons type="info"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">详细信息</text>
        </view>

        <view class="detail-grid">
          <view v-if="bookDetail.rare_book_number"
                class="detail-item">
            <text class="detail-label">善本书号</text>
            <text class="detail-value rare-number">{{ bookDetail.rare_book_number }}</text>
          </view>

          <view v-if="bookDetail.edition"
                class="detail-item">
            <text class="detail-label">版本项</text>
            <text class="detail-value">{{ bookDetail.edition }}</text>
          </view>

          <view v-if="bookDetail.publication"
                class="detail-item">
            <text class="detail-label">出版发行项</text>
            <text class="detail-value">{{ bookDetail.publication }}</text>
          </view>

          <view v-if="bookDetail.size_info"
                class="detail-item">
            <text class="detail-label">尺寸规格</text>
            <text class="detail-value">{{ bookDetail.size_info }}</text>
          </view>

          <view v-if="bookDetail.preservation_status"
                class="detail-item">
            <text class="detail-label">保存状态</text>
            <text class="detail-value">{{ getPreservationStatusLabel(bookDetail.preservation_status) }}</text>
          </view>

          <!-- <view class="detail-item">
            <text class="detail-label">数字化状态</text>
            <text class="detail-value"
                  :class="{ digitized: hasDigitizedVolumes }">
              {{ hasDigitizedVolumes ? '部分数字化' : '未数字化' }}
            </text>
          </view> -->
        </view>
      </view>

      <!-- 内容简介 -->
      <view v-if="bookDetail.description"
            class="description-card">
        <view class="card-title">
          <uni-icons type="chat"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">内容简介</text>
        </view>
        <text class="description-text">{{ bookDetail.description }}</text>
      </view>

      <!-- 关键词 -->
      <view v-if="bookDetail.keywords"
            class="keywords-card">
        <view class="card-title">
          <uni-icons type="tags"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">关键词</text>
        </view>
        <view class="keywords-container">
          <view v-for="keyword in keywordList"
                :key="keyword"
                class="keyword-tag">
            {{ keyword }}
          </view>
        </view>
      </view>

      <!-- 版本书目史注 -->
      <view v-if="bookDetail.bibliographic_note"
            class="note-card">
        <view class="card-title">
          <uni-icons type="paperplane"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">版本书目史注</text>
        </view>
        <text class="note-text">{{ bookDetail.bibliographic_note }}</text>
      </view>

      <!-- 卷册列表 -->
      <view class="volumes-card">
        <view class="card-title">
          <uni-icons type="list"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">卷册列表</text>
          <text class="volume-count">(共{{ bookDetail.volumes.length }}册)</text>
        </view>

        <view class="volumes-list">
          <view v-for="volume in bookDetail.volumes"
                :key="volume.id"
                class="volume-item"
                @click="viewVolume(volume)">
            <view class="volume-cover">
              <image :src="volume.cover_image || bookDetail.cover_image || '/static/images/no-image.svg'"
                     mode="aspectFit"
                     class="volume-image" />
              <view v-if="volume.is_digitized"
                    class="digital-badge">
                <uni-icons type="checkmarkempty"
                           size="12"
                           color="#fff" />
              </view>
            </view>

            <view class="volume-info">
              <text class="volume-title">{{ volume.display_title }}</text>
              <view class="volume-meta">
                <view class="meta-item">
                  <text class="meta-label">页数：</text>
                  <text class="meta-value">{{ volume.page_count || 0 }}页</text>
                </view>
                <view class="meta-item">
                  <text class="meta-label">状态：</text>
                  <text class="meta-value"
                        :class="{ digitized: volume.is_digitized }">
                    {{ volume.is_digitized ? '已数字化' : '未数字化' }}
                  </text>
                </view>
              </view>

              <view v-if="volume.content_description"
                    class="volume-desc">
                {{ volume.content_description }}
              </view>
            </view>

            <view class="volume-actions">
              <button class="action-btn read-btn"
                      @click.stop="readVolume(volume)"
                      :disabled="!volume.is_digitized">
                <uni-icons type="eye"
                           size="16"
                           color="#fff" />
                <text>阅读</text>
              </button>
              <button class="action-btn favorite-btn"
                      :class="{ favorited: isVolumeFavorited(volume.id) }"
                      @click.stop="toggleVolumeFavorite(volume)">
                <uni-icons :type="isVolumeFavorited(volume.id) ? 'heart-filled' : 'heart'"
                           size="16"
                           :color="isVolumeFavorited(volume.id) ? '#FF6B6B' : '#666'" />
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 相关推荐 -->
      <view v-if="relatedBooks.length > 0"
            class="related-card">
        <view class="card-title">
          <uni-icons type="star"
                     size="20"
                     color="#007AFF" />
          <text class="title-text">相关推荐</text>
        </view>

        <scroll-view scroll-x="true"
                     class="related-scroll">
          <view class="related-list">
            <view v-for="book in relatedBooks"
                  :key="book.id"
                  class="related-item"
                  @click="viewRelatedBook(book)">
              <image :src="book.cover_image || '/static/images/no-image.svg'"
                     mode="aspectFit"
                     class="related-cover" />
              <text class="related-title">{{ book.title }}</text>
              <text class="related-author">{{ book.author || '佚名' }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else
          class="error-state">
      <image src="/static/images/error-image.svg"
             class="error-image" />
      <text class="error-text">加载失败，请重试</text>
      <button class="retry-btn"
              @click="loadBookDetail">重新加载</button>
    </view>
  </view>
</template>

<script>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAncientBookDetail, PRESERVATION_STATUS_OPTIONS } from '../../api/ancient_books'

export default {
  name: 'AncientBookDetail',
  setup () {
    const bookDetail = ref(null)
    const relatedBooks = ref([])
    const loading = ref(false)
    const isFavorited = ref(false)
    const favoriteVolumes = ref(new Set())
    const bookId = ref(null)

    // 计算属性
    const hasDigitizedVolumes = computed(() => {
      return bookDetail.value?.volumes?.some(volume => volume.is_digitized) || false
    })

    // 关键词列表
    const keywordList = computed(() => {
      if (!bookDetail.value?.keywords) return []
      return bookDetail.value.keywords.split(',').map(keyword => keyword.trim()).filter(keyword => keyword)
    })

    // 加载古籍详情
    const loadBookDetail = async () => {
      if (!bookId.value) {
        uni.showToast({
          title: '古籍ID无效',
          icon: 'error'
        })
        return
      }

      loading.value = true
      try {
        console.log('正在加载古籍详情，ID:', bookId.value)

        const book = await getAncientBookDetail(bookId.value)

        if (book) {
          bookDetail.value = book
          console.log('加载古籍详情成功:', book)

          // 模拟收藏状态（这里可以后续接入真实的收藏API）
          isFavorited.value = false

          // 加载相关推荐（暂时使用空数组，后续可以接入推荐API）
          relatedBooks.value = []
        } else {
          console.error('古籍详情为空')
          uni.showToast({
            title: '古籍不存在',
            icon: 'error'
          })
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)
        }
      } catch (error) {
        console.error('加载古籍详情失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'error'
        })
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      uni.navigateBack()
    }

    const toggleFavorite = async () => {
      try {
        // TODO: 这里后续可以接入真实的收藏API
        isFavorited.value = !isFavorited.value
        uni.showToast({
          title: isFavorited.value ? '收藏成功' : '取消收藏成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }

    const isVolumeFavorited = (volumeId) => {
      return favoriteVolumes.value.has(volumeId)
    }

    const toggleVolumeFavorite = async (volume) => {
      try {
        // TODO: 这里后续可以接入真实的卷册收藏API
        if (isVolumeFavorited(volume.id)) {
          favoriteVolumes.value.delete(volume.id)
          uni.showToast({
            title: '取消收藏成功',
            icon: 'success'
          })
        } else {
          favoriteVolumes.value.add(volume.id)
          uni.showToast({
            title: '收藏成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }

    const viewVolume = (volume) => {
      if (!volume.is_digitized) {
        uni.showToast({
          title: '该卷册尚未数字化',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/volume-detail?id=${volume.id}`
      })
    }

    const readVolume = (volume) => {
      if (!volume.is_digitized) {
        uni.showToast({
          title: '该卷册尚未数字化',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/culture/volume-reader?id=${volume.id}`
      })
    }

    const viewRelatedBook = (book) => {
      uni.navigateTo({
        url: `/pages/culture/ancient-book-detail?id=${book.id}`
      })
    }

    // 获取保存状态标签
    const getPreservationStatusLabel = (value) => {
      const option = PRESERVATION_STATUS_OPTIONS.find(item => item.value === value)
      return option ? option.label : value
    }

    // 页面加载
    onLoad((options) => {
      console.log('古籍详情页面接收参数:', options)

      if (options && options.id) {
        bookId.value = parseInt(options.id)
        loadBookDetail()
      } else {
        console.error('缺少古籍ID参数')
        uni.showToast({
          title: '缺少必要参数',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 2000)
      }
    })

    return {
      bookDetail,
      relatedBooks,
      loading,
      isFavorited,
      favoriteVolumes,
      hasDigitizedVolumes,
      keywordList,
      loadBookDetail,
      goBack,
      toggleFavorite,
      isVolumeFavorited,
      toggleVolumeFavorite,
      viewVolume,
      readVolume,
      viewRelatedBook,
      getPreservationStatusLabel
    }
  }
}
</script>

<style scoped>
.book-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部导航 */
.header-section {
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  height: 88rpx;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.nav-right {
  width: 60rpx;
  display: flex;
  justify-content: flex-end;
}

/* 内容区域 */
.detail-content {
  padding: 20rpx;
}

/* 基本信息卡片 */
.info-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.book-header {
  display: flex;
  margin-bottom: 30rpx;
}

.book-cover {
  position: relative;
  width: 160rpx;
  height: 200rpx;
  margin-right: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.rare-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(255, 215, 0, 0.9);
  border-radius: 16rpx;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.rare-text {
  font-size: 22rpx;
  color: #8b4513;
  font-weight: bold;
}

.book-basic-info {
  flex: 1;
}

.book-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.book-meta {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  font-size: 26rpx;
  color: #666;
  min-width: 80rpx;
  margin-right: 16rpx;
}

.meta-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.category-highlight {
  color: #1976d2;
  font-weight: 500;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 通用卡片样式 */
.detail-card,
.description-card,
.note-card,
.volumes-card,
.related-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.volume-count {
  font-size: 24rpx;
  color: #666;
}

/* 详细信息 */
.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
  margin-right: 20rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.rare-number {
  color: #f57c00;
  font-weight: bold;
  font-family: monospace;
}

.detail-value.digitized {
  color: #4caf50;
}

/* 简介和史注 */
.description-text,
.note-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

.note-text {
  background: #fff9e6;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #ffd700;
}

/* 关键词样式 */
.keywords-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.keywords-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.keyword-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.keyword-tag:active {
  transform: scale(0.95);
}

/* 卷册列表 */
.volumes-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.volume-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.volume-item:active {
  transform: scale(0.98);
  background: #f0f1f2;
}

.volume-cover {
  position: relative;
  width: 100rpx;
  height: 120rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #fff;
}

.volume-image {
  width: 100%;
  height: 100%;
}

.digital-badge {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 24rpx;
  height: 24rpx;
  background: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-info {
  flex: 1;
  margin-right: 20rpx;
}

.volume-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.volume-meta {
  display: flex;
  gap: 24rpx;
  margin-bottom: 8rpx;
}

.volume-meta .meta-item {
  display: flex;
  align-items: center;
}

.volume-meta .meta-label {
  font-size: 22rpx;
  color: #666;
  margin-right: 6rpx;
}

.volume-meta .meta-value {
  font-size: 22rpx;
  color: #333;
}

.volume-meta .meta-value.digitized {
  color: #4caf50;
}

.volume-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  margin-top: 8rpx;
}

.volume-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  min-width: 100rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.3s ease;
}

.read-btn {
  background: #007aff;
  color: #fff;
}

.read-btn:disabled {
  background: #ccc;
  color: #999;
}

.favorite-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e0e0e0;
  min-height: 60rpx;
  min-width: 60rpx;
  padding: 0;
}

.favorite-btn.favorited {
  background: #ffebee;
  color: #ff6b6b;
  border-color: #ff6b6b;
}

/* 相关推荐 */
.related-scroll {
  white-space: nowrap;
}

.related-list {
  display: flex;
  gap: 20rpx;
  padding: 10rpx 0;
}

.related-item {
  width: 160rpx;
  text-align: center;
}

.related-cover {
  width: 120rpx;
  height: 150rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  margin: 0 auto 12rpx;
  display: block;
}

.related-title {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-author {
  font-size: 20rpx;
  color: #666;
  display: block;
}

/* 加载和错误状态 */
.loading-container {
  padding: 100rpx 40rpx;
  text-align: center;
}

.error-state {
  padding: 100rpx 40rpx;
  text-align: center;
}

.error-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
  display: block;
}

.retry-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
</style> 