from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from datetime import datetime
import json
from typing import Optional

from app.database.db import get_db
from app.schemas.user_schemas import (
    WechatLoginRequest, PhoneLoginRequest, LoginResponse, 
    TokenRefreshRequest, TokenRefreshResponse, UserResponse,
    UserUpdateRequest
)
from app.models.users import User, LoginLog, UserR<PERSON>
from app.core.wechat import wechat_service
from app.core.auth import token_service
from app.core.dependencies import get_current_active_user

router = APIRouter()


@router.post("/wechat/login", response_model=LoginResponse, summary="微信小程序登录")
async def wechat_login(
    request: WechatLoginRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """
    微信小程序登录接口
    
    - **code**: 微信小程序授权码
    - **encrypted_data**: 可选，加密的用户信息
    - **iv**: 可选，初始向量
    """
    try:
        # 获取openid和session_key
        wechat_result = await wechat_service.code2session(request.code)
        openid = wechat_result["openid"]
        session_key = wechat_result["session_key"]
        unionid = wechat_result.get("unionid")
        
        # 检查用户是否已存在
        user = db.query(User).filter(User.openid == openid).first()
        
        user_info = {}
        if request.encrypted_data and request.iv:
            try:
                # 解密用户信息
                user_info = wechat_service.decrypt_data(
                    request.encrypted_data, 
                    session_key, 
                    request.iv
                )
            except Exception as e:
                # 解密失败不影响登录，只是无法获取用户信息
                pass
        
        if not user:
            # 创建新用户
            user = User(
                openid=openid,
                unionid=unionid,
                session_key=session_key,
                nickname=user_info.get("nickName"),
                avatar_url=user_info.get("avatarUrl"),
                gender=user_info.get("gender", 0),
                role=UserRole.GUEST
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # 更新现有用户信息
            user.session_key = session_key
            if unionid:
                user.unionid = unionid
            if user_info:
                user.nickname = user_info.get("nickName", user.nickname)
                user.avatar_url = user_info.get("avatarUrl", user.avatar_url)
                user.gender = user_info.get("gender", user.gender)
            user.last_login_at = datetime.utcnow()
            db.commit()
        
        # 记录登录日志
        login_log = LoginLog(
            user_id=user.id,
            openid=openid,
            login_type="wechat",
            success=True,
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent")
        )
        db.add(login_log)
        db.commit()
        
        # 创建用户会话
        device_info = json.dumps({
            "user_agent": http_request.headers.get("user-agent"),
            "ip": http_request.client.host if http_request.client else None
        })
        
        session_data = await token_service.create_user_session(
            db, user.id, device_info
        )
        
        # 构建响应
        from app.api.endpoints.users import add_region_names_to_user
        user_response = add_region_names_to_user(user, db)
        
        return LoginResponse(
            **session_data,
            user=user_response
        )
        
    except ValueError as e:
        # 记录失败日志
        login_log = LoginLog(
            openid=request.code,  # 临时使用code记录
            login_type="wechat",
            success=False,
            failure_reason=str(e),
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent")
        )
        db.add(login_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/phone/bind", response_model=LoginResponse, summary="绑定手机号登录")
async def bind_phone(
    request: PhoneLoginRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    绑定手机号并登录
    
    - **code**: 微信小程序获取手机号的code
    - **encrypted_data**: 加密的手机号信息
    - **iv**: 初始向量
    """
    try:
        # 方式1：通过新接口获取手机号（推荐）
        try:
            phone_info = await wechat_service.get_phone_number(request.code)
            phone = phone_info.get("phoneNumber")
        except Exception:
            # 方式2：通过解密获取手机号（兼容旧版本）
            if not current_user.session_key:
                raise ValueError("无法获取手机号，请重新登录")
            
            phone_data = wechat_service.decrypt_data(
                request.encrypted_data,
                current_user.session_key,
                request.iv
            )
            phone = phone_data.get("phoneNumber")
        
        if not phone:
            raise ValueError("获取手机号失败")
        
        # 检查手机号是否已被其他用户绑定
        existing_user = db.query(User).filter(
            User.phone == phone,
            User.id != current_user.id
        ).first()
        
        if existing_user:
            raise ValueError("该手机号已被其他用户绑定")
        
        # 更新用户手机号
        current_user.phone = phone
        current_user.last_login_at = datetime.utcnow()
        db.commit()
        
        # 记录登录日志
        login_log = LoginLog(
            user_id=current_user.id,
            phone=phone,
            login_type="phone",
            success=True,
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent")
        )
        db.add(login_log)
        db.commit()
        
        # 创建新的用户会话
        device_info = json.dumps({
            "user_agent": http_request.headers.get("user-agent"),
            "ip": http_request.client.host if http_request.client else None
        })
        
        session_data = await token_service.create_user_session(
            db, current_user.id, device_info
        )
        
        # 构建响应
        from app.api.endpoints.users import add_region_names_to_user
        user_response = add_region_names_to_user(current_user, db)
        
        return LoginResponse(
            **session_data,
            user=user_response
        )
        
    except ValueError as e:
        # 记录失败日志
        login_log = LoginLog(
            user_id=current_user.id,
            login_type="phone",
            success=False,
            failure_reason=str(e),
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent")
        )
        db.add(login_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refresh", response_model=TokenRefreshResponse, summary="刷新访问令牌")
async def refresh_token(
    request: TokenRefreshRequest,
    db: Session = Depends(get_db)
):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    session_data = token_service.refresh_user_session(db, request.refresh_token)
    
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )
    
    return TokenRefreshResponse(**session_data)


@router.post("/logout", summary="用户登出")
async def logout(
    request: TokenRefreshRequest,
    db: Session = Depends(get_db)
):
    """
    用户登出，撤销会话
    
    - **refresh_token**: 刷新令牌
    """
    success = token_service.revoke_user_session(db, request.refresh_token)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="登出失败"
        )
    
    return {"message": "登出成功"}


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    from app.api.endpoints.users import add_region_names_to_user
    return add_region_names_to_user(current_user, db)


@router.put("/me", response_model=UserResponse, summary="更新当前用户信息")
async def update_current_user(
    request: UserUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新当前用户信息
    
    - **nickname**: 昵称
    - **avatar_url**: 头像URL
    - **gender**: 性别（0-未知,1-男,2-女）
    - **phone**: 手机号码
    """
    # 如果要更新手机号，检查是否已被其他用户使用
    if request.phone is not None:
        existing_user = db.query(User).filter(
            User.phone == request.phone,
            User.id != current_user.id
        ).first()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该手机号已被其他用户使用"
            )
    
    # 更新用户信息
    if request.nickname is not None:
        current_user.nickname = request.nickname
    if request.avatar_url is not None:
        current_user.avatar_url = request.avatar_url
    if request.gender is not None:
        current_user.gender = request.gender
    if request.phone is not None:
        current_user.phone = request.phone
    
    db.commit()
    
    from app.api.endpoints.users import add_region_names_to_user
    return add_region_names_to_user(current_user, db) 