from fastapi import APIRouter
from app.api.endpoints import auth, users, heritage, upload, admin_divisions, ancient_books, ancient_book_volumes

# 创建API路由实例
api_router = APIRouter()

# 注册API路由
api_router.include_router(admin_divisions.router, prefix="/api/v1")
api_router.include_router(auth.router, prefix="/api/v1/auth", tags=["用户认证"])
api_router.include_router(users.router, prefix="/api/v1/users", tags=["用户管理"])
api_router.include_router(heritage.router, prefix="/api/v1/heritage", tags=["文化遗产管理"])
api_router.include_router(ancient_books.router, prefix="/api/v1/ancient-books", tags=["古籍典藏"])
api_router.include_router(ancient_book_volumes.router, prefix="/api/v1/ancient-book-volumes", tags=["古籍卷册管理"])
api_router.include_router(upload.router, prefix="/api/v1/upload", tags=["文件上传"])