/**
 * uni-app多平台开发脚本
 * 通过读取manifest.json配置，运行所有配置的平台
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// 读取manifest.json配置
const manifestPath = path.resolve(__dirname, '../src/manifest.json');
const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

// 支持的平台列表
const platforms = [];

// 检查各平台的配置
if (manifest['mp-weixin']) {
  platforms.push('mp-weixin');
}
if (manifest['mp-alipay']) {
  platforms.push('mp-alipay');
}
if (manifest['mp-baidu']) {
  platforms.push('mp-baidu');
}
if (manifest['mp-toutiao']) {
  platforms.push('mp-toutiao');
}
if (manifest['mp-qq']) {
  platforms.push('mp-qq');
}
if (manifest['h5']) {
  platforms.push('h5');
}
if (manifest['app-plus']) {
  platforms.push('app-plus');
}
if (manifest['quickapp']) {
  platforms.push('quickapp-webview');
}

console.log('准备开发的平台:', platforms);

// 为每个平台启动开发服务
platforms.forEach(platform => {
  const command = `npm run dev:${platform}`;
  console.log(`启动 ${platform} 平台开发服务: ${command}`);
  
  const process = exec(command);
  
  process.stdout.on('data', (data) => {
    console.log(`[${platform}] ${data}`);
  });
  
  process.stderr.on('data', (data) => {
    console.error(`[${platform}] 错误: ${data}`);
  });
  
  process.on('close', (code) => {
    console.log(`[${platform}] 开发服务已退出，退出码: ${code}`);
  });
});

console.log('所有平台开发服务已启动'); 