<template>
  <view class="container">
    <view class="header">
      <text class="title">图片上传测试</text>
    </view>

    <view class="test-section">
      <text class="section-title">单图上传测试</text>
      <ImageUpload v-model="singleImage"
                   :multiple="false"
                   upload-type="heritage"
                   @change="onSingleImageChange" />
      <text class="result-text">结果: {{ singleImageResult }}</text>
    </view>

    <view class="test-section">
      <text class="section-title">多图上传测试</text>
      <ImageUpload v-model="multipleImages"
                   :multiple="true"
                   :max-count="6"
                   upload-type="memory"
                   @change="onMultipleImagesChange" />
      <text class="result-text">结果: {{ multipleImagesResult }}</text>
    </view>

    <view class="test-section">
      <text class="section-title">快速上传测试</text>
      <view class="button-group">
        <button class="test-btn"
                @click="testQuickUpload"
                :disabled="quickUploading">
          {{ quickUploading ? '上传中...' : '快速上传单图' }}
        </button>

        <button class="test-btn"
                @click="testBatchUpload"
                :disabled="batchUploading">
          {{ batchUploading ? '上传中...' : '批量上传' }}
        </button>
      </view>
      <text class="result-text">快速上传结果: {{ quickUploadResult }}</text>
    </view>

    <view class="test-section">
      <text class="section-title">功能测试</text>
      <view class="button-group">
        <button class="test-btn"
                @click="testImagePreview">预览图片</button>
        <button class="test-btn"
                @click="testImageDelete">删除第一张图片</button>
        <button class="test-btn"
                @click="clearAllImages">清空所有图片</button>
      </view>
    </view>

    <view class="debug-section">
      <text class="section-title">调试信息</text>
      <view class="debug-info">
        <text class="debug-text">单图: {{ JSON.stringify(singleImage) }}</text>
        <text class="debug-text">多图: {{ JSON.stringify(multipleImages) }}</text>
        <text class="debug-text">Token: {{ token ? '已设置' : '未设置' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import ImageUpload from '@/components/common/ImageUpload.vue'
import {
  selectAndUploadImage,
  selectAndUploadImages,
  deleteImage,
  previewImages
} from '@/utils/upload'

export default {
  name: 'ImageUploadTest',
  components: {
    ImageUpload
  },
  data () {
    return {
      singleImage: [],
      multipleImages: [],
      singleImageResult: '未上传',
      multipleImagesResult: '未上传',
      quickUploadResult: '未测试',
      quickUploading: false,
      batchUploading: false,
      token: ''
    }
  },
  onLoad () {
    // 获取token
    this.token = uni.getStorageSync('token')

    // 如果没有token，设置一个测试token
    if (!this.token) {
      const testToken = 'test-token-' + Date.now()
      uni.setStorageSync('token', testToken)
      this.token = testToken
      console.log('设置测试token:', testToken)
    }
  },
  methods: {
    onSingleImageChange (images) {
      console.log('单图变化:', images)
      this.singleImageResult = images.length > 0 ? '上传成功' : '已清空'
    },

    onMultipleImagesChange (images) {
      console.log('多图变化:', images)
      this.multipleImagesResult = `共${images.length}张图片`
    },

    async testQuickUpload () {
      this.quickUploading = true
      this.quickUploadResult = '正在上传...'

      try {
        const result = await selectAndUploadImage('timeline', (progress) => {
          this.quickUploadResult = `上传进度: ${progress}%`
        })

        if (result.success) {
          this.quickUploadResult = `上传成功: ${result.url}`
        } else {
          this.quickUploadResult = `上传失败: ${result.message}`
        }
      } catch (error) {
        this.quickUploadResult = `上传异常: ${error.message}`
      } finally {
        this.quickUploading = false
      }
    },

    async testBatchUpload () {
      this.batchUploading = true
      this.quickUploadResult = '正在批量上传...'

      try {
        const results = await selectAndUploadImages(3, 'place', (progress) => {
          this.quickUploadResult = `批量上传进度: ${progress}%`
        })

        const successCount = results.filter(r => r.success).length
        const totalCount = results.length

        this.quickUploadResult = `批量上传完成: ${successCount}/${totalCount} 成功`
      } catch (error) {
        this.quickUploadResult = `批量上传异常: ${error.message}`
      } finally {
        this.batchUploading = false
      }
    },

    testImagePreview () {
      const allImages = [...this.singleImage, ...this.multipleImages]
      if (allImages.length > 0) {
        previewImages(allImages, 0)
      } else {
        uni.showToast({
          title: '没有图片可预览',
          icon: 'none'
        })
      }
    },

    async testImageDelete () {
      if (this.multipleImages.length > 0) {
        const imageUrl = this.multipleImages[0]
        const success = await deleteImage(imageUrl)

        if (success) {
          this.multipleImages.splice(0, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      } else {
        uni.showToast({
          title: '没有图片可删除',
          icon: 'none'
        })
      }
    },

    clearAllImages () {
      this.singleImage = []
      this.multipleImages = []
      this.singleImageResult = '已清空'
      this.multipleImagesResult = '已清空'
      this.quickUploadResult = '已清空'

      uni.showToast({
        title: '已清空所有图片',
        icon: 'success'
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.result-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
  display: block;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn:disabled {
  background-color: #ccc;
  color: #999;
}

.test-btn:active:not(:disabled) {
  background-color: #0056b3;
}

.debug-section {
  background-color: #f8f9fa;
  padding: 40rpx;
  border-radius: 16rpx;
  border: 2rpx dashed #ddd;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.debug-text {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
}
</style> 