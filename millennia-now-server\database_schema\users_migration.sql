-- 用户表结构迁移 - 从微信登录改为账号密码登录
-- 执行时间：请在维护窗口期间执行

-- 1. 备份现有用户表
CREATE TABLE users_backup AS SELECT * FROM users;

-- 2. 创建新的用户表结构
DROP TABLE IF EXISTS users_new;
CREATE TABLE users_new (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号（登录账号）',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    avatar_url VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知,1-男,2-女',
    role ENUM('guest', 'admin', 'super_admin') NOT NULL DEFAULT 'guest' COMMENT '用户角色',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    province_id SMALLINT DEFAULT NULL COMMENT '管理的省份ID，仅管理员有效',
    city_id SMALLINT DEFAULT NULL COMMENT '管理的城市ID，仅管理员有效',
    district_id SMALLINT DEFAULT NULL COMMENT '管理的区县ID，仅管理员有效',
    module_permissions JSON DEFAULT NULL COMMENT '模块管理权限JSON',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 迁移现有用户数据（如果有手机号的用户）
-- 注意：没有手机号的用户将无法迁移，需要重新注册
INSERT INTO users_new (
    id, phone, password_hash, nickname, avatar_url, gender, 
    role, is_active, province_id, city_id, district_id, 
    module_permissions, created_at, updated_at, last_login_at
)
SELECT 
    id,
    phone,
    '$2b$12$defaultpasswordhash' as password_hash, -- 默认密码哈希，用户需要重置密码
    nickname,
    avatar_url,
    gender,
    role,
    is_active,
    province_id,
    city_id,
    district_id,
    module_permissions,
    created_at,
    updated_at,
    last_login_at
FROM users 
WHERE phone IS NOT NULL AND phone != '';

-- 4. 更新登录日志表结构
ALTER TABLE login_logs 
DROP COLUMN openid,
DROP COLUMN unionid,
MODIFY COLUMN login_type VARCHAR(20) NOT NULL DEFAULT 'password' COMMENT '登录方式：password';

-- 5. 替换原用户表
DROP TABLE users;
RENAME TABLE users_new TO users;

-- 6. 创建用户会话表（如果不存在）
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(255) NOT NULL UNIQUE COMMENT '会话标识',
    access_token VARCHAR(500) NOT NULL COMMENT '访问令牌',
    refresh_token VARCHAR(500) NOT NULL UNIQUE COMMENT '刷新令牌',
    device_info JSON DEFAULT NULL COMMENT '设备信息',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_access_token (access_token(100)),
    INDEX idx_refresh_token (refresh_token(100)),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 7. 创建默认管理员账号（可选）
-- 密码：admin123（请在生产环境中修改）
INSERT INTO users (phone, password_hash, nickname, role, is_active) 
VALUES (
    '13800000000', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- admin123
    '系统管理员', 
    'super_admin', 
    TRUE
) ON DUPLICATE KEY UPDATE nickname = '系统管理员';

-- 8. 验证迁移结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
    COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as super_admin_count,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users
FROM users;

-- 迁移完成提示
SELECT '用户表迁移完成！请注意：' as message;
SELECT '1. 原有用户数据已备份到 users_backup 表' as note1;
SELECT '2. 只有有手机号的用户被迁移到新表' as note2;
SELECT '3. 所有用户的密码已重置，需要用户重新设置密码' as note3;
SELECT '4. 默认管理员账号：13800000000，密码：admin123' as note4;
SELECT '5. 请在生产环境中修改默认管理员密码' as note5;
