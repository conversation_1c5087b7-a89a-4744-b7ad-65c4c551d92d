import { request } from '@/utils/request'

// 古籍卷册相关接口

/**
 * 获取古籍详情
 */
export function getAncientBookDetail(bookId: number) {
  return request({
    url: `/ancient-books/${bookId}`,
    method: 'GET'
  })
}

/**
 * 获取卷册详情
 */
export function getVolumeDetail(volumeId: number) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}`,
    method: 'GET'
  })
}

/**
 * 创建卷册
 */
export function createVolume(data: any) {
  return request({
    url: '/ancient-book-volumes/volumes/',
    method: 'POST',
    data
  })
}

/**
 * 更新卷册
 */
export function updateVolume(volumeId: number, data: any) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除卷册
 */
export function deleteVolume(volumeId: number) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}`,
    method: 'DELETE'
  })
}

/**
 * 获取古籍的卷册列表
 */
export function getBookVolumes(bookId: number, params?: any) {
  return request({
    url: `/ancient-book-volumes/volumes/`,
    method: 'GET',
    data: {
      book_id: bookId,
      ...params
    }
  })
}

/**
 * 上传古籍图片
 */
export function uploadAncientBookImage(filePath: string) {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('access_token')
    uni.uploadFile({
      url: 'http://192.168.101.7:8000/api/v1/upload/ancient-books/image',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.success) {
            resolve(data.data.url)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 创建页面
 */
export function createPage(data: any) {
  return request({
    url: '/ancient-book-volumes/pages/',
    method: 'POST',
    data
  })
}

/**
 * 更新页面
 */
export function updatePage(pageId: number, data: any) {
  return request({
    url: `/ancient-book-volumes/pages/${pageId}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除页面
 */
export function deletePage(pageId: number) {
  return request({
    url: `/ancient-book-volumes/pages/${pageId}`,
    method: 'DELETE'
  })
}

/**
 * 获取页面列表
 */
export function getPages(params?: any) {
  return request({
    url: '/ancient-book-volumes/pages/',
    method: 'GET',
    data: params
  })
}

/**
 * 获取卷册的页面列表
 */
export function getVolumePages(volumeId: number, params?: any) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}/pages`,
    method: 'GET',
    data: params
  })
}

/**
 * 批量创建页面
 */
export function batchCreatePages(pages: any[]) {
  return request({
    url: '/ancient-book-volumes/pages/batch',
    method: 'POST',
    data: {
      pages: pages
    }
  })
}

/**
 * 批量更新页面
 */
export function batchUpdatePages(pages: any[]) {
  return request({
    url: '/ancient-book-volumes/pages/batch',
    method: 'PUT',
    data: {
      pages: pages
    }
  })
}

/**
 * 批量删除页面
 */
export function batchDeletePages(pageIds: number[]) {
  return request({
    url: '/ancient-book-volumes/pages/batch',
    method: 'DELETE',
    data: {
      page_ids: pageIds
    }
  })
}

/**
 * 执行OCR识别 (需要页面ID)
 */
export function performOCR(pageId: number, ocrProvider: string = 'default') {
  return request({
    url: `/ancient-book-volumes/pages/${pageId}/ocr`,
    method: 'POST',
    data: {
      ocr_provider: ocrProvider
    }
  })
}

/**
 * 批量OCR识别
 */
export function batchOCR(pageIds: number[]) {
  return request({
    url: '/ancient-book-volumes/ocr/batch',
    method: 'POST',
    data: {
      page_ids: pageIds
    }
  })
}

/**
 * 获取OCR任务状态
 */
export function getOCRTaskStatus(taskId: number) {
  return request({
    url: `/ancient-book-volumes/ocr/tasks/${taskId}`,
    method: 'GET'
  })
}

/**
 * 获取卷册统计信息
 */
export function getVolumeStats(volumeId: number) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}/stats`,
    method: 'GET'
  })
}

/**
 * 搜索卷册
 */
export function searchVolumes(params: any) {
  return request({
    url: '/ancient-book-volumes/volumes/search',
    method: 'GET',
    data: params
  })
}

/**
 * 保存卷册和页面（一次性保存）
 */
export function saveVolumeWithPages(volumeId: number | null, volumeData: any, pageChanges: any) {
  const url = volumeId 
    ? `/ancient-book-volumes/volumes/${volumeId}/save-with-pages`
    : '/ancient-book-volumes/volumes/create-with-pages'
  
  return request({
    url: url,
    method: 'POST',
    data: {
      volume: volumeData,
      page_changes: pageChanges
    }
  })
}

/**
 * 重新排序页面
 */
export function reorderPages(volumeId: number, pageOrders: { page_id: number, page_number: number }[]) {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}/reorder-pages`,
    method: 'POST',
    data: {
      page_orders: pageOrders
    }
  })
}

/**
 * 复制页面
 */
export function copyPage(pageId: number, targetVolumeId?: number) {
  return request({
    url: `/ancient-book-volumes/pages/${pageId}/copy`,
    method: 'POST',
    data: {
      target_volume_id: targetVolumeId
    }
  })
}

/**
 * 导出卷册数据
 */
export function exportVolume(volumeId: number, format: string = 'json') {
  return request({
    url: `/ancient-book-volumes/volumes/${volumeId}/export`,
    method: 'GET',
    data: {
      format: format
    }
  })
}

/**
 * 导入卷册数据
 */
export function importVolume(bookId: number, data: any) {
  return request({
    url: `/ancient-book-volumes/volumes/import`,
    method: 'POST',
    data: {
      book_id: bookId,
      import_data: data
    }
  })
} 