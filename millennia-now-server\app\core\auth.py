from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import secrets
import os
from jose import jwt, JW<PERSON>rror
from passlib.context import CryptContext
from dotenv import load_dotenv
from sqlalchemy.orm import Session
from app.models.users import User, UserSession, UserRole
from app.database.db import get_db

load_dotenv()

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", secrets.token_urlsafe(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class TokenService:
    """令牌服务"""
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            if payload.get("type") != token_type:
                return None
            return payload
        except JWTError:
            return None
    
    @staticmethod
    async def create_user_session(db: Session, user_id: int, device_info: Optional[str] = None) -> Dict[str, Any]:
        """创建用户会话"""
        # 生成令牌
        token_data = {"sub": str(user_id), "user_id": user_id}
        access_token = TokenService.create_access_token(token_data)
        refresh_token = TokenService.create_refresh_token(token_data)
        
        # 保存会话到数据库
        session = UserSession(
            user_id=user_id,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS),
            device_info=device_info
        )
        
        db.add(session)
        db.commit()
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
    
    @staticmethod
    def revoke_user_session(db: Session, refresh_token: str) -> bool:
        """撤销用户会话"""
        session = db.query(UserSession).filter(
            UserSession.refresh_token == refresh_token,
            UserSession.is_active == True
        ).first()
        
        if session:
            session.is_active = False
            db.commit()
            return True
        return False
    
    @staticmethod
    def refresh_user_session(db: Session, refresh_token: str) -> Optional[Dict[str, Any]]:
        """刷新用户会话"""
        # 验证刷新令牌
        payload = TokenService.verify_token(refresh_token, "refresh")
        if not payload:
            return None
        
        # 检查会话是否存在且有效
        session = db.query(UserSession).filter(
            UserSession.refresh_token == refresh_token,
            UserSession.is_active == True,
            UserSession.expires_at > datetime.utcnow()
        ).first()
        
        if not session:
            return None
        
        # 生成新的令牌
        user_id = session.user_id
        token_data = {"sub": str(user_id), "user_id": user_id}
        new_access_token = TokenService.create_access_token(token_data)
        new_refresh_token = TokenService.create_refresh_token(token_data)
        
        # 更新会话
        session.access_token = new_access_token
        session.refresh_token = new_refresh_token
        session.expires_at = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        db.commit()
        
        return {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }


class PermissionService:
    """权限服务"""
    
    @staticmethod
    def check_permission(user: User, required_role: UserRole, 
                        province_id: Optional[int] = None,
                        city_id: Optional[int] = None,
                        district_id: Optional[int] = None) -> bool:
        """检查用户权限"""
        if not user.is_active:
            return False
        
        # 超级管理员拥有所有权限
        if user.role == UserRole.SUPER_ADMIN:
            return True
        
        # 检查角色等级
        role_levels = {
            UserRole.GUEST: 0,
            UserRole.DISTRICT_ADMIN: 1,
            UserRole.CITY_ADMIN: 2,
            UserRole.PROVINCE_ADMIN: 3,
            UserRole.SUPER_ADMIN: 4
        }
        
        user_level = role_levels.get(user.role, 0)
        required_level = role_levels.get(required_role, 0)
        
        if user_level < required_level:
            return False
        
        # 检查管理区域权限
        if required_role != UserRole.GUEST:
            if province_id and user.province_id and user.province_id != province_id:
                return False
            if city_id and user.city_id and user.city_id != city_id:
                return False
            if district_id and user.district_id and user.district_id != district_id:
                return False
        
        return True
    
    @staticmethod
    def can_manage_user(manager: User, target_user: User) -> bool:
        """检查是否可以管理目标用户"""
        if not manager.is_active:
            return False
        
        # 超级管理员可以管理所有用户
        if manager.role == UserRole.SUPER_ADMIN:
            return True
        
        # 管理员权限等级
        role_levels = {
            UserRole.GUEST: 0,
            UserRole.DISTRICT_ADMIN: 1,
            UserRole.CITY_ADMIN: 2,
            UserRole.PROVINCE_ADMIN: 3,
            UserRole.SUPER_ADMIN: 4
        }
        
        manager_level = role_levels.get(manager.role, 0)
        target_level = role_levels.get(target_user.role, 0)
        
        # 不能管理同级或更高级的用户（超级管理员除外）
        if manager_level <= target_level:
            return False
        
        # 检查管理权限
        if target_user.role == UserRole.GUEST:
            # 所有管理员都可以管理游客用户（游客的地理信息不影响管理权限）
            return True
        else:
            # 对于管理员用户，需要检查管辖区域
            if manager.role == UserRole.PROVINCE_ADMIN:
                # 省级管理员可以管理同省内的下级管理员
                return target_user.province_id == manager.province_id
            elif manager.role == UserRole.CITY_ADMIN:
                # 市级管理员可以管理同市内的区县管理员
                return (target_user.province_id == manager.province_id and 
                       target_user.city_id == manager.city_id)
            elif manager.role == UserRole.DISTRICT_ADMIN:
                # 区县管理员不能管理其他管理员
                return False
        
        return False


# FastAPI依赖项
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# 安全认证
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证访问令牌
        payload = token_service.verify_token(credentials.credentials, "access")
        if payload is None:
            raise credentials_exception
        
        user_id: int = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
            
    except Exception:
        raise credentials_exception
    
    # 查询用户
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    if user is None:
        raise credentials_exception
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def require_role(required_role: UserRole):
    """角色权限装饰器"""
    def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if not permission_service.check_permission(current_user, required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return role_checker

# 全局实例
token_service = TokenService()
permission_service = PermissionService() 