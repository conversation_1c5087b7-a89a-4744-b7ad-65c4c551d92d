from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import List, Optional
import logging
from app.core.minio_client import minio_client
from app.core.auth import get_current_user
from app.models.users import User

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/image")
async def upload_single_image(
    file: UploadFile = File(...),
    prefix: str = "images",
    current_user: User = Depends(get_current_user)
):
    """
    上传单个图片
    
    Args:
        file: 上传的图片文件
        prefix: 存储路径前缀
        current_user: 当前用户
    
    Returns:
        上传结果和图片URL
    """
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只能上传图片文件")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 上传到Minio
        success, result = await minio_client.upload_image(
            file_content=file_content,
            original_filename=file.filename or "image.jpg",
            prefix=prefix,
            optimize=True
        )
        
        if success:
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": "图片上传成功",
                    "data": {
                        "url": result,
                        "filename": file.filename
                    }
                }
            )
        else:
            raise HTTPException(status_code=400, detail=result)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Image upload error: {e}")
        raise HTTPException(status_code=500, detail="图片上传失败")

@router.post("/images")
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    prefix: str = "images",
    current_user: User = Depends(get_current_user)
):
    """
    上传多个图片
    
    Args:
        files: 上传的图片文件列表
        prefix: 存储路径前缀
        current_user: 当前用户
    
    Returns:
        上传结果和图片URL列表
    """
    try:
        # 限制文件数量
        if len(files) > 10:
            raise HTTPException(status_code=400, detail="一次最多只能上传10个文件")
        
        results = []
        failed_files = []
        
        for file in files:
            try:
                # 检查文件类型
                if not file.content_type or not file.content_type.startswith('image/'):
                    failed_files.append({
                        "filename": file.filename,
                        "error": "只能上传图片文件"
                    })
                    continue
                
                # 读取文件内容
                file_content = await file.read()
                
                # 上传到Minio
                success, result = await minio_client.upload_image(
                    file_content=file_content,
                    original_filename=file.filename or "image.jpg",
                    prefix=prefix,
                    optimize=True
                )
                
                if success:
                    results.append({
                        "filename": file.filename,
                        "url": result,
                        "success": True
                    })
                else:
                    failed_files.append({
                        "filename": file.filename,
                        "error": result
                    })
                    
            except Exception as e:
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"成功上传 {len(results)} 个文件",
                "data": {
                    "uploaded": results,
                    "failed": failed_files,
                    "total": len(files),
                    "success_count": len(results),
                    "failed_count": len(failed_files)
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Multiple images upload error: {e}")
        raise HTTPException(status_code=500, detail="图片上传失败")

@router.delete("/image")
async def delete_image(
    url: str,
    current_user: User = Depends(get_current_user)
):
    """
    删除图片
    
    Args:
        url: 图片URL
        current_user: 当前用户
    
    Returns:
        删除结果
    """
    try:
        success = await minio_client.delete_image(url)
        
        if success:
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": "图片删除成功"
                }
            )
        else:
            raise HTTPException(status_code=400, detail="图片删除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Image delete error: {e}")
        raise HTTPException(status_code=500, detail="图片删除失败")

@router.post("/heritage/image")
async def upload_heritage_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传文化遗产相关图片"""
    return await upload_single_image(file, "heritage", current_user)

@router.post("/heritage/images")
async def upload_heritage_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传文化遗产相关多个图片"""
    return await upload_multiple_images(files, "heritage", current_user)

@router.post("/memory/image")
async def upload_memory_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传城市记忆相关图片"""
    return await upload_single_image(file, "memory", current_user)

@router.post("/memory/images")
async def upload_memory_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传城市记忆相关多个图片"""
    return await upload_multiple_images(files, "memory", current_user)

@router.post("/timeline/image")
async def upload_timeline_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传时间轴相关图片"""
    return await upload_single_image(file, "timeline", current_user)

@router.post("/timeline/images")
async def upload_timeline_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传时间轴相关多个图片"""
    return await upload_multiple_images(files, "timeline", current_user)

@router.post("/place/image")
async def upload_place_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传地点相关图片"""
    return await upload_single_image(file, "place", current_user)

@router.post("/place/images")
async def upload_place_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传地点相关多个图片"""
    return await upload_multiple_images(files, "place", current_user)

@router.post("/ancient-books/image")
async def upload_ancient_book_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传古籍相关图片"""
    return await upload_single_image(file, "ancient-books", current_user)

@router.post("/ancient-books/images")
async def upload_ancient_book_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传古籍相关多个图片"""
    return await upload_multiple_images(files, "ancient-books", current_user)

@router.post("/archive/image")
async def upload_archive_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传档案相关图片"""
    return await upload_single_image(file, "archive", current_user)

@router.post("/archive/images")
async def upload_archive_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传档案相关多个图片"""
    return await upload_multiple_images(files, "archive", current_user)

@router.get("/status")
async def get_upload_status(current_user: User = Depends(get_current_user)):
    """
    获取上传服务状态
    """
    try:
        # 获取Minio状态
        status = minio_client.get_status()
        
        # 测试连接
        connection_ok, connection_msg = minio_client.test_connection()
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "上传服务状态",
                "data": {
                    "minio_status": status,
                    "connection_test": {
                        "success": connection_ok,
                        "message": connection_msg
                    }
                }
            }
        )
    except Exception as e:
        logger.error(f"获取上传状态失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取状态失败: {str(e)}"
            }
        )