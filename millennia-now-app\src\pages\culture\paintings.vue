<template>
  <view class="paintings-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <image class="header-bg"
             src="/static/images/paintings-bg.jpg"
             mode="aspectFill">
      </image>
      <view class="header-overlay">
        <text class="header-title">书画珍品</text>
        <text class="header-subtitle">名人字画艺术传承</text>
      </view>
    </view>

    <!-- 筛选导航 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll"
                   scroll-x="true"
                   show-scrollbar="false">
        <view class="filter-list">
          <text v-for="filter in filterOptions"
                :key="filter.value"
                :class="['filter-item', { active: currentFilter === filter.value }]"
                @click="switchFilter(filter.value)">
            {{ filter.label }}
          </text>
        </view>
      </scroll-view>

      <view class="view-toggle">
        <view :class="['toggle-btn', { active: viewMode === 'grid' }]"
              @click="setViewMode('grid')">
          <image src="/static/icons/grid.svg"
                 class="toggle-icon"></image>
        </view>
        <view :class="['toggle-btn', { active: viewMode === 'list' }]"
              @click="setViewMode('list')">
          <image src="/static/icons/list.svg"
                 class="toggle-icon"></image>
        </view>
      </view>
    </view>

    <!-- 作品列表 -->
    <view class="content-section">
      <view v-if="loading"
            class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 网格视图 -->
      <view v-else-if="viewMode === 'grid'"
            class="grid-view">
        <view v-for="painting in filteredPaintings"
              :key="painting.id"
              class="grid-item"
              @click="viewPaintingDetail(painting)">

          <view class="grid-image-container">
            <image :src="painting.thumbnail"
                   mode="aspectFill"
                   class="grid-image"
                   @error="onImageError">
            </image>
            <view class="image-overlay">
              <view class="painting-type">{{ painting.type }}</view>
              <view v-if="painting.isHighRes"
                    class="quality-badge">
                <image src="/static/icons/hd.svg"
                       class="badge-icon"></image>
              </view>
            </view>
          </view>

          <view class="grid-info">
            <text class="painting-title">{{ painting.title }}</text>
            <text class="painting-artist">{{ painting.artist }}</text>
            <text class="painting-dynasty">{{ painting.dynasty }}</text>
          </view>
        </view>
      </view>

      <!-- 列表视图 -->
      <view v-else
            class="list-view">
        <view v-for="painting in filteredPaintings"
              :key="painting.id"
              class="list-item"
              @click="viewPaintingDetail(painting)">

          <view class="list-image-container">
            <image :src="painting.thumbnail"
                   mode="aspectFill"
                   class="list-image">
            </image>
            <view class="image-badge">
              <text class="badge-text">{{ painting.type }}</text>
            </view>
          </view>

          <view class="list-content">
            <view class="painting-info">
              <text class="painting-title">{{ painting.title }}</text>
              <text class="painting-meta">{{ painting.artist }} · {{ painting.dynasty }}</text>
              <text class="painting-desc">{{ painting.description }}</text>
            </view>

            <!-- 技法标签 -->
            <view class="technique-tags">
              <text v-for="technique in painting.techniques"
                    :key="technique"
                    class="technique-tag">{{ technique }}</text>
            </view>

            <!-- 分析功能 -->
            <view class="analysis-features">
              <view v-if="painting.hasStyleAnalysis"
                    class="feature-item">
                <image src="/static/icons/style.svg"
                       class="feature-icon"></image>
                <text class="feature-text">风格分析</text>
              </view>
              <view v-if="painting.hasTechniqueAnalysis"
                    class="feature-item">
                <image src="/static/icons/technique.svg"
                       class="feature-icon"></image>
                <text class="feature-text">技法解析</text>
              </view>
              <view v-if="painting.hasColorAnalysis"
                    class="feature-item">
                <image src="/static/icons/color.svg"
                       class="feature-icon"></image>
                <text class="feature-text">色彩分析</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="list-actions">
              <view class="action-btn primary"
                    @click.stop="viewHighRes(painting)">
                <image src="/static/icons/zoom.svg"
                       class="action-icon"></image>
                <text>高清查看</text>
              </view>
              <view class="action-btn secondary"
                    @click.stop="showAnalysis(painting)">
                <image src="/static/icons/analysis.svg"
                       class="action-icon"></image>
                <text>AI分析</text>
              </view>
              <view class="action-btn favorite"
                    :class="{ active: painting.isFavorited }"
                    @click.stop="toggleFavorite(painting)">
                <image :src="painting.isFavorited ? '/static/icons/heart-filled.svg' : '/static/icons/heart.svg'"
                       class="action-icon"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && filteredPaintings.length === 0"
            class="empty-state">
        <image src="/static/icons/empty-paintings.svg"
               class="empty-icon"></image>
        <text class="empty-title">暂无书画作品</text>
        <text class="empty-desc">当前分类下暂无书画珍品</text>
      </view>
    </view>

    <!-- AI分析浮层 -->
    <view v-if="showAnalysisModal"
          class="analysis-overlay"
          @click="closeAnalysis">
      <view class="analysis-panel"
            @click.stop>
        <view class="panel-header">
          <text class="panel-title">AI智能分析</text>
          <text @click="closeAnalysis"
                class="close-btn">×</text>
        </view>

        <scroll-view class="analysis-content"
                     scroll-y="true">
          <!-- 风格分析 -->
          <view v-if="currentPainting.hasStyleAnalysis"
                class="analysis-section">
            <text class="section-title">风格分析</text>
            <view class="style-analysis">
              <view class="style-item">
                <text class="style-label">绘画流派</text>
                <text class="style-value">{{ currentPainting.styleAnalysis.school }}</text>
              </view>
              <view class="style-item">
                <text class="style-label">艺术风格</text>
                <text class="style-value">{{ currentPainting.styleAnalysis.style }}</text>
              </view>
              <view class="style-item">
                <text class="style-label">相似度评分</text>
                <progress :percent="currentPainting.styleAnalysis.similarity"
                          stroke-width="6"
                          activeColor="#FF6B35"
                          class="similarity-bar" />
                <text class="similarity-text">{{ currentPainting.styleAnalysis.similarity }}%</text>
              </view>
            </view>
          </view>

          <!-- 技法分析 -->
          <view v-if="currentPainting.hasTechniqueAnalysis"
                class="analysis-section">
            <text class="section-title">技法解析</text>
            <view class="technique-analysis">
              <view v-for="technique in currentPainting.techniqueAnalysis"
                    :key="technique.name"
                    class="technique-detail">
                <text class="technique-name">{{ technique.name }}</text>
                <text class="technique-desc">{{ technique.description }}</text>
                <view class="technique-examples">
                  <image v-for="example in technique.examples"
                         :key="example"
                         :src="example"
                         class="example-image"
                         mode="aspectFill">
                  </image>
                </view>
              </view>
            </view>
          </view>

          <!-- 色彩分析 -->
          <view v-if="currentPainting.hasColorAnalysis"
                class="analysis-section">
            <text class="section-title">色彩分析</text>
            <view class="color-analysis">
              <view class="color-palette">
                <view v-for="color in currentPainting.colorAnalysis.palette"
                      :key="color.hex"
                      class="color-item"
                      :style="{ backgroundColor: color.hex }">
                  <text class="color-percent">{{ color.percent }}%</text>
                </view>
              </view>
              <text class="color-desc">{{ currentPainting.colorAnalysis.description }}</text>
            </view>
          </view>

          <!-- 相关作品推荐 -->
          <view class="analysis-section">
            <text class="section-title">相关作品</text>
            <scroll-view class="related-scroll"
                         scroll-x="true"
                         show-scrollbar="false">
              <view class="related-list">
                <view v-for="related in currentPainting.relatedWorks"
                      :key="related.id"
                      class="related-item"
                      @click="viewPaintingDetail(related)">
                  <image :src="related.thumbnail"
                         class="related-image"
                         mode="aspectFill"></image>
                  <text class="related-title">{{ related.title }}</text>
                  <text class="related-artist">{{ related.artist }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 高清查看浮层 -->
    <view v-if="showHighResModal"
          class="highres-overlay"
          @click="closeHighRes">
      <view class="highres-container"
            @click.stop>
        <view class="highres-header">
          <text class="highres-title">{{ currentPainting.title }}</text>
          <text @click="closeHighRes"
                class="close-btn">×</text>
        </view>
        <scroll-view class="highres-content"
                     scroll-y="true"
                     scroll-x="true">
          <image :src="currentPainting.highResImage"
                 mode="widthFix"
                 class="highres-image"
                 @load="onHighResLoad">
          </image>
        </scroll-view>
        <view class="highres-tools">
          <view class="tool-btn"
                @click="downloadHighRes">
            <image src="/static/icons/download.svg"
                   class="tool-icon"></image>
            <text>下载</text>
          </view>
          <view class="tool-btn"
                @click="shareHighRes">
            <image src="/static/icons/share.svg"
                   class="tool-icon"></image>
            <text>分享</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部工具栏 -->
    <view class="toolbar">
      <view class="tool-item"
            @click="showStyleGuide">
        <image src="/static/icons/style-guide.svg"
               class="tool-icon"></image>
        <text class="tool-text">风格指南</text>
      </view>
      <view class="tool-item"
            @click="showTechniqueGuide">
        <image src="/static/icons/technique-guide.svg"
               class="tool-icon"></image>
        <text class="tool-text">技法解析</text>
      </view>
      <view class="tool-item"
            @click="showArtHistory">
        <image src="/static/icons/history.svg"
               class="tool-icon"></image>
        <text class="tool-text">艺术史</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Paintings',
  data () {
    return {
      loading: true,
      viewMode: 'grid', // grid | list
      currentFilter: 'all',
      showAnalysisModal: false,
      showHighResModal: false,
      currentPainting: null,

      filterOptions: [
        { label: '全部', value: 'all' },
        { label: '山水画', value: 'landscape' },
        { label: '花鸟画', value: 'flower-bird' },
        { label: '人物画', value: 'portrait' },
        { label: '书法', value: 'calligraphy' },
        { label: '工笔画', value: 'gongbi' },
        { label: '写意画', value: 'xieyi' }
      ],

      paintings: [
        {
          id: 1,
          title: '富春山居图',
          artist: '黄公望',
          dynasty: '元代',
          type: '山水画',
          description: '中国十大传世名画之一，被誉为"画中之兰亭"，展现了富春江两岸初秋的秀丽景色。',
          thumbnail: '/static/images/paintings/fuchun-thumb.jpg',
          highResImage: '/static/images/paintings/fuchun-hd.jpg',
          isHighRes: true,
          isFavorited: false,
          techniques: ['皴法', '渲染', '留白'],
          hasStyleAnalysis: true,
          hasTechniqueAnalysis: true,
          hasColorAnalysis: true,
          styleAnalysis: {
            school: '元代山水画派',
            style: '文人画风格',
            similarity: 95
          },
          techniqueAnalysis: [
            {
              name: '披麻皴',
              description: '用于表现江南山石的质感，线条柔和细腻',
              examples: ['/static/images/techniques/pima1.jpg', '/static/images/techniques/pima2.jpg']
            },
            {
              name: '干笔渲染',
              description: '营造远山朦胧的效果，层次丰富',
              examples: ['/static/images/techniques/ganbi1.jpg']
            }
          ],
          colorAnalysis: {
            palette: [
              { hex: '#8B7355', percent: 35 },
              { hex: '#D2B48C', percent: 25 },
              { hex: '#F5E6D3', percent: 20 },
              { hex: '#A0522D', percent: 12 },
              { hex: '#654321', percent: 8 }
            ],
            description: '以棕色、土黄色为主调，体现江南山水的温润典雅'
          },
          relatedWorks: [
            { id: 2, title: '千里江山图', artist: '王希孟', thumbnail: '/static/images/related/qianli-thumb.jpg' },
            { id: 3, title: '清明上河图', artist: '张择端', thumbnail: '/static/images/related/qingming-thumb.jpg' }
          ]
        },
        {
          id: 2,
          title: '墨梅图',
          artist: '王冕',
          dynasty: '元代',
          type: '花鸟画',
          description: '以墨代色，表现梅花的清雅脱俗，体现了文人画的高雅品味。',
          thumbnail: '/static/images/paintings/momei-thumb.jpg',
          highResImage: '/static/images/paintings/momei-hd.jpg',
          isHighRes: true,
          isFavorited: true,
          techniques: ['墨法', '留白', '写意'],
          hasStyleAnalysis: true,
          hasTechniqueAnalysis: true,
          hasColorAnalysis: false,
          styleAnalysis: {
            school: '文人画派',
            style: '写意花鸟',
            similarity: 92
          },
          techniqueAnalysis: [
            {
              name: '墨分五色',
              description: '运用墨色的浓淡变化表现梅花的层次',
              examples: ['/static/images/techniques/mo1.jpg', '/static/images/techniques/mo2.jpg']
            }
          ],
          relatedWorks: []
        }
      ]
    }
  },

  computed: {
    filteredPaintings () {
      if (this.currentFilter === 'all') {
        return this.paintings
      }

      const filterMap = {
        'landscape': '山水画',
        'flower-bird': '花鸟画',
        'portrait': '人物画',
        'calligraphy': '书法',
        'gongbi': '工笔画',
        'xieyi': '写意画'
      }

      return this.paintings.filter(painting =>
        painting.type === filterMap[this.currentFilter] ||
        painting.techniques.some(tech => tech.includes(filterMap[this.currentFilter]))
      )
    }
  },

  methods: {
    switchFilter (filter) {
      this.currentFilter = filter
      this.loadPaintings()
    },

    setViewMode (mode) {
      this.viewMode = mode
    },

    viewPaintingDetail (painting) {
      uni.navigateTo({
        url: `/pages/culture/painting-detail?id=${painting.id}`
      })
    },

    viewHighRes (painting) {
      this.currentPainting = painting
      this.showHighResModal = true
    },

    closeHighRes () {
      this.showHighResModal = false
    },

    onHighResLoad () {
      console.log('高清图像加载完成')
    },

    downloadHighRes () {
      uni.showLoading({
        title: '准备下载...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        })
      }, 2000)
    },

    shareHighRes () {
      uni.share({
        provider: 'weixin',
        type: 0,
        title: this.currentPainting.title,
        summary: this.currentPainting.description,
        imageUrl: this.currentPainting.thumbnail
      })
    },

    showAnalysis (painting) {
      this.currentPainting = painting
      this.showAnalysisModal = true
    },

    closeAnalysis () {
      this.showAnalysisModal = false
    },

    toggleFavorite (painting) {
      painting.isFavorited = !painting.isFavorited

      uni.showToast({
        title: painting.isFavorited ? '已收藏' : '已取消收藏',
        icon: painting.isFavorited ? 'success' : 'none'
      })
    },

    showStyleGuide () {
      uni.navigateTo({
        url: '/pages/culture/style-guide'
      })
    },

    showTechniqueGuide () {
      uni.navigateTo({
        url: '/pages/culture/technique-guide'
      })
    },

    showArtHistory () {
      uni.navigateTo({
        url: '/pages/culture/art-history'
      })
    },

    onImageError () {
      console.log('图片加载失败')
    },

    async loadPaintings () {
      this.loading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.loading = false
      } catch (error) {
        console.error('加载书画数据失败:', error)
        this.loading = false
      }
    }
  },

  onLoad () {
    this.loadPaintings()
  }
}
</script>

<style scoped>
.paintings-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 页面头部 */
.page-header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 107, 53, 0.8),
    rgba(255, 140, 0, 0.6)
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 筛选导航 */
.filter-section {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  flex: 1;
  height: 80rpx;
}

.filter-list {
  display: flex;
  gap: 24rpx;
  padding-right: 20rpx;
}

.filter-item {
  font-size: 26rpx;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: white;
  background: #ff6b35;
}

.view-toggle {
  display: flex;
  gap: 8rpx;
  margin-left: 20rpx;
}

.toggle-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background: #f0f0f0;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: #ff6b35;
}

.toggle-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 内容区域 */
.content-section {
  padding: 20rpx 30rpx;
  padding-bottom: 200rpx;
}

.loading-container {
  padding: 100rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 网格视图 */
.grid-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.grid-item {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.grid-item:active {
  transform: translateY(-2rpx);
}

.grid-image-container {
  position: relative;
  height: 240rpx;
  overflow: hidden;
}

.grid-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  right: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.painting-type {
  background: rgba(255, 107, 53, 0.9);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

.quality-badge {
  width: 40rpx;
  height: 40rpx;
  background: rgba(76, 175, 80, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-icon {
  width: 24rpx;
  height: 24rpx;
}

.grid-info {
  padding: 20rpx;
}

.painting-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.painting-artist,
.painting-dynasty {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

/* 列表视图 */
.list-view {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.list-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.list-item:active {
  transform: translateY(-2rpx);
}

.list-image-container {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.list-image {
  width: 100%;
  height: 100%;
}

.image-badge {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20rpx 16rpx 12rpx;
}

.badge-text {
  color: white;
  font-size: 20rpx;
  font-weight: 500;
}

.list-content {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.painting-info {
  margin-bottom: 16rpx;
}

.painting-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.painting-meta {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.painting-desc {
  font-size: 24rpx;
  color: #888;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 技法标签 */
.technique-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin: 12rpx 0;
}

.technique-tag {
  font-size: 20rpx;
  background: #fff3e0;
  color: #ff6b35;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

/* 分析功能 */
.analysis-features {
  display: flex;
  gap: 16rpx;
  margin: 12rpx 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.feature-icon {
  width: 24rpx;
  height: 24rpx;
}

.feature-text {
  font-size: 20rpx;
  color: #666;
}

/* 列表操作按钮 */
.list-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  gap: 6rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  flex: 1;
  background: #ff6b35;
  color: white;
}

.action-btn.secondary {
  flex: 1;
  background: #e0e0e0;
  color: #333;
}

.action-btn.favorite {
  width: 60rpx;
  background: #f5f5f5;
}

.action-btn.favorite.active {
  background: #ffebee;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* AI分析浮层 */
.analysis-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.analysis-panel {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.analysis-content {
  height: 60vh;
  padding: 30rpx;
}

.analysis-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #ff6b35;
  padding-left: 16rpx;
}

/* 风格分析 */
.style-analysis {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

.style-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.style-item:last-child {
  margin-bottom: 0;
}

.style-label {
  font-size: 24rpx;
  color: #666;
}

.style-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.similarity-bar {
  flex: 1;
  margin: 0 20rpx;
}

.similarity-text {
  font-size: 22rpx;
  color: #ff6b35;
  font-weight: 600;
}

/* 技法分析 */
.technique-analysis {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.technique-detail {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

.technique-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.technique-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.technique-examples {
  display: flex;
  gap: 12rpx;
}

.example-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

/* 色彩分析 */
.color-analysis {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

.color-palette {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.color-item {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.color-percent {
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.color-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 相关作品 */
.related-scroll {
  height: 200rpx;
}

.related-list {
  display: flex;
  gap: 20rpx;
  padding-right: 20rpx;
}

.related-item {
  text-align: center;
  width: 120rpx;
  flex-shrink: 0;
}

.related-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}

.related-title {
  font-size: 22rpx;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-artist {
  font-size: 20rpx;
  color: #666;
}

/* 高清查看浮层 */
.highres-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.highres-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.highres-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.highres-title {
  font-size: 32rpx;
  font-weight: 600;
}

.highres-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highres-image {
  max-width: 100%;
  min-height: 200rpx;
}

.highres-tools {
  display: flex;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  gap: 40rpx;
  justify-content: center;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  gap: 8rpx;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 底部工具栏 */
.toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.tool-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  transition: background-color 0.3s ease;
}

.tool-item:active {
  background-color: #f8f9fa;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 22rpx;
  color: #666;
}
</style> 