// 区域ID全局状态管理
// 存储当前选中的省市区ID，用于后续业务数据加载

import { reactive } from 'vue'
import { getProvinces, getCities, getDistricts, searchDistrictsByName } from '@/api/admin_divisions'
import type { Province, City, District } from '@/api/admin_divisions'
import { getHeritageePlaceByRegion, type HeritagePlace } from '@/api/heritage'

// 区域ID信息接口
export interface RegionIds {
  provinceId: number
  provinceName: string
  cityId: number
  cityName: string
  districtId: number
  districtName: string
}

// 区域状态接口（包含heritage信息）
export interface RegionState {
  regionIds: RegionIds
  isLoading: boolean
  heritagePlace: HeritagePlace | null
  heritageLoading: boolean
}

// 默认区域ID信息
const defaultRegionIds: RegionIds = {
  provinceId: 0,
  provinceName: '',
  cityId: 0,
  cityName: '',
  districtId: 0,
  districtName: ''
}

// 存储键名
const STORAGE_KEY = 'region_ids'

// 响应式状态
const state = reactive<RegionState>({
  regionIds: { ...defaultRegionIds },
  isLoading: false,
  heritagePlace: null,
  heritageLoading: false
})

// 保存到本地存储
function saveToStorage(regionIds: RegionIds) {
  try {
    uni.setStorageSync(STORAGE_KEY, JSON.stringify(regionIds))
  } catch (error) {
    console.warn('保存区域ID信息失败:', error)
  }
}

// 从本地存储加载
function loadFromStorage(): RegionIds {
  try {
    const stored = uni.getStorageSync(STORAGE_KEY)
    if (stored) {
      const parsed = JSON.parse(stored)
      return { ...defaultRegionIds, ...parsed }
    }
  } catch (error) {
    console.warn('读取区域ID信息失败:', error)
  }
  return { ...defaultRegionIds }
}

// 初始化加载
state.regionIds = loadFromStorage()

/**
 * 通过名称匹配省份ID
 */
async function matchProvinceByName(provinceName: string): Promise<Province | null> {
  try {
    const provinces = await getProvinces()
    
    // 先精确匹配
    let matched = provinces.find(p => p.name === provinceName)
    if (matched) return matched
    
    // 模糊匹配（去掉省市区等后缀）
    const cleanName = provinceName.replace(/[省市自治区特别行政区]/g, '')
    matched = provinces.find(p => 
      p.name.replace(/[省市自治区特别行政区]/g, '') === cleanName ||
      p.name.includes(cleanName) ||
      cleanName.includes(p.name.replace(/[省市自治区特别行政区]/g, ''))
    )
    
    return matched || null
  } catch (error) {
    console.error('匹配省份失败:', error)
    return null
  }
}

/**
 * 通过名称匹配城市ID
 */
async function matchCityByName(provinceId: number, cityName: string): Promise<City | null> {
  try {
    const cities = await getCities(provinceId)
    
    // 先精确匹配
    let matched = cities.find(c => c.name === cityName)
    if (matched) return matched
    
    // 模糊匹配（去掉市区县等后缀）
    const cleanName = cityName.replace(/[市区县自治州地区盟]/g, '')
    matched = cities.find(c => 
      c.name.replace(/[市区县自治州地区盟]/g, '') === cleanName ||
      c.name.includes(cleanName) ||
      cleanName.includes(c.name.replace(/[市区县自治州地区盟]/g, ''))
    )
    
    return matched || null
  } catch (error) {
    console.error('匹配城市失败:', error)
    return null
  }
}

/**
 * 通过名称匹配区县ID
 */
async function matchDistrictByName(provinceId: number, cityId: number, districtName: string): Promise<District | null> {
  try {
    const districts = await getDistricts(provinceId, cityId)
    
    // 先精确匹配
    let matched = districts.find(d => d.name === districtName)
    if (matched) return matched
    
    // 模糊匹配（去掉区县市等后缀）
    const cleanName = districtName.replace(/[区县市旗]/g, '')
    matched = districts.find(d => 
      d.name.replace(/[区县市旗]/g, '') === cleanName ||
      d.name.includes(cleanName) ||
      cleanName.includes(d.name.replace(/[区县市旗]/g, ''))
    )
    
    return matched || null
  } catch (error) {
    console.error('匹配区县失败:', error)
    return null
  }
}

/**
 * 在指定省份下通过区县名称查找区县，并返回对应的城市信息
 * 主要用于直辖市场景
 */
async function findDistrictAndCityByName(provinceId: number, districtName: string): Promise<{district: District, city: City} | null> {
  try {
    // 在省份下搜索匹配的区县
    const matchedDistricts = await searchDistrictsByName(provinceId, districtName)
    
    if (matchedDistricts.length === 0) {
      console.warn('未找到匹配的区县:', districtName)
      return null
    }
    
    // 取第一个匹配的区县
    const district = matchedDistricts[0]
    console.log('找到匹配的区县:', district)
    
    // 通过区县的city_id获取对应的城市信息
    const cities = await getCities(provinceId)
    const city = cities.find(c => c.city_id === district.city_id)
    
    if (!city) {
      console.warn('未找到区县对应的城市:', district.city_id)
      return null
    }
    
    console.log('找到区县对应的城市:', city)
    return { district, city }
  } catch (error) {
    console.error('通过区县查找城市失败:', error)
    return null
  }
}

// 区域ID管理器
export const regionManager = {
  // 获取当前区域ID信息
  get currentRegionIds(): RegionIds {
    return { ...state.regionIds }
  },

  // 获取加载状态
  get isLoading(): boolean {
    return state.isLoading
  },

  // 设置区域ID信息
  setRegionIds(regionIds: Partial<RegionIds>): void {
    state.regionIds = { ...defaultRegionIds, ...regionIds }
    saveToStorage(state.regionIds)
    console.log('区域ID已更新:', state.regionIds)
  },

  // 清空区域ID信息
  clearRegionIds(): void {
    state.regionIds = { ...defaultRegionIds }
    try {
      uni.removeStorageSync(STORAGE_KEY)
    } catch (error) {
      console.warn('清除区域ID信息失败:', error)
    }
    console.log('区域ID已清空')
  },

  /**
   * 通过定位信息匹配区域ID
   * @param locationInfo 定位得到的地址信息
   */
  async matchRegionIdsByLocation(locationInfo: {
    province: string
    city: string
    district: string
  }): Promise<RegionIds | null> {
    state.isLoading = true
    
    try {
      console.log('开始匹配区域ID:', locationInfo)
      
      // 1. 匹配省份
      const matchedProvince = await matchProvinceByName(locationInfo.province)
      if (!matchedProvince) {
        console.warn('未找到匹配的省份:', locationInfo.province)
        return null
      }
      
      console.log('匹配到省份:', matchedProvince)
      
      // 2. 检查是否为直辖市
      const directMunicipalities = ['北京', '上海', '天津', '重庆']
      const isDirectMunicipality = directMunicipalities.some(city => 
        matchedProvince.name.includes(city)
      )
      
      let matchedCity: City | null = null
      let matchedDistrict: District | null = null
      
      if (isDirectMunicipality && locationInfo.district) {
        console.log('检测到直辖市，尝试通过区县反推城市')
        
        // 对于直辖市，先通过区县名称查找区县和对应的城市
        const result = await findDistrictAndCityByName(matchedProvince.province_id, locationInfo.district)
        
        if (result) {
          matchedCity = result.city
          matchedDistrict = result.district
          console.log('直辖市通过区县匹配成功 - 城市:', matchedCity, '区县:', matchedDistrict)
        } else {
          console.log('直辖市通过区县匹配失败，尝试传统方式')
          // 如果通过区县匹配失败，使用传统方式
          matchedCity = await matchCityByName(matchedProvince.province_id, locationInfo.city)
          
          if (!matchedCity) {
            // 尝试用省份名称匹配城市
            matchedCity = await matchCityByName(matchedProvince.province_id, matchedProvince.name)
            
            // 如果还是没找到，使用第一个城市
            if (!matchedCity) {
              const cities = await getCities(matchedProvince.province_id)
              console.log('直辖市城市列表:', cities)
              if (cities.length > 0) {
                matchedCity = cities[0]
                console.log('使用直辖市第一个城市:', matchedCity)
              }
            }
          }
        }
      } else {
        // 非直辖市，使用传统的城市匹配方式
        matchedCity = await matchCityByName(matchedProvince.province_id, locationInfo.city)
      }
      
      if (!matchedCity) {
        console.warn('未找到匹配的城市:', locationInfo.city)
        // 只有省份信息也可以
        const regionIds: RegionIds = {
          provinceId: matchedProvince.province_id,
          provinceName: matchedProvince.name,
          cityId: 0,
          cityName: '',
          districtId: 0,
          districtName: ''
        }
        this.setRegionIds(regionIds)
        return regionIds
      }
      
      console.log('匹配到城市:', matchedCity)
      
      // 3. 如果还没有匹配到区县，且有区县信息，尝试匹配区县
      if (!matchedDistrict && locationInfo.district) {
        matchedDistrict = await matchDistrictByName(
          matchedProvince.province_id, 
          matchedCity.city_id, 
          locationInfo.district
        )
        console.log('匹配到区县:', matchedDistrict)
      }
      
      // 4. 构建结果
      const regionIds: RegionIds = {
        provinceId: matchedProvince.province_id,
        provinceName: matchedProvince.name,
        cityId: matchedCity.city_id,
        cityName: matchedCity.name,
        districtId: matchedDistrict?.district_id || 0,
        districtName: matchedDistrict?.name || ''
      }
      
      this.setRegionIds(regionIds)
      console.log('区域ID匹配成功:', regionIds)
      return regionIds
      
    } catch (error) {
      console.error('匹配区域ID失败:', error)
      return null
    } finally {
      state.isLoading = false
    }
  },

  /**
   * 手动设置选中的区域ID
   * @param province 选中的省份
   * @param city 选中的城市
   * @param district 选中的区县（可选）
   */
  setSelectedRegionIds(
    province: Province | null,
    city: City | null,
    district: District | null = null
  ): void {
    const regionIds: RegionIds = {
      provinceId: province?.province_id || 0,
      provinceName: province?.name || '',
      cityId: city?.city_id || 0,
      cityName: city?.name || '',
      districtId: district?.district_id || 0,
      districtName: district?.name || ''
    }
    
    this.setRegionIds(regionIds)
    
    // 清空当前的heritage place，让系统使用新的区域ID重新获取
    this.clearHeritagePlace()
    
    console.log('手动设置区域ID:', regionIds)
    console.log('已清空heritage place，将使用新区域ID获取数据')
  },

  /**
   * 获取完整的区域名称
   */
  get fullRegionName(): string {
    const { provinceName, cityName, districtName } = state.regionIds
    let name = provinceName
    if (cityName && cityName !== provinceName) {
      name += cityName
    }
    if (districtName) {
      name += districtName
    }
    return name || '请选择地区'
  },

  /**
   * 检查是否有完整的区域信息
   */
  get hasCompleteRegion(): boolean {
    return !!(state.regionIds.provinceId && state.regionIds.cityId)
  },

  /**
   * 检查是否有区县信息
   */
  get hasDistrict(): boolean {
    return !!state.regionIds.districtId
  },

  /**
   * 获取当前区域的heritage place信息
   */
  get currentHeritagePlace(): HeritagePlace | null {
    return state.heritagePlace
  },

  /**
   * 获取heritage加载状态
   */
  get isHeritageLoading(): boolean {
    return state.heritageLoading
  },

  /**
   * 根据当前区域ID获取heritage place信息
   */
  async fetchHeritagePlace(): Promise<HeritagePlace | null> {
    const { provinceId, cityId, districtId } = state.regionIds
    
    if (!provinceId) {
      console.warn('没有省份ID，无法获取heritage place')
      return null
    }

    state.heritageLoading = true
    
    try {
      console.log('开始获取heritage place:', { provinceId, cityId, districtId })
      
      const heritagePlace = await getHeritageePlaceByRegion({
        province_id: provinceId || undefined,
        city_id: cityId || undefined,
        district_id: districtId || undefined
      })
      
      state.heritagePlace = heritagePlace
      console.log('获取heritage place成功:', heritagePlace)
      
      return heritagePlace
    } catch (error) {
      console.error('获取heritage place失败:', error)
      state.heritagePlace = null
      return null
    } finally {
      state.heritageLoading = false
    }
  },

  /**
   * 清空heritage place信息
   */
  clearHeritagePlace(): void {
    state.heritagePlace = null
    console.log('heritage place信息已清空')
  },

  /**
   * 当区域ID更新时，自动获取对应的heritage place
   */
  async updateRegionAndFetchHeritage(regionIds: Partial<RegionIds>): Promise<HeritagePlace | null> {
    // 先更新区域ID
    this.setRegionIds(regionIds)
    
    // 然后获取heritage place
    return await this.fetchHeritagePlace()
  }
} 