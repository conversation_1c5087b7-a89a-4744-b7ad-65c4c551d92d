import axios from 'axios';
import { getBaseURL, getImageProxyUrl, getTimeout, isDebugMode } from '../config/api';

// 使用统一的配置管理
const BASE_URL = getBaseURL();

// 导出图片代理函数供其他模块使用
export { getImageProxyUrl };

// 当代记忆项目接口
export interface MemoryItem {
  id: number;
  title: string;
  year: string;
  image?: string;
  description?: string;
  detail_content?: string;
  detail_images?: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
  place_id: number;
}

// 当代记忆列表响应
export interface MemoryListResponse {
  items: MemoryItem[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 地点信息接口
export interface PlaceInfo {
  id: number;
  place_name: string;
  place_desc?: string;
  province_id?: number;
  city_id?: number;
  district_id?: number;
  header_bg_image?: string;
  introduction?: string;
  footer_text?: string;
}

// 当代记忆页面数据
export interface MemoryPageData {
  place_info: PlaceInfo;
  memory_data: MemoryItem[];
}

/**
 * 根据省市区ID获取地点信息
 */
export async function getPlaceByRegion(params: {
  province_id?: number;
  city_id?: number;
  district_id?: number;
}): Promise<PlaceInfo | null> {
  try {
    const response = await axios.get(`${BASE_URL}/heritage/places/by-region`, { 
      params: {
        province_id: params.province_id,
        city_id: params.city_id,
        district_id: params.district_id
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('获取地点信息失败:', error);
    return null;
  }
}

/**
 * 获取当代记忆数据（优化版本）
 */
export async function getMemoryData(params: {
  province_id?: number;
  city_id?: number;
  district_id?: number;
  page?: number;
  page_size?: number;
}): Promise<MemoryListResponse> {
  try {
    console.log('🔍 获取当代记忆数据，参数:', params);

    // 检查参数是否有效（至少需要省份ID）
    if (!params.province_id) {
      console.log('⚠️ 没有提供省份ID，返回空数据');
      return {
        items: [],
        total: 0,
        page: params.page || 1,
        page_size: params.page_size || 50,
        total_pages: 0
      };
    }

    // 首先获取地点信息
    const placeResponse = await getPlaceByRegion({
      province_id: params.province_id,
      city_id: params.city_id,
      district_id: params.district_id
    });

    console.log('📍 地点信息响应:', placeResponse);

    if (!placeResponse || !placeResponse.id) {
      console.log('⚠️ 没有找到对应的地点信息，返回空数据');
      return {
        items: [],
        total: 0,
        page: params.page || 1,
        page_size: params.page_size || 50,
        total_pages: 0
      };
    }

    // 获取该地点的当代记忆数据
    const placeId = placeResponse.id;
    console.log(`🎯 获取地点ID为 ${placeId} 的当代记忆数据`);

    const memoryResponse = await axios.get(`${BASE_URL}/heritage/places/${placeId}/memory`, {
      params: {
        page: params.page || 1,
        page_size: params.page_size || 50
      },
      timeout: getTimeout()
    });

    console.log('✅ 当代记忆数据响应:', memoryResponse.data);

    // 处理图片URL
    if (memoryResponse.data.items) {
      memoryResponse.data.items = memoryResponse.data.items.map((item: MemoryItem) => ({
        ...item,
        image: item.image ? getImageProxyUrl(item.image) : undefined,
        detail_images: item.detail_images ? item.detail_images.map(img => getImageProxyUrl(img)) : []
      }));
    }

    return memoryResponse.data;
  } catch (error) {
    console.error('❌ 获取当代记忆数据失败:', error);
    // 返回空数据而不是抛出错误
    return {
      items: [],
      total: 0,
      page: params.page || 1,
      page_size: params.page_size || 50,
      total_pages: 0
    };
  }
}

/**
 * 根据省市区ID获取完整的当代记忆页面数据（优化版本）
 */
export async function getMemoryPageDataByRegion(params: {
  province_id?: number;
  city_id?: number;
  district_id?: number;
}): Promise<MemoryPageData | null> {
  try {
    console.log('🔍 获取完整页面数据，参数:', params);

    const response = await axios.get(`${BASE_URL}/heritage/places/by-region/full`, {
      params: {
        province_id: params.province_id,
        city_id: params.city_id,
        district_id: params.district_id,
        include_binary_data: false
      },
      timeout: getTimeout()
    });

    // 检查是否是错误响应
    if (response.data && typeof response.data === 'object' && 'statusCode' in response.data) {
      if (response.data.statusCode === 404) {
        console.log('⚠️ 当前区域未构建数据');
        return null;
      }
    }

    console.log('✅ 完整页面数据获取成功');

    // 处理图片URL
    if (response.data && response.data.memory_data) {
      response.data.memory_data = response.data.memory_data.map((item: MemoryItem) => ({
        ...item,
        image: item.image ? getImageProxyUrl(item.image) : undefined,
        detail_images: item.detail_images ? item.detail_images.map(img => getImageProxyUrl(img)) : []
      }));
    }

    return response.data;
  } catch (error) {
    console.error('❌ 获取当代记忆页面数据失败:', error);
    return null;
  }
}

/**
 * 获取当代记忆项目详情
 */
export async function getMemoryItemDetail(itemId: number): Promise<MemoryItem | null> {
  try {
    const response = await axios.get(`${BASE_URL}/heritage/memory/${itemId}`);
    return response.data;
  } catch (error) {
    console.error('获取当代记忆项目详情失败:', error);
    return null;
  }
}

/**
 * 生成默认当代记忆数据（当API调用失败时使用）
 */
export function generateDefaultMemoryData(): MemoryItem[] {
  const years = ['2020年', '2015年', '2010年', '2005年', '2000年', '1995年', '1990年', '1985年'];
  const themes = ['城市建设', '文化活动', '社会变迁', '科技发展', '教育进步', '环境改善', '交通发展', '民生改善'];
  
  return Array(8).fill(null).map((_, index) => ({
    id: index + 1,
    title: `${themes[index % themes.length]} - ${years[index % years.length]}`,
    year: years[index % years.length],
    image: `https://picsum.photos/seed/${index + 400}/800/600`,
    description: `这是${years[index % years.length]}的一段珍贵记忆，记录了城市发展进程中的重要时刻。通过这些影像资料，我们可以看到城市的变迁和人民生活的改善。`,
    detail_content: `<h4>时代背景</h4><p>这段记忆记录了${years[index % years.length]}城市发展的重要节点，见证了改革开放以来的巨大变化。</p><h4>重要意义</h4><p>这些珍贵的影像资料不仅记录了城市的物理变化，更承载着人们的情感记忆和时代精神。</p><h4>现实价值</h4><p>通过回顾这些历史瞬间，我们能够更好地理解城市发展的脉络，为未来的建设提供借鉴。</p>`,
    detail_images: [
      `https://picsum.photos/seed/${index + 500}/800/600`,
      `https://picsum.photos/seed/${index + 501}/800/600`,
      `https://picsum.photos/seed/${index + 502}/800/600`
    ],
    is_active: true,
    sort_order: index + 1,
    created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    place_id: 1
  }));
}
