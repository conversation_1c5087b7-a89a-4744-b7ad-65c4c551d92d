from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from fastapi.responses import JSONResponse, Response, StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from typing import Optional, List, Union
import json
import os

from app.database.db import get_db
# 移除二进制数据转换，直接返回数据库原始URL
from app.schemas.heritage_schemas import (
    HeritagePlaceCreate, HeritagePlaceUpdate, HeritagePlaceResponse, HeritagePlaceListResponse,
    TimelineItemCreate, TimelineItemUpdate, TimelineItemResponse, TimelineItemListResponse,
    HeritageItemCreate, HeritageItemUpdate, HeritageItemResponse, HeritageItemListResponse,
    MemoryItemCreate, MemoryItemUpdate, MemoryItemResponse, MemoryItemListResponse,
    HeritagePageResponse, HeritageTagsResponse
)
from app.models.heritage import HeritagePlace, TimelineItem, HeritageItem, MemoryItem
from app.models.users import User, UserRole
from app.core.dependencies import require_admin, get_current_user
from app.core.auth import permission_service

router = APIRouter()


def can_manage_heritage_place(current_user: User, place: HeritagePlace) -> bool:
    """检查用户是否有权限管理指定的文化遗产地点"""
    if current_user.role == UserRole.SUPER_ADMIN:
        return True
    elif current_user.role == UserRole.PROVINCE_ADMIN:
        return place.province_id == current_user.province_id
    elif current_user.role == UserRole.CITY_ADMIN:
        return (place.province_id == current_user.province_id and 
                place.city_id == current_user.city_id)
    elif current_user.role == UserRole.DISTRICT_ADMIN:
        return (place.province_id == current_user.province_id and 
                place.city_id == current_user.city_id and 
                place.district_id == current_user.district_id)
    return False


def parse_json_list(json_str: Optional[str]) -> List[str]:
    """解析JSON字符串为列表"""
    if not json_str:
        return []
    try:
        result = json.loads(json_str)
        return result if isinstance(result, list) else []
    except (json.JSONDecodeError, TypeError):
        return []


def serialize_json_list(data: Optional[List[str]]) -> Optional[str]:
    """序列化列表为JSON字符串"""
    if not data:
        return None
    try:
        return json.dumps(data, ensure_ascii=False)
    except (TypeError, ValueError):
        return None


# ==================== 地点管理 ====================

@router.get("/places", response_model=HeritagePlaceListResponse, summary="获取文化遗产地点列表")
async def get_heritage_places(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    province_id: Optional[int] = Query(None, description="省份ID筛选"),
    city_id: Optional[int] = Query(None, description="城市ID筛选"),
    district_id: Optional[int] = Query(None, description="区县ID筛选"),
    search: Optional[str] = Query(None, description="搜索地点名称"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """获取文化遗产地点列表（需要管理员权限）"""
    query = db.query(HeritagePlace)
    
    # 根据用户权限过滤
    if current_user.role != UserRole.SUPER_ADMIN:
        if current_user.role == UserRole.PROVINCE_ADMIN:
            query = query.filter(HeritagePlace.province_id == current_user.province_id)
        elif current_user.role == UserRole.CITY_ADMIN:
            query = query.filter(
                and_(HeritagePlace.province_id == current_user.province_id,
                     HeritagePlace.city_id == current_user.city_id)
            )
        elif current_user.role == UserRole.DISTRICT_ADMIN:
            query = query.filter(
                and_(HeritagePlace.province_id == current_user.province_id,
                     HeritagePlace.city_id == current_user.city_id,
                     HeritagePlace.district_id == current_user.district_id)
            )
    
    # 应用筛选条件
    if province_id:
        query = query.filter(HeritagePlace.province_id == province_id)
    if city_id:
        query = query.filter(HeritagePlace.city_id == city_id)
    if district_id:
        query = query.filter(HeritagePlace.district_id == district_id)
    if search:
        query = query.filter(HeritagePlace.place_name.like(f"%{search}%"))
    
    # 排序
    query = query.order_by(desc(HeritagePlace.sort_order), desc(HeritagePlace.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    places = query.offset(offset).limit(page_size).all()
    
    return HeritagePlaceListResponse(
        places=places,
        total=total,
        page=page,
        page_size=page_size
    )


@router.post("/places", response_model=HeritagePlaceResponse, summary="创建文化遗产地点")
async def create_heritage_place(
    request: HeritagePlaceCreate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """创建文化遗产地点（需要管理员权限）"""
    # 权限检查：确保用户有权限在指定区域创建地点
    if current_user.role != UserRole.SUPER_ADMIN:
        if current_user.role == UserRole.PROVINCE_ADMIN:
            if request.province_id != current_user.province_id:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权在该省份创建地点")
        elif current_user.role == UserRole.CITY_ADMIN:
            if (request.province_id != current_user.province_id or 
                request.city_id != current_user.city_id):
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权在该城市创建地点")
        elif current_user.role == UserRole.DISTRICT_ADMIN:
            if (request.province_id != current_user.province_id or 
                request.city_id != current_user.city_id or 
                request.district_id != current_user.district_id):
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权在该区县创建地点")
    
    place = HeritagePlace(**request.dict())
    db.add(place)
    db.commit()
    db.refresh(place)
    
    return place 


@router.get("/places/by-region", response_model=HeritagePlaceResponse, summary="根据省市区ID获取文化遗产地点")
async def get_heritage_place_by_region(
    province_id: Optional[int] = Query(None, description="省份ID"),
    city_id: Optional[int] = Query(None, description="城市ID"),
    district_id: Optional[int] = Query(None, description="区县ID"),
    db: Session = Depends(get_db)
):
    """根据省市区ID获取文化遗产地点（公共接口，无需登录）"""
    query = db.query(HeritagePlace).filter(HeritagePlace.is_active == True)
    
    place = None
    
    # 按优先级匹配：区县 > 城市 > 省份
    if district_id and city_id and province_id:
        # 优先匹配区县（需要所有三个ID都存在）
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id == city_id,
                HeritagePlace.district_id == district_id
            )
        ).first()
    
    if not place and city_id and province_id:
        # 其次匹配城市（需要省份ID和城市ID）
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id == city_id,
                HeritagePlace.district_id.is_(None)
            )
        ).first()
    
    if not place and province_id:
        # 最后匹配省份
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id.is_(None),
                HeritagePlace.district_id.is_(None)
            )
        ).first()
    
    if not place:
        # 如果没有找到匹配的地点，返回404
        return JSONResponse(
            status_code=200,
            content={
                "statusCode": 404,
                "message": "当前区域无数据",
                "data": None
            }
        )
    
    # 直接返回数据库原始数据
    return place.__dict__


@router.get("/places/by-region/full", response_model=HeritagePageResponse, summary="根据省市区ID获取完整的文化遗产页面数据")
async def get_heritage_page_data_by_region(
    province_id: Optional[int] = Query(None, description="省份ID"),
    city_id: Optional[int] = Query(None, description="城市ID"),
    district_id: Optional[int] = Query(None, description="区县ID"),
    db: Session = Depends(get_db)
):
    """根据省市区ID获取完整的文化遗产页面数据（公共接口，无需登录）"""
    query = db.query(HeritagePlace).filter(HeritagePlace.is_active == True)
    
    place = None
    
    # 按优先级匹配：区县 > 城市 > 省份
    if district_id and city_id and province_id:
        # 优先匹配区县（需要所有三个ID都存在）
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id == city_id,
                HeritagePlace.district_id == district_id
            )
        ).first()
    
    if not place and city_id and province_id:
        # 其次匹配城市（需要省份ID和城市ID）
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id == city_id,
                HeritagePlace.district_id.is_(None)
            )
        ).first()
    
    if not place and province_id:
        # 最后匹配省份
        place = query.filter(
            and_(
                HeritagePlace.province_id == province_id,
                HeritagePlace.city_id.is_(None),
                HeritagePlace.district_id.is_(None)
            )
        ).first()
    
    if not place:
        # 如果没有找到匹配的地点，返回404
        return JSONResponse(
            status_code=200,
            content={
                "statusCode": 404,
                "message": "当前区域无数据",
                "data": None
            }
        )
    
    # 获取时间轴数据
    timeline_items = db.query(TimelineItem).filter(
        and_(TimelineItem.place_id == place.id, TimelineItem.is_active == True)
    ).order_by(desc(TimelineItem.sort_order), desc(TimelineItem.created_at)).all()
    
    # 获取文化遗产数据
    heritage_items = db.query(HeritageItem).filter(
        and_(HeritageItem.place_id == place.id, HeritageItem.is_active == True)
    ).order_by(desc(HeritageItem.sort_order), desc(HeritageItem.created_at)).all()
    
    # 获取城市记忆数据
    memory_items = db.query(MemoryItem).filter(
        and_(MemoryItem.place_id == place.id, MemoryItem.is_active == True)
    ).order_by(desc(MemoryItem.sort_order), desc(MemoryItem.created_at)).all()
    
    # 处理地点信息：直接返回原始数据
    place_dict = place.__dict__.copy()
    
    # 处理JSON字段，直接返回原始数据
    timeline_data = []
    for item in timeline_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
        timeline_data.append(TimelineItemResponse(**item_dict))
    
    heritage_data = []
    for item in heritage_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        heritage_data.append(HeritageItemResponse(**item_dict))
    
    memory_data = []
    for item in memory_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        memory_data.append(MemoryItemResponse(**item_dict))
    
    return HeritagePageResponse(
        place_info=HeritagePlaceResponse(**place_dict),
        timeline_data=timeline_data,
        heritage_data=heritage_data,
        memory_data=memory_data
    )


@router.get("/places/{place_id}", response_model=HeritagePlaceResponse, summary="获取文化遗产地点详情")
async def get_heritage_place(
    place_id: int,
    db: Session = Depends(get_db)
):
    """获取文化遗产地点详情"""
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        # 返回200状态码，提示当前区域未构建数据
        return JSONResponse(
            status_code=200,
            content={
                "statusCode": 404,
                "message": "当前区域未构建数据",
                "data": None
            }
        )
    
    # 直接返回数据库原始数据
    return place.__dict__


@router.put("/places/{place_id}", response_model=HeritagePlaceResponse, summary="更新文化遗产地点")
async def update_heritage_place(
    place_id: int,
    request: HeritagePlaceUpdate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """更新文化遗产地点（需要管理员权限）"""
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该地点")
    
    # 更新字段
    update_data = request.dict(exclude_unset=True)
    
    # 特殊处理：如果前端明确传递了header_bg_image字段（即使是空字符串），也要更新
    request_dict = request.dict()
    if 'header_bg_image' in request_dict:
        update_data['header_bg_image'] = request_dict['header_bg_image']
    
    for field, value in update_data.items():
        setattr(place, field, value)
    
    db.commit()
    db.refresh(place)
    
    return place


@router.delete("/places/{place_id}", summary="删除文化遗产地点")
async def delete_heritage_place(
    place_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """删除文化遗产地点（需要管理员权限）"""
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该地点")
    
    db.delete(place)
    db.commit()
    
    return {"message": "地点删除成功"}


# ==================== 时间轴管理 ====================

@router.get("/places/{place_id}/timeline", response_model=TimelineItemListResponse, summary="获取时间轴列表")
async def get_timeline_items(
    place_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取指定地点的时间轴列表"""
    # 检查地点是否存在
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    query = db.query(TimelineItem).filter(
        and_(TimelineItem.place_id == place_id, TimelineItem.is_active == True)
    ).order_by(desc(TimelineItem.sort_order), desc(TimelineItem.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    items = query.offset(offset).limit(page_size).all()
    
    # 处理JSON字段
    result_items = []
    for item in items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
        result_items.append(TimelineItemResponse(**item_dict))
    
    return TimelineItemListResponse(
        items=result_items,
        total=total,
        page=page,
        page_size=page_size
    )


@router.post("/places/{place_id}/timeline", response_model=TimelineItemResponse, summary="创建时间轴项目")
async def create_timeline_item(
    place_id: int,
    request: TimelineItemCreate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """创建时间轴项目（需要管理员权限）"""
    # 检查地点是否存在且有权限管理
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该地点")
    
    # 创建时间轴项目
    item_data = request.dict()
    item_data['place_id'] = place_id
    item_data['detail_images'] = serialize_json_list(item_data.get('detail_images'))
    item_data['heritage_tags'] = serialize_json_list(item_data.get('heritage_tags'))
    
    item = TimelineItem(**item_data)
    db.add(item)
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
    
    return TimelineItemResponse(**item_dict)


@router.put("/timeline/{item_id}", response_model=TimelineItemResponse, summary="更新时间轴项目")
async def update_timeline_item(
    item_id: int,
    request: TimelineItemUpdate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """更新时间轴项目（需要管理员权限）"""
    item = db.query(TimelineItem).filter(TimelineItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="时间轴项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    # 更新字段
    update_data = request.dict(exclude_unset=True)
    if 'detail_images' in update_data:
        update_data['detail_images'] = serialize_json_list(update_data['detail_images'])
    if 'heritage_tags' in update_data:
        update_data['heritage_tags'] = serialize_json_list(update_data['heritage_tags'])
    
    for field, value in update_data.items():
        setattr(item, field, value)
    
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
    
    return TimelineItemResponse(**item_dict)


@router.get("/timeline/{item_id}", response_model=TimelineItemResponse, summary="获取时间轴项目详情")
async def get_timeline_item(
    item_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """获取时间轴项目详情（需要管理员权限）"""
    item = db.query(TimelineItem).filter(TimelineItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="时间轴项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    # 直接返回原始数据并处理JSON字段
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
    
    return TimelineItemResponse(**item_dict)


@router.delete("/timeline/{item_id}", summary="删除时间轴项目")
async def delete_timeline_item(
    item_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """删除时间轴项目（需要管理员权限）"""
    item = db.query(TimelineItem).filter(TimelineItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="时间轴项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    db.delete(item)
    db.commit()
    
    return {"message": "时间轴项目删除成功"}


# ==================== 文化遗产管理 ====================

@router.get("/places/{place_id}/heritage", response_model=HeritageItemListResponse, summary="获取文化遗产列表")
async def get_heritage_items(
    place_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取指定地点的文化遗产列表"""
    # 检查地点是否存在
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    query = db.query(HeritageItem).filter(
        and_(HeritageItem.place_id == place_id, HeritageItem.is_active == True)
    ).order_by(desc(HeritageItem.sort_order), desc(HeritageItem.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    items = query.offset(offset).limit(page_size).all()
    
    # 处理JSON字段
    result_items = []
    for item in items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        result_items.append(HeritageItemResponse(**item_dict))
    
    return HeritageItemListResponse(
        items=result_items,
        total=total,
        page=page,
        page_size=page_size
    )


@router.post("/places/{place_id}/heritage", response_model=HeritageItemResponse, summary="创建文化遗产项目")
async def create_heritage_item(
    place_id: int,
    request: HeritageItemCreate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """创建文化遗产项目（需要管理员权限）"""
    # 检查地点是否存在且有权限管理
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该地点")
    
    # 创建文化遗产项目
    item_data = request.dict()
    item_data['place_id'] = place_id
    item_data['detail_images'] = serialize_json_list(item_data.get('detail_images'))
    
    item = HeritageItem(**item_data)
    db.add(item)
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return HeritageItemResponse(**item_dict)


@router.put("/heritage/{item_id}", response_model=HeritageItemResponse, summary="更新文化遗产项目")
async def update_heritage_item(
    item_id: int,
    request: HeritageItemUpdate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """更新文化遗产项目（需要管理员权限）"""
    item = db.query(HeritageItem).filter(HeritageItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文化遗产项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    # 更新字段
    update_data = request.dict(exclude_unset=True)
    if 'detail_images' in update_data:
        update_data['detail_images'] = serialize_json_list(update_data['detail_images'])
    
    for field, value in update_data.items():
        setattr(item, field, value)
    
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return HeritageItemResponse(**item_dict)


@router.get("/heritage/{item_id}", response_model=HeritageItemResponse, summary="获取文化遗产项目详情")
async def get_heritage_item(
    item_id: int,
    db: Session = Depends(get_db)
):
    """获取文化遗产项目详情（公开接口，游客可访问）"""
    item = db.query(HeritageItem).filter(HeritageItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文化遗产项目不存在")
    
    # 只返回启用的项目
    if not item.is_active:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文化遗产项目不存在")
    
    # 直接返回原始数据并处理JSON字段
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return HeritageItemResponse(**item_dict)


@router.delete("/heritage/{item_id}", summary="删除文化遗产项目")
async def delete_heritage_item(
    item_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """删除文化遗产项目（需要管理员权限）"""
    item = db.query(HeritageItem).filter(HeritageItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文化遗产项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    db.delete(item)
    db.commit()
    
    return {"message": "文化遗产项目删除成功"}


# ==================== 城市记忆管理 ====================

@router.get("/places/{place_id}/memory", response_model=MemoryItemListResponse, summary="获取城市记忆列表")
async def get_memory_items(
    place_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取指定地点的城市记忆列表"""
    # 检查地点是否存在
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    query = db.query(MemoryItem).filter(
        and_(MemoryItem.place_id == place_id, MemoryItem.is_active == True)
    ).order_by(desc(MemoryItem.sort_order), desc(MemoryItem.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    items = query.offset(offset).limit(page_size).all()
    
    # 处理JSON字段
    result_items = []
    for item in items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        result_items.append(MemoryItemResponse(**item_dict))
    
    return MemoryItemListResponse(
        items=result_items,
        total=total,
        page=page,
        page_size=page_size
    )


@router.post("/places/{place_id}/memory", response_model=MemoryItemResponse, summary="创建城市记忆项目")
async def create_memory_item(
    place_id: int,
    request: MemoryItemCreate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """创建城市记忆项目（需要管理员权限）"""
    # 检查地点是否存在且有权限管理
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="地点不存在")
    
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该地点")
    
    # 创建城市记忆项目
    item_data = request.dict()
    item_data['place_id'] = place_id
    item_data['detail_images'] = serialize_json_list(item_data.get('detail_images'))
    
    item = MemoryItem(**item_data)
    db.add(item)
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return MemoryItemResponse(**item_dict)


@router.put("/memory/{item_id}", response_model=MemoryItemResponse, summary="更新城市记忆项目")
async def update_memory_item(
    item_id: int,
    request: MemoryItemUpdate,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """更新城市记忆项目（需要管理员权限）"""
    item = db.query(MemoryItem).filter(MemoryItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="城市记忆项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    # 更新字段
    update_data = request.dict(exclude_unset=True)
    if 'detail_images' in update_data:
        update_data['detail_images'] = serialize_json_list(update_data['detail_images'])
    
    for field, value in update_data.items():
        setattr(item, field, value)
    
    db.commit()
    db.refresh(item)
    
    # 返回响应
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return MemoryItemResponse(**item_dict)


@router.get("/memory/{item_id}", response_model=MemoryItemResponse, summary="获取城市记忆项目详情")
async def get_memory_item(
    item_id: int,
    db: Session = Depends(get_db)
):
    """获取城市记忆项目详情"""
    item = db.query(MemoryItem).filter(MemoryItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="城市记忆项目不存在")
    
    # 直接返回原始数据并处理JSON字段
    item_dict = item.__dict__.copy()
    item_dict['detail_images'] = parse_json_list(item.detail_images)
    
    return MemoryItemResponse(**item_dict)


@router.delete("/memory/{item_id}", summary="删除城市记忆项目")
async def delete_memory_item(
    item_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """删除城市记忆项目（需要管理员权限）"""
    item = db.query(MemoryItem).filter(MemoryItem.id == item_id).first()
    
    if not item:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="城市记忆项目不存在")
    
    # 检查是否有权限管理该项目
    place = db.query(HeritagePlace).filter(HeritagePlace.id == item.place_id).first()
    if not can_manage_heritage_place(current_user, place):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权管理该项目")
    
    db.delete(item)
    db.commit()
    
    return {"message": "城市记忆项目删除成功"}


# ==================== 综合接口 ====================

@router.get("/places/{place_id}/full", response_model=HeritagePageResponse, summary="获取完整的文化遗产页面数据")
async def get_heritage_page_data(
    place_id: int,
    db: Session = Depends(get_db)
):
    """获取完整的文化遗产页面数据"""
    # 检查地点是否存在
    place = db.query(HeritagePlace).filter(HeritagePlace.id == place_id).first()
    if not place:
        # 返回200状态码，提示当前区域未构建数据
        return JSONResponse(
            status_code=200,
            content={
                "statusCode": 404,
                "message": "当前区域未构建数据",
                "data": None
            }
        )
    
    # 获取时间轴数据
    timeline_items = db.query(TimelineItem).filter(
        and_(TimelineItem.place_id == place_id, TimelineItem.is_active == True)
    ).order_by(desc(TimelineItem.sort_order), desc(TimelineItem.created_at)).all()
    
    # 获取文化遗产数据
    heritage_items = db.query(HeritageItem).filter(
        and_(HeritageItem.place_id == place_id, HeritageItem.is_active == True)
    ).order_by(desc(HeritageItem.sort_order), desc(HeritageItem.created_at)).all()
    
    # 获取城市记忆数据
    memory_items = db.query(MemoryItem).filter(
        and_(MemoryItem.place_id == place_id, MemoryItem.is_active == True)
    ).order_by(desc(MemoryItem.sort_order), desc(MemoryItem.created_at)).all()
    
    # 处理地点信息：直接返回原始数据
    place_dict = place.__dict__.copy()
    
    # 处理JSON字段，直接返回原始数据
    timeline_data = []
    for item in timeline_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        item_dict['heritage_tags'] = parse_json_list(item.heritage_tags)
        timeline_data.append(TimelineItemResponse(**item_dict))
    
    heritage_data = []
    for item in heritage_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        heritage_data.append(HeritageItemResponse(**item_dict))
    
    memory_data = []
    for item in memory_items:
        item_dict = item.__dict__.copy()
        item_dict['detail_images'] = parse_json_list(item.detail_images)
        memory_data.append(MemoryItemResponse(**item_dict))
    
    return HeritagePageResponse(
        place_info=HeritagePlaceResponse(**place_dict),
        timeline_data=timeline_data,
        heritage_data=heritage_data,
        memory_data=memory_data
    )


@router.get("/heritage-tags", response_model=HeritageTagsResponse, summary="获取文化遗产标签列表")
async def get_heritage_tags(
    db: Session = Depends(get_db)
):
    """获取系统中所有文化遗产标签（公开接口，游客可访问）"""
    # 获取系统中存在的所有heritage_tags
    timeline_items = db.query(TimelineItem.heritage_tags).filter(
        and_(TimelineItem.heritage_tags.isnot(None), TimelineItem.is_active == True)
    ).all()
    
    all_tags = set()
    for item in timeline_items:
        if item.heritage_tags:
            tags = parse_json_list(item.heritage_tags)
            all_tags.update(tags)
    
    return HeritageTagsResponse(tags=sorted(list(all_tags))) 