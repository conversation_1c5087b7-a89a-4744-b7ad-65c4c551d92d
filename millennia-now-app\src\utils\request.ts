// 请求工具
// 封装uni.request，提供统一的API请求接口，针对微信小程序做特殊处理

// 导入统一的API配置
import { getApiConfig } from '@/config/api'

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
}

// 响应接口
export interface RequestResponse<T = any> {
  data: T
  statusCode: number
  header: Record<string, string>
}

// 基础配置 - 现在从统一配置获取
const BASE_CONFIG = {
  get baseURL() {
    return getApiConfig().baseURL // 直接从配置获取，避免循环依赖
  },
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  } as Record<string, string>
}

/**
 * 处理请求失败
 */
function handleRequestError(error: any, reject: Function) {
  uni.hideLoading()
  
  // 判断是否为网络错误
  if (error.errMsg && (
    error.errMsg.includes('request:fail') || 
    error.errMsg.includes('timeout')
  )) {
    uni.showToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none',
      duration: 2000
    })
  } else {
    uni.showToast({
      title: error.errMsg || '请求失败',
      icon: 'none',
      duration: 2000
    })
  }
  
  reject(new Error(error.errMsg || '网络请求失败'))
}

/**
 * 微信小程序专用的网络状态检查
 */
function checkNetworkStatus(): Promise<boolean> {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN
    uni.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          uni.showToast({
            title: '当前无网络连接，请检查网络设置',
            icon: 'none',
            duration: 2000
          })
          resolve(false)
        } else {
          resolve(true)
        }
      },
      fail: () => resolve(true) // 检查失败时默认允许请求
    })
    // #endif
    
    // 非微信小程序环境直接返回true
    // #ifndef MP-WEIXIN
    resolve(true)
    // #endif
  })
}

// 添加网络状态监听（微信小程序特有）
export function setupNetworkListener() {
  // #ifdef MP-WEIXIN
  if (uni.canIUse('onNetworkStatusChange')) {
    uni.onNetworkStatusChange(function(res) {
      if (!res.isConnected) {
        uni.showToast({
          title: '网络连接已断开',
          icon: 'none',
          duration: 2000
        })
      } else {
        console.log('网络已连接，类型:', res.networkType)
      }
    })
  }
  // #endif
}

/**
 * 统一请求方法
 */
export function request<T = any>(config: RequestConfig): Promise<T> {
  return new Promise(async (resolve, reject) => {
    // 检查网络状态
    const networkAvailable = await checkNetworkStatus()
    if (!networkAvailable) {
      reject(new Error('网络连接不可用'))
      return
    }

    // 构建完整URL
    const url = config.url.startsWith('http') 
      ? config.url 
      : `${BASE_CONFIG.baseURL}${config.url}`

    // 合并请求头
    const header: Record<string, string> = {
      ...BASE_CONFIG.header,
      ...config.header
    }

    // 获取存储的token（如果有）
    try {
      const token = uni.getStorageSync('access_token')
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }
    } catch (error) {
      console.warn('获取token失败:', error)
    }

    // 发起请求前显示加载中
    uni.showLoading({
      title: '加载中...',
      mask: true
    })

    // 发起请求
    uni.request({
      url,
      method: config.method || 'GET',
      data: config.data,
      header,
      timeout: config.timeout || BASE_CONFIG.timeout,
      success: (response: any) => {
        // 隐藏加载提示
        uni.hideLoading()
        
        // 在微信开发工具控制台输出请求信息
        console.log(`[${config.method || 'GET'}] ${url}:`, response)
        
        // 检查HTTP状态码
        if (response.statusCode >= 200 && response.statusCode < 300) {
          resolve(response.data)
        } else {
          // 错误处理
          let errMsg = response.data?.message || '请求失败'
          
          // 如果是401错误，可能是token过期
          if (response.statusCode === 401) {
            // 清除token
            uni.removeStorageSync('access_token')
            
            // 提示用户重新登录
            uni.showModal({
              title: '提示',
              content: '登录已过期，请重新登录',
              showCancel: false,
              success: () => {
                // 跳转到登录页面
                uni.navigateTo({
                  url: '/pages/user/index'
                })
              }
            })
            errMsg = '登录已过期'
          } else {
            // 显示错误提示
            uni.showToast({
              title: errMsg,
              icon: 'none',
              duration: 2000
            })
          }
          
          console.error('请求失败:', response)
          reject(new Error(`HTTP ${response.statusCode}: ${errMsg}`))
        }
      },
      fail: (error: any) => {
        handleRequestError(error, reject)
      },
      complete: () => {
        // 确保加载提示被隐藏
        uni.hideLoading()
      }
    })
  })
}

/**
 * GET请求
 */
export function get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
  const requestKey = `GET:${url}:${JSON.stringify(params || {})}`
  
  // 如果存在相同请求，则取消前一个
  if (requestTasks.has(requestKey)) {
    requestTasks.get(requestKey)?.abort()
    requestTasks.delete(requestKey)
  }
  
  return new Promise((resolve, reject) => {
    const task = uni.request({
      url: url.startsWith('http') ? url : `${BASE_CONFIG.baseURL}${url}`,
    method: 'GET',
    data: params,
      header: {
        ...BASE_CONFIG.header,
        ...(config?.header || {})
      },
      success: (res: any) => {
        resolve(res.data)
      },
      fail: (err) => {
        reject(err)
      },
      complete: () => {
        requestTasks.delete(requestKey)
      }
    })
    
    requestTasks.set(requestKey, task)
  })
}

/**
 * POST请求
 */
export function post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  })
}

/**
 * PUT请求
 */
export function put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

/**
 * DELETE请求
 */
export function del<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    data: params,
    ...config
  })
}

/**
 * 获取基础URL
 */
export function getBaseURL(): string {
  return BASE_CONFIG.baseURL
}

/**
 * 获取存储的token
 */
export function getToken(): string {
  try {
    return uni.getStorageSync('access_token') || ''
  } catch (error) {
    console.warn('获取token失败:', error)
    return ''
  }
}

// 微信小程序特有的请求任务对象管理
const requestTasks: Map<string, UniApp.RequestTask> = new Map()

// 默认导出
export default {
  request,
  get,
  post,
  put,
  delete: del,
  getBaseURL,
  getToken,
  setupNetworkListener
} 