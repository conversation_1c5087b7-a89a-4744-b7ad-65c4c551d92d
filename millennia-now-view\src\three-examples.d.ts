/**
 * Declaration file for Three.js example modules
 */

// OrbitControls
declare module 'three/examples/jsm/controls/OrbitControls' {
  import { Camera, EventDispatcher, Object3D } from 'three';
  
  export class OrbitControls extends EventDispatcher {
    constructor(object: Camera, domElement?: HTMLElement);
    
    object: Camera;
    domElement: HTMLElement;
    
    enabled: boolean;
    target: Object3D;
    
    // OrbitControls特有属性
    enableDamping: boolean;
    dampingFactor: number;
    enableZoom: boolean;
    autoRotate: boolean;
    autoRotateSpeed: number;
    
    update(): boolean;
    dispose(): void;
  }
}

// PointerLockControls
declare module 'three/examples/jsm/controls/PointerLockControls' {
  import { Camera, EventDispatcher, Object3D, Vector3 } from 'three';
  
  export class PointerLockControls extends EventDispatcher {
    constructor(camera: Camera, domElement?: HTMLElement);
    
    domElement: HTMLElement;
    isLocked: boolean;
    
    connect(): void;
    disconnect(): void;
    dispose(): void;
    getObject(): Object3D;
    getDirection(v: Vector3): Vector3;
    moveForward(distance: number): void;
    moveRight(distance: number): void;
    lock(): void;
    unlock(): void;
  }
}

// GLTFLoader
declare module 'three/examples/jsm/loaders/GLTFLoader' {
  import { AnimationClip, Camera, LoadingManager, Object3D, Scene } from 'three';
  
  export interface GLTF {
    scene: Scene;
    scenes: Scene[];
    cameras: Camera[];
    animations: AnimationClip[];
    asset: Object;
  }
  
  export class GLTFLoader {
    constructor(manager?: LoadingManager);
    
    load(
      url: string,
      onLoad: (gltf: GLTF) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
    
    parse(
      data: ArrayBuffer | string,
      path: string,
      onLoad: (gltf: GLTF) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
  }
}

// DRACOLoader
declare module 'three/examples/jsm/loaders/DRACOLoader' {
  import { LoadingManager } from 'three';
  
  export class DRACOLoader {
    constructor(manager?: LoadingManager);
    
    load(
      url: string,
      onLoad: (geometry: object) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
    
    setDecoderPath(path: string): DRACOLoader;
    setDecoderConfig(config: object): DRACOLoader;
    setWorkerLimit(workerLimit: number): DRACOLoader;
  }
} 