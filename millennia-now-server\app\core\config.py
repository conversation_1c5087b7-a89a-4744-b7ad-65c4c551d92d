"""
应用配置文件
用于存储各类配置信息
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class MinioConfig(BaseSettings):
    """Minio对象存储配置"""
    # Minio服务器配置
    endpoint: str = "192.168.8.96:9000"
    access_key: str = "admin"
    secret_key: str = "12345678"
    secure: bool = False
    
    # 存储桶配置
    bucket_name: str = "millennia-now"
    
    # 文件上传配置
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_extensions: list = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    
    # 图片处理配置
    max_width: int = 1920
    quality: int = 85
    
    class Config:
        env_prefix = "MINIO_"
        case_sensitive = False


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = "root"
    database: str = "millennia_now"
    charset: str = "utf8mb4"
    
    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    
    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    class Config:
        env_prefix = "DB_"
        case_sensitive = False


class RedisConfig(BaseSettings):
    """Redis配置"""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    
    # 连接配置
    max_connections: int = 10
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    
    class Config:
        env_prefix = "REDIS_"
        case_sensitive = False


class SecurityConfig(BaseSettings):
    """安全配置"""
    # JWT配置
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # 微信小程序配置
    wechat_app_id: str = ""
    wechat_app_secret: str = ""
    
    class Config:
        env_prefix = "SECURITY_"
        case_sensitive = False


class AppConfig(BaseSettings):
    """应用配置"""
    # 应用基本信息
    app_name: str = "千年今朝"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # API配置
    api_prefix: str = "/api/v1"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    
    # 跨域配置
    cors_origins: list = ["*"]
    cors_methods: list = ["*"]
    cors_headers: list = ["*"]
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "app.log"
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False


class Settings:
    """统一配置管理"""
    
    def __init__(self):
        self.minio = MinioConfig()
        self.database = DatabaseConfig()
        self.redis = RedisConfig()
        self.security = SecurityConfig()
        self.app = AppConfig()
    
    def get_minio_config(self) -> MinioConfig:
        """获取Minio配置"""
        return self.minio
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return self.database
    
    def get_redis_config(self) -> RedisConfig:
        """获取Redis配置"""
        return self.redis
    
    def get_security_config(self) -> SecurityConfig:
        """获取安全配置"""
        return self.security
    
    def get_app_config(self) -> AppConfig:
        """获取应用配置"""
        return self.app


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings 